# Solana Token Watchlist - Complete Implementation Specification

## Overview
Build a comprehensive Solana token watchlist page that allows users to monitor token prices, manage their watchlist, and integrate with trading functionality. This is a production-ready component with real-time updates, bulk operations, and professional UI/UX.

## Core Features

### 1. Token Management
- **Add Individual Tokens**: Single token addition with contract address validation
- **Bulk Import**: Multi-line textarea supporting formats:
  - `tokenAddress` (one per line)
  - `tokenAddress,customName` (comma-separated)
- **Remove Tokens**: Individual token removal with confirmation
- **Pin/Unpin System**: Star tokens for priority monitoring (pinned items appear first)
- **Custom Names**: Override token symbols with custom names
- **Notes System**: Add and edit notes for each token

### 2. Real-Time Market Data
- **Auto-refresh**: Polls market data every 60 seconds
- **Manual Refresh**: Button to force immediate data update
- **Price Formatting**: Dynamic decimal places based on token value
  - Micro-caps: `$0.000001` (6 decimals)
  - Small caps: `$0.1234` (4 decimals)  
  - Large caps: `$123.456` (3 decimals)
- **Change Indicators**: Color-coded price changes with trend icons
- **Market Metrics**: Price, 1h/24h changes, volume, market cap, FDV, liquidity

### 3. User Interface Components
- **Responsive Table**: Horizontal scroll on mobile, sortable columns
- **Modal Dialogs**: Add token, bulk import, edit notes
- **Dropdown Menus**: Token actions (edit, view explorer, remove)
- **Loading States**: Spinners and skeleton screens
- **Empty States**: Helpful messaging when no tokens exist
- **Toast Notifications**: Success/error feedback for all actions

## Technical Implementation

### Data Structures

\`\`\`typescript
interface WatchlistItem {
  id: string
  tokenAddress: string
  tokenSymbol?: string
  tokenName?: string
  customName?: string
  notes?: string
  isPinned: boolean
  createdAt: Date
  updatedAt: Date
  metrics?: TokenMetrics
}

interface TokenMetrics {
  price: string
  priceChange1h?: number
  priceChange24h?: number
  volume24h?: string
  marketCap?: string
  fdv?: string
  liquidity?: string
}
\`\`\`

### Required Dependencies
\`\`\`json
{
  "dependencies": {
    "react": "^18.0.0",
    "next": "^14.0.0",
    "lucide-react": "^0.263.1",
    "sonner": "^1.0.0"
  }
}
\`\`\`

### Component Architecture
- **Main Component**: `WatchlistPage` (client component)
- **UI Components**: shadcn/ui components (Card, Table, Dialog, Button, etc.)
- **State Management**: React useState/useEffect hooks
- **API Integration**: Mock API calls with realistic delays

## Detailed Feature Specifications

### Header Section
\`\`\`tsx
// Header with title, description, and action buttons
<header className="border-b border-border">
  <div className="container mx-auto px-4 py-4">
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-2xl font-bold">Watchlist</h1>
        <p className="text-muted-foreground">Monitor tokens and discover trading opportunities</p>
      </div>
      <div className="flex items-center space-x-2">
        {/* Refresh, Bulk Add, Add Token buttons */}
      </div>
    </div>
    {/* Last update timestamp */}
  </div>
</header>
\`\`\`

### Data Table Structure
\`\`\`tsx
<Table>
  <TableHeader>
    <TableRow>
      <TableHead className="w-8"></TableHead> {/* Pin column */}
      <TableHead>Token</TableHead>
      <TableHead className="text-right">Price</TableHead>
      <TableHead className="text-right">1h</TableHead>
      <TableHead className="text-right">24h</TableHead>
      <TableHead className="text-right">Volume</TableHead>
      <TableHead className="text-right">Market Cap</TableHead>
      <TableHead className="w-24">Actions</TableHead>
    </TableRow>
  </TableHeader>
  <TableBody>
    {/* Dynamic rows with token data */}
  </TableBody>
</Table>
\`\`\`

### Modal Dialogs

#### Add Token Dialog
- **Fields**: Token address (required), Notes (optional)
- **Validation**: Address length >= 32 characters
- **Options**: Checkbox to "Open swap after save"
- **Actions**: Add Token, Cancel

#### Bulk Import Dialog
- **Field**: Textarea for multiple addresses
- **Format Support**: 
  - `EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v`
  - `EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v,USDC`
- **Parsing**: Split by newlines, then by comma/pipe for custom names
- **Options**: Checkbox to "Open swap after save"

#### Edit Notes Dialog
- **Field**: Textarea for notes editing
- **Context**: Shows token symbol/name being edited
- **Actions**: Save Notes, Cancel

### Sorting Logic
\`\`\`typescript
const sortedWatchlist = [...watchlist].sort((a, b) => {
  // Pinned items first
  if (a.isPinned && !b.isPinned) return -1
  if (!a.isPinned && b.isPinned) return 1
  // Then by creation date (newest first)
  return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
})
\`\`\`

### Price Change Formatting
\`\`\`typescript
const formatChange = (change: number) => {
  const isPositive = change >= 0
  return (
    <span className={`flex items-center ${isPositive ? "text-green-600" : "text-red-600"}`}>
      {isPositive ? <TrendingUp className="h-3 w-3 mr-1" /> : <TrendingDown className="h-3 w-3 mr-1" />}
      {Math.abs(change).toFixed(2)}%
    </span>
  )
}
\`\`\`

### Integration Points

#### Swap Integration
- **Route**: `/swap?mint={tokenAddress}`
- **Trigger**: Arrow button in actions column
- **Behavior**: Opens in new tab/window

#### Explorer Integration
- **URL**: `https://solscan.io/token/{tokenAddress}`
- **Trigger**: "View on Solscan" dropdown menu item
- **Behavior**: Opens in new tab

### Mock Data Implementation

#### Sample Watchlist Items
\`\`\`typescript
const mockWatchlist: WatchlistItem[] = [
  {
    id: "1",
    tokenAddress: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
    tokenSymbol: "USDC",
    tokenName: "USD Coin",
    customName: null,
    notes: "Stable coin for reference",
    isPinned: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    metrics: {
      price: "1.000",
      priceChange1h: 0.01,
      priceChange24h: -0.02,
      volume24h: "1.2B",
      marketCap: "32.1B",
      fdv: "32.1B",
      liquidity: "850M",
    },
  },
  // Add more sample tokens...
]
\`\`\`

#### Market Data Simulation
\`\`\`typescript
const updateMarketData = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800))
  
  // Update prices with random fluctuation
  setWatchlist(prev => prev.map(item => ({
    ...item,
    metrics: item.metrics ? {
      ...item.metrics,
      price: (parseFloat(item.metrics.price) * (0.98 + Math.random() * 0.04)).toFixed(3),
      priceChange1h: (Math.random() - 0.5) * 10,
      priceChange24h: (Math.random() - 0.5) * 20,
    } : undefined
  })))
}
\`\`\`

## Visual Design Requirements

### Color Scheme
- **Positive Changes**: `text-green-600` with `TrendingUp` icon
- **Negative Changes**: `text-red-600` with `TrendingDown` icon
- **Pinned Badge**: `variant="secondary"` with yellow star icon
- **Muted Text**: `text-muted-foreground` for secondary information

### Typography
- **Headers**: `text-2xl font-bold` for main title
- **Token Symbols**: `font-medium` for primary token display
- **Addresses/Prices**: `font-mono` for monospace formatting
- **Notes**: `italic` styling for user notes

### Spacing & Layout
- **Container**: `container mx-auto px-4` for consistent margins
- **Card Padding**: `py-8` for main content, `py-4` for header
- **Button Spacing**: `space-x-2` between action buttons
- **Table Cells**: Appropriate padding with `text-right` for numbers

### Responsive Behavior
- **Mobile**: Horizontal scroll for table, stacked layout for dialogs
- **Desktop**: Full table display, side-by-side dialog layouts
- **Touch Targets**: Minimum 44px for mobile tap targets

## Error Handling & Edge Cases

### Validation Rules
- **Token Address**: Minimum 32 characters, alphanumeric + base58
- **Bulk Import**: Skip invalid lines, show count of successful imports
- **Network Errors**: Graceful fallback with retry options
- **Empty States**: Helpful messaging and call-to-action buttons

### Loading States
- **Initial Load**: Full-page spinner with "Loading watchlist..." message
- **Refresh**: Button spinner with disabled state
- **Add Token**: Form submission loading state
- **Market Updates**: Subtle "Updating..." indicator

### Toast Notifications
- **Success**: "Token added to watchlist", "Pin status updated", etc.
- **Errors**: "Failed to load watchlist", "Failed to add token", etc.
- **Info**: Bulk import results with count of successful additions

## Performance Considerations

### Optimization Strategies
- **Polling**: 60-second intervals, pause when tab inactive
- **State Updates**: Batch updates for market data refresh
- **Memory**: Clean up intervals on component unmount
- **Rendering**: Memoize expensive calculations and formatting

### Data Management
- **Local State**: Use React useState for UI state
- **Persistence**: Consider localStorage for user preferences
- **API Calls**: Debounce user inputs, cache responses when possible

## Testing Requirements

### Unit Tests
- Token addition/removal functionality
- Pin/unpin toggle behavior
- Price formatting functions
- Sorting logic validation

### Integration Tests
- Modal dialog workflows
- Bulk import parsing
- Market data updates
- Navigation to swap page

### User Experience Tests
- Mobile responsiveness
- Loading state transitions
- Error message clarity
- Accessibility compliance

## Implementation Checklist

- [ ] Set up component structure with TypeScript interfaces
- [ ] Implement basic CRUD operations for watchlist items
- [ ] Create modal dialogs for add/edit functionality
- [ ] Build responsive data table with sorting
- [ ] Add pin/unpin functionality with visual indicators
- [ ] Implement bulk import with parsing logic
- [ ] Create market data polling system
- [ ] Add price formatting and change indicators
- [ ] Integrate with swap page routing
- [ ] Add external explorer links
- [ ] Implement toast notifications
- [ ] Add loading states and error handling
- [ ] Test responsive design on mobile devices
- [ ] Validate accessibility compliance
- [ ] Add comprehensive error boundaries

This specification provides everything needed to recreate the exact watchlist functionality with professional-grade features and user experience.
