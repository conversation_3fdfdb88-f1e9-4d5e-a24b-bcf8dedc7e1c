# Epic 2: Token Discovery & Watchlist System

**Epic Goal:** Provide comprehensive token research and monitoring capabilities that enable informed trading decisions through real-time market data aggregation and seamless integration with the trading workflow.

## Story 2.1: Watchlist Data Model and Storage
**As a trader,**  
**I want a persistent watchlist to track tokens of interest with custom notes and organization,**  
**so that I can maintain a curated list of potential trading opportunities.**

### Acceptance Criteria
1. Prisma database model supporting token address, name, symbol, notes, and pinned status
2. Database migration and schema generation for watchlist_items table
3. Unique constraint on token address to prevent duplicates
4. Automatic timestamp tracking for created_at and updated_at fields
5. Support for custom token naming and personal notes storage
6. Pin/unpin functionality for priority token highlighting
7. Soft delete capability to maintain historical watchlist data
8. Performance indexes for efficient querying by pin status and creation date

## Story 2.2: Market Data Integration Framework
**As a trader,**  
**I want real-time market data for watchlist tokens including price, volume, and fundamental metrics,**  
**so that I can assess trading opportunities with current market information.**

### Acceptance Criteria
1. Extensible metrics adapter interface supporting multiple data providers
2. Token snapshot data structure including price, 1h/24h changes, liquidity, FDV, and age
3. Stub implementation returning mock data with clear integration points for future APIs
4. Batch data fetching capability to optimize API usage across multiple tokens
5. Error handling and fallback strategies for unavailable data sources
6. Data freshness tracking and cache invalidation for accurate information
7. Rate limit management framework for external API compliance
8. Future integration planning for Birdeye, Jupiter, and Helius API endpoints

## Story 2.3: Watchlist Management API Endpoints
**As a system,**  
**I need robust API endpoints for watchlist CRUD operations with proper validation,**  
**so that the frontend can reliably manage watchlist data with comprehensive error handling.**

### Acceptance Criteria
1. GET /api/watchlist endpoint returning all items ordered by pinned status and creation date
2. POST /api/watchlist endpoint for single token addition with address validation using @solana/web3.js
3. PATCH /api/watchlist/[id] endpoint for updating pin status, notes, and custom names
4. DELETE /api/watchlist/[id] endpoint for token removal from watchlist
5. POST /api/watchlist/bulk endpoint supporting multi-line token import with validation
6. Bulk import with automatic deduplication and invalid address filtering
7. GET /api/watchlist/metrics endpoint merging watchlist data with current market metrics
8. Comprehensive error responses with actionable feedback for validation failures
9. Proper HTTP status codes and JSON response formatting throughout all endpoints

## Story 2.4: State-Aware Polling and Performance Optimization
**As a system,**  
**I need intelligent polling frequency management based on user engagement and token proximity to action thresholds,**  
**so that market data remains current while optimizing API usage and system resources.**

### Acceptance Criteria
1. Default 60-second polling interval for standard watchlist monitoring
2. Accelerated 15-second polling when any tokens are pinned (high priority)
3. Ultra-fast 7-10 second polling when tokens approach predefined alert thresholds (future enhancement)
4. Dynamic polling interval adjustment based on current watchlist state
5. Efficient batch API calls combining all watchlist tokens in single requests
6. Client-side polling management using useEffect and setInterval patterns
7. Cache-busting strategies ensuring fresh data on each poll cycle
8. Performance monitoring and optimization for large watchlist sizes
9. Graceful degradation when external APIs are unavailable or rate-limited

## Story 2.5: Comprehensive Watchlist User Interface
**As a trader,**  
**I want an intuitive watchlist interface with inline editing, bulk operations, and direct trading integration,**  
**so that I can efficiently research tokens and seamlessly transition to trading decisions.**

### Acceptance Criteria
1. Clean table layout displaying pin status, token info, price metrics, and action buttons
2. Inline name editing with click-to-edit functionality and keyboard shortcuts (Enter/Escape)
3. Pin toggle buttons with visual indicators (★/☆) and immediate state updates
4. Single token addition dialog with address input and optional note field
5. Bulk addition dialog supporting multiple input formats (address only, address|name, address,name)
6. Bulk addition with "Open swap after save" option routing to last valid token
7. Direct "Send to Swap" buttons routing to /swap?mint=<address> for seamless trading
8. Token removal with confirmation and toast notifications for user feedback
9. Responsive design working effectively on both desktop and mobile devices
10. Professional styling using shadcn/ui components consistent with application design
11. Real-time polling status indicator showing current refresh interval
12. Error handling with user-friendly messages for all operations

## Story 2.6: Trading Integration and Workflow Optimization  
**As a trader,**  
**I want seamless integration between watchlist research and trading execution,**  
**so that I can move efficiently from token discovery to position creation.**

### Acceptance Criteria
1. Direct routing from watchlist to trading interface with pre-populated token address
2. Optional Zustand state management integration for shared token selection
3. Consistent token address handling across watchlist and trading components
4. URL parameter support for bookmarking specific tokens (/swap?mint=<address>)
5. Trading interface recognition of watchlist-originated token selections
6. Breadcrumb navigation allowing easy return to watchlist from trading interface
7. Recent trading activity influence on watchlist prioritization (future enhancement)
8. Integration testing ensuring smooth workflow across both interfaces
