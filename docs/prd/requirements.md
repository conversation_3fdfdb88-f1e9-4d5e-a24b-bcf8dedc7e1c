# Requirements

## Functional Requirements

**FR1**: The system shall accept manual token contract address input and display token information before purchase  
**FR2**: The system shall integrate with Jupiter Aggregator API to find optimal buy routes and quotes  
**FR3**: The system shall use Jupiter Swap API to build buy transactions with MEV protection parameters  
**FR4**: The system shall execute buy transactions using local wallet signing and Helius RPC  
**FR5**: The system shall automatically attach predefined exit strategies immediately after successful buy confirmation  
**FR6**: The system shall support multi-tier take profit levels with configurable percentage allocations  
**FR7**: The system shall implement stop loss and trailing stop mechanisms  
**FR8**: The system shall poll CMC DEX API for price monitoring to trigger exits  
**FR9**: The system shall use Helius Webhooks for instant on-chain event alerts (optional enhancement)  
**FR10**: The system shall execute sell transactions within 5 seconds using Jupiter Aggregator + Helius RPC  
**FR11**: The system shall provide real-time PnL tracking through backend + price feed integration  
**FR12**: The system shall send alerts via Telegram/Discord for buy confirmations, exit triggers, and closures  
**FR13**: The system shall enable manual position closure with immediate MEV-protected execution  
**FR14**: The system shall log all trades to backend database for analytics and history dashboard  
**FR15**: The system shall provide a watchlist page for token discovery and pre-trade analysis with real-time market data

## Non-Functional Requirements

**NFR1**: All exit transactions must execute within 5 seconds of trigger activation to prevent slippage cascades  
**NFR2**: The system must operate within Helius free tier limits (1M credits/month, 10 req/sec, 1 TX/sec)  
**NFR3**: Price polling must batch multiple coins in single CMC DEX API calls to optimize rate limits  
**NFR4**: Transaction success rate must exceed 95% under normal network conditions  
**NFR5**: MEV protection (priority fees + compute units) must be enabled for all transactions  
**NFR6**: The system must maintain 24/7 uptime for position monitoring and exit execution  
**NFR7**: Local wallet signing must be supported without browser extension dependencies  
**NFR8**: System must handle concurrent exit triggers through transaction queuing (respecting 1 TX/sec limit)  
**NFR9**: Data persistence must maintain trade history and position state across restarts  
**NFR10**: Error handling must gracefully manage failed transactions with retry mechanisms
