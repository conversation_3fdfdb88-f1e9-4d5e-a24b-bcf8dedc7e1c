# Solana Trading App Fullstack Architecture Document

## Table of Contents

- [Solana Trading App Fullstack Architecture Document](#table-of-contents)
  - [Introduction](./introduction.md)
    - [Starter Template or Existing Project](./introduction.md#starter-template-or-existing-project)
    - [Change Log](./introduction.md#change-log)
  - [High Level Architecture](./high-level-architecture.md)
    - [Technical Summary](./high-level-architecture.md#technical-summary)
    - [Platform and Infrastructure Choice](./high-level-architecture.md#platform-and-infrastructure-choice)
    - [Repository Structure](./high-level-architecture.md#repository-structure)
    - [High Level Architecture Diagram](./high-level-architecture.md#high-level-architecture-diagram)
    - [Architectural Patterns](./high-level-architecture.md#architectural-patterns)
  - [Tech Stack](./tech-stack.md)
    - [Technology Stack Table](./tech-stack.md#technology-stack-table)
  - [Data Models](./data-models.md)
    - [Position](./data-models.md#position)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [ExitStrategy](./data-models.md#exitstrategy)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [Transaction](./data-models.md#transaction)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [WatchlistItem](./data-models.md#watchlistitem)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [PriceSnapshot](./data-models.md#pricesnapshot)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
  - [API Specification](./api-specification.md)
    - [REST API Specification](./api-specification.md#rest-api-specification)
  - [Components](./components.md)
    - [Trading Engine](./components.md#trading-engine)
    - [Exit Strategy Engine](./components.md#exit-strategy-engine)
    - [Price Monitor](./components.md#price-monitor)
    - [Position Manager](./components.md#position-manager)
    - [Watchlist Manager](./components.md#watchlist-manager)
    - [Notification System](./components.md#notification-system)
    - [Frontend Dashboard](./components.md#frontend-dashboard)
  - [External APIs](./external-apis.md)
    - [Jupiter Aggregator API](./external-apis.md#jupiter-aggregator-api)
    - [Helius RPC API](./external-apis.md#helius-rpc-api)
    - [CoinMarketCap DEX API](./external-apis.md#coinmarketcap-dex-api)
    - [Telegram Bot API](./external-apis.md#telegram-bot-api)
  - [Core Workflows](./core-workflows.md)
  - [Database Schema](./database-schema.md)
  - [Frontend Architecture](./frontend-architecture.md)
    - [Component Architecture](./frontend-architecture.md#component-architecture)
      - [Component Organization](./frontend-architecture.md#component-organization)
      - [Component Template](./frontend-architecture.md#component-template)
    - [State Management Architecture](./frontend-architecture.md#state-management-architecture)
      - [State Structure](./frontend-architecture.md#state-structure)
      - [State Management Patterns](./frontend-architecture.md#state-management-patterns)
    - [Routing Architecture](./frontend-architecture.md#routing-architecture)
      - [Route Organization](./frontend-architecture.md#route-organization)
      - [Protected Route Pattern](./frontend-architecture.md#protected-route-pattern)
    - [Frontend Services Layer](./frontend-architecture.md#frontend-services-layer)
      - [API Client Setup](./frontend-architecture.md#api-client-setup)
      - [Service Example](./frontend-architecture.md#service-example)
  - [Backend Architecture](./backend-architecture.md)
    - [Service Architecture](./backend-architecture.md#service-architecture)
      - [Controller/Route Organization](./backend-architecture.md#controllerroute-organization)
      - [Controller Template](./backend-architecture.md#controller-template)
    - [Database Architecture](./backend-architecture.md#database-architecture)
      - [Schema Design](./backend-architecture.md#schema-design)
      - [Data Access Layer](./backend-architecture.md#data-access-layer)
    - [Authentication and Authorization](./backend-architecture.md#authentication-and-authorization)
      - [Auth Flow](./backend-architecture.md#auth-flow)
      - [Middleware/Guards](./backend-architecture.md#middlewareguards)
  - [Unified Project Structure](./unified-project-structure.md)
  - [Development Workflow](./development-workflow.md)
    - [Local Development Setup](./development-workflow.md#local-development-setup)
      - [Prerequisites](./development-workflow.md#prerequisites)
      - [Initial Setup](./development-workflow.md#initial-setup)
      - [Development Commands](./development-workflow.md#development-commands)
    - [Environment Configuration](./development-workflow.md#environment-configuration)
      - [Required Environment Variables](./development-workflow.md#required-environment-variables)
  - [Deployment Architecture](./deployment-architecture.md)
    - [Deployment Strategy](./deployment-architecture.md#deployment-strategy)
    - [CI/CD Pipeline](./deployment-architecture.md#cicd-pipeline)
    - [Environments](./deployment-architecture.md#environments)
  - [Security and Performance](./security-and-performance.md)
    - [Security Requirements](./security-and-performance.md#security-requirements)
    - [Performance Optimization](./security-and-performance.md#performance-optimization)
  - [Testing Strategy](./testing-strategy.md)
    - [Testing Pyramid](./testing-strategy.md#testing-pyramid)
    - [Test Organization](./testing-strategy.md#test-organization)
      - [Frontend Tests](./testing-strategy.md#frontend-tests)
      - [Backend Tests](./testing-strategy.md#backend-tests)
      - [E2E Tests](./testing-strategy.md#e2e-tests)
    - [Test Examples](./testing-strategy.md#test-examples)
      - [Frontend Component Test](./testing-strategy.md#frontend-component-test)
      - [Backend API Test](./testing-strategy.md#backend-api-test)
      - [E2E Test](./testing-strategy.md#e2e-test)
  - [Coding Standards](./coding-standards.md)
    - [Critical Fullstack Rules](./coding-standards.md#critical-fullstack-rules)
    - [Naming Conventions](./coding-standards.md#naming-conventions)
  - [Error Handling Strategy](./error-handling-strategy.md)
    - [Error Flow](./error-handling-strategy.md#error-flow)
    - [Error Response Format](./error-handling-strategy.md#error-response-format)
    - [Frontend Error Handling](./error-handling-strategy.md#frontend-error-handling)
    - [Backend Error Handling](./error-handling-strategy.md#backend-error-handling)
  - [Overview](./overview.md)
  - [Prerequisites Installation (Exact Versions)](./prerequisites-installation-exact-versions.md)
    - [1. Node.js 20.x LTS](./prerequisites-installation-exact-versions.md#1-nodejs-20x-lts)
    - [2. pnpm Package Manager](./prerequisites-installation-exact-versions.md#2-pnpm-package-manager)
    - [3. Docker and Docker Compose](./prerequisites-installation-exact-versions.md#3-docker-and-docker-compose)
    - [4. Git Configuration](./prerequisites-installation-exact-versions.md#4-git-configuration)
    - [5. Additional Development Tools](./prerequisites-installation-exact-versions.md#5-additional-development-tools)
  - [Repository Setup and Monorepo Configuration](./repository-setup-and-monorepo-configuration.md)
    - [1. Clone and Initialize Repository](./repository-setup-and-monorepo-configuration.md#1-clone-and-initialize-repository)
    - [2. Create Monorepo Structure](./repository-setup-and-monorepo-configuration.md#2-create-monorepo-structure)
    - [3. Setup Workspace Packages](./repository-setup-and-monorepo-configuration.md#3-setup-workspace-packages)
  - [Environment Configuration (Complete .env Setup)](./environment-configuration-complete-env-setup.md)
    - [1. Frontend Environment Variables](./environment-configuration-complete-env-setup.md#1-frontend-environment-variables)
    - [2. Backend Environment Variables](./environment-configuration-complete-env-setup.md#2-backend-environment-variables)
    - [3. Required API Keys Setup](./environment-configuration-complete-env-setup.md#3-required-api-keys-setup)
      - [Helius API Key](./environment-configuration-complete-env-setup.md#helius-api-key)
      - [CoinMarketCap API Key](./environment-configuration-complete-env-setup.md#coinmarketcap-api-key)
      - [Telegram Bot (Optional)](./environment-configuration-complete-env-setup.md#telegram-bot-optional)
      - [Solana Wallet Setup](./environment-configuration-complete-env-setup.md#solana-wallet-setup)
  - [Local Services Infrastructure (Docker-based)](./local-services-infrastructure-docker-based.md)
    - [1. Docker Compose Configuration](./local-services-infrastructure-docker-based.md#1-docker-compose-configuration)
    - [2. Start Local Services](./local-services-infrastructure-docker-based.md#2-start-local-services)
    - [3. Service Access Points](./local-services-infrastructure-docker-based.md#3-service-access-points)
  - [Database Setup and Initialization](./database-setup-and-initialization.md)
    - [1. Create Database Initialization Script](./database-setup-and-initialization.md#1-create-database-initialization-script)
    - [2. Setup Prisma](./database-setup-and-initialization.md#2-setup-prisma)
    - [3. Generate and Run Migrations](./database-setup-and-initialization.md#3-generate-and-run-migrations)
    - [4. Verify Database Setup](./database-setup-and-initialization.md#4-verify-database-setup)
  - [Application Startup (Multi-service)](./application-startup-multi-service.md)
    - [1. Frontend Setup (Next.js)](./application-startup-multi-service.md#1-frontend-setup-nextjs)
    - [2. Backend Setup (Express.js)](./application-startup-multi-service.md#2-backend-setup-expressjs)
    - [3. Concurrent Development Setup](./application-startup-multi-service.md#3-concurrent-development-setup)
  - [Verification and Testing Steps](./verification-and-testing-steps.md)
    - [1. Service Health Checks](./verification-and-testing-steps.md#1-service-health-checks)
    - [2. API Endpoint Testing](./verification-and-testing-steps.md#2-api-endpoint-testing)
    - [3. Database Connectivity Tests](./verification-and-testing-steps.md#3-database-connectivity-tests)
    - [4. Frontend Integration Testing](./verification-and-testing-steps.md#4-frontend-integration-testing)
    - [5. Job Queue Testing](./verification-and-testing-steps.md#5-job-queue-testing)
  - [Development Workflow Commands](./development-workflow-commands.md)
    - [1. Daily Development Commands](./development-workflow-commands.md#1-daily-development-commands)
    - [2. Database Operations](./development-workflow-commands.md#2-database-operations)
    - [3. Code Quality and Testing](./development-workflow-commands.md#3-code-quality-and-testing)
    - [4. Debugging and Monitoring](./development-workflow-commands.md#4-debugging-and-monitoring)
    - [5. Environment Management](./development-workflow-commands.md#5-environment-management)
    - [6. Troubleshooting Commands](./development-workflow-commands.md#6-troubleshooting-commands)
  - [Next Steps](./next-steps.md)
  - [Monitoring and Observability](./monitoring-and-observability.md)
    - [Monitoring Stack](./monitoring-and-observability.md#monitoring-stack)
    - [Key Metrics](./monitoring-and-observability.md#key-metrics)
