# Coding Standards

## Critical Fullstack Rules

- **Type Sharing:** Always define types in packages/shared and import from there - prevents frontend/backend type mismatches
- **API Calls:** Never make direct HTTP calls - use the service layer for consistent error handling and retry logic
- **Environment Variables:** Access only through config objects, never process.env directly - enables validation and type safety
- **Error Handling:** All API routes must use the standard error handler - ensures consistent error responses
- **State Updates:** Never mutate state directly - use proper state management patterns to prevent bugs
- **Database Queries:** Always use repository pattern with Prisma - enables testing and consistent data access
- **Decimal Math:** Use Decimal.js for all financial calculations - prevents floating point precision errors
- **Async Operations:** Always handle Promise rejections - use proper try/catch or .catch() methods
- **Configuration:** Validate all config at startup - fail fast if environment is misconfigured

## Naming Conventions

| Element | Frontend | Backend | Example |
|---------|----------|---------|---------|
| Components | PascalCase | - | `UserProfile.tsx` |
| Hooks | camelCase with 'use' | - | `useAuth.ts` |
| API Routes | - | kebab-case | `/api/user-profile` |
| Database Tables | - | snake_case | `user_profiles` |
| Constants | SCREAMING_SNAKE_CASE | SCREAMING_SNAKE_CASE | `MAX_SLIPPAGE_BPS` |
| Services | PascalCase | PascalCase | `TradingService` |
| Types/Interfaces | PascalCase | PascalCase | `Position`, `ExitStrategy` |
