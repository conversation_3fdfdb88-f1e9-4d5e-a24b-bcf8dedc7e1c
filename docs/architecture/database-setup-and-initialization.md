# Database Setup and Initialization

## 1. Create Database Initialization Script

```bash
mkdir -p database/init
cat > database/init/01-create-extensions.sql << 'EOF'
-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "timescaledb" CASCADE;

-- Create database if not exists
SELECT 'CREATE DATABASE solana_trading_db'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'solana_trading_db');
EOF
```

## 2. Setup Prisma

```bash
cd apps/api
