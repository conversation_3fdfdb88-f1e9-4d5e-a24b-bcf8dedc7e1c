# API Specification

## REST API Specification

```yaml
openapi: 3.0.0
info:
  title: Solana Trading App API
  version: 1.0.0
  description: RESTful API for automated Solana token trading with exit strategies
servers:
  - url: https://api-production-railway.app
    description: Production server
  - url: http://localhost:3001
    description: Local development server

paths:
  # Trading endpoints
  /api/trades/quote:
    post:
      summary: Get Jupiter buy quote for token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [tokenAddress, amountUsd, slippageBps]
              properties:
                tokenAddress:
                  type: string
                  example: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
                amountUsd:
                  type: number
                  example: 100.50
                slippageBps:
                  type: integer
                  example: 300
      responses:
        '200':
          description: Quote retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteResponse'

  /api/trades/buy:
    post:
      summary: Execute buy transaction with MEV protection
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [tokenAddress, amountUsd, slippageBps, exitStrategy]
              properties:
                tokenAddress:
                  type: string
                amountUsd:
                  type: number
                slippageBps:
                  type: integer
                exitStrategy:
                  $ref: '#/components/schemas/ExitStrategyInput'
      responses:
        '200':
          description: Buy transaction initiated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionResponse'

  # Position management
  /api/positions:
    get:
      summary: Get all active positions
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [active, closing, closed, all]
            default: active
      responses:
        '200':
          description: Positions retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Position'

    delete:
      summary: Close all active positions (emergency)
      responses:
        '200':
          description: All positions closing initiated

  /api/positions/{id}:
    get:
      summary: Get position details
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Position details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PositionDetail'

    patch:
      summary: Update position (modify exit strategy)
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                exitStrategy:
                  $ref: '#/components/schemas/ExitStrategyInput'
                isActive:
                  type: boolean
      responses:
        '200':
          description: Position updated

    delete:
      summary: Close position immediately
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Position closing initiated

  # Watchlist management
  /api/watchlist:
    get:
      summary: Get all watchlist items with market data
      responses:
        '200':
          description: Watchlist items with current prices
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/WatchlistItemWithPrice'

    post:
      summary: Add token to watchlist
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [tokenAddress]
              properties:
                tokenAddress:
                  type: string
                customName:
                  type: string
                notes:
                  type: string
                isPinned:
                  type: boolean
                  default: false
      responses:
        '201':
          description: Token added to watchlist

    post:
      summary: Bulk add tokens to watchlist
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [tokens]
              properties:
                tokens:
                  type: array
                  items:
                    type: object
                    required: [tokenAddress]
                    properties:
                      tokenAddress:
                        type: string
                      customName:
                        type: string
                      notes:
                        type: string
      responses:
        '201':
          description: Tokens bulk added

  /api/watchlist/{id}:
    patch:
      summary: Update watchlist item
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                customName:
                  type: string
                notes:
                  type: string
                isPinned:
                  type: boolean
      responses:
        '200':
          description: Watchlist item updated

    delete:
      summary: Remove from watchlist
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Item removed from watchlist

  # System endpoints
  /api/health:
    get:
      summary: System health check
      responses:
        '200':
          description: System status
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "healthy"
                  services:
                    type: object
                    properties:
                      database:
                        type: string
                      redis:
                        type: string
                      jupiter:
                        type: string
                      helius:
                        type: string
                      cmc:
                        type: string

components:
  schemas:
    Position:
      type: object
      properties:
        id:
          type: string
        tokenAddress:
          type: string
        tokenSymbol:
          type: string
        amountTokens:
          type: string
        entryPrice:
          type: string
        status:
          type: string
          enum: [active, closing, closed]
        currentPrice:
          type: string
        pnlUsd:
          type: string
        pnlPercent:
          type: string
        createdAt:
          type: string
          format: date-time

    ExitStrategyInput:
      type: object
      properties:
        takeProfitTiers:
          type: array
          items:
            type: object
            properties:
              targetPrice:
                type: string
              percentageToSell:
                type: integer
        stopLoss:
          type: object
          properties:
            triggerPrice:
              type: string
            percentageToSell:
              type: integer
        trailingStop:
          type: object
          properties:
            trailDistance:
              type: string

    QuoteResponse:
      type: object
      properties:
        inputMint:
          type: string
        outputMint:
          type: string
        outputAmount:
          type: string
        priceImpact:
          type: string
        route:
          type: array
          items:
            type: object

    TransactionResponse:
      type: object
      properties:
        signature:
          type: string
        status:
          type: string
        positionId:
          type: string

    WatchlistItemWithPrice:
      type: object
      properties:
        id:
          type: string
        tokenAddress:
          type: string
        tokenSymbol:
          type: string
        customName:
          type: string
        notes:
          type: string
        isPinned:
          type: boolean
        currentPrice:
          type: string
        priceChange24h:
          type: string
        volume24h:
          type: string
        marketCap:
          type: string
```
