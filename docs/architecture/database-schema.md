# Database Schema

```sql
-- Enable TimescaleDB extension for time-series optimization
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Positions table: Core trading positions
CREATE TABLE positions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_address VARCHAR(50) NOT NULL,
    token_symbol VARCHAR(20),
    token_name VARCHAR(100),
    amount_tokens DECIMAL(20,8) NOT NULL,
    entry_price DECIMAL(20,8) NOT NULL,
    entry_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    status VARCHAR(20) NOT NULL DEFAULT 'active'
        CHECK (status IN ('active', 'closing', 'closed')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Exit strategies table: Automation rules for positions
CREATE TABLE exit_strategies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    position_id UUID NOT NULL REFERENCES positions(id) ON DELETE CASCADE,
    take_profit_tiers JSONB, -- Array of {tierNumber, targetPrice, percentageToSell, status}
    stop_loss JSONB, -- {triggerPrice, percentageToSell, status}
    trailing_stop JSONB, -- {trailDistance, highestPrice, currentStopPrice, isActive}
    moon_bag JSONB, -- {percentage, minPrice}
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(position_id) -- One strategy per position
);

-- Transactions table: Immutable blockchain transaction records
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    position_id UUID NOT NULL REFERENCES positions(id),
    type VARCHAR(10) NOT NULL CHECK (type IN ('buy', 'sell', 'fee')),
    signature VARCHAR(100) NOT NULL UNIQUE,
    amount_tokens DECIMAL(20,8) NOT NULL,
    price_per_token DECIMAL(20,8) NOT NULL,
    total_usd DECIMAL(20,8) NOT NULL,
    fees JSONB NOT NULL, -- {networkFee, priorityFee, jupiterFee}
    status VARCHAR(20) NOT NULL DEFAULT 'pending'
        CHECK (status IN ('pending', 'confirmed', 'failed')),
    block_number BIGINT,
    executed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Watchlist table: Tokens tracked for potential trading
CREATE TABLE watchlist_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_address VARCHAR(50) NOT NULL UNIQUE,
    token_symbol VARCHAR(20),
    token_name VARCHAR(100),
    custom_name VARCHAR(100),
    notes TEXT,
    is_pinned BOOLEAN NOT NULL DEFAULT false,
    added_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_viewed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Price snapshots table: TimescaleDB hypertable for time-series price data
CREATE TABLE price_snapshots (
    timestamp TIMESTAMPTZ NOT NULL,
    token_address VARCHAR(50) NOT NULL,
    price_usd DECIMAL(20,8) NOT NULL,
    volume_24h DECIMAL(20,2),
    market_cap DECIMAL(20,2),
    price_change_1h DECIMAL(8,4),
    price_change_24h DECIMAL(8,4),
    liquidity DECIMAL(20,2),
    fdv DECIMAL(20,2), -- Fully diluted valuation
    source VARCHAR(20) NOT NULL CHECK (source IN ('cmc', 'jupiter', 'helius')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Convert price_snapshots to TimescaleDB hypertable
SELECT create_hypertable('price_snapshots', 'timestamp');

-- Job queue state table: BullMQ job persistence and monitoring
CREATE TABLE job_queue_state (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id VARCHAR(100) NOT NULL UNIQUE,
    job_type VARCHAR(50) NOT NULL, -- 'price_monitor', 'exit_execution', 'notification'
    status VARCHAR(20) NOT NULL DEFAULT 'pending'
        CHECK (status IN ('pending', 'active', 'completed', 'failed', 'delayed')),
    data JSONB NOT NULL,
    priority INTEGER NOT NULL DEFAULT 0,
    attempts INTEGER NOT NULL DEFAULT 0,
    max_attempts INTEGER NOT NULL DEFAULT 3,
    scheduled_at TIMESTAMPTZ,
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    error_message TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for query optimization
CREATE INDEX idx_positions_status ON positions(status);
CREATE INDEX idx_positions_token_address ON positions(token_address);
CREATE INDEX idx_positions_created_at ON positions(created_at DESC);

CREATE INDEX idx_exit_strategies_position_id ON exit_strategies(position_id);
CREATE INDEX idx_exit_strategies_is_active ON exit_strategies(is_active);

CREATE INDEX idx_transactions_position_id ON transactions(position_id);
CREATE INDEX idx_transactions_signature ON transactions(signature);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_type ON transactions(type);

CREATE INDEX idx_watchlist_is_pinned ON watchlist_items(is_pinned DESC);
CREATE INDEX idx_watchlist_created_at ON watchlist_items(created_at DESC);
CREATE INDEX idx_watchlist_token_address ON watchlist_items(token_address);

-- TimescaleDB optimized indexes for price data
CREATE INDEX idx_price_snapshots_token_timestamp ON price_snapshots(token_address, timestamp DESC);
CREATE INDEX idx_price_snapshots_source ON price_snapshots(source);

CREATE INDEX idx_job_queue_status ON job_queue_state(status);
CREATE INDEX idx_job_queue_type ON job_queue_state(job_type);
CREATE INDEX idx_job_queue_scheduled_at ON job_queue_state(scheduled_at);

-- State-aware polling configuration table
CREATE TABLE polling_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL UNIQUE,
    interval_seconds INTEGER NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Insert default polling configurations
INSERT INTO polling_config (name, interval_seconds, description) VALUES
('watchlist_default', 60, 'Standard watchlist monitoring'),
('watchlist_pinned', 15, 'Enhanced monitoring for pinned tokens'),
('position_active', 30, 'Active position monitoring (armed mode)'),
('position_near_target', 5, 'Ultra-fast polling near exit triggers');

-- Update triggers for timestamp management
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_positions_updated_at BEFORE UPDATE ON positions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_exit_strategies_updated_at BEFORE UPDATE ON exit_strategies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_watchlist_items_updated_at BEFORE UPDATE ON watchlist_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_job_queue_state_updated_at BEFORE UPDATE ON job_queue_state
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_polling_config_updated_at BEFORE UPDATE ON polling_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```
