# Tech Stack

## Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|----------|-----------|
| Frontend Language | TypeScript | 5.x | Type-safe React development | Prevents runtime errors and enables better IDE support |
| Frontend Framework | Next.js | 14.x | React framework with App Router | Built-in optimization, API routes, and deployment integration |
| UI Component Library | shadcn/ui | Latest | Consistent, customizable components | Professional design system with Tailwind integration |
| State Management | Zustand | 4.x | Lightweight state management | Minimal boilerplate compared to Redux, perfect for trading data |
| Backend Language | Node.js + TypeScript | 20.x LTS | Unified language across stack | Shared types and reduced context switching |
| Backend Framework | Express.js | 4.x | Lightweight, flexible API framework | Battle-tested with extensive middleware ecosystem |
| API Style | REST | - | RESTful API design | Simple, cacheable, works well with trading operations |
| Database | PostgreSQL + TimescaleDB | 15.x | Time-series optimized relational DB | Excellent performance for price data with SQL familiarity |
| Cache | Redis | 7.x | In-memory cache and job queue | High-performance caching and BullMQ backend |
| File Storage | Local/Vercel Static | - | Static asset serving | Simple deployment with CDN distribution |
| Authentication | Session-based | - | Simplified single-user auth | No JWT complexity needed for single-user application |
| Frontend Testing | Vitest | 1.x | Fast unit testing | Faster than Jest with better ESM support |
| Backend Testing | Vitest | 1.x | Unified testing framework | Consistent tooling across frontend and backend |
| E2E Testing | Playwright | Latest | End-to-end browser testing | Reliable automation testing for trading workflows |
| Build Tool | npm | 10.x | Package management and scripts | Built-in workspace support for monorepo |
| Bundler | Next.js built-in | - | Webpack-based bundling | Optimized for React with automatic code splitting |
| IaC Tool | Railway CLI | Latest | Infrastructure deployment | Simple configuration for full-stack deployment |
| CI/CD | GitHub Actions | - | Automated testing and deployment | Free tier sufficient, integrates with Vercel/Railway |
| Monitoring | Railway + Vercel Analytics | - | Application performance monitoring | Built-in monitoring with both platforms |
| Logging | Pino | 8.x | High-performance structured logging | Fast JSON logging with good TypeScript support |
| CSS Framework | Tailwind CSS | 3.x | Utility-first styling | Rapid development with consistent design system |
| Containerization | Docker + Docker Compose | Latest | Local development environment | Consistent PostgreSQL, Redis, and TimescaleDB setup across machines |
