# Frontend Architecture

## Component Architecture

### Component Organization

```
src/
├── components/
│   ├── ui/               # shadcn/ui base components
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   ├── dialog.tsx
│   │   └── table.tsx
│   ├── trading/          # Trading-specific components
│   │   ├── TradingPanel.tsx
│   │   ├── QuoteDisplay.tsx
│   │   ├── ExitStrategyForm.tsx
│   │   └── SlippageSelector.tsx
│   ├── positions/        # Position management components
│   │   ├── PositionCard.tsx
│   │   ├── PositionDashboard.tsx
│   │   ├── PositionDetails.tsx
│   │   └── ManualCloseButton.tsx
│   ├── watchlist/        # Watchlist components
│   │   ├── WatchlistTable.tsx
│   │   ├── AddTokenDialog.tsx
│   │   ├── BulkAddDialog.tsx
│   │   └── WatchlistMetrics.tsx
│   ├── layout/           # Layout and navigation
│   │   ├── Header.tsx
│   │   ├── Navigation.tsx
│   │   ├── StatusBar.tsx
│   │   └── AlertCenter.tsx
│   └── charts/           # Data visualization
│       ├── PriceChart.tsx
│       ├── PnLChart.tsx
│       └── PerformanceChart.tsx
```

### Component Template

```typescript
interface PositionCardProps {
  position: Position;
  currentPrice?: number;
  onClose: (positionId: string) => void;
  onModifyStrategy: (positionId: string) => void;
  className?: string;
}

export function PositionCard({
  position,
  currentPrice,
  onClose,
  onModifyStrategy,
  className
}: PositionCardProps) {
  const { pnlUsd, pnlPercent } = usePositionPnL(position, currentPrice);

  return (
    <Card className={cn("p-4", className)}>
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-semibold">{position.tokenSymbol}</h3>
          <p className="text-sm text-muted-foreground">
            {formatTokenAmount(position.amountTokens)} tokens
          </p>
        </div>
        <div className="text-right">
          <div className={cn(
            "text-lg font-bold",
            pnlPercent >= 0 ? "text-green-600" : "text-red-600"
          )}>
            {pnlPercent >= 0 ? '+' : ''}{pnlPercent.toFixed(2)}%
          </div>
          <div className="text-sm text-muted-foreground">
            ${pnlUsd?.toFixed(2)}
          </div>
        </div>
      </div>

      <div className="mt-4 flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onModifyStrategy(position.id)}
        >
          Modify Strategy
        </Button>
        <Button
          variant="destructive"
          size="sm"
          onClick={() => onClose(position.id)}
        >
          Close Position
        </Button>
      </div>
    </Card>
  );
}
```

## State Management Architecture

### State Structure

```typescript
// stores/tradingStore.ts
interface TradingState {
  // Active positions with real-time updates
  positions: Position[];
  selectedPosition: Position | null;

  // Trading panel state
  currentQuote: TradingQuote | null;
  isQuoteLoading: boolean;
  quoteError: string | null;

  // Exit strategy configuration
  exitStrategy: ExitStrategyConfig;
  strategyPresets: ExitStrategyPreset[];

  // Actions
  setPositions: (positions: Position[]) => void;
  updatePosition: (id: string, updates: Partial<Position>) => void;
  selectPosition: (position: Position | null) => void;

  fetchQuote: (tokenAddress: string, amountUsd: number) => Promise<void>;
  executeBuy: (quote: TradingQuote, strategy: ExitStrategyConfig) => Promise<void>;
  closePosition: (positionId: string) => Promise<void>;
}

// stores/watchlistStore.ts
interface WatchlistState {
  items: WatchlistItem[];
  isLoading: boolean;
  error: string | null;

  // Polling state
  pollingInterval: number; // Dynamic based on pinned items
  lastUpdate: Date | null;

  // Actions
  addItem: (tokenAddress: string, metadata?: Partial<WatchlistItem>) => Promise<void>;
  bulkAdd: (tokens: BulkAddToken[]) => Promise<void>;
  updateItem: (id: string, updates: Partial<WatchlistItem>) => Promise<void>;
  removeItem: (id: string) => Promise<void>;

  refreshPrices: () => Promise<void>;
  startPolling: () => void;
  stopPolling: () => void;
}

// stores/systemStore.ts
interface SystemState {
  isOnline: boolean;
  apiStatus: Record<string, 'healthy' | 'degraded' | 'down'>;
  notifications: Notification[];

  // Real-time price updates
  priceUpdates: Record<string, PriceData>; // tokenAddress -> price

  addNotification: (notification: Omit<Notification, 'id'>) => void;
  removeNotification: (id: string) => void;
  updatePrices: (updates: Record<string, PriceData>) => void;
}
```

### State Management Patterns

- **Separation of Concerns:** Each store manages a specific domain (trading, watchlist, system)
- **Computed Values:** Derived state like PnL calculations computed in custom hooks
- **Optimistic Updates:** UI updates immediately, with rollback on API failure
- **Real-time Sync:** WebSocket-style price updates merged into stores
- **Persistence:** Critical state (exit strategies) persisted to localStorage as backup
- **Error Boundaries:** Each store includes error state for graceful failure handling

## Routing Architecture

### Route Organization

```
app/
├── layout.tsx            # Root layout with providers
├── page.tsx             # Dashboard/home page
├── trading/             # Trading interface
│   ├── page.tsx         # Main trading panel
│   └── quote/[id]/      # Quote detail page
├── positions/           # Position management
│   ├── page.tsx         # Position dashboard
│   └── [id]/            # Position detail page
├── watchlist/           # Token research
│   ├── page.tsx         # Watchlist table
│   └── add/            # Add tokens page
├── settings/            # Configuration
│   ├── page.tsx         # General settings
│   ├── strategies/      # Exit strategy presets
│   └── notifications/   # Alert preferences
└── api/                 # API routes
    ├── trades/
    ├── positions/
    ├── watchlist/
    └── health/
```

### Protected Route Pattern

```typescript
// components/auth/ProtectedRoute.tsx
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
}

export function ProtectedRoute({
  children,
  requiredPermission
}: ProtectedRouteProps) {
  const { isAuthenticated, hasPermission } = useAuth();

  if (!isAuthenticated) {
    redirect('/login');
  }

  if (requiredPermission && !hasPermission(requiredPermission)) {
    return (
      <div className="p-8 text-center">
        <h2 className="text-xl font-semibold">Access Denied</h2>
        <p>You don't have permission to view this page.</p>
      </div>
    );
  }

  return <>{children}</>;
}

// app/positions/layout.tsx
export default function PositionsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ProtectedRoute requiredPermission="view_positions">
      <div className="container mx-auto py-6">
        {children}
      </div>
    </ProtectedRoute>
  );
}
```

## Frontend Services Layer

### API Client Setup

```typescript
// lib/api-client.ts
class ApiClient {
  private baseURL: string;
  private defaultHeaders: HeadersInit;

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001';
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    const config: RequestInit = {
      ...options,
      headers: {
        ...this.defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new ApiError(data.error?.message || 'Request failed', response.status);
      }

      return {
        data,
        status: response.status,
        headers: response.headers,
      };
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError('Network error', 0);
    }
  }

  async get<T>(endpoint: string, params?: Record<string, string>): Promise<T> {
    const searchParams = params ? `?${new URLSearchParams(params)}` : '';
    const result = await this.request<T>(`${endpoint}${searchParams}`);
    return result.data;
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    const result = await this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
    return result.data;
  }

  async patch<T>(endpoint: string, data: any): Promise<T> {
    const result = await this.request<T>(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
    return result.data;
  }

  async delete<T>(endpoint: string): Promise<T> {
    const result = await this.request<T>(endpoint, {
      method: 'DELETE',
    });
    return result.data;
  }
}

export const apiClient = new ApiClient();
```

### Service Example

```typescript
// services/tradingService.ts
export class TradingService {
  async getQuote(tokenAddress: string, amountUsd: number, slippageBps: number): Promise<TradingQuote> {
    return apiClient.post<TradingQuote>('/api/trades/quote', {
      tokenAddress,
      amountUsd,
      slippageBps,
    });
  }

  async executeBuy(
    tokenAddress: string,
    amountUsd: number,
    slippageBps: number,
    exitStrategy: ExitStrategyConfig
  ): Promise<TransactionResult> {
    return apiClient.post<TransactionResult>('/api/trades/buy', {
      tokenAddress,
      amountUsd,
      slippageBps,
      exitStrategy,
    });
  }

  async getPositions(status: PositionStatus = 'active'): Promise<Position[]> {
    return apiClient.get<Position[]>('/api/positions', { status });
  }

  async closePosition(positionId: string): Promise<TransactionResult> {
    return apiClient.delete<TransactionResult>(`/api/positions/${positionId}`);
  }

  async modifyExitStrategy(
    positionId: string,
    exitStrategy: ExitStrategyConfig
  ): Promise<Position> {
    return apiClient.patch<Position>(`/api/positions/${positionId}`, {
      exitStrategy,
    });
  }
}

export const tradingService = new TradingService();

// hooks/useTrading.ts - React hook wrapper
export function useTrading() {
  const [positions, setPositions] = useState<Position[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPositions = useCallback(async (status?: PositionStatus) => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await tradingService.getPositions(status);
      setPositions(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch positions');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const closePosition = useCallback(async (positionId: string) => {
    try {
      setError(null);
      await tradingService.closePosition(positionId);
      // Optimistically remove from UI
      setPositions(prev => prev.filter(p => p.id !== positionId));
      // Refresh to get actual state
      await fetchPositions();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to close position');
      // Revert optimistic update
      await fetchPositions();
    }
  }, [fetchPositions]);

  return {
    positions,
    isLoading,
    error,
    fetchPositions,
    closePosition,
  };
}
```
