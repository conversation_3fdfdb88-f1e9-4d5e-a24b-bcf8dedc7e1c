# Development Workflow Documentation

This document outlines the development workflow, best practices, and guidelines for the BMad Method Solana trading application.

## Table of Contents

- [Getting Started](#getting-started)
- [Development Environment Setup](#development-environment-setup)
- [Code Quality Standards](#code-quality-standards)
- [Testing Strategies](#testing-strategies)
- [Security Practices](#security-practices)
- [Database Management](#database-management)
- [API Development](#api-development)
- [Frontend Development](#frontend-development)
- [Deployment Guidelines](#deployment-guidelines)
- [Troubleshooting](#troubleshooting)

## Getting Started

### Prerequisites

- Node.js >= 20.0.0
- npm >= 10.0.0
- Docker and Docker Compose
- Git

### Quick Setup

```bash
# 1. Clone and install dependencies
npm install

# 2. Setup development services (PostgreSQL, Redis)
npm run dev:services

# 3. Setup environment variables
cp .env.example .env
# Edit .env with your configuration

# 4. Generate development wallet
npm run wallet:create
npm run wallet:secrets

# 5. Initialize database
npm run db:migrate
npm run db:seed

# 6. Start development servers
npm run dev
```

### Alternative: Fresh Setup

```bash
# Complete fresh setup (cleans everything first)
npm run setup:fresh
```

## Development Environment Setup

### Environment Variables

Use the provided `.env.example` as a template:

```bash
# Copy and customize
cp .env.example .env

# Validate environment configuration
npm run validate:env
npm run wallet:validate
```

### Development Wallet

The application uses a secure wallet system for development:

```bash
# Create new development wallet
npm run wallet:create

# Generate security secrets
npm run wallet:secrets

# View wallet info (without exposing private key)
npm run wallet:info

# Export wallet keys (use with caution)
npm run wallet:export

# Validate wallet configuration
npm run wallet:validate
```

**Security Note**: Development wallets are for testing only. Never use development keys with real funds.

### Database Setup

```bash
# Run migrations
npm run db:migrate

# Seed development data
npm run db:seed

# Open Prisma Studio for database management
npm run db:studio

# Reset database (destructive)
npm run db:reset
```

### Docker Services

```bash
# Start all services (PostgreSQL, Redis)
npm run docker:up

# Stop all services
npm run docker:down

# View service logs
npm run docker:logs
```

## Code Quality Standards

### Linting and Formatting

The project uses ESLint and Prettier with strict configurations:

```bash
# Run linting (auto-fix)
npm run lint

# Check linting without fixing
npm run lint:check

# Format code
npm run format

# Check formatting
npm run format:check

# Run all quality checks
npm run check

# Fix common issues
npm run fix
```

### Pre-commit Hooks

Husky and lint-staged ensure code quality:

- **Pre-commit**: Runs ESLint and Prettier on staged files
- **Commit-msg**: Validates commit message format (conventional commits)

### TypeScript

- Strict TypeScript configuration
- No explicit `any` types (warn level)
- Comprehensive type coverage

```bash
# Type checking
npm run typecheck
```

## Testing Strategies

### Test Structure

```
apps/api/
├── src/
│   ├── __tests__/          # Unit tests
│   ├── lib/__tests__/      # Library unit tests
│   └── middleware/__tests__/ # Middleware tests
└── tests/
    ├── integration/        # Integration tests
    ├── e2e/               # End-to-end tests
    └── mocks/             # Mock services
```

### Running Tests

```bash
# Run all tests
npm run test

# Unit tests only
npm run test:unit

# Integration tests only
npm run test:integration

# End-to-end tests
npm run test:e2e

# Watch mode for development
npm run test:watch

# Generate coverage report
npm run test:coverage

# Full coverage report with badges
npm run test:coverage:report
```

### Coverage Requirements

- **Lines**: 80% minimum
- **Functions**: 85% minimum
- **Branches**: 75% minimum
- **Statements**: 80% minimum

Coverage badges are automatically generated in `coverage/badges.md`.

### Mock Services

External APIs are mocked for testing:

- **Jupiter Aggregator**: Token swaps and quotes
- **Helius RPC**: Solana blockchain data
- **CoinMarketCap**: Price data

Mock services provide realistic responses for comprehensive testing.

## Security Practices

### Request Validation

All API endpoints use comprehensive validation:

```typescript
import { validateSchema, commonSchemas } from '../middleware/validation';

// Example endpoint with validation
app.post('/api/trade',
  validateSchema({
    body: z.object({
      tokenAddress: z.string().regex(/^[1-9A-HJ-NP-Za-km-z]{32,44}$/),
      amount: z.string().refine(val => !isNaN(parseFloat(val)) && parseFloat(val) > 0)
    }),
    query: commonSchemas.pagination,
  }),
  tradeController
);
```

### Security Middleware

- **Rate Limiting**: Different limits for various endpoint types
- **Request Sanitization**: XSS protection with DOMPurify
- **Security Headers**: Comprehensive security headers with Helmet
- **Request Size Limiting**: Prevents DoS attacks

### Data Retention

Automated data cleanup for compliance:

```typescript
// Configure retention policies
const retentionConfig = {
  policies: [
    { table: 'trade_logs', retentionPeriodDays: 90 },
    { table: 'api_logs', retentionPeriodDays: 30 },
    { table: 'audit_logs', retentionPeriodDays: 2555 }, // 7 years
  ],
  enabled: true,
  dryRun: false, // Set to true for testing
};
```

Cleanup runs automatically daily at 2 AM UTC.

## Database Management

### Migrations

```bash
# Create new migration
npx prisma migrate dev --name migration_name

# Apply migrations to production
npx prisma migrate deploy

# Reset database (development only)
npx prisma migrate reset
```

### Schema Development

1. Edit `prisma/schema.prisma`
2. Generate migration: `npm run db:migrate`
3. Update seed data if needed: `prisma/seed.ts`
4. Test with fresh database: `npm run db:reset`

### Data Retention

Monitor data retention policies:

```bash
# Check retention status
curl http://localhost:3000/admin/retention/status

# Trigger manual cleanup (admin only)
curl -X POST http://localhost:3000/admin/retention/cleanup
```

## API Development

### Endpoint Structure

```
/api/v1/
├── auth/          # Authentication
├── trading/       # Trading operations
├── wallet/        # Wallet management
├── tokens/        # Token information
├── history/       # Historical data
└── admin/         # Admin operations
```

### Error Handling

Consistent error responses:

```typescript
// Success response
{
  success: true,
  data: { ... },
  meta: { pagination, timestamp }
}

// Error response
{
  success: false,
  error: {
    type: 'validation_error',
    message: 'Request validation failed',
    errors: [{ field: 'amount', message: 'Must be positive' }]
  }
}
```

### Logging

Structured logging with Pino:

```typescript
import { apiLogger } from '../lib/logger';

apiLogger.info({ userId, action: 'trade_executed' }, 'Trade completed successfully');
apiLogger.warn({ error, context }, 'Validation warning');
apiLogger.error({ error, stack: error.stack }, 'Unexpected error occurred');
```

## Frontend Development

### Component Structure

```
apps/web/src/
├── components/
│   ├── ui/            # Reusable UI components
│   ├── features/      # Feature-specific components
│   └── layout/        # Layout components
├── hooks/             # Custom React hooks
├── services/          # API services
├── stores/            # State management
└── utils/             # Utility functions
```

### State Management

- **React Query**: Server state and caching
- **Zustand**: Client state management
- **React Hook Form**: Form state

### Testing Frontend

```bash
# Frontend unit tests
npm run test:unit --workspace=apps/web

# Component testing with React Testing Library
npm run test --workspace=apps/web

# E2E tests with Playwright
npm run test:e2e --workspace=apps/web
```

## Deployment Guidelines

### Pre-deployment Checklist

1. **Code Quality**: All linting and type checks pass
2. **Tests**: All tests pass with required coverage
3. **Security**: Security scan completed
4. **Environment**: Production environment variables set
5. **Database**: Migrations ready
6. **Monitoring**: Health checks configured

### Environment-specific Configuration

```bash
# Development
NODE_ENV=development
ENABLE_DATA_RETENTION=false
LOG_LEVEL=debug

# Staging
NODE_ENV=staging
ENABLE_DATA_RETENTION=true
LOG_LEVEL=info

# Production
NODE_ENV=production
ENABLE_DATA_RETENTION=true
LOG_LEVEL=warn
```

### Database Migrations

```bash
# Production deployment
npm run build
npx prisma migrate deploy
npm run start
```

## Troubleshooting

### Common Issues

#### Development Wallet Issues

```bash
# Wallet not found
npm run wallet:create

# Invalid configuration
npm run wallet:validate
npm run validate:env

# Permission denied on wallet file
chmod 600 .dev-wallet.json
```

#### Database Connection Issues

```bash
# Check Docker services
npm run docker:logs

# Reset database connection
npm run docker:down
npm run docker:up
npm run db:migrate
```

#### Test Failures

```bash
# Clear test cache
npm run test -- --clearCache

# Run specific test file
npm run test -- src/lib/__tests__/wallet.test.ts

# Debug mode
npm run test -- --inspect-brk
```

#### Build Issues

```bash
# Clean and rebuild
npm run clean
npm install
npm run build

# Check TypeScript errors
npm run typecheck
```

### Performance Monitoring

- **Database**: Monitor query performance with `EXPLAIN ANALYZE`
- **API**: Use response time monitoring
- **Memory**: Monitor Node.js heap usage
- **Trading**: Monitor execution times and success rates

### Debugging

```bash
# API debugging
DEBUG=app:* npm run dev:api

# Database query debugging
DEBUG=prisma:query npm run dev:api

# Full debug mode
DEBUG=* npm run dev
```

### Health Checks

- **API**: `GET /health`
- **Database**: Connection and migration status
- **Redis**: Connection and performance
- **External APIs**: Mock service availability

## Best Practices Summary

1. **Always run tests** before committing
2. **Use semantic commit messages** (conventional commits)
3. **Validate environment** configuration regularly
4. **Monitor data retention** policies
5. **Keep dependencies updated** with security patches
6. **Document breaking changes** thoroughly
7. **Use TypeScript strictly** - avoid `any` types
8. **Implement comprehensive error handling**
9. **Follow security guidelines** for wallet management
10. **Maintain test coverage** above minimum thresholds

---

For questions or issues not covered in this documentation, please refer to:

- **Project Issues**: GitHub Issues tracker
- **API Documentation**: Generated OpenAPI docs
- **Database Schema**: Prisma Studio
- **Monitoring**: Application logs and metrics