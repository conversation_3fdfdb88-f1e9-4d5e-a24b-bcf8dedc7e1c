price
GET
https://lite-api.jup.ag/price/v3
Returns prices of specified tokens.

note
Refer to Price API doc for more information.
Request
Query Parameters
ids
string
required
Comma separate to pass in multiple

Example: So11111111111111111111111111111111111111112,EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v
Responses
200
400
500
Successful response

application/json
Schema
Example (auto)
Schema
property name*
object

const axios = require('axios');

let config = {
  method: 'get',
  maxBodyLength: Infinity,
  url: 'https://lite-api.jup.ag/price/v3',
  headers: {
    'Accept': 'application/json'
  }
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});


Request
Collapse all
Base URL
https://lite-api.jup.ag/price/v3
Parameters
ids — queryrequired
Comma separate to pass in multiple
Send API Request
Price API V3 (Beta)
Price API V3 aims to provide a one source of truth across all Jupiter UIs and integrator platforms. The simplified format allows easy integration while letting Jupiter handle the complexity of ensuring the accuracy of the provided prices.

note
Lite URL: https://lite-api.jup.ag/price/v3
Pro URL: https://api.jup.ag/price/v3
To upgrade to Pro or understand our rate limiting, please refer to this section.

API Key Setup
API Rate Limit
info
This is in Beta and subject to changes, if you need help please reach out to us in Discord

How Price is Derived
Price API V3 price tokens by using the last swapped price (across all transactions). The swaps are priced by working outwards from a small set of reliable tokens (like SOL) whose price we get from external oracle sources.

While and also after deriving the last swap price, we also utilize a number of heuristics to ensure the accuracy of the price and eliminate any outliers:

Asset origin and launch method
Market liquidity metrics
Market behaviour patterns
Holder distribution statistics
Trading activity indicators
Organic Score
Get Price
Simply request via the base URL with the query parameters of your desired mint addresses. You can also comma-separate them to request for multiple prices.

const price = await (
    await fetch(
        'https://lite-api.jup.ag/price/v3?ids=So11111111111111111111111111111111111111112,JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN'
    )
).json();
console.log(JSON.stringify(price, null, 2));


Price Response
Here is the sample response, notice a few details here:

The usdPrice is the only price.
The decimals response is helpful to display price information on the UI.
The blockId can be used to verify the recency of the price.
{
  "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN": {
    "usdPrice": 0.4056018512541055,
    "blockId": 348004026,
    "decimals": 6,
    "priceChange24h": 0.5292887924920519
  },
  "So11111111111111111111111111111111111111112": {
    "usdPrice": 147.4789340738336,
    "blockId": 348004023,
    "decimals": 9,
    "priceChange24h": 1.2907622140620008
  }
}

Limitations
Query limits
You can query up to 50 ids at once.
If the price of a token cannot be found
Typically, it is likely that the token has not been traded recently - in the last 7 days.
There are also additional risk factors (refer to How Price is Derived) we consider when determining if a token's price can be provided - if certain combinations of these factors indicate potential issues with price reliability or market health, the token will be flagged and not provided a price.
V2 had more information
Yes V2 had more information, however, we think that it is not the best representation of price and it also caused different interpretations of price across the different platforms.
With Price API V3, we are handling the complexities to ensure price accuracy and elimate outliers using the heuristics as mentioned above, so there will only be one stable and accurate price source for all.
If you require more information like Price API V2, you can use the /quote endpoint of the Swap API to derive those data (you can refer to this post about how Price API V2 is derived).
const axios = require('axios');

let config = {
  method: 'get',
  maxBodyLength: Infinity,
  url: 'https://lite-api.jup.ag/price/v3',
  headers: {
    'Accept': 'application/json'
  }
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log(error);
});price
GET
https://lite-api.jup.ag/price/v3
Returns prices of specified tokens.

note
Refer to Price API doc for more information.
Request
Query Parameters
ids
string
required
Comma separate to pass in multiple

Example: So11111111111111111111111111111111111111112,EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v
Responses
200
400
500
Successful response

application/json
Schema
Example (auto)

400

Bad request
500
Internal server error
