### CoinMarketCap API Documentation

Introduction
The CoinMarketCap API is a suite of high-performance RESTful JSON endpoints that are specifically designed to meet the mission-critical demands of application developers, data scientists, and enterprise business platforms.

This API reference includes all technical documentation developers need to integrate third-party applications and platforms. Additional answers to common questions can be found in the CoinMarketCap API FAQ.

Quick Start Guide
For developers eager to hit the ground running with the CoinMarketCap API here are a few quick steps to make your first call with the API.

Sign up for a free Developer Portal account. You can sign up at pro.coinmarketcap.com - This is our live production environment with the latest market data. Select the free Basic plan if it meets your needs or upgrade to a paid tier.
Copy your API Key. Once you sign up you'll land on your Developer Portal account dashboard. Copy your API from the API Key box in the top left panel.
Make a test call using your key. You may use the code examples provided below to make a test call with your programming language of choice. This example fetches all active cryptocurrencies by market cap and return market values in USD.
Be sure to replace the API Key in sample code with your own and use API domain pro-api.coinmarketcap.com or use the test API Key b54bcf4d-1bca-4e8e-9a24-22ff2c3d462c for sandbox-api.coinmarketcap.com testing with our sandbox environment. Please note that our sandbox api has mock data and should not be used in your application.
Postman Collection To help with development, we provide a fully featured postman collection that you can import and use immediately! Read more here.
Implement your application. Now that you've confirmed your API Key is working, get familiar with the API by reading the rest of this API Reference and commence building your application!
Note: Making HTTP requests on the client side with Javascript is currently prohibited through CORS configuration. This is to protect your API Key which should not be visible to users of your application so your API Key is not stolen. Secure your API Key by routing calls through your own backend service.

View Quick Start Code Examples
cURL command line

curl -H "X-CMC_PRO_API_KEY: b54bcf4d-1bca-4e8e-9a24-22ff2c3d462c" -H "Accept: application/json" -d "start=1&limit=5000&convert=USD" -G https://sandbox-api.coinmarketcap.com/v1/cryptocurrency/listings/latest

Node.js
Python
PHP
Java
C#
Go
Authentication
Acquiring an API Key
All HTTP requests made against the CoinMarketCap API must be validated with an API Key. If you don't have an API Key yet visit the API Developer Portal to register for one.

Using Your API Key
You may use any server side programming language that can make HTTP requests to target the CoinMarketCap API. All requests should target domain https://pro-api.coinmarketcap.com.

You can supply your API Key in REST API calls in one of two ways:

Preferred method: Via a custom header named X-CMC_PRO_API_KEY
Convenience method: Via a query string parameter named CMC_PRO_API_KEY
Security Warning: It's important to secure your API Key against public access. The custom header option is strongly recommended over the querystring option for passing your API Key in a production environment.

API Key Usage Credits
Most API plans include a daily and monthly limit or "hard cap" to the number of data calls that can be made. This usage is tracked as API "call credits" which are incremented 1:1 against successful (HTTP Status 200) data calls made with your key with these exceptions:

Account management endpoints, usage stats endpoints, and error responses are not included in this limit.
Paginated endpoints: List-based endpoints track an additional call credit for every 100 data points returned (rounded up) beyond our 100 data point defaults. Our lightweight /map endpoints are not included in this limit and always count as 1 credit. See individual endpoint documentation for more details.
Bundled API calls: Many endpoints support resource and currency conversion bundling. Bundled resources are also tracked as 1 call credit for every 100 resources returned (rounded up). Optional currency conversion bundling using the convert parameter also increment an additional API call credit for every conversion requested beyond the first.
You can log in to the Developer Portal to view live stats on your API Key usage and limits including the number of credits used for each call. You can also find call credit usage in the JSON response for each API call. See the status object for details. You may also use the /key/info endpoint to quickly review your usage and when daily/monthly credits reset directly from the API.

Note: "day" and "month" credit usage periods are defined relative to your API subscription. For example, if your monthly subscription started on the 5th at 5:30am, this billing anchor is also when your monthly credits refresh each month. The free Basic tier resets each day at UTC midnight and each calendar month at UTC midnight.

Endpoint Overview
The CoinMarketCap API is divided into 8 top-level categories
Endpoint Category	Description
/cryptocurrency/*	Endpoints that return data around cryptocurrencies such as ordered cryptocurrency lists or price and volume data.
/exchange/*	Endpoints that return data around cryptocurrency exchanges such as ordered exchange lists and market pair data.
/global-metrics/*	Endpoints that return aggregate market data such as global market cap and BTC dominance.
/tools/*	Useful utilities such as cryptocurrency and fiat price conversions.
/blockchain/*	Endpoints that return block explorer related data for blockchains.
/fiat/*	Endpoints that return data around fiats currencies including mapping to CMC IDs.
/partners/*	Endpoints for convenient access to 3rd party crypto data.
/key/*	API key administration endpoints to review and manage your usage.
/content/*	Endpoints that return cryptocurrency-related news, headlines, articles, posts, and comments.
Endpoint paths follow a pattern matching the type of data provided
Endpoint Path	Endpoint Type	Description
*/latest	Latest Market Data	Latest market ticker quotes and averages for cryptocurrencies and exchanges.
*/historical	Historical Market Data	Intervals of historic market data like OHLCV data or data for use in charting libraries.
*/info	Metadata	Cryptocurrency and exchange metadata like block explorer URLs and logos.
*/map	ID Maps	Utility endpoints to get a map of resources to CoinMarketCap IDs.
Cryptocurrency and exchange endpoints provide 2 different ways of accessing data depending on purpose
Listing endpoints: Flexible paginated */listings/* endpoints allow you to sort and filter lists of data like cryptocurrencies by market cap or exchanges by volume.
Item endpoints: Convenient ID-based resource endpoints like */quotes/* and */market-pairs/* allow you to bundle several IDs; for example, this allows you to get latest market quotes for a specific set of cryptocurrencies in one call.
Standards and Conventions
Each HTTP request must contain the header Accept: application/json. You should also send an Accept-Encoding: deflate, gzip header to receive data fast and efficiently.

Endpoint Response Payload Format
All endpoints return data in JSON format with the results of your query under data if the call is successful.

A Status object is always included for both successful calls and failures when possible. The Status object always includes the current time on the server when the call was executed as timestamp, the number of API call credits this call utilized as credit_count, and the number of milliseconds it took to process the request as elapsed. Any details about errors encountered can be found under the error_code and error_message. See Errors and Rate Limits for details on errors.

{
  "data" : {
    ...
  },
  "status": {
    "timestamp": "2018-06-06T07:52:27.273Z",
    "error_code": 400,
    "error_message": "Invalid value for \"id\"",
    "elapsed": 0,
    "credit_count": 0
  }
}
Cryptocurrency, Exchange, and Fiat currency identifiers
Cryptocurrencies may be identified in endpoints using either the cryptocurrency's unique CoinMarketCap ID as id (eg. id=1 for Bitcoin) or the cryptocurrency's symbol (eg. symbol=BTC for Bitcoin). For a current list of supported cryptocurrencies use our /cryptocurrency/map call.
Exchanges may be identified in endpoints using either the exchange's unique CoinMarketCap ID as id (eg. id=270 for Binance) or the exchange's web slug (eg. slug=binance for Binance). For a current list of supported exchanges use our /exchange/map call.
All fiat currency options use the standard ISO 8601 currency code (eg. USD for the US Dollar). For a current list of supported fiat currencies use our /fiat/map endpoint. Unless otherwise stated, endpoints with fiat currency options like our convert parameter support these 93 major currency codes:
Currency	Currency Code	CoinMarketCap ID
United States Dollar ($)	USD	2781
Albanian Lek (L)	ALL	3526
Algerian Dinar (د.ج)	DZD	3537
Argentine Peso ($)	ARS	2821
Armenian Dram (֏)	AMD	3527
Australian Dollar ($)	AUD	2782
Azerbaijani Manat (₼)	AZN	3528
Bahraini Dinar (.د.ب)	BHD	3531
Bangladeshi Taka (৳)	BDT	3530
Belarusian Ruble (Br)	BYN	3533
Bermudan Dollar ($)	BMD	3532
Bolivian Boliviano (Bs.)	BOB	2832
Bosnia-Herzegovina Convertible Mark (KM)	BAM	3529
Brazilian Real (R$)	BRL	2783
Bulgarian Lev (лв)	BGN	2814
Cambodian Riel (៛)	KHR	3549
Canadian Dollar ($)	CAD	2784
Chilean Peso ($)	CLP	2786
Chinese Yuan (¥)	CNY	2787
Colombian Peso ($)	COP	2820
Costa Rican Colón (₡)	CRC	3534
Croatian Kuna (kn)	HRK	2815
Cuban Peso ($)	CUP	3535
Czech Koruna (Kč)	CZK	2788
Danish Krone (kr)	DKK	2789
Dominican Peso ($)	DOP	3536
Egyptian Pound (£)	EGP	3538
Euro (€)	EUR	2790
Georgian Lari (₾)	GEL	3539
Ghanaian Cedi (₵)	GHS	3540
Guatemalan Quetzal (Q)	GTQ	3541
Honduran Lempira (L)	HNL	3542
Hong Kong Dollar ($)	HKD	2792
Hungarian Forint (Ft)	HUF	2793
Icelandic Króna (kr)	ISK	2818
Indian Rupee (₹)	INR	2796
Indonesian Rupiah (Rp)	IDR	2794
Iranian Rial (﷼)	IRR	3544
Iraqi Dinar (ع.د)	IQD	3543
Israeli New Shekel (₪)	ILS	2795
Jamaican Dollar ($)	JMD	3545
Japanese Yen (¥)	JPY	2797
Jordanian Dinar (د.ا)	JOD	3546
Kazakhstani Tenge (₸)	KZT	3551
Kenyan Shilling (Sh)	KES	3547
Kuwaiti Dinar (د.ك)	KWD	3550
Kyrgystani Som (с)	KGS	3548
Lebanese Pound (ل.ل)	LBP	3552
Macedonian Denar (ден)	MKD	3556
Malaysian Ringgit (RM)	MYR	2800
Mauritian Rupee (₨)	MUR	2816
Mexican Peso ($)	MXN	2799
Moldovan Leu (L)	MDL	3555
Mongolian Tugrik (₮)	MNT	3558
Moroccan Dirham (د.م.)	MAD	3554
Myanma Kyat (Ks)	MMK	3557
Namibian Dollar ($)	NAD	3559
Nepalese Rupee (₨)	NPR	3561
New Taiwan Dollar (NT$)	TWD	2811
New Zealand Dollar ($)	NZD	2802
Nicaraguan Córdoba (C$)	NIO	3560
Nigerian Naira (₦)	NGN	2819
Norwegian Krone (kr)	NOK	2801
Omani Rial (ر.ع.)	OMR	3562
Pakistani Rupee (₨)	PKR	2804
Panamanian Balboa (B/.)	PAB	3563
Peruvian Sol (S/.)	PEN	2822
Philippine Peso (₱)	PHP	2803
Polish Złoty (zł)	PLN	2805
Pound Sterling (£)	GBP	2791
Qatari Rial (ر.ق)	QAR	3564
Romanian Leu (lei)	RON	2817
Russian Ruble (₽)	RUB	2806
Saudi Riyal (ر.س)	SAR	3566
Serbian Dinar (дин.)	RSD	3565
Singapore Dollar (S$)	SGD	2808
South African Rand (R)	ZAR	2812
South Korean Won (₩)	KRW	2798
South Sudanese Pound (£)	SSP	3567
Sovereign Bolivar (Bs.)	VES	3573
Sri Lankan Rupee (Rs)	LKR	3553
Swedish Krona ( kr)	SEK	2807
Swiss Franc (Fr)	CHF	2785
Thai Baht (฿)	THB	2809
Trinidad and Tobago Dollar ($)	TTD	3569
Tunisian Dinar (د.ت)	TND	3568
Turkish Lira (₺)	TRY	2810
Ugandan Shilling (Sh)	UGX	3570
Ukrainian Hryvnia (₴)	UAH	2824
United Arab Emirates Dirham (د.إ)	AED	2813
Uruguayan Peso ($)	UYU	3571
Uzbekistan Som (so'm)	UZS	3572
Vietnamese Dong (₫)	VND	2823
Along with these four precious metals:

Precious Metal	Currency Code	CoinMarketCap ID
Gold Troy Ounce	XAU	3575
Silver Troy Ounce	XAG	3574
Platinum Ounce	XPT	3577
Palladium Ounce	XPD	3576
Warning: Using CoinMarketCap IDs is always recommended as not all cryptocurrency symbols are unique. They can also change with a cryptocurrency rebrand. If a symbol is used the API will always default to the cryptocurrency with the highest market cap if there are multiple matches. Our convert parameter also defaults to fiat if a cryptocurrency symbol also matches a supported fiat currency. You may use the convenient /map endpoints to quickly find the corresponding CoinMarketCap ID for a cryptocurrency or exchange.

Bundling API Calls
Many endpoints support ID and crypto/fiat currency conversion bundling. This means you can pass multiple comma-separated values to an endpoint to query or convert several items at once. Check the id, symbol, slug, and convert query parameter descriptions in the endpoint documentation to see if this is supported for an endpoint.
Endpoints that support bundling return data as an object map instead of an array. Each key-value pair will use the identifier you passed in as the key.
For example, if you passed symbol=BTC,ETH to /v1/cryptocurrency/quotes/latest you would receive:

"data" : {
    "BTC" : {
      ...
    },
    "ETH" : {
      ...
    }
}
Or if you passed id=1,1027 you would receive:

"data" : {
    "1" : {
      ...
    },
    "1027" : {
      ...
    }
}
Price conversions that are returned inside endpoint responses behave in the same fashion. These are enclosed in a quote object.

Date and Time Formats
All endpoints that require date/time parameters allow timestamps to be passed in either ISO 8601 format (eg. 2018-06-06T01:46:40Z) or in Unix time (eg. 1528249600). Timestamps that are passed in ISO 8601 format support basic and extended notations; if a timezone is not included, UTC will be the default.
All timestamps returned in JSON payloads are returned in UTC time using human-readable ISO 8601 format which follows this pattern: yyyy-mm-ddThh:mm:ss.mmmZ. The final .mmm designates milliseconds. Per the ISO 8601 spec the final Z is a constant that represents UTC time.
Data is collected, recorded, and reported in UTC time unless otherwise specified.
Versioning
The CoinMarketCap API is versioned to guarantee new features and updates are non-breaking. The latest version of this API is /v1/.

Errors and Rate Limits
API Request Throttling
Use of the CoinMarketCap API is subject to API call rate limiting or "request throttling". This is the number of HTTP calls that can be made simultaneously or within the same minute with your API Key before receiving an HTTP 429 "Too Many Requests" throttling error. This limit scales with the usage tier and resets every 60 seconds. Please review our Best Practices for implementation strategies that work well with rate limiting.

HTTP Status Codes
The API uses standard HTTP status codes to indicate the success or failure of an API call.

400 (Bad Request) The server could not process the request, likely due to an invalid argument.
401 (Unauthorized) Your request lacks valid authentication credentials, likely an issue with your API Key.
402 (Payment Required) Your API request was rejected due to it being a paid subscription plan with an overdue balance. Pay the balance in the Developer Portal billing tab and it will be enabled.
403 (Forbidden) Your request was rejected due to a permission issue, likely a restriction on the API Key's associated service plan. Here is a convenient map of service plans to endpoints.
429 (Too Many Requests) The API Key's rate limit was exceeded; consider slowing down your API Request frequency if this is an HTTP request throttling error. Consider upgrading your service plan if you have reached your monthly API call credit limit for the day/month.
500 (Internal Server Error) An unexpected server issue was encountered.
Error Response Codes
A Status object is always included in the JSON response payload for both successful calls and failures when possible. During error scenarios you may reference the error_code and error_message properties of the Status object. One of the API error codes below will be returned if applicable otherwise the HTTP status code for the general error type is returned.

HTTP Status	Error Code	Error Message
401	1001 [API_KEY_INVALID]	This API Key is invalid.
401	1002 [API_KEY_MISSING]	API key missing.
402	1003 [API_KEY_PLAN_REQUIRES_PAYEMENT]	Your API Key must be activated. Please go to pro.coinmarketcap.com/account/plan.
402	1004 [API_KEY_PLAN_PAYMENT_EXPIRED]	Your API Key's subscription plan has expired.
403	1005 [API_KEY_REQUIRED]	An API Key is required for this call.
403	1006 [API_KEY_PLAN_NOT_AUTHORIZED]	Your API Key subscription plan doesn't support this endpoint.
403	1007 [API_KEY_DISABLED]	This API Key has been disabled. Please contact support.
429	1008 [API_KEY_PLAN_MINUTE_RATE_LIMIT_REACHED]	You've exceeded your API Key's HTTP request rate limit. Rate limits reset every minute.
429	1009 [API_KEY_PLAN_DAILY_RATE_LIMIT_REACHED]	You've exceeded your API Key's daily rate limit.
429	1010 [API_KEY_PLAN_MONTHLY_RATE_LIMIT_REACHED]	You've exceeded your API Key's monthly rate limit.
429	1011 [IP_RATE_LIMIT_REACHED]	You've hit an IP rate limit.
Best Practices
This section contains a few recommendations on how to efficiently utilize the CoinMarketCap API for your enterprise application, particularly if you already have a large base of users for your application.

Use CoinMarketCap ID Instead of Cryptocurrency Symbol
Utilizing common cryptocurrency symbols to reference cryptocurrencies on the API is easy and convenient but brittle. You should know that many cryptocurrencies have the same symbol, for example, there are currently three cryptocurrencies that commonly refer to themselves by the symbol HOT. Cryptocurrency symbols also often change with cryptocurrency rebrands. When fetching cryptocurrency by a symbol that matches several active cryptocurrencies we return the one with the highest market cap at the time of the query. To ensure you always target the cryptocurrency you expect, use our permanent CoinMarketCap IDs. These IDs are used reliably by numerous mission critical platforms and never change.

We make fetching a map of all active cryptocurrencies' CoinMarketCap IDs very easy. Just call our /cryptocurrency/map endpoint to receive a list of all active currencies mapped to the unique id property. This map also includes other typical identifiying properties like name, symbol and platform token_address that can be cross referenced. In cryptocurrency calls you would then send, for example id=1027, instead of symbol=ETH. It's strongly recommended that any production code utilize these IDs for cryptocurrencies, exchanges, and markets to future-proof your code.

Use the Right Endpoints for the Job
You may have noticed that /cryptocurrency/listings/latest and /cryptocurrency/quotes/latest return the same crypto data but in different formats. This is because the former is for requesting paginated and ordered lists of all cryptocurrencies while the latter is for selectively requesting only the specific cryptocurrencies you require. Many endpoints follow this pattern, allow the design of these endpoints to work for you!

Implement a Caching Strategy If Needed
There are standard legal data safeguards built into the Commercial User Terms that application developers should keep in mind. These Terms help prevent unauthorized scraping and redistributing of CMC data but are intentionally worded to allow legitimate local caching of market data to support the operation of your application. If your application has a significant user base and you are concerned with staying within the call credit and API throttling limits of your subscription plan consider implementing a data caching strategy.

For example instead of making a /cryptocurrency/quotes/latest call every time one of your application's users needs to fetch market rates for specific cryptocurrencies, you could pre-fetch and cache the latest market data for every cryptocurrency in your application's local database every 60 seconds. This would only require 1 API call, /cryptocurrency/listings/latest?limit=5000, every 60 seconds. Then, anytime one of your application's users need to load a custom list of cryptocurrencies you could simply pull this latest market data from your local cache without the overhead of additional calls. This kind of optimization is practical for customers with large, demanding user bases.

Code Defensively to Ensure a Robust REST API Integration
Whenever implementing any high availability REST API service for mission critical operations it's recommended to code defensively. Since the API is versioned, any breaking request or response format change would only be introduced through new versions of each endpoint, however existing endpoints may still introduce new convenience properties over time.

We suggest these best practices:

You should parse the API response JSON as JSON and not through a regular expression or other means to avoid brittle parsing logic.
Your parsing code should explicitly parse only the response properties you require to guarantee new fields that may be returned in the future are ignored.
You should add robust field validation to your response parsing logic. You can wrap complex field parsing, like dates, in try/catch statements to minimize the impact of unexpected parsing issues (like the unlikely return of a null value).
Implement a "Retry with exponential backoff" coding pattern for your REST API call logic. This means if your HTTP request happens to get rate limited (HTTP 429) or encounters an unexpected server-side condition (HTTP 5xx) your code would automatically recover and try again using an intelligent recovery scheme. You may use one of the many libraries available; for example, this one for Node or this one for Python.
Reach Out and Upgrade Your Plan
If you're uncertain how to best implement the CoinMarketCap API in your application or your needs outgrow our current self-serve subscription tiers you can reach <NAME_EMAIL>. We'll review your needs and budget and may be able to tailor a custom enterprise plan that is right for you.

Version History
The CoinMarketCap API utilizes Semantic Versioning in the format major.minor.patch. The current major version is incorporated into the API request path as /v1/. Non-breaking minor and patch updates to the API are released regularly. These may include new endpoints, data points, and API plan features which are always introduced in a non-breaking manner. This means you can expect new properties to become available in our existing /v1/ endpoints however any breaking change will be introduced under a new major version of the API with legacy versions supported indefinitely unless otherwise stated.

You can subscribe to our API Newsletter to get monthly email updates on CoinMarketCap API enhancements.

v2.0.10 on Oct 14, 2024
/v3/fear-and-greed/latest and /v3/fear-and-greed/historical now available to get CMC Fear and Greed Index
v2.0.9 on June 1, 2023
/v1/community/trending/topic now available to get community trending topics.
/v1/community/trending/token now available to get community trending tokens.
v2.0.8 on November 25, 2022
/v1/exchange/assets now available to get exchange assets in the form of token holdings.
v2.0.7 on September 19, 2022
/v1/content/posts/top now available to get cryptocurrency-related top posts.
/v1/content/posts/latest now available to get cryptocurrency-related latest posts.
/v1/content/posts/comments now available to get comments of the post.
v2.0.6 on Augest 18, 2022
/v1/content/latest now available to get news/headlines and Alexandria articles.
v2.0.5 on Augest 4, 2022
/v1/tools/postman now API postman collection is available.
v2.0.4 on October 11, 2021
/v1/cryptocurrency/listings/latest now includes volume_change_24h.
/v2/cryptocurrency/quotes/latest now includes volume_change_24h.
v2.0.3 on October 6, 2021
/v1/cryptocurrency/trending/latest now supports time_period as an optional parameter.
v2.0.2 on September 13, 2021
/exchange/map now available to Free tier users.
/exchange/info now available to Free tier users.
v2.0.1 on September 8, 2021
/exchange/market-pairs/latest now includes volume_24h, depth_negative_two, depth_positive_two and volume_percentage.
/exchange/listings/latest now includes open_interest.
v2.0.0 on August 17, 2021
By popular request we have added a number of new useful endpoints !
/v1/cryptocurrency/categories can be used to access a list of categories and their associated coins. You can also filter the list of categories by one or more cryptocurrencies.
/v1/cryptocurrency/category can be used to load only a single category of coins, listing the coins within that category.
/v1/cryptocurrency/airdrops can be used to access a list of CoinMarketCap’s free airdrops. This defaults to a status of ONGOING but can be filtered to UPCOMING or ENDED. You can also query for a list of airdrops by cryptocurrency.
/v1/cryptocurrency/airdrop can be used to load a single airdrop and its associated cryptocurrency.
/v1/cryptocurrency/trending/latest can be used to load the most searched for cryptocurrencies within a period of time. This defaults to a time_period of the previous 24h, but can be changed to 30d, or 7d for a larger window of time.
/v1/cryptocurrency/trending/most-visited can be used to load the most visited cryptocurrencies within a period of time. This defaults to a time_period of the previous 24h, but can be changed to 30d, or 7d for a larger window of time.
/v1/cryptocurrency/trending/gainers-losers can be used to load the biggest gainers & losers within a period of time. This defaults to a time_period of the previous 24h, but can be changed to 30d, or 7d for a larger window of time.
v1.28.0 on August 9, 2021
/v1/cryptocurrency/listings/latest now includes market_cap_dominance and fully_diluted_market_cap.
/v1/cryptocurrency/quotes/latest now includes market_cap_dominance and fully_diluted_market_cap.
v1.27.0 on January 27, 2021
/v2/cryptocurrency/info response format changed to allow for multiple coins per symbol.
/v2/cryptocurrency/market-pairs/latest response format changed to allow for multiple coins per symbol.
/v2/cryptocurrency/quotes/historical response format changed to allow for multiple coins per symbol.
/v2/cryptocurrency/ohlcv/historical response format changed to allow for multiple coins per symbol.
/v2/tools/price-conversion response format changed to allow for multiple coins per symbol.
/v2/cryptocurrency/ohlcv/latest response format changed to allow for multiple coins per symbol.
/v2/cryptocurrency/price-performance-stats/latest response format changed to allow for multiple coins per symbol.
v1.26.0 on January 21, 2021
/v2/cryptocurrency/quotes/latest response format changed to allow for multiple coins per symbol.
v1.25.0 on April 17, 2020
/v1.1/cryptocurrency/listings/latest now includes a more robust tags response with slug, name, and category.
/cryptocurrency/quotes/historical and /cryptocurrency/quotes/latest now include is_active and is_fiat in the response.
v1.24.0 on Feb 24, 2020
/cryptocurrency/ohlcv/historical has been modified to include the high and low timestamps.
/exchange/market-pairs/latest now includes category and fee_type market pair filtering options.
/cryptocurrency/listings/latest now includes category and fee_type market pair filtering options.
v1.23.0 on Feb 3, 2020
/fiat/map is now available to fetch the latest mapping of supported fiat currencies to CMC IDs.
/exchange/market-pairs/latest now includes matched_id and matched_symbol market pair filtering options.
/cryptocurrency/listings/latest now provides filter parameters price_min, price_max, market_cap_min, market_cap_max, percent_change_24h_min, percent_change_24h_max, volume_24h_max, circulating_supply_min and circulating_supply_max in addition to the existing volume_24h_min filter.
v1.22.0 on Oct 16, 2019
/global-metrics/quotes/latest now additionally returns total_cryptocurrencies and total_exchanges counts which include inactive projects who's data is still available via API.
v1.21.0 on Oct 1, 2019
/exchange/map now includes sort options including volume_24h.
/cryptocurrency/map fix for a scenario where first_historical_data and last_historical_data may not be populated.
Additional improvements to alphanumeric sorts.
v1.20.0 on Sep 25, 2019
By popular request you may now configure API plan usage notifications and email alerts in the Developer Portal.
/cryptocurrency/map now includes sort options including cmc_rank.
v1.19.0 on Sep 19, 2019
A new /blockchain/ category of endpoints is now available with the introduction of our new /v1/blockchain/statistics/latest endpoint. This endpoint can be used to poll blockchain statistics data as seen in our Blockchain Explorer.
Additional platform error codes are now surfaced during HTTP Status Code 401, 402, 403, and 429 scenarios as documented in Errors and Rate Limits.
OHLCV endpoints using the convert option now match historical UTC open period exchange rates with greater accuracy.
/cryptocurrency/info and /exchange/info now include the optional aux parameter where listing status can be requested in the list of supplemental properties.
/cryptocurrency/listings/latest and /cryptocurrency/quotes/latest: The accuracy of percent_change_ conversions was improved when passing non-USD fiat convert options.
/cryptocurrency/ohlcv/historical and /cryptocurrency/quotes/latest now support relaxed request validation rules via the skip_invalid request parameter.
We also now return a helpful notice warning when API key usage is above 95% of daily and monthly API credit usage limits.
v1.18.0 on Aug 28, 2019
/key/info has been added as a new endpoint. It may be used programmatically monitor your key usage compared to the rate limit and daily/monthly credit limits available to your API plan as an alternative to using the Developer Portal Dashboard.
/cryptocurrency/quotes/historical and /v1/global-metrics/quotes/historical have new options to make charting tasks easier and more efficient. Use the new aux parameter to cut out response properties you don't need and include the new search_interval timestamp to normalize disparate historical records against the same interval time periods.
A 4 hour interval option 4h was added to all historical time series data endpoints.
v1.17.0 on Aug 22, 2019
/cryptocurrency/price-performance-stats/latest has been added as our 21st endpoint! It returns launch price ROI, all-time high / all-time low, and other price stats over several supported time periods.
/cryptocurrency/market-pairs/latest now has the ability to filter all active markets for a cryptocurrency to specific base/quote pairs. Want to return only BTC/USD and BTC/USDT markets? Just pass ?symbol=BTC&matched_symbol=USD,USDT or ?id=1&matched_id=2781,825.
/cryptocurrency/market-pairs/latest now features sort options including cmc_rank to reproduce the methodology based sort on pages like Bitcoin Markets.
/cryptocurrency/market-pairs/latest can now return any exchange level CMC notices affecting a market via the new notice aux parameter.
/cryptocurrency/quotes/latest will now continue to return the last updated price data for cryptocurrency that have transitioned to an inactive state instead of returning an HTTP 400 error. These active coins that have gone inactive can easily be identified as having a num_market_pairs of 0 and a stale last_updated date.
/exchange/info now includes a brief text summary for most exchanges as description.
v1.16.0 on Aug 9, 2019
We've introduced a new partners category of endpoints for convenient access to 3rd party crypto data. FlipSide Crypto's Fundamental Crypto Asset Score (FCAS) is now available as the first partner integration.
/cryptocurrency/listings/latest now provides a volume_24h_min filter parameter. It can be used when a threshold of volume is required like in our Biggest Gainers and Losers lists.
/cryptocurrency/listings/latest and /cryptocurrency/quotes/latest can now return rolling volume_7d and volume_30d via the supplemental aux parameter and sort options by these fields.
volume_24h_reported, volume_7d_reported, volume_30d_reported, and market_cap_by_total_supply are also now available through the aux parameter with an additional sort option for the latter.
/cryptocurrency/market-pairs/latest can now provide market price relative to the quote currency. Just pass price_quote to the supplemental aux parameter. This can be used to display consistent price data for a cryptocurrency across several markets no matter if it is the base or quote in each pair as seen in our Bitcoin markets price column.
When requesting a custom sort on our list based endpoints, numeric fields like percent_change_7d now conveniently return non-applicable null values last regardless of sort order.
v1.15.0 on Jul 10, 2019
/cryptocurrency/map and /v1/exchange/map now expose a 3rd listing state of untracked between active and inactive as outlined in our methodology. See endpoint documentation for additional details.
/cryptocurrency/quotes/historical, /cryptocurrency/ohlcv/historical, and /exchange/quotes/latest now support fetching multiple cryptocurrencies and exchanges in the same call.
/global-metrics/quotes/latest now updates more frequently, every minute. It aslo now includes total_volume_24h_reported, altcoin_volume_24h, altcoin_volume_24h_reported, and altcoin_market_cap.
/global-metrics/quotes/historical also includes these new dimensions along with historical active_cryptocurrencies, active_exchanges, and active_market_pairs counts.
We've also added a new aux auxiliary parameter to many endpoints which can be used to customize your request. You may request new supplemental data properties that are not returned by default or slim down your response payload by excluding default aux fields you don't need in endpoints like /cryptocurrency/listings/latest. /cryptocurrency/market-pairs/latest and /exchange/market-pairs/latest can now supply market_url, currency_name, and currency_slug for each market using this new parameter. /exchange/listings/latest can now include the exchange date_launched.
v1.14.1 on Jun 14, 2019 - DATA: Phase 1 methodology updates
Per our May 1 announ
