# Solana Trading App Fullstack Architecture Document

## Introduction

This document outlines the complete fullstack architecture for Solana Trading App, including backend systems, frontend implementation, and their integration. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack.

This unified approach combines what would traditionally be separate backend and frontend architecture documents, streamlining the development process for modern fullstack applications where these concerns are increasingly intertwined.

### Starter Template or Existing Project

N/A - Greenfield project with custom requirements for Solana blockchain integration and trading automation.

### Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-08-09 | 1.0 | Initial architecture document created from PRD analysis | <PERSON> (Architect) |

## High Level Architecture

### Technical Summary

The Solana Trading App follows a modern monorepo architecture with Next.js frontend and Express.js backend, deployed across Vercel and Railway respectively. The system integrates Jupiter Aggregator for optimal DEX routing, Helius for low-latency blockchain access, and implements BullMQ job queues for reliable trading automation. Key integration points include shared TypeScript interfaces, RESTful APIs for frontend-backend communication, and real-time price monitoring through batched CMC API calls. This architecture achieves sub-5-second execution requirements through optimized polling strategies, MEV-protected transactions, and priority-based job processing within Helius rate limits.

### Platform and Infrastructure Choice

**Platform:** Vercel + Railway + TimescaleDB Cloud
**Key Services:** 
- Frontend: Vercel (Next.js hosting, edge functions, automatic deployments)
- Backend: Railway (Node.js, PostgreSQL with TimescaleDB, Redis hosting)
- Database: Railway PostgreSQL with TimescaleDB extension for time-series price data
- Cache/Queue: Railway Redis for BullMQ and caching
- Monitoring: Railway metrics + Vercel analytics

**Deployment Host and Regions:** 
- Vercel: Global edge network with primary regions in US-East, US-West
- Railway: US-West region for low latency to Solana RPC endpoints
- TimescaleDB Cloud: Co-located with Railway for optimal database performance

### Repository Structure

**Structure:** Monorepo with npm workspaces
**Monorepo Tool:** npm workspaces (lightweight, built-in dependency management)
**Package Organization:** 
- `/apps` - Frontend (web) and backend (api) applications
- `/packages` - Shared utilities (shared types, UI components, config)
- Clear separation between client and server code with shared interfaces

### High Level Architecture Diagram

```mermaid
graph TB
    User[User] --> Web[Next.js Frontend<br/>Vercel]
    Web --> API[Express API<br/>Railway]
    API --> Queue[BullMQ Jobs<br/>Redis]
    API --> DB[(PostgreSQL<br/>TimescaleDB)]
    
    Queue --> Jupiter[Jupiter Aggregator<br/>DEX Routing]
    Queue --> Helius[Helius RPC<br/>Blockchain Access]
    Queue --> CMC[CoinMarketCap<br/>Price Data]
    Queue --> Telegram[Telegram Bot<br/>Notifications]
    
    API --> Cache[(Redis Cache<br/>Price Data)]
    Web --> CDN[Vercel Edge<br/>Static Assets]
    
    Jupiter --> Solana[Solana Blockchain]
    Helius --> Solana
    
    style User fill:#e1f5fe
    style Web fill:#f3e5f5
    style API fill:#e8f5e8
    style Queue fill:#fff3e0
    style DB fill:#fce4ec
    style Solana fill:#f1f8e9
```

### Architectural Patterns

- **Monorepo Architecture:** Single repository with multiple packages for shared code and simplified dependency management - _Rationale:_ Enables type safety across frontend/backend boundaries and simplifies development workflow
- **Job Queue Pattern:** BullMQ handles all critical trading operations with retry logic and priority scheduling - _Rationale:_ Ensures reliable execution of time-sensitive trades within API rate limits
- **Repository Pattern:** Abstract data access layer with TypeScript interfaces - _Rationale:_ Enables testing and future database migration flexibility while maintaining type safety
- **State-Aware Polling:** Dynamic polling intervals based on position status and user engagement - _Rationale:_ Optimizes API usage while maintaining responsiveness for active trading
- **Circuit Breaker Pattern:** Graceful degradation when external APIs fail or hit rate limits - _Rationale:_ Maintains system stability and user experience during external service disruptions

## Tech Stack

### Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|----------|-----------|
| Frontend Language | TypeScript | 5.x | Type-safe React development | Prevents runtime errors and enables better IDE support |
| Frontend Framework | Next.js | 14.x | React framework with App Router | Built-in optimization, API routes, and deployment integration |
| UI Component Library | shadcn/ui | Latest | Consistent, customizable components | Professional design system with Tailwind integration |
| State Management | Zustand | 4.x | Lightweight state management | Minimal boilerplate compared to Redux, perfect for trading data |
| Backend Language | Node.js + TypeScript | 20.x LTS | Unified language across stack | Shared types and reduced context switching |
| Backend Framework | Express.js | 4.x | Lightweight, flexible API framework | Battle-tested with extensive middleware ecosystem |
| API Style | REST | - | RESTful API design | Simple, cacheable, works well with trading operations |
| Database | PostgreSQL + TimescaleDB | 15.x | Time-series optimized relational DB | Excellent performance for price data with SQL familiarity |
| Cache | Redis | 7.x | In-memory cache and job queue | High-performance caching and BullMQ backend |
| File Storage | Local/Vercel Static | - | Static asset serving | Simple deployment with CDN distribution |
| Authentication | Session-based | - | Simplified single-user auth | No JWT complexity needed for single-user application |
| Frontend Testing | Vitest | 1.x | Fast unit testing | Faster than Jest with better ESM support |
| Backend Testing | Vitest | 1.x | Unified testing framework | Consistent tooling across frontend and backend |
| E2E Testing | Playwright | Latest | End-to-end browser testing | Reliable automation testing for trading workflows |
| Build Tool | npm | 10.x | Package management and scripts | Built-in workspace support for monorepo |
| Bundler | Next.js built-in | - | Webpack-based bundling | Optimized for React with automatic code splitting |
| IaC Tool | Railway CLI | Latest | Infrastructure deployment | Simple configuration for full-stack deployment |
| CI/CD | GitHub Actions | - | Automated testing and deployment | Free tier sufficient, integrates with Vercel/Railway |
| Monitoring | Railway + Vercel Analytics | - | Application performance monitoring | Built-in monitoring with both platforms |
| Logging | Pino | 8.x | High-performance structured logging | Fast JSON logging with good TypeScript support |
| CSS Framework | Tailwind CSS | 3.x | Utility-first styling | Rapid development with consistent design system |
| Containerization | Docker + Docker Compose | Latest | Local development environment | Consistent PostgreSQL, Redis, and TimescaleDB setup across machines |

## Data Models

### Position

**Purpose:** Represents an active trading position with associated exit strategies and real-time tracking data.

**Key Attributes:**
- id: string - Unique position identifier
- tokenAddress: string - Solana token mint address
- tokenSymbol: string - Human-readable token symbol
- amountTokens: Decimal - Quantity of tokens purchased
- entryPrice: Decimal - Purchase price per token in USD
- entryTimestamp: DateTime - When position was created
- status: PositionStatus - Current position state (active, closing, closed)
- exitStrategy: ExitStrategy - Attached automation rules

#### TypeScript Interface

```typescript
interface Position {
  id: string;
  tokenAddress: string;
  tokenSymbol: string;
  tokenName: string;
  amountTokens: Decimal;
  entryPrice: Decimal;
  entryTimestamp: Date;
  status: 'active' | 'closing' | 'closed';
  exitStrategy?: ExitStrategy;
  transactions: Transaction[];
  currentPrice?: Decimal;
  currentValue?: Decimal;
  pnlUsd?: Decimal;
  pnlPercent?: Decimal;
  createdAt: Date;
  updatedAt: Date;
}
```

#### Relationships
- Has many: Transaction records (buy, sell, partial exits)
- Has one: ExitStrategy (optional, for automated management)
- Belongs to: User (implied single-user system)

### ExitStrategy

**Purpose:** Defines automated exit rules including multi-tier take profits, stop losses, and trailing stops for position management.

**Key Attributes:**
- id: string - Unique strategy identifier
- positionId: string - Associated position
- takeProfitTiers: TakeProfitTier[] - Multiple exit levels
- stopLoss: StopLoss - Protection against losses
- trailingStop: TrailingStop - Dynamic stop adjustment
- moonBag: MoonBag - Small position retention
- isActive: boolean - Whether automation is enabled

#### TypeScript Interface

```typescript
interface ExitStrategy {
  id: string;
  positionId: string;
  takeProfitTiers: TakeProfitTier[];
  stopLoss?: StopLoss;
  trailingStop?: TrailingStop;
  moonBag?: MoonBag;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface TakeProfitTier {
  id: string;
  tierNumber: number;
  targetPrice: Decimal;
  percentageToSell: number; // 1-100
  status: 'pending' | 'triggered' | 'executed';
}

interface StopLoss {
  triggerPrice: Decimal;
  percentageToSell: number; // typically 100
  status: 'active' | 'triggered' | 'executed';
}

interface TrailingStop {
  trailDistance: Decimal; // USD amount to trail by
  highestPrice: Decimal; // tracks highest price reached
  currentStopPrice: Decimal; // current stop loss level
  isActive: boolean;
}
```

#### Relationships
- Belongs to: Position (one-to-one relationship)
- Has many: Exit executions tracked in Transaction records

### Transaction

**Purpose:** Immutable record of all blockchain transactions including buys, sells, and fees for audit trail and analytics.

**Key Attributes:**
- id: string - Unique transaction identifier
- positionId: string - Associated position
- type: TransactionType - Buy, sell, or fee transaction
- signature: string - Solana transaction signature
- amountTokens: Decimal - Quantity traded
- pricePerToken: Decimal - Execution price
- totalUsd: Decimal - Total USD value
- fees: TransactionFees - Breakdown of all fees paid
- status: TransactionStatus - Transaction state
- executedAt: DateTime - Blockchain confirmation time

#### TypeScript Interface

```typescript
interface Transaction {
  id: string;
  positionId: string;
  type: 'buy' | 'sell' | 'fee';
  signature: string;
  amountTokens: Decimal;
  pricePerToken: Decimal;
  totalUsd: Decimal;
  fees: {
    networkFee: Decimal; // SOL network fee
    priorityFee: Decimal; // MEV protection fee
    jupiterFee: Decimal; // Platform fee
  };
  status: 'pending' | 'confirmed' | 'failed';
  blockNumber?: number;
  executedAt: Date;
  createdAt: Date;
}
```

#### Relationships
- Belongs to: Position (many transactions per position)
- References: ExitStrategy triggers (for sell transactions)

### WatchlistItem

**Purpose:** Tracks tokens of interest for research and monitoring before trading decisions are made.

**Key Attributes:**
- id: string - Unique item identifier
- tokenAddress: string - Solana mint address
- customName: string - User-defined token name
- notes: string - Research notes and observations
- isPinned: boolean - Priority flag for enhanced monitoring
- addedAt: DateTime - When added to watchlist
- lastViewedAt: DateTime - User engagement tracking

#### TypeScript Interface

```typescript
interface WatchlistItem {
  id: string;
  tokenAddress: string;
  tokenSymbol?: string; // fetched from metadata
  tokenName?: string; // fetched from metadata
  customName?: string; // user override
  notes?: string;
  isPinned: boolean;
  addedAt: Date;
  lastViewedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

#### Relationships
- Independent entity (no foreign keys)
- Conceptually related to Position creation workflows

### PriceSnapshot

**Purpose:** Time-series storage of token price data for monitoring, analytics, and trigger detection using TimescaleDB optimization.

**Key Attributes:**
- timestamp: DateTime - Price observation time (TimescaleDB partition key)
- tokenAddress: string - Token identifier
- priceUsd: Decimal - Current USD price
- volume24h: Decimal - 24-hour trading volume
- marketCap: Decimal - Current market capitalization
- priceChange1h: Decimal - 1-hour price change percentage
- priceChange24h: Decimal - 24-hour price change percentage
- source: string - Data provider (CMC, Jupiter, etc.)

#### TypeScript Interface

```typescript
interface PriceSnapshot {
  timestamp: Date; // TimescaleDB hypertable partition key
  tokenAddress: string;
  priceUsd: Decimal;
  volume24h?: Decimal;
  marketCap?: Decimal;
  priceChange1h?: Decimal;
  priceChange24h?: Decimal;
  liquidity?: Decimal;
  fdv?: Decimal; // Fully diluted valuation
  source: 'cmc' | 'jupiter' | 'helius';
}
```

#### Relationships
- Time-series data indexed by timestamp and tokenAddress
- Used by Position monitoring and WatchlistItem price updates

## API Specification

### REST API Specification

```yaml
openapi: 3.0.0
info:
  title: Solana Trading App API
  version: 1.0.0
  description: RESTful API for automated Solana token trading with exit strategies
servers:
  - url: https://api-production-railway.app
    description: Production server
  - url: http://localhost:3001
    description: Local development server

paths:
  # Trading endpoints
  /api/trades/quote:
    post:
      summary: Get Jupiter buy quote for token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [tokenAddress, amountUsd, slippageBps]
              properties:
                tokenAddress:
                  type: string
                  example: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
                amountUsd:
                  type: number
                  example: 100.50
                slippageBps:
                  type: integer
                  example: 300
      responses:
        '200':
          description: Quote retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteResponse'

  /api/trades/buy:
    post:
      summary: Execute buy transaction with MEV protection
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [tokenAddress, amountUsd, slippageBps, exitStrategy]
              properties:
                tokenAddress:
                  type: string
                amountUsd:
                  type: number
                slippageBps:
                  type: integer
                exitStrategy:
                  $ref: '#/components/schemas/ExitStrategyInput'
      responses:
        '200':
          description: Buy transaction initiated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionResponse'

  # Position management
  /api/positions:
    get:
      summary: Get all active positions
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [active, closing, closed, all]
            default: active
      responses:
        '200':
          description: Positions retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Position'

    delete:
      summary: Close all active positions (emergency)
      responses:
        '200':
          description: All positions closing initiated

  /api/positions/{id}:
    get:
      summary: Get position details
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Position details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PositionDetail'

    patch:
      summary: Update position (modify exit strategy)
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                exitStrategy:
                  $ref: '#/components/schemas/ExitStrategyInput'
                isActive:
                  type: boolean
      responses:
        '200':
          description: Position updated

    delete:
      summary: Close position immediately
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Position closing initiated

  # Watchlist management
  /api/watchlist:
    get:
      summary: Get all watchlist items with market data
      responses:
        '200':
          description: Watchlist items with current prices
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/WatchlistItemWithPrice'

    post:
      summary: Add token to watchlist
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [tokenAddress]
              properties:
                tokenAddress:
                  type: string
                customName:
                  type: string
                notes:
                  type: string
                isPinned:
                  type: boolean
                  default: false
      responses:
        '201':
          description: Token added to watchlist

    post:
      summary: Bulk add tokens to watchlist
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [tokens]
              properties:
                tokens:
                  type: array
                  items:
                    type: object
                    required: [tokenAddress]
                    properties:
                      tokenAddress:
                        type: string
                      customName:
                        type: string
                      notes:
                        type: string
      responses:
        '201':
          description: Tokens bulk added

  /api/watchlist/{id}:
    patch:
      summary: Update watchlist item
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                customName:
                  type: string
                notes:
                  type: string
                isPinned:
                  type: boolean
      responses:
        '200':
          description: Watchlist item updated

    delete:
      summary: Remove from watchlist
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Item removed from watchlist

  # System endpoints
  /api/health:
    get:
      summary: System health check
      responses:
        '200':
          description: System status
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "healthy"
                  services:
                    type: object
                    properties:
                      database:
                        type: string
                      redis:
                        type: string
                      jupiter:
                        type: string
                      helius:
                        type: string
                      cmc:
                        type: string

components:
  schemas:
    Position:
      type: object
      properties:
        id:
          type: string
        tokenAddress:
          type: string
        tokenSymbol:
          type: string
        amountTokens:
          type: string
        entryPrice:
          type: string
        status:
          type: string
          enum: [active, closing, closed]
        currentPrice:
          type: string
        pnlUsd:
          type: string
        pnlPercent:
          type: string
        createdAt:
          type: string
          format: date-time

    ExitStrategyInput:
      type: object
      properties:
        takeProfitTiers:
          type: array
          items:
            type: object
            properties:
              targetPrice:
                type: string
              percentageToSell:
                type: integer
        stopLoss:
          type: object
          properties:
            triggerPrice:
              type: string
            percentageToSell:
              type: integer
        trailingStop:
          type: object
          properties:
            trailDistance:
              type: string

    QuoteResponse:
      type: object
      properties:
        inputMint:
          type: string
        outputMint:
          type: string
        outputAmount:
          type: string
        priceImpact:
          type: string
        route:
          type: array
          items:
            type: object

    TransactionResponse:
      type: object
      properties:
        signature:
          type: string
        status:
          type: string
        positionId:
          type: string

    WatchlistItemWithPrice:
      type: object
      properties:
        id:
          type: string
        tokenAddress:
          type: string
        tokenSymbol:
          type: string
        customName:
          type: string
        notes:
          type: string
        isPinned:
          type: boolean
        currentPrice:
          type: string
        priceChange24h:
          type: string
        volume24h:
          type: string
        marketCap:
          type: string
```

## Components

### Trading Engine

**Responsibility:** Core trading operations including quote fetching, transaction building, and execution coordination with external APIs.

**Key Interfaces:**
- `getTradingQuote(tokenAddress, amountUsd, slippageBps)` - Jupiter integration for optimal routing
- `executeBuyOrder(quote, exitStrategy)` - MEV-protected transaction execution
- `executeSellOrder(position, amount?, reason)` - Automated and manual position closure

**Dependencies:** Jupiter Aggregator API, Helius RPC, Wallet Service, Database repositories

**Technology Stack:** Node.js/TypeScript with @solana/web3.js, Jupiter SDK, Express.js route handlers, Prisma for position persistence

### Exit Strategy Engine

**Responsibility:** Automated exit management including trigger detection, priority handling, and execution coordination through job queues.

**Key Interfaces:**
- `attachExitStrategy(positionId, strategy)` - Associate automation with position
- `evaluateExitTriggers(positions[])` - Batch trigger detection from price monitoring
- `executeExitTrigger(trigger, position)` - Priority-based execution scheduling

**Dependencies:** Price Monitor, Trading Engine, BullMQ job processing, Database for trigger state

**Technology Stack:** BullMQ workers, Redis job storage, Prisma repository pattern, TypeScript for type-safe trigger logic

### Price Monitor

**Responsibility:** Real-time price data collection and distribution with state-aware polling optimization for efficiency and trigger detection.

**Key Interfaces:**
- `startPolling()` - Initialize monitoring with dynamic intervals
- `getPriceSnapshot(tokenAddresses[])` - Batch price fetching
- `subscribeToUpdates(callback)` - Real-time price change notifications

**Dependencies:** CoinMarketCap DEX API, Redis caching, TimescaleDB price storage, polling strategy configuration

**Technology Stack:** Node.js intervals with state management, CMC API integration, Redis caching, TimescaleDB hypertables for time-series data

### Position Manager

**Responsibility:** Position lifecycle management including creation, updates, analytics, and historical tracking with real-time PnL calculations.

**Key Interfaces:**
- `createPosition(transaction, exitStrategy)` - New position initialization
- `updatePosition(id, updates)` - Position modification and exit strategy changes
- `calculatePnL(position, currentPrice)` - Real-time profit/loss computation

**Dependencies:** Database repositories, Price Monitor for current prices, Transaction records for cost basis calculation

**Technology Stack:** TypeScript business logic, Prisma ORM, PostgreSQL with TimescaleDB, Decimal.js for financial calculations

### Watchlist Manager

**Responsibility:** Token discovery and monitoring capabilities including bulk operations, market data integration, and trading workflow integration.

**Key Interfaces:**
- `addToWatchlist(tokenAddress, metadata?)` - Single token addition with validation
- `bulkAddTokens(tokenList[])` - Multi-token import with deduplication
- `getWatchlistWithPrices()` - Enriched watchlist with current market data

**Dependencies:** Price Monitor for market data, Database for watchlist storage, Trading Engine for routing integration

**Technology Stack:** Express.js API routes, Prisma models, @solana/web3.js for address validation, integration with Price Monitor

### Notification System

**Responsibility:** Multi-channel alert delivery including Telegram integration, in-app notifications, and event-driven messaging for all trading activities.

**Key Interfaces:**
- `sendAlert(type, data, priority)` - Unified alert dispatch
- `configureTelegram(botToken, chatId)` - External notification setup
- `getNotificationHistory(filter?)` - Alert audit trail and management

**Dependencies:** Telegram Bot API, BullMQ for reliable delivery, Database for notification history and preferences

**Technology Stack:** Telegram Bot API integration, BullMQ job queues for delivery reliability, Redis for rate limiting, Express.js webhooks

### Frontend Dashboard

**Responsibility:** Real-time trading interface with position monitoring, manual controls, and responsive design for desktop and mobile access.

**Key Interfaces:**
- `<PositionCard />` - Individual position display with real-time updates
- `<TradingPanel />` - Token input, quote display, and buy execution
- `<WatchlistPage />` - Token research and monitoring interface

**Dependencies:** Backend API services, Zustand state management, real-time price updates via polling

**Technology Stack:** Next.js 14 with App Router, React components, shadcn/ui, Tailwind CSS, Zustand for state, SWR for data fetching

## External APIs

### Jupiter Aggregator API

- **Purpose:** DEX aggregation for optimal token swap routing and quote generation
- **Documentation:** https://docs.jup.ag/apis/swap-api
- **Base URL(s):** https://quote-api.jup.ag/v6
- **Authentication:** No API key required (rate limited)
- **Rate Limits:** 600 requests/minute per IP

**Key Endpoints Used:**
- `GET /quote` - Get swap quotes with route optimization
- `POST /swap` - Generate swap transactions with MEV protection

**Integration Notes:** Essential for buy/sell execution. Supports priority fees and compute unit optimization for MEV protection. Route data used for price impact warnings.

### Helius RPC API

- **Purpose:** High-performance Solana RPC with enhanced transaction broadcasting and webhook capabilities
- **Documentation:** https://docs.helius.xyz/
- **Base URL(s):** https://rpc.helius.xyz/?api-key={key}
- **Authentication:** API key in URL parameter
- **Rate Limits:** Free tier: 100 req/sec, 150k req/day

**Key Endpoints Used:**
- `POST /` - Standard Solana RPC methods (sendTransaction, getTransaction, etc.)
- `POST /v0/transactions` - Enhanced transaction submission with priority handling

**Integration Notes:** Primary blockchain interface for transaction submission and confirmation. Webhook integration for real-time transaction monitoring (future enhancement).

### CoinMarketCap DEX API

- **Purpose:** Real-time price data and market metrics for token monitoring and trigger detection
- **Documentation:** https://coinmarketcap.com/api/documentation/v1/
- **Base URL(s):** https://pro-api.coinmarketcap.com/v2/
- **Authentication:** API key in X-CMC_PRO_API_KEY header
- **Rate Limits:** Basic plan: 10k calls/month, 333 calls/day

**Key Endpoints Used:**
- `GET /cryptocurrency/quotes/latest` - Batch price quotes (up to 100 tokens per call)

**Integration Notes:** Batch fetching optimizes API usage for multiple position monitoring. Price data cached in Redis for polling efficiency.

### Telegram Bot API

- **Purpose:** Real-time notifications for trading events, alerts, and system status updates
- **Documentation:** https://core.telegram.org/bots/api
- **Base URL(s):** https://api.telegram.org/bot{token}/
- **Authentication:** Bot token in URL path
- **Rate Limits:** 30 messages/second per bot

**Key Endpoints Used:**
- `POST /sendMessage` - Send text notifications with formatting
- `POST /sendPhoto` - Send charts and visual updates (future enhancement)

**Integration Notes:** Critical for mobile notifications and hands-off trading monitoring. Messages queued through BullMQ for reliability and rate limit compliance.

## Core Workflows

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API
    participant J as Jupiter
    participant H as Helius
    participant Q as Job Queue
    participant P as Price Monitor
    participant T as Telegram

    Note over U,T: Buy Order with Exit Strategy
    U->>F: Enter token address + amount
    F->>A: GET /api/trades/quote
    A->>J: Request quote with slippage
    J-->>A: Return optimal route + price impact
    A-->>F: Quote data with route visualization
    F-->>U: Display quote + exit strategy form
    
    U->>F: Confirm buy + configure exit strategy
    F->>A: POST /api/trades/buy
    A->>J: Build swap transaction
    J-->>A: Transaction data with MEV protection
    A->>H: Submit transaction with priority fees
    H-->>A: Transaction signature
    A->>Q: Queue position monitoring job
    A-->>F: Transaction initiated response
    F-->>U: "Buy order submitted" notification

    Note over Q,T: Automated Position Monitoring
    Q->>P: Start position price monitoring
    loop Every 30 seconds (Armed mode)
        P->>CMC: Batch price request for active positions
        CMC-->>P: Current price data
        P->>Q: Trigger evaluation job
        Q->>A: Check exit conditions
        alt Exit trigger detected
            Q->>A: Execute sell transaction
            A->>J: Get sell quote
            J-->>A: Sell transaction data
            A->>H: Submit sell transaction
            H-->>A: Sell signature
            A->>T: Send "Take profit executed" alert
            Q->>A: Update position status
        end
    end

    Note over U,T: Manual Position Management
    U->>F: View positions dashboard
    F->>A: GET /api/positions
    A-->>F: Active positions with real-time PnL
    U->>F: Click "Close Position" 
    F->>A: DELETE /api/positions/{id}
    A->>Q: Queue immediate sell job (high priority)
    Q->>A: Execute sell transaction
    A->>T: Send "Manual close executed" alert
```

## Database Schema

```sql
-- Enable TimescaleDB extension for time-series optimization
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Positions table: Core trading positions
CREATE TABLE positions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_address VARCHAR(50) NOT NULL,
    token_symbol VARCHAR(20),
    token_name VARCHAR(100),
    amount_tokens DECIMAL(20,8) NOT NULL,
    entry_price DECIMAL(20,8) NOT NULL,
    entry_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    status VARCHAR(20) NOT NULL DEFAULT 'active'
        CHECK (status IN ('active', 'closing', 'closed')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Exit strategies table: Automation rules for positions
CREATE TABLE exit_strategies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    position_id UUID NOT NULL REFERENCES positions(id) ON DELETE CASCADE,
    take_profit_tiers JSONB, -- Array of {tierNumber, targetPrice, percentageToSell, status}
    stop_loss JSONB, -- {triggerPrice, percentageToSell, status}
    trailing_stop JSONB, -- {trailDistance, highestPrice, currentStopPrice, isActive}
    moon_bag JSONB, -- {percentage, minPrice}
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(position_id) -- One strategy per position
);

-- Transactions table: Immutable blockchain transaction records
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    position_id UUID NOT NULL REFERENCES positions(id),
    type VARCHAR(10) NOT NULL CHECK (type IN ('buy', 'sell', 'fee')),
    signature VARCHAR(100) NOT NULL UNIQUE,
    amount_tokens DECIMAL(20,8) NOT NULL,
    price_per_token DECIMAL(20,8) NOT NULL,
    total_usd DECIMAL(20,8) NOT NULL,
    fees JSONB NOT NULL, -- {networkFee, priorityFee, jupiterFee}
    status VARCHAR(20) NOT NULL DEFAULT 'pending'
        CHECK (status IN ('pending', 'confirmed', 'failed')),
    block_number BIGINT,
    executed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Watchlist table: Tokens tracked for potential trading
CREATE TABLE watchlist_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_address VARCHAR(50) NOT NULL UNIQUE,
    token_symbol VARCHAR(20),
    token_name VARCHAR(100),
    custom_name VARCHAR(100),
    notes TEXT,
    is_pinned BOOLEAN NOT NULL DEFAULT false,
    added_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_viewed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Price snapshots table: TimescaleDB hypertable for time-series price data
CREATE TABLE price_snapshots (
    timestamp TIMESTAMPTZ NOT NULL,
    token_address VARCHAR(50) NOT NULL,
    price_usd DECIMAL(20,8) NOT NULL,
    volume_24h DECIMAL(20,2),
    market_cap DECIMAL(20,2),
    price_change_1h DECIMAL(8,4),
    price_change_24h DECIMAL(8,4),
    liquidity DECIMAL(20,2),
    fdv DECIMAL(20,2), -- Fully diluted valuation
    source VARCHAR(20) NOT NULL CHECK (source IN ('cmc', 'jupiter', 'helius')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Convert price_snapshots to TimescaleDB hypertable
SELECT create_hypertable('price_snapshots', 'timestamp');

-- Job queue state table: BullMQ job persistence and monitoring
CREATE TABLE job_queue_state (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id VARCHAR(100) NOT NULL UNIQUE,
    job_type VARCHAR(50) NOT NULL, -- 'price_monitor', 'exit_execution', 'notification'
    status VARCHAR(20) NOT NULL DEFAULT 'pending'
        CHECK (status IN ('pending', 'active', 'completed', 'failed', 'delayed')),
    data JSONB NOT NULL,
    priority INTEGER NOT NULL DEFAULT 0,
    attempts INTEGER NOT NULL DEFAULT 0,
    max_attempts INTEGER NOT NULL DEFAULT 3,
    scheduled_at TIMESTAMPTZ,
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    error_message TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for query optimization
CREATE INDEX idx_positions_status ON positions(status);
CREATE INDEX idx_positions_token_address ON positions(token_address);
CREATE INDEX idx_positions_created_at ON positions(created_at DESC);

CREATE INDEX idx_exit_strategies_position_id ON exit_strategies(position_id);
CREATE INDEX idx_exit_strategies_is_active ON exit_strategies(is_active);

CREATE INDEX idx_transactions_position_id ON transactions(position_id);
CREATE INDEX idx_transactions_signature ON transactions(signature);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_type ON transactions(type);

CREATE INDEX idx_watchlist_is_pinned ON watchlist_items(is_pinned DESC);
CREATE INDEX idx_watchlist_created_at ON watchlist_items(created_at DESC);
CREATE INDEX idx_watchlist_token_address ON watchlist_items(token_address);

-- TimescaleDB optimized indexes for price data
CREATE INDEX idx_price_snapshots_token_timestamp ON price_snapshots(token_address, timestamp DESC);
CREATE INDEX idx_price_snapshots_source ON price_snapshots(source);

CREATE INDEX idx_job_queue_status ON job_queue_state(status);
CREATE INDEX idx_job_queue_type ON job_queue_state(job_type);
CREATE INDEX idx_job_queue_scheduled_at ON job_queue_state(scheduled_at);

-- State-aware polling configuration table
CREATE TABLE polling_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL UNIQUE,
    interval_seconds INTEGER NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Insert default polling configurations
INSERT INTO polling_config (name, interval_seconds, description) VALUES
('watchlist_default', 60, 'Standard watchlist monitoring'),
('watchlist_pinned', 15, 'Enhanced monitoring for pinned tokens'),
('position_active', 30, 'Active position monitoring (armed mode)'),
('position_near_target', 5, 'Ultra-fast polling near exit triggers');

-- Update triggers for timestamp management
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_positions_updated_at BEFORE UPDATE ON positions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_exit_strategies_updated_at BEFORE UPDATE ON exit_strategies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_watchlist_items_updated_at BEFORE UPDATE ON watchlist_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_job_queue_state_updated_at BEFORE UPDATE ON job_queue_state
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_polling_config_updated_at BEFORE UPDATE ON polling_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## Frontend Architecture

### Component Architecture

#### Component Organization

```
src/
├── components/
│   ├── ui/               # shadcn/ui base components
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   ├── dialog.tsx
│   │   └── table.tsx
│   ├── trading/          # Trading-specific components
│   │   ├── TradingPanel.tsx
│   │   ├── QuoteDisplay.tsx
│   │   ├── ExitStrategyForm.tsx
│   │   └── SlippageSelector.tsx
│   ├── positions/        # Position management components
│   │   ├── PositionCard.tsx
│   │   ├── PositionDashboard.tsx
│   │   ├── PositionDetails.tsx
│   │   └── ManualCloseButton.tsx
│   ├── watchlist/        # Watchlist components
│   │   ├── WatchlistTable.tsx
│   │   ├── AddTokenDialog.tsx
│   │   ├── BulkAddDialog.tsx
│   │   └── WatchlistMetrics.tsx
│   ├── layout/           # Layout and navigation
│   │   ├── Header.tsx
│   │   ├── Navigation.tsx
│   │   ├── StatusBar.tsx
│   │   └── AlertCenter.tsx
│   └── charts/           # Data visualization
│       ├── PriceChart.tsx
│       ├── PnLChart.tsx
│       └── PerformanceChart.tsx
```

#### Component Template

```typescript
interface PositionCardProps {
  position: Position;
  currentPrice?: number;
  onClose: (positionId: string) => void;
  onModifyStrategy: (positionId: string) => void;
  className?: string;
}

export function PositionCard({ 
  position, 
  currentPrice, 
  onClose, 
  onModifyStrategy,
  className 
}: PositionCardProps) {
  const { pnlUsd, pnlPercent } = usePositionPnL(position, currentPrice);
  
  return (
    <Card className={cn("p-4", className)}>
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-semibold">{position.tokenSymbol}</h3>
          <p className="text-sm text-muted-foreground">
            {formatTokenAmount(position.amountTokens)} tokens
          </p>
        </div>
        <div className="text-right">
          <div className={cn(
            "text-lg font-bold",
            pnlPercent >= 0 ? "text-green-600" : "text-red-600"
          )}>
            {pnlPercent >= 0 ? '+' : ''}{pnlPercent.toFixed(2)}%
          </div>
          <div className="text-sm text-muted-foreground">
            ${pnlUsd?.toFixed(2)}
          </div>
        </div>
      </div>
      
      <div className="mt-4 flex gap-2">
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => onModifyStrategy(position.id)}
        >
          Modify Strategy
        </Button>
        <Button 
          variant="destructive" 
          size="sm"
          onClick={() => onClose(position.id)}
        >
          Close Position
        </Button>
      </div>
    </Card>
  );
}
```

### State Management Architecture

#### State Structure

```typescript
// stores/tradingStore.ts
interface TradingState {
  // Active positions with real-time updates
  positions: Position[];
  selectedPosition: Position | null;
  
  // Trading panel state
  currentQuote: TradingQuote | null;
  isQuoteLoading: boolean;
  quoteError: string | null;
  
  // Exit strategy configuration
  exitStrategy: ExitStrategyConfig;
  strategyPresets: ExitStrategyPreset[];
  
  // Actions
  setPositions: (positions: Position[]) => void;
  updatePosition: (id: string, updates: Partial<Position>) => void;
  selectPosition: (position: Position | null) => void;
  
  fetchQuote: (tokenAddress: string, amountUsd: number) => Promise<void>;
  executeBuy: (quote: TradingQuote, strategy: ExitStrategyConfig) => Promise<void>;
  closePosition: (positionId: string) => Promise<void>;
}

// stores/watchlistStore.ts  
interface WatchlistState {
  items: WatchlistItem[];
  isLoading: boolean;
  error: string | null;
  
  // Polling state
  pollingInterval: number; // Dynamic based on pinned items
  lastUpdate: Date | null;
  
  // Actions
  addItem: (tokenAddress: string, metadata?: Partial<WatchlistItem>) => Promise<void>;
  bulkAdd: (tokens: BulkAddToken[]) => Promise<void>;
  updateItem: (id: string, updates: Partial<WatchlistItem>) => Promise<void>;
  removeItem: (id: string) => Promise<void>;
  
  refreshPrices: () => Promise<void>;
  startPolling: () => void;
  stopPolling: () => void;
}

// stores/systemStore.ts
interface SystemState {
  isOnline: boolean;
  apiStatus: Record<string, 'healthy' | 'degraded' | 'down'>;
  notifications: Notification[];
  
  // Real-time price updates
  priceUpdates: Record<string, PriceData>; // tokenAddress -> price
  
  addNotification: (notification: Omit<Notification, 'id'>) => void;
  removeNotification: (id: string) => void;
  updatePrices: (updates: Record<string, PriceData>) => void;
}
```

#### State Management Patterns

- **Separation of Concerns:** Each store manages a specific domain (trading, watchlist, system)
- **Computed Values:** Derived state like PnL calculations computed in custom hooks
- **Optimistic Updates:** UI updates immediately, with rollback on API failure
- **Real-time Sync:** WebSocket-style price updates merged into stores
- **Persistence:** Critical state (exit strategies) persisted to localStorage as backup
- **Error Boundaries:** Each store includes error state for graceful failure handling

### Routing Architecture

#### Route Organization

```
app/
├── layout.tsx            # Root layout with providers
├── page.tsx             # Dashboard/home page
├── trading/             # Trading interface
│   ├── page.tsx         # Main trading panel
│   └── quote/[id]/      # Quote detail page
├── positions/           # Position management
│   ├── page.tsx         # Position dashboard
│   └── [id]/            # Position detail page
├── watchlist/           # Token research
│   ├── page.tsx         # Watchlist table
│   └── add/            # Add tokens page
├── settings/            # Configuration
│   ├── page.tsx         # General settings
│   ├── strategies/      # Exit strategy presets
│   └── notifications/   # Alert preferences
└── api/                 # API routes
    ├── trades/
    ├── positions/
    ├── watchlist/
    └── health/
```

#### Protected Route Pattern

```typescript
// components/auth/ProtectedRoute.tsx
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
}

export function ProtectedRoute({ 
  children, 
  requiredPermission 
}: ProtectedRouteProps) {
  const { isAuthenticated, hasPermission } = useAuth();
  
  if (!isAuthenticated) {
    redirect('/login');
  }
  
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return (
      <div className="p-8 text-center">
        <h2 className="text-xl font-semibold">Access Denied</h2>
        <p>You don't have permission to view this page.</p>
      </div>
    );
  }
  
  return <>{children}</>;
}

// app/positions/layout.tsx
export default function PositionsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ProtectedRoute requiredPermission="view_positions">
      <div className="container mx-auto py-6">
        {children}
      </div>
    </ProtectedRoute>
  );
}
```

### Frontend Services Layer

#### API Client Setup

```typescript
// lib/api-client.ts
class ApiClient {
  private baseURL: string;
  private defaultHeaders: HeadersInit;
  
  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001';
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }
  
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    const config: RequestInit = {
      ...options,
      headers: {
        ...this.defaultHeaders,
        ...options.headers,
      },
    };
    
    try {
      const response = await fetch(url, config);
      const data = await response.json();
      
      if (!response.ok) {
        throw new ApiError(data.error?.message || 'Request failed', response.status);
      }
      
      return {
        data,
        status: response.status,
        headers: response.headers,
      };
    } catch (error) {
      if (error instanceof ApiError) throw error;
      throw new ApiError('Network error', 0);
    }
  }
  
  async get<T>(endpoint: string, params?: Record<string, string>): Promise<T> {
    const searchParams = params ? `?${new URLSearchParams(params)}` : '';
    const result = await this.request<T>(`${endpoint}${searchParams}`);
    return result.data;
  }
  
  async post<T>(endpoint: string, data?: any): Promise<T> {
    const result = await this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
    return result.data;
  }
  
  async patch<T>(endpoint: string, data: any): Promise<T> {
    const result = await this.request<T>(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
    return result.data;
  }
  
  async delete<T>(endpoint: string): Promise<T> {
    const result = await this.request<T>(endpoint, {
      method: 'DELETE',
    });
    return result.data;
  }
}

export const apiClient = new ApiClient();
```

#### Service Example

```typescript
// services/tradingService.ts
export class TradingService {
  async getQuote(tokenAddress: string, amountUsd: number, slippageBps: number): Promise<TradingQuote> {
    return apiClient.post<TradingQuote>('/api/trades/quote', {
      tokenAddress,
      amountUsd,
      slippageBps,
    });
  }
  
  async executeBuy(
    tokenAddress: string, 
    amountUsd: number, 
    slippageBps: number,
    exitStrategy: ExitStrategyConfig
  ): Promise<TransactionResult> {
    return apiClient.post<TransactionResult>('/api/trades/buy', {
      tokenAddress,
      amountUsd,
      slippageBps,
      exitStrategy,
    });
  }
  
  async getPositions(status: PositionStatus = 'active'): Promise<Position[]> {
    return apiClient.get<Position[]>('/api/positions', { status });
  }
  
  async closePosition(positionId: string): Promise<TransactionResult> {
    return apiClient.delete<TransactionResult>(`/api/positions/${positionId}`);
  }
  
  async modifyExitStrategy(
    positionId: string, 
    exitStrategy: ExitStrategyConfig
  ): Promise<Position> {
    return apiClient.patch<Position>(`/api/positions/${positionId}`, {
      exitStrategy,
    });
  }
}

export const tradingService = new TradingService();

// hooks/useTrading.ts - React hook wrapper
export function useTrading() {
  const [positions, setPositions] = useState<Position[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const fetchPositions = useCallback(async (status?: PositionStatus) => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await tradingService.getPositions(status);
      setPositions(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch positions');
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  const closePosition = useCallback(async (positionId: string) => {
    try {
      setError(null);
      await tradingService.closePosition(positionId);
      // Optimistically remove from UI
      setPositions(prev => prev.filter(p => p.id !== positionId));
      // Refresh to get actual state
      await fetchPositions();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to close position');
      // Revert optimistic update
      await fetchPositions();
    }
  }, [fetchPositions]);
  
  return {
    positions,
    isLoading,
    error,
    fetchPositions,
    closePosition,
  };
}
```

## Backend Architecture

### Service Architecture

#### Controller/Route Organization

```
src/
├── routes/
│   ├── index.ts          # Route aggregation and setup
│   ├── trades.ts         # Trading operations (/api/trades/*)
│   ├── positions.ts      # Position management (/api/positions/*)
│   ├── watchlist.ts      # Watchlist operations (/api/watchlist/*)
│   ├── health.ts         # System health checks (/api/health)
│   └── webhooks.ts       # External webhook handlers
├── controllers/
│   ├── TradeController.ts    # Trading logic coordination
│   ├── PositionController.ts # Position CRUD operations
│   ├── WatchlistController.ts # Watchlist management
│   └── SystemController.ts   # Health and status endpoints
├── services/
│   ├── TradingService.ts     # Core trading operations
│   ├── ExitStrategyService.ts # Automated exit logic
│   ├── PriceMonitorService.ts # Price monitoring and alerts
│   ├── NotificationService.ts # Alert delivery
│   └── ExternalApiService.ts  # Jupiter, Helius, CMC integration
├── repositories/
│   ├── PositionRepository.ts  # Position data access
│   ├── TransactionRepository.ts # Transaction records
│   ├── WatchlistRepository.ts  # Watchlist data access
│   └── PriceRepository.ts     # Time-series price data
├── jobs/
│   ├── workers/              # BullMQ job processors
│   │   ├── priceMonitor.ts   # Price monitoring job
│   │   ├── exitExecution.ts  # Exit strategy execution
│   │   └── notifications.ts  # Notification delivery
│   ├── schedulers/           # Job scheduling logic
│   └── queues.ts            # Queue configuration
└── middleware/
    ├── auth.ts              # Authentication middleware
    ├── validation.ts        # Request validation
    ├── errorHandler.ts      # Global error handling
    └── rateLimit.ts         # API rate limiting
```

#### Controller Template

```typescript
// controllers/PositionController.ts
export class PositionController {
  constructor(
    private positionService: PositionService,
    private exitStrategyService: ExitStrategyService,
    private notificationService: NotificationService
  ) {}
  
  async getAllPositions(req: Request, res: Response): Promise<void> {
    try {
      const { status = 'active' } = req.query;
      const positions = await this.positionService.getPositions(status as PositionStatus);
      
      res.json({
        data: positions,
        total: positions.length,
        status: status,
      });
    } catch (error) {
      throw new ApiError('Failed to fetch positions', 500, error);
    }
  }
  
  async updatePosition(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { exitStrategy, isActive } = req.body;
      
      // Validate position exists
      const position = await this.positionService.getById(id);
      if (!position) {
        throw new ApiError('Position not found', 404);
      }
      
      // Update exit strategy if provided
      if (exitStrategy) {
        await this.exitStrategyService.updateStrategy(id, exitStrategy);
      }
      
      // Update position status if provided
      if (typeof isActive === 'boolean') {
        await this.positionService.updateStatus(id, isActive ? 'active' : 'paused');
      }
      
      const updatedPosition = await this.positionService.getById(id);
      
      // Send notification for significant changes
      if (exitStrategy) {
        await this.notificationService.sendAlert({
          type: 'strategy_updated',
          message: `Exit strategy updated for ${position.tokenSymbol}`,
          data: { positionId: id, strategy: exitStrategy },
        });
      }
      
      res.json({
        data: updatedPosition,
        message: 'Position updated successfully',
      });
    } catch (error) {
      throw new ApiError('Failed to update position', 500, error);
    }
  }
  
  async closePosition(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const position = await this.positionService.getById(id);
      
      if (!position) {
        throw new ApiError('Position not found', 404);
      }
      
      if (position.status !== 'active') {
        throw new ApiError('Position is not active', 400);
      }
      
      // Queue immediate sell job with high priority
      const jobResult = await this.exitStrategyService.queueManualClose(id);
      
      res.json({
        message: 'Position close initiated',
        jobId: jobResult.id,
        estimatedTime: '5-15 seconds',
      });
    } catch (error) {
      throw new ApiError('Failed to initiate position close', 500, error);
    }
  }
  
  async emergencyCloseAll(req: Request, res: Response): Promise<void> {
    try {
      const activePositions = await this.positionService.getPositions('active');
      
      const closeJobs = await Promise.all(
        activePositions.map(position => 
          this.exitStrategyService.queueManualClose(position.id, 'emergency')
        )
      );
      
      await this.notificationService.sendAlert({
        type: 'emergency_close',
        message: `Emergency close initiated for ${activePositions.length} positions`,
        priority: 'critical',
      });
      
      res.json({
        message: `Emergency close initiated for ${activePositions.length} positions`,
        jobIds: closeJobs.map(job => job.id),
        estimatedTime: '15-60 seconds',
      });
    } catch (error) {
      throw new ApiError('Emergency close failed', 500, error);
    }
  }
}
```

### Database Architecture

#### Schema Design

```sql
-- Core position tracking with audit trail
CREATE TABLE positions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_address VARCHAR(50) NOT NULL,
    token_symbol VARCHAR(20),
    token_name VARCHAR(100),
    amount_tokens DECIMAL(20,8) NOT NULL,
    entry_price DECIMAL(20,8) NOT NULL,
    entry_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    status VARCHAR(20) NOT NULL DEFAULT 'active'
        CHECK (status IN ('active', 'closing', 'closed', 'paused')),
    
    -- Performance tracking
    highest_price DECIMAL(20,8), -- For trailing stops
    lowest_price DECIMAL(20,8),  -- For analytics
    realized_pnl DECIMAL(20,8) DEFAULT 0, -- Completed exits only
    
    -- Metadata
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    closed_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT positive_amount CHECK (amount_tokens > 0),
    CONSTRAINT positive_entry_price CHECK (entry_price > 0)
);

-- Exit strategy configurations with JSONB for flexibility
CREATE TABLE exit_strategies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    position_id UUID NOT NULL REFERENCES positions(id) ON DELETE CASCADE,
    
    -- Serialized strategy configuration
    take_profit_tiers JSONB, -- [{tierNumber, targetPrice, percentageToSell, status, executedAt}]
    stop_loss JSONB,         -- {triggerPrice, percentageToSell, status, executedAt}
    trailing_stop JSONB,     -- {trailDistance, highestPrice, currentStopPrice, isActive, adjustments[]}
    moon_bag JSONB,          -- {percentage, minPrice, isActive}
    
    -- Configuration metadata
    is_active BOOLEAN NOT NULL DEFAULT true,
    preset_name VARCHAR(50), -- Reference to saved preset
    
    -- Audit trail
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(position_id), -- One strategy per position
    CONSTRAINT valid_strategy CHECK (
        take_profit_tiers IS NOT NULL OR 
        stop_loss IS NOT NULL OR 
        trailing_stop IS NOT NULL
    )
);

-- Time-series price data optimized for monitoring
CREATE TABLE price_snapshots (
    timestamp TIMESTAMPTZ NOT NULL,
    token_address VARCHAR(50) NOT NULL,
    price_usd DECIMAL(20,8) NOT NULL,
    volume_24h DECIMAL(20,2),
    market_cap DECIMAL(20,2),
    price_change_1h DECIMAL(8,4),
    price_change_24h DECIMAL(8,4),
    liquidity DECIMAL(20,2),
    fdv DECIMAL(20,2),
    source VARCHAR(20) NOT NULL,
    
    -- TimescaleDB optimization
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT positive_price CHECK (price_usd > 0),
    CONSTRAINT valid_source CHECK (source IN ('cmc', 'jupiter', 'helius', 'birdeye'))
);

-- Convert to TimescaleDB hypertable for time-series optimization
SELECT create_hypertable('price_snapshots', 'timestamp');

-- Data retention policy (keep 90 days of price data)
SELECT add_retention_policy('price_snapshots', INTERVAL '90 days');
```

#### Data Access Layer

```typescript
// repositories/PositionRepository.ts
export class PositionRepository {
  constructor(private prisma: PrismaClient) {}
  
  async create(data: CreatePositionData): Promise<Position> {
    return this.prisma.position.create({
      data: {
        ...data,
        status: 'active',
        entryTimestamp: new Date(),
      },
      include: {
        exitStrategy: true,
        transactions: true,
      },
    });
  }
  
  async findByStatus(status: PositionStatus): Promise<Position[]> {
    return this.prisma.position.findMany({
      where: { status },
      include: {
        exitStrategy: true,
        transactions: {
          orderBy: { createdAt: 'desc' },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }
  
  async findById(id: string): Promise<Position | null> {
    return this.prisma.position.findUnique({
      where: { id },
      include: {
        exitStrategy: true,
        transactions: {
          orderBy: { createdAt: 'desc' },
        },
      },
    });
  }
  
  async updatePriceTracking(
    id: string, 
    currentPrice: Decimal,
    updateHighest: boolean = true
  ): Promise<void> {
    const position = await this.findById(id);
    if (!position) throw new Error('Position not found');
    
    const updates: Partial<Position> = {
      updatedAt: new Date(),
    };
    
    // Track highest price for trailing stops
    if (updateHighest && (!position.highestPrice || currentPrice.gt(position.highestPrice))) {
      updates.highestPrice = currentPrice;
    }
    
    // Track lowest price for analytics
    if (!position.lowestPrice || currentPrice.lt(position.lowestPrice)) {
      updates.lowestPrice = currentPrice;
    }
    
    await this.prisma.position.update({
      where: { id },
      data: updates,
    });
  }
  
  async closePosition(id: string, realizedPnL: Decimal): Promise<Position> {
    return this.prisma.position.update({
      where: { id },
      data: {
        status: 'closed',
        realizedPnl: realizedPnL,
        closedAt: new Date(),
        updatedAt: new Date(),
      },
    });
  }
  
  // Advanced queries for analytics
  async getPositionMetrics(timeframe: 'day' | 'week' | 'month' = 'week'): Promise<PositionMetrics> {
    const since = new Date();
    const days = timeframe === 'day' ? 1 : timeframe === 'week' ? 7 : 30;
    since.setDate(since.getDate() - days);
    
    const [totalPositions, activePositions, closedPositions] = await Promise.all([
      this.prisma.position.count({
        where: { createdAt: { gte: since } },
      }),
      this.prisma.position.count({
        where: { status: 'active' },
      }),
      this.prisma.position.findMany({
        where: {
          status: 'closed',
          closedAt: { gte: since },
        },
        select: { realizedPnl: true },
      }),
    ]);
    
    const totalRealizedPnL = closedPositions.reduce(
      (sum, pos) => sum.add(pos.realizedPnl || new Decimal(0)),
      new Decimal(0)
    );
    
    const winningTrades = closedPositions.filter(
      pos => pos.realizedPnl && pos.realizedPnl.gt(0)
    ).length;
    
    return {
      totalPositions,
      activePositions,
      closedPositions: closedPositions.length,
      totalRealizedPnL,
      winRate: closedPositions.length > 0 ? winningTrades / closedPositions.length : 0,
      timeframe,
    };
  }
}
```

### Authentication and Authorization

#### Auth Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API
    participant S as Session Store
    participant D as Database

    Note over U,D: Session-Based Authentication (Single User)
    U->>F: Access application
    F->>A: GET /api/auth/status
    A->>S: Check session cookie
    
    alt No valid session
        S-->>A: No session found
        A-->>F: 401 Unauthorized
        F-->>U: Redirect to login
        
        U->>F: Enter credentials
        F->>A: POST /api/auth/login
        A->>D: Validate credentials
        D-->>A: User data
        A->>S: Create session
        S-->>A: Session ID
        A-->>F: Set session cookie + user data
        F-->>U: Redirect to dashboard
    else Valid session exists
        S-->>A: Session data
        A-->>F: User data
        F-->>U: Show authenticated interface
    end
    
    Note over U,D: Authenticated Request
    U->>F: Trading action
    F->>A: POST /api/trades/buy (with session cookie)
    A->>S: Validate session
    S-->>A: Session valid
    A->>A: Process trading request
    A-->>F: Trading response
    F-->>U: Update UI
```

#### Middleware/Guards

```typescript
// middleware/auth.ts
export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    permissions: string[];
  };
  session?: {
    id: string;
    expiresAt: Date;
  };
}

export async function requireAuth(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const sessionId = req.cookies.sessionId;
    
    if (!sessionId) {
      return res.status(401).json({
        error: {
          code: 'NO_SESSION',
          message: 'Authentication required',
        },
      });
    }
    
    // Check session in Redis (fast lookup)
    const sessionData = await redis.get(`session:${sessionId}`);
    
    if (!sessionData) {
      return res.status(401).json({
        error: {
          code: 'INVALID_SESSION',
          message: 'Session expired or invalid',
        },
      });
    }
    
    const session = JSON.parse(sessionData);
    
    // Validate session expiration
    if (new Date() > new Date(session.expiresAt)) {
      await redis.del(`session:${sessionId}`);
      return res.status(401).json({
        error: {
          code: 'SESSION_EXPIRED',
          message: 'Session has expired',
        },
      });
    }
    
    // Extend session expiration on activity
    const extendedSession = {
      ...session,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    };
    
    await redis.setex(
      `session:${sessionId}`,
      24 * 60 * 60, // 24 hours in seconds
      JSON.stringify(extendedSession)
    );
    
    // Attach user data to request
    req.user = session.user;
    req.session = {
      id: sessionId,
      expiresAt: new Date(extendedSession.expiresAt),
    };
    
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(500).json({
      error: {
        code: 'AUTH_ERROR',
        message: 'Authentication system error',
      },
    });
  }
}

// Specialized middleware for trading operations
export async function requireTradingPermission(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> {
  if (!req.user) {
    return res.status(401).json({
      error: {
        code: 'NO_USER',
        message: 'User not authenticated',
      },
    });
  }
  
  // For single-user app, all authenticated users have trading permission
  // In multi-user version, check permissions array
  const hasPermission = req.user.permissions.includes('trading') || 
                       req.user.permissions.includes('admin');
  
  if (!hasPermission) {
    return res.status(403).json({
      error: {
        code: 'INSUFFICIENT_PERMISSIONS',
        message: 'Trading permission required',
      },
    });
  }
  
  next();
}

// Routes setup with middleware
// routes/trades.ts
const router = express.Router();

router.use(requireAuth); // All trading routes require authentication
router.use(requireTradingPermission); // All trading routes require trading permission

router.post('/quote', tradeController.getQuote);
router.post('/buy', tradeController.executeBuy);
```

## Unified Project Structure

```
solana-trading-app/
├── .github/                    # CI/CD workflows
│   └── workflows/
│       ├── ci.yaml
│       └── deploy.yaml
├── apps/                       # Application packages
│   ├── web/                    # Frontend application
│   │   ├── src/
│   │   │   ├── app/            # Next.js App Router pages
│   │   │   │   ├── layout.tsx
│   │   │   │   ├── page.tsx
│   │   │   │   ├── trading/
│   │   │   │   ├── positions/
│   │   │   │   ├── watchlist/
│   │   │   │   ├── settings/
│   │   │   │   └── api/        # API route handlers
│   │   │   ├── components/     # UI components
│   │   │   │   ├── ui/         # shadcn/ui components
│   │   │   │   ├── trading/
│   │   │   │   ├── positions/
│   │   │   │   ├── watchlist/
│   │   │   │   └── layout/
│   │   │   ├── hooks/          # Custom React hooks
│   │   │   │   ├── useTrading.ts
│   │   │   │   ├── usePositions.ts
│   │   │   │   ├── useWatchlist.ts
│   │   │   │   └── useRealTimePrice.ts
│   │   │   ├── services/       # API client services
│   │   │   │   ├── api-client.ts
│   │   │   │   ├── trading.ts
│   │   │   │   ├── positions.ts
│   │   │   │   └── watchlist.ts
│   │   │   ├── stores/         # Zustand state management
│   │   │   │   ├── tradingStore.ts
│   │   │   │   ├── positionStore.ts
│   │   │   │   ├── watchlistStore.ts
│   │   │   │   └── systemStore.ts
│   │   │   ├── lib/            # Frontend utilities
│   │   │   │   ├── utils.ts
│   │   │   │   ├── constants.ts
│   │   │   │   ├── formatters.ts
│   │   │   │   └── validators.ts
│   │   │   └── styles/         # Global styles/themes
│   │   │       ├── globals.css
│   │   │       └── components.css
│   │   ├── public/             # Static assets
│   │   │   ├── favicon.ico
│   │   │   └── images/
│   │   ├── tests/              # Frontend tests
│   │   │   ├── components/
│   │   │   ├── hooks/
│   │   │   ├── services/
│   │   │   └── utils/
│   │   ├── next.config.js
│   │   ├── tailwind.config.js
│   │   ├── vitest.config.ts
│   │   └── package.json
│   └── api/                    # Backend application
│       ├── src/
│       │   ├── routes/         # API routes/controllers
│       │   │   ├── index.ts
│       │   │   ├── trades.ts
│       │   │   ├── positions.ts
│       │   │   ├── watchlist.ts
│       │   │   ├── health.ts
│       │   │   └── webhooks.ts
│       │   ├── controllers/    # Request handlers
│       │   │   ├── TradeController.ts
│       │   │   ├── PositionController.ts
│       │   │   ├── WatchlistController.ts
│       │   │   └── SystemController.ts
│       │   ├── services/       # Business logic
│       │   │   ├── TradingService.ts
│       │   │   ├── ExitStrategyService.ts
│       │   │   ├── PriceMonitorService.ts
│       │   │   ├── NotificationService.ts
│       │   │   └── ExternalApiService.ts
│       │   ├── repositories/   # Data access layer
│       │   │   ├── PositionRepository.ts
│       │   │   ├── TransactionRepository.ts
│       │   │   ├── WatchlistRepository.ts
│       │   │   └── PriceRepository.ts
│       │   ├── jobs/           # BullMQ job processing
│       │   │   ├── workers/
│       │   │   │   ├── priceMonitor.ts
│       │   │   │   ├── exitExecution.ts
│       │   │   │   └── notifications.ts
│       │   │   ├── schedulers/
│       │   │   └── queues.ts
│       │   ├── middleware/     # Express middleware
│       │   │   ├── auth.ts
│       │   │   ├── validation.ts
│       │   │   ├── errorHandler.ts
│       │   │   └── rateLimit.ts
│       │   ├── lib/            # Backend utilities
│       │   │   ├── database.ts
│       │   │   ├── redis.ts
│       │   │   ├── logger.ts
│       │   │   └── config.ts
│       │   ├── types/          # TypeScript definitions
│       │   │   ├── api.ts
│       │   │   ├── jobs.ts
│       │   │   └── external.ts
│       │   └── server.ts       # Express server entry point
│       ├── tests/              # Backend tests
│       │   ├── integration/
│       │   ├── unit/
│       │   └── fixtures/
│       ├── prisma/             # Database schema and migrations
│       │   ├── schema.prisma
│       │   ├── migrations/
│       │   └── seed.ts
│       ├── vitest.config.ts
│       └── package.json
├── packages/                   # Shared packages
│   ├── shared/                 # Shared types/utilities
│   │   ├── src/
│   │   │   ├── types/          # TypeScript interfaces
│   │   │   │   ├── position.ts
│   │   │   │   ├── trading.ts
│   │   │   │   ├── watchlist.ts
│   │   │   │   ├── external-api.ts
│   │   │   │   └── index.ts
│   │   │   ├── constants/      # Shared constants
│   │   │   │   ├── trading.ts
│   │   │   │   ├── api.ts
│   │   │   │   └── index.ts
│   │   │   ├── utils/          # Shared utilities
│   │   │   │   ├── validation.ts
│   │   │   │   ├── formatters.ts
│   │   │   │   ├── calculations.ts
│   │   │   │   └── index.ts
│   │   │   └── index.ts
│   │   ├── tests/
│   │   └── package.json
│   ├── ui/                     # Shared UI components
│   │   ├── src/
│   │   │   ├── components/
│   │   │   │   ├── charts/
│   │   │   │   └── forms/
│   │   │   └── index.ts
│   │   └── package.json
│   └── config/                 # Shared configuration
│       ├── eslint/
│       │   ├── base.js
│       │   ├── react.js
│       │   └── node.js
│       ├── typescript/
│       │   ├── base.json
│       │   ├── nextjs.json
│       │   └── node.json
│       ├── tailwind/
│       │   └── base.js
│       └── vitest/
│           └── base.ts
├── infrastructure/             # IaC definitions
│   ├── railway/
│   │   ├── railway.json
│   │   └── database.json
│   ├── vercel/
│   │   └── vercel.json
│   └── docker/
│       ├── Dockerfile.api
│       └── docker-compose.yml
├── scripts/                    # Build/deploy scripts
│   ├── setup.sh
│   ├── build.sh
│   ├── test.sh
│   └── deploy.sh
├── docs/                       # Documentation
│   ├── prd.md
│   ├── architecture.md
│   ├── api-reference.md
│   └── deployment.md
├── .env.example                # Environment template
├── .gitignore
├── package.json                # Root package.json
├── tsconfig.json              # Root TypeScript config
└── README.md
```

## Development Workflow

### Local Development Setup

#### Prerequisites

```bash
# Install Node.js (v20 LTS)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 20
nvm use 20

# Install pnpm (faster package manager)
npm install -g pnpm

# Install Docker for local services
# macOS: Download Docker Desktop
# Ubuntu: apt-get install docker.io docker-compose

# Install Railway CLI for deployment
npm install -g @railway/cli

# Install Git (if not already installed)
git --version
```

#### Initial Setup

```bash
# Clone repository
git clone <repository-url>
cd solana-trading-app

# Install all dependencies (monorepo)
pnpm install

# Copy environment files
cp .env.example .env.local
cp apps/api/.env.example apps/api/.env

# Start local services (PostgreSQL, Redis, TimescaleDB)
docker-compose up -d

# Setup database
pnpm db:setup

# Generate Prisma client
pnpm db:generate

# Run database migrations
pnpm db:migrate

# Seed initial data
pnpm db:seed
```

#### Development Commands

```bash
# Start all services (frontend + backend + jobs)
pnpm dev

# Start frontend only (Next.js)
pnpm dev:web

# Start backend only (Express + jobs)
pnpm dev:api

# Run tests
pnpm test          # Run all tests
pnpm test:unit     # Unit tests only
pnpm test:integration # Integration tests only
pnpm test:e2e      # End-to-end tests only

# Database operations
pnpm db:studio     # Open Prisma Studio
pnpm db:reset      # Reset database
pnpm db:backup     # Create backup
pnpm db:restore    # Restore from backup

# Code quality
pnpm lint          # ESLint
pnpm type-check    # TypeScript checking
pnpm format        # Prettier formatting

# Build for production
pnpm build         # Build all apps
pnpm build:web     # Build frontend only
pnpm build:api     # Build backend only
```

### Environment Configuration

#### Required Environment Variables

```bash
# Frontend (.env.local)
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001
NEXT_PUBLIC_WS_URL=ws://localhost:3001
NEXT_PUBLIC_ENVIRONMENT=development
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn_if_enabled

# Backend (.env)
PORT=3001
NODE_ENV=development
DATABASE_URL=postgresql://postgres:password@localhost:5432/trading_app
REDIS_URL=redis://localhost:6379

# External API keys
HELIUS_API_KEY=your_helius_api_key
CMC_API_KEY=your_coinmarketcap_api_key
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# Solana configuration
SOLANA_RPC_URL=https://rpc.helius.xyz/?api-key=${HELIUS_API_KEY}
WALLET_PRIVATE_KEY=your_base58_private_key

# Session management
SESSION_SECRET=your_strong_session_secret
COOKIE_SECRET=your_cookie_signing_secret

# Shared
LOG_LEVEL=debug
CORS_ORIGIN=http://localhost:3000
API_RATE_LIMIT=100
```

## Deployment Architecture

### Deployment Strategy

**Frontend Deployment:**
- **Platform:** Vercel
- **Build Command:** `pnpm build:web`
- **Output Directory:** `apps/web/.next`
- **CDN/Edge:** Vercel Edge Network with automatic optimization

**Backend Deployment:**
- **Platform:** Railway
- **Build Command:** `pnpm build:api && pnpm db:migrate`
- **Deployment Method:** Git-based with automatic deployments from main branch

### CI/CD Pipeline

```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: timescale/timescaledb:latest-pg15
        env:
          POSTGRES_PASSWORD: password
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      
      - name: Install pnpm
        run: npm install -g pnpm
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Type checking
        run: pnpm type-check
      
      - name: Linting
        run: pnpm lint
      
      - name: Unit tests
        run: pnpm test:unit
        env:
          DATABASE_URL: postgresql://postgres:password@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379
      
      - name: Integration tests
        run: pnpm test:integration
        env:
          DATABASE_URL: postgresql://postgres:password@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379
          HELIUS_API_KEY: ${{ secrets.HELIUS_API_KEY_TEST }}
      
      - name: Build applications
        run: pnpm build

  deploy-frontend:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Deploy to Vercel
        uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: apps/web

  deploy-backend:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Deploy to Railway
        run: |
          npm install -g @railway/cli
          railway login --token ${{ secrets.RAILWAY_TOKEN }}
          railway up --service backend --directory apps/api
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}

  e2e-tests:
    needs: [deploy-frontend, deploy-backend]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Run E2E tests
        run: pnpm test:e2e
        env:
          E2E_BASE_URL: ${{ secrets.PRODUCTION_FRONTEND_URL }}
          E2E_API_URL: ${{ secrets.PRODUCTION_BACKEND_URL }}
```

### Environments

| Environment | Frontend URL | Backend URL | Purpose |
|------------|--------------|-------------|----------|
| Development | http://localhost:3000 | http://localhost:3001 | Local development |
| Staging | https://staging.solana-trader.app | https://staging-api.solana-trader.app | Pre-production testing |
| Production | https://app.solana-trader.com | https://api.solana-trader.com | Live environment |

## Security and Performance

### Security Requirements

**Frontend Security:**
- CSP Headers: `default-src 'self'; script-src 'self' 'unsafe-eval'; connect-src 'self' https://api.solana-trader.com https://rpc.helius.xyz; img-src 'self' data: https:;`
- XSS Prevention: Content Security Policy, input sanitization with DOMPurify, secure cookie settings
- Secure Storage: Sensitive data in httpOnly cookies, no localStorage for secrets, session tokens only

**Backend Security:**
- Input Validation: Joi/Zod schemas for all API inputs, parameterized database queries, rate limiting per endpoint
- Rate Limiting: 100 requests/minute per IP, 10 requests/second for trading endpoints, progressive backoff
- CORS Policy: `{origin: ['https://app.solana-trader.com', 'http://localhost:3000'], credentials: true}`

**Authentication Security:**
- Token Storage: Session-based auth with httpOnly cookies, 24-hour expiration, secure flag in production
- Session Management: Redis-based session store, automatic cleanup, session rotation on sensitive operations
- Password Policy: N/A (single-user with private key authentication)

### Performance Optimization

**Frontend Performance:**
- Bundle Size Target: < 500KB initial bundle, code splitting by route, lazy loading for non-critical components
- Loading Strategy: SSR for initial page, client-side navigation, prefetching for likely routes
- Caching Strategy: SWR for API data, service worker for static assets, CDN edge caching

**Backend Performance:**
- Response Time Target: < 200ms for API endpoints, < 2s for complex trading operations, < 5s for exit execution
- Database Optimization: Indexed queries, connection pooling, TimescaleDB for time-series data
- Caching Strategy: Redis for frequently accessed data, 30s TTL for price data, permanent cache for token metadata

## Testing Strategy

### Testing Pyramid

```
              E2E Tests
             /        \
        Integration Tests
           /            \
      Frontend Unit  Backend Unit
```

### Test Organization

#### Frontend Tests

```
apps/web/tests/
├── components/           # Component unit tests
│   ├── trading/
│   │   ├── TradingPanel.test.tsx
│   │   ├── QuoteDisplay.test.tsx
│   │   └── ExitStrategyForm.test.tsx
│   ├── positions/
│   │   ├── PositionCard.test.tsx
│   │   ├── PositionDashboard.test.tsx
│   │   └── ManualCloseButton.test.tsx
│   └── watchlist/
│       ├── WatchlistTable.test.tsx
│       ├── AddTokenDialog.test.tsx
│       └── BulkAddDialog.test.tsx
├── hooks/                # Custom hook tests
│   ├── useTrading.test.ts
│   ├── usePositions.test.ts
│   ├── useWatchlist.test.ts
│   └── useRealTimePrice.test.ts
├── services/             # API service tests
│   ├── trading.test.ts
│   ├── positions.test.ts
│   └── watchlist.test.ts
├── stores/               # State management tests
│   ├── tradingStore.test.ts
│   ├── positionStore.test.ts
│   └── watchlistStore.test.ts
├── utils/                # Utility function tests
│   ├── formatters.test.ts
│   ├── validators.test.ts
│   └── calculations.test.ts
└── setup.ts              # Test environment setup
```

#### Backend Tests

```
apps/api/tests/
├── unit/                 # Unit tests
│   ├── services/
│   │   ├── TradingService.test.ts
│   │   ├── ExitStrategyService.test.ts
│   │   ├── PriceMonitorService.test.ts
│   │   └── NotificationService.test.ts
│   ├── repositories/
│   │   ├── PositionRepository.test.ts
│   │   ├── TransactionRepository.test.ts
│   │   └── WatchlistRepository.test.ts
│   ├── controllers/
│   │   ├── TradeController.test.ts
│   │   ├── PositionController.test.ts
│   │   └── WatchlistController.test.ts
│   └── utils/
│       ├── calculations.test.ts
│       ├── validators.test.ts
│       └── formatters.test.ts
├── integration/          # Integration tests
│   ├── api/
│   │   ├── trades.test.ts
│   │   ├── positions.test.ts
│   │   ├── watchlist.test.ts
│   │   └── health.test.ts
│   ├── jobs/
│   │   ├── priceMonitor.test.ts
│   │   ├── exitExecution.test.ts
│   │   └── notifications.test.ts
│   └── external-apis/
│       ├── jupiter.test.ts
│       ├── helius.test.ts
│       └── coinmarketcap.test.ts
├── fixtures/             # Test data
│   ├── positions.json
│   ├── transactions.json
│   ├── watchlist.json
│   └── api-responses.json
└── setup.ts              # Test environment setup
```

#### E2E Tests

```
tests/e2e/
├── trading/              # Trading workflow tests
│   ├── buy-flow.spec.ts
│   ├── sell-flow.spec.ts
│   ├── quote-accuracy.spec.ts
│   └── exit-strategy.spec.ts
├── positions/            # Position management tests
│   ├── position-monitoring.spec.ts
│   ├── manual-close.spec.ts
│   ├── strategy-modification.spec.ts
│   └── emergency-close.spec.ts
├── watchlist/            # Watchlist functionality tests
│   ├── add-tokens.spec.ts
│   ├── bulk-operations.spec.ts
│   ├── price-updates.spec.ts
│   └── integration-flow.spec.ts
├── system/               # System-level tests
│   ├── health-checks.spec.ts
│   ├── error-handling.spec.ts
│   ├── rate-limiting.spec.ts
│   └── performance.spec.ts
└── utils/                # Test utilities
    ├── test-helpers.ts
    ├── mock-data.ts
    └── page-objects/
```

### Test Examples

#### Frontend Component Test

```typescript
// apps/web/tests/components/positions/PositionCard.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { PositionCard } from '../../../src/components/positions/PositionCard';
import { mockPosition } from '../../fixtures/positions';

const mockOnClose = vi.fn();
const mockOnModifyStrategy = vi.fn();

describe('PositionCard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('displays position information correctly', () => {
    const position = mockPosition({
      tokenSymbol: 'BONK',
      amountTokens: '1000000',
      entryPrice: '0.000015',
    });

    render(
      <PositionCard
        position={position}
        currentPrice={0.000018}
        onClose={mockOnClose}
        onModifyStrategy={mockOnModifyStrategy}
      />
    );

    expect(screen.getByText('BONK')).toBeInTheDocument();
    expect(screen.getByText('1,000,000 tokens')).toBeInTheDocument();
    expect(screen.getByText('+20.00%')).toBeInTheDocument();
    expect(screen.getByText('$3.00')).toBeInTheDocument();
  });

  it('calculates PnL correctly for profitable position', () => {
    const position = mockPosition({
      amountTokens: '100000',
      entryPrice: '0.0001',
    });

    render(
      <PositionCard
        position={position}
        currentPrice={0.00015} // 50% gain
        onClose={mockOnClose}
        onModifyStrategy={mockOnModifyStrategy}
      />
    );

    expect(screen.getByText('+50.00%')).toBeInTheDocument();
    expect(screen.getByText('$5.00')).toBeInTheDocument();
  });

  it('handles close position action', async () => {
    const position = mockPosition();

    render(
      <PositionCard
        position={position}
        onClose={mockOnClose}
        onModifyStrategy={mockOnModifyStrategy}
      />
    );

    const closeButton = screen.getByText('Close Position');
    fireEvent.click(closeButton);

    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalledWith(position.id);
    });
  });

  it('handles modify strategy action', async () => {
    const position = mockPosition();

    render(
      <PositionCard
        position={position}
        onClose={mockOnClose}
        onModifyStrategy={mockOnModifyStrategy}
      />
    );

    const modifyButton = screen.getByText('Modify Strategy');
    fireEvent.click(modifyButton);

    await waitFor(() => {
      expect(mockOnModifyStrategy).toHaveBeenCalledWith(position.id);
    });
  });
});
```

#### Backend API Test

```typescript
// apps/api/tests/integration/api/trades.test.ts
import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import { TestServer } from '../../utils/test-server';
import { createMockPosition } from '../../fixtures/positions';
import { mockJupiterQuote } from '../../fixtures/external-api-responses';

describe('Trades API', () => {
  let server: TestServer;

  beforeAll(async () => {
    server = new TestServer();
    await server.start();
  });

  afterAll(async () => {
    await server.stop();
  });

  beforeEach(async () => {
    await server.resetDatabase();
  });

  describe('POST /api/trades/quote', () => {
    it('returns valid quote for legitimate token', async () => {
      // Mock Jupiter API response
      server.mockExternalApi('jupiter', '/v6/quote', mockJupiterQuote);

      const response = await server.request
        .post('/api/trades/quote')
        .send({
          tokenAddress: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
          amountUsd: 100,
          slippageBps: 300,
        })
        .expect(200);

      expect(response.body).toMatchObject({
        inputMint: 'So11111111111111111111111111111111111111112', // SOL
        outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
        outputAmount: expect.any(String),
        priceImpact: expect.any(String),
        route: expect.any(Array),
      });
    });

    it('validates input parameters', async () => {
      const response = await server.request
        .post('/api/trades/quote')
        .send({
          tokenAddress: 'invalid-address',
          amountUsd: -10, // Invalid amount
        })
        .expect(400);

      expect(response.body.error.code).toBe('VALIDATION_ERROR');
      expect(response.body.error.details).toContain('tokenAddress');
      expect(response.body.error.details).toContain('amountUsd');
    });

    it('handles external API failures gracefully', async () => {
      // Mock Jupiter API failure
      server.mockExternalApiError('jupiter', '/v6/quote', 503);

      const response = await server.request
        .post('/api/trades/quote')
        .send({
          tokenAddress: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
          amountUsd: 100,
          slippageBps: 300,
        })
        .expect(503);

      expect(response.body.error.code).toBe('EXTERNAL_API_ERROR');
      expect(response.body.error.message).toContain('Jupiter');
    });
  });

  describe('POST /api/trades/buy', () => {
    it('executes buy order with exit strategy', async () => {
      // Mock Jupiter swap response
      server.mockExternalApi('jupiter', '/v6/swap', {
        swapTransaction: 'base64-encoded-transaction',
      });

      // Mock Helius transaction submission
      server.mockExternalApi('helius', '/', {
        result: '5VERSomeTxSignatureHere1234567890',
      });

      const exitStrategy = {
        takeProfitTiers: [
          { targetPrice: '0.0002', percentageToSell: 50 },
          { targetPrice: '0.0003', percentageToSell: 50 },
        ],
        stopLoss: { triggerPrice: '0.00008', percentageToSell: 100 },
      };

      const response = await server.request
        .post('/api/trades/buy')
        .send({
          tokenAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263', // BONK
          amountUsd: 50,
          slippageBps: 500,
          exitStrategy,
        })
        .expect(200);

      expect(response.body).toMatchObject({
        signature: expect.any(String),
        status: 'initiated',
        positionId: expect.any(String),
      });

      // Verify position was created in database
      const position = await server.db.position.findFirst({
        where: { id: response.body.positionId },
        include: { exitStrategy: true },
      });

      expect(position).toBeTruthy();
      expect(position?.exitStrategy).toBeTruthy();
    });
  });
});
```

#### E2E Test

```typescript
// tests/e2e/trading/buy-flow.spec.ts
import { test, expect } from '@playwright/test';
import { TestHelper } from '../utils/test-helper';

test.describe('Buy Flow', () => {
  let helper: TestHelper;

  test.beforeEach(async ({ page }) => {
    helper = new TestHelper(page);
    await helper.login();
    await helper.navigateToTrading();
  });

  test('complete buy flow with exit strategy', async ({ page }) => {
    // Enter token address
    await page.fill('[data-testid="token-address-input"]', 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263');
    await page.fill('[data-testid="amount-input"]', '100');

    // Wait for quote to load
    await page.waitForSelector('[data-testid="quote-display"]');
    
    // Verify quote information
    const outputAmount = await page.textContent('[data-testid="output-amount"]');
    expect(outputAmount).toContain('BONK');

    const priceImpact = await page.textContent('[data-testid="price-impact"]');
    expect(priceImpact).toMatch(/\d+\.\d{2}%/);

    // Configure exit strategy
    await page.click('[data-testid="exit-strategy-tab"]');
    
    // Set take profit
    await page.fill('[data-testid="tp-tier-1-price"]', '0.000020');
    await page.fill('[data-testid="tp-tier-1-percent"]', '50');
    
    await page.click('[data-testid="add-tier-button"]');
    await page.fill('[data-testid="tp-tier-2-price"]', '0.000030');
    await page.fill('[data-testid="tp-tier-2-percent"]', '50');

    // Set stop loss
    await page.fill('[data-testid="stop-loss-price"]', '0.000010');

    // Execute buy
    await page.click('[data-testid="execute-buy-button"]');

    // Confirm transaction
    await page.click('[data-testid="confirm-transaction-button"]');

    // Wait for transaction confirmation
    await page.waitForSelector('[data-testid="transaction-success"]');

    // Verify redirect to positions dashboard
    await expect(page).toHaveURL(/\/positions/);

    // Verify position appears in dashboard
    await page.waitForSelector('[data-testid="position-card"]');
    
    const positionCard = page.locator('[data-testid="position-card"]').first();
    await expect(positionCard.locator('[data-testid="token-symbol"]')).toHaveText('BONK');
    await expect(positionCard.locator('[data-testid="position-status"]')).toHaveText('Active');
  });

  test('handles insufficient balance error', async ({ page }) => {
    await helper.setMockBalance('0'); // No SOL balance

    await page.fill('[data-testid="token-address-input"]', 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263');
    await page.fill('[data-testid="amount-input"]', '1000'); // Large amount

    await page.click('[data-testid="execute-buy-button"]');

    // Verify error message
    await expect(page.locator('[data-testid="error-message"]')).toHaveText(/Insufficient SOL balance/);
    
    // Verify buy button is disabled
    await expect(page.locator('[data-testid="execute-buy-button"]')).toBeDisabled();
  });
});
```

## Coding Standards

### Critical Fullstack Rules

- **Type Sharing:** Always define types in packages/shared and import from there - prevents frontend/backend type mismatches
- **API Calls:** Never make direct HTTP calls - use the service layer for consistent error handling and retry logic
- **Environment Variables:** Access only through config objects, never process.env directly - enables validation and type safety
- **Error Handling:** All API routes must use the standard error handler - ensures consistent error responses
- **State Updates:** Never mutate state directly - use proper state management patterns to prevent bugs
- **Database Queries:** Always use repository pattern with Prisma - enables testing and consistent data access
- **Decimal Math:** Use Decimal.js for all financial calculations - prevents floating point precision errors
- **Async Operations:** Always handle Promise rejections - use proper try/catch or .catch() methods
- **Configuration:** Validate all config at startup - fail fast if environment is misconfigured

### Naming Conventions

| Element | Frontend | Backend | Example |
|---------|----------|---------|---------|
| Components | PascalCase | - | `UserProfile.tsx` |
| Hooks | camelCase with 'use' | - | `useAuth.ts` |
| API Routes | - | kebab-case | `/api/user-profile` |
| Database Tables | - | snake_case | `user_profiles` |
| Constants | SCREAMING_SNAKE_CASE | SCREAMING_SNAKE_CASE | `MAX_SLIPPAGE_BPS` |
| Services | PascalCase | PascalCase | `TradingService` |
| Types/Interfaces | PascalCase | PascalCase | `Position`, `ExitStrategy` |

## Error Handling Strategy

### Error Flow

```mermaid
sequenceDiagram
    participant F as Frontend
    participant A as API
    participant S as Service
    participant E as External API
    participant L as Logger

    Note over F,L: Error Handling Flow
    F->>A: API Request
    A->>S: Business Logic Call
    S->>E: External API Call
    
    alt External API Error
        E-->>S: API Error Response
        S->>L: Log external API error
        S->>S: Apply circuit breaker logic
        alt Circuit Open
            S-->>A: ServiceUnavailableError
        else Circuit Closed
            S->>S: Retry with backoff
            S->>E: Retry external call
            E-->>S: Success/Failure
        end
    end
    
    alt Service Error
        S-->>A: BusinessLogicError
        A->>L: Log service error with context
        A->>A: Transform to API error format
    end
    
    alt Database Error
        S-->>A: DatabaseError
        A->>L: Log database error (critical)
        A->>A: Transform to generic error (no DB details exposed)
    end
    
    A-->>F: Standardized Error Response
    F->>F: Display user-friendly message
    F->>L: Log frontend error (optional)
```

### Error Response Format

```typescript
interface ApiError {
  error: {
    code: string;
    message: string;
    details?: Record<string, any>;
    timestamp: string;
    requestId: string;
  };
}
```

### Frontend Error Handling

```typescript
// services/error-handler.ts
export class ErrorHandler {
  static handleApiError(error: unknown): UserFriendlyError {
    if (error instanceof ApiError) {
      return this.transformApiError(error);
    }
    
    if (error instanceof NetworkError) {
      return {
        message: 'Network connection error. Please check your internet connection.',
        code: 'NETWORK_ERROR',
        severity: 'warning',
        retryable: true,
      };
    }
    
    // Log unexpected errors
    console.error('Unexpected error:', error);
    
    return {
      message: 'An unexpected error occurred. Please try again.',
      code: 'UNKNOWN_ERROR',
      severity: 'error',
      retryable: false,
    };
  }
  
  private static transformApiError(apiError: ApiError): UserFriendlyError {
    const { code, message, details } = apiError.error;
    
    // Map API error codes to user-friendly messages
    const errorMap: Record<string, string> = {
      'INSUFFICIENT_BALANCE': 'You don\'t have enough SOL to complete this transaction.',
      'INVALID_TOKEN_ADDRESS': 'The token address you entered is not valid.',
      'PRICE_IMPACT_TOO_HIGH': 'Price impact is too high. Try reducing the amount or increasing slippage.',
      'EXTERNAL_API_ERROR': 'Trading services are temporarily unavailable. Please try again.',
      'RATE_LIMIT_EXCEEDED': 'Too many requests. Please wait a moment and try again.',
      'POSITION_NOT_FOUND': 'The position you\'re trying to access was not found.',
      'STRATEGY_VALIDATION_ERROR': 'Exit strategy configuration is invalid. Please check your settings.',
    };
    
    return {
      message: errorMap[code] || message,
      code,
      severity: this.getSeverity(code),
      retryable: this.isRetryable(code),
      details,
    };
  }
  
  private static getSeverity(code: string): 'info' | 'warning' | 'error' | 'critical' {
    const criticalCodes = ['DATABASE_ERROR', 'SYSTEM_FAILURE'];
    const warningCodes = ['EXTERNAL_API_ERROR', 'RATE_LIMIT_EXCEEDED'];
    const infoCodes = ['VALIDATION_ERROR', 'POSITION_NOT_FOUND'];
    
    if (criticalCodes.includes(code)) return 'critical';
    if (warningCodes.includes(code)) return 'warning';
    if (infoCodes.includes(code)) return 'info';
    return 'error';
  }
  
  private static isRetryable(code: string): boolean {
    const retryableCodes = [
      'NETWORK_ERROR',
      'EXTERNAL_API_ERROR',
      'RATE_LIMIT_EXCEEDED',
      'TEMPORARY_UNAVAILABLE',
    ];
    return retryableCodes.includes(code);
  }
}

// hooks/useErrorHandler.ts
export function useErrorHandler() {
  const showNotification = useNotificationStore(state => state.addNotification);
  
  const handleError = useCallback((error: unknown, context?: string) => {
    const friendlyError = ErrorHandler.handleApiError(error);
    
    showNotification({
      type: friendlyError.severity,
      title: context ? `${context} Error` : 'Error',
      message: friendlyError.message,
      action: friendlyError.retryable ? {
        label: 'Retry',
        onClick: () => window.location.reload(),
      } : undefined,
    });
    
    // Log to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      logError(error, context);
    }
  }, [showNotification]);
  
  return { handleError };
}
```

### Backend Error Handling

```typescript
// middleware/errorHandler.ts
export class GlobalErrorHandler {
  static handle(
    error: Error,
    req: Request,
    res: Response,
    next: NextFunction
  ): void {
    const requestId = req.headers['x-request-id'] as string || generateRequestId();
    const timestamp = new Date().toISOString();
    
    // Log error with context
    logger.error('Request failed', {
      requestId,
      method: req.method,
      path: req.path,
      error: error.message,
      stack: error.stack,
      userId: req.user?.id,
    });
    
    if (error instanceof ValidationError) {
      res.status(400).json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Request validation failed',
          details: error.details,
          timestamp,
          requestId,
        },
      });
      return;
    }
    
    if (error instanceof BusinessLogicError) {
      res.status(400).json({
        error: {
          code: error.code,
          message: error.message,
          details: error.details,
          timestamp,
          requestId,
        },
      });
      return;
    }
    
    if (error instanceof ExternalApiError) {
      const statusCode = error.isTemporary ? 503 : 502;
      res.status(statusCode).json({
        error: {
          code: 'EXTERNAL_API_ERROR',
          message: 'External service temporarily unavailable',
          timestamp,
          requestId,
        },
      });
      return;
    }
    
    if (error instanceof DatabaseError) {
      // Never expose database details to client
      res.status(500).json({
        error: {
          code: 'DATABASE_ERROR',
          message: 'Internal server error',
          timestamp,
          requestId,
        },
      });
      return;
    }
    
    // Generic error fallback
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred',
        timestamp,
        requestId,
      },
    });
  }
}

// Custom error classes
export class BusinessLogicError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: Record<string, any>
  ) {
    super(message);
    this.name = 'BusinessLogicError';
  }
}

export class ExternalApiError extends Error {
  constructor(
    message: string,
    public service: string,
    public isTemporary: boolean = true,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'ExternalApiError';
  }
}

// Circuit breaker for external APIs
export class CircuitBreaker {
  private failures = 0;
  private lastFailure = 0;
  private state: 'closed' | 'open' | 'half-open' = 'closed';
  
  constructor(
    private failureThreshold: number = 5,
    private timeout: number = 60000 // 1 minute
  ) {}
  
  async execute<T>(fn: () => Promise<T>): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailure < this.timeout) {
        throw new ExternalApiError('Circuit breaker is open', 'circuit-breaker');
      }
      this.state = 'half-open';
    }
    
    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  private onSuccess(): void {
    this.failures = 0;
    this.state = 'closed';
  }
  
  private onFailure(): void {
    this.failures++;
    this.lastFailure = Date.now();
    
    if (this.failures >= this.failureThreshold) {
      this.state = 'open';
    }
  }
}
```

# Local Development Setup

## Overview

This section provides comprehensive instructions for setting up the Solana Trading App locally on your development machine. This setup creates a fully functional local environment that replicates the production architecture behavior, allowing complete development and testing of all Solana trading features without any external deployments.

**Goal:** Create a self-contained local development environment with Next.js frontend (localhost:3000), Express API (localhost:3001), PostgreSQL with TimescaleDB, Redis, and BullMQ job processing.

## Prerequisites Installation (Exact Versions)

### 1. Node.js 20.x LTS

```bash
# Install Node.js 20.x LTS
# Option 1: Using nvm (recommended)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 20
nvm use 20

# Option 2: Direct download from https://nodejs.org/

# Verify installation
node --version  # Should output v20.x.x
npm --version   # Should output 10.x.x
```

### 2. pnpm Package Manager

```bash
# Install pnpm globally
npm install -g pnpm

# Verify installation
pnpm --version  # Should output 8.x.x or higher

# Enable pnpm workspaces
pnpm config set auto-install-peers true
```

### 3. Docker and Docker Compose

```bash
# Install Docker Desktop
# Visit: https://docs.docker.com/desktop/

# Verify installation
docker --version         # Should output Docker version 20.x.x or higher
docker compose version   # Should output Docker Compose version v2.x.x or higher

# Test Docker is running
docker run hello-world
```

### 4. Git Configuration

```bash
# Verify Git installation
git --version  # Should output git version 2.x.x or higher

# Configure Git (if not already done)
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

### 5. Additional Development Tools

```bash
# Install useful development tools
npm install -g @types/node typescript ts-node nodemon

# Optional: Install database client tools
# PostgreSQL client tools for database management
brew install postgresql  # macOS
# OR
sudo apt-get install postgresql-client  # Ubuntu/Debian
```

## Repository Setup and Monorepo Configuration

### 1. Clone and Initialize Repository

```bash
# Clone the repository
git clone <repository-url> solana-trading-app
cd solana-trading-app

# Verify project structure
ls -la  # Should see docs/, package.json, etc.
```

### 2. Create Monorepo Structure

```bash
# Create the monorepo directory structure
mkdir -p apps/web apps/api packages/shared
mkdir -p packages/shared/src/types packages/shared/src/utils
mkdir -p packages/shared/src/config

# Create root package.json with workspaces
cat > package.json << 'EOF'
{
  "name": "solana-trading-app",
  "version": "1.0.0",
  "private": true,
  "workspaces": [
    "apps/*",
    "packages/*"
  ],
  "scripts": {
    "dev": "pnpm --parallel --stream dev",
    "build": "pnpm --recursive build",
    "clean": "pnpm --recursive clean",
    "lint": "pnpm --recursive lint",
    "test": "pnpm --recursive test",
    "type-check": "pnpm --recursive type-check"
  },
  "devDependencies": {
    "typescript": "^5.3.0",
    "prettier": "^3.1.0",
    "eslint": "^8.57.0"
  }
}
EOF

# Install root dependencies
pnpm install
```

### 3. Setup Workspace Packages

```bash
# Create shared package
cd packages/shared
cat > package.json << 'EOF'
{
  "name": "@solana-trading/shared",
  "version": "1.0.0",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "scripts": {
    "build": "tsc",
    "dev": "tsc --watch",
    "clean": "rm -rf dist"
  },
  "devDependencies": {
    "typescript": "^5.3.0"
  },
  "dependencies": {
    "decimal.js": "^10.4.3"
  }
}
EOF

# Create TypeScript config for shared package
cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "commonjs",
    "declaration": true,
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
EOF

cd ../..
```

## Environment Configuration (Complete .env Setup)

### 1. Frontend Environment Variables

Create `apps/web/.env.local`:

```bash
mkdir -p apps/web
cat > apps/web/.env.local << 'EOF'
# Frontend Environment Variables
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_WS_URL=ws://localhost:3001

# Environment
NODE_ENV=development

# Optional: Analytics (leave empty for local dev)
NEXT_PUBLIC_SENTRY_DSN=
NEXT_PUBLIC_GA_TRACKING_ID=

# Feature Flags
NEXT_PUBLIC_ENABLE_DEVTOOLS=true
NEXT_PUBLIC_ENABLE_MOCK_DATA=false
EOF
```

### 2. Backend Environment Variables

Create `apps/api/.env`:

```bash
mkdir -p apps/api
cat > apps/api/.env << 'EOF'
# Backend Environment Variables

# Environment
NODE_ENV=development
PORT=3001

# Database Configuration (TimescaleDB)
DATABASE_URL=postgresql://postgres:password@localhost:5432/solana_trading_db
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=solana_trading_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password

# Redis Configuration (BullMQ + Caching)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379

# Session Configuration
SESSION_SECRET=your-super-secret-session-key-change-in-production
SESSION_NAME=solana-trading-session

# Solana Configuration
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_WALLET_PRIVATE_KEY=your-base58-private-key-here
SOLANA_NETWORK=devnet

# External API Keys (REQUIRED - see setup instructions below)
HELIUS_API_KEY=your-helius-api-key
COINMARKETCAP_API_KEY=your-cmc-api-key
JUPITER_API_URL=https://quote-api.jup.ag/v6

# Telegram Bot Configuration (Optional)
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_CHAT_ID=your-telegram-chat-id

# Monitoring and Logging
LOG_LEVEL=debug
SENTRY_DSN=

# Rate Limiting
API_RATE_LIMIT=1000
API_RATE_WINDOW=900000

# Job Queue Configuration
BULLMQ_REDIS_URL=redis://localhost:6379
MAX_JOB_ATTEMPTS=3
JOB_TIMEOUT=300000

# Development Settings
ENABLE_API_DOCS=true
ENABLE_BULL_BOARD=true
BULL_BOARD_PATH=/admin/queues
EOF
```

### 3. Required API Keys Setup

#### Helius API Key
1. Visit [https://www.helius.xyz/](https://www.helius.xyz/)
2. Sign up for a free account
3. Create a new project and copy the API key
4. Add to `.env`: `HELIUS_API_KEY=your-helius-api-key`

#### CoinMarketCap API Key  
1. Visit [https://pro.coinmarketcap.com/signup](https://pro.coinmarketcap.com/signup)
2. Sign up for the Basic plan (free)
3. Go to API section and copy your API key
4. Add to `.env`: `COINMARKETCAP_API_KEY=your-cmc-api-key`

#### Telegram Bot (Optional)
1. Message [@BotFather](https://t.me/botfather) on Telegram
2. Use `/newbot` command to create a bot
3. Copy the bot token
4. Get your chat ID by messaging [@userinfobot](https://t.me/userinfobot)
5. Add to `.env`: `TELEGRAM_BOT_TOKEN=...` and `TELEGRAM_CHAT_ID=...`

#### Solana Wallet Setup
```bash
# Generate a new Solana keypair for development
solana-keygen new --outfile ~/.config/solana/devnet-wallet.json
solana-keygen pubkey ~/.config/solana/devnet-wallet.json

# Get the private key in base58 format
solana-keygen pubkey ~/.config/solana/devnet-wallet.json --output json

# Add to .env (replace with your generated key)
# SOLANA_WALLET_PRIVATE_KEY=your-base58-private-key
```

## Local Services Infrastructure (Docker-based)

### 1. Docker Compose Configuration

Create `docker-compose.yml` in project root:

```yaml
version: '3.8'

services:
  # PostgreSQL with TimescaleDB Extension
  postgres:
    image: timescale/timescaledb:2.11.2-pg15
    container_name: solana-trading-postgres
    environment:
      POSTGRES_DB: solana_trading_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d solana_trading_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - solana-trading-network

  # Redis for BullMQ and Caching
  redis:
    image: redis:7.2-alpine
    container_name: solana-trading-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - solana-trading-network

  # Redis Commander (Web UI for Redis)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: solana-trading-redis-ui
    hostname: redis-commander
    ports:
      - "8081:8081"
    environment:
      REDIS_HOSTS: "local:redis:6379"
      HTTP_USER: admin
      HTTP_PASSWORD: admin
    depends_on:
      - redis
    networks:
      - solana-trading-network

  # pgAdmin (Web UI for PostgreSQL)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: solana-trading-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - solana-trading-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  solana-trading-network:
    driver: bridge
```

### 2. Start Local Services

```bash
# Start all services in detached mode
docker compose up -d

# Check service health
docker compose ps

# View logs
docker compose logs -f postgres
docker compose logs -f redis

# Stop services when needed
docker compose down

# Reset all data (WARNING: Destroys all local data)
docker compose down -v
```

### 3. Service Access Points

Once services are running:
- **PostgreSQL Database**: localhost:5432
- **Redis**: localhost:6379  
- **pgAdmin** (Database UI): http://localhost:8080 (<EMAIL> / admin)
- **Redis Commander** (Redis UI): http://localhost:8081 (admin / admin)

## Database Setup and Initialization

### 1. Create Database Initialization Script

```bash
mkdir -p database/init
cat > database/init/01-create-extensions.sql << 'EOF'
-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "timescaledb" CASCADE;

-- Create database if not exists
SELECT 'CREATE DATABASE solana_trading_db'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'solana_trading_db');
EOF
```

### 2. Setup Prisma

```bash
cd apps/api

# Initialize Prisma
npx prisma init

# Create Prisma schema based on architecture
cat > prisma/schema.prisma << 'EOF'
generator client {
  provider = "prisma-client-js"
  output   = "./generated/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Position {
  id                String      @id @default(uuid()) @db.Uuid
  tokenAddress      String      @map("token_address") @db.VarChar(50)
  tokenSymbol       String?     @map("token_symbol") @db.VarChar(20)
  tokenName         String?     @map("token_name") @db.VarChar(100)
  amountTokens      Decimal     @map("amount_tokens") @db.Decimal(20, 8)
  entryPrice        Decimal     @map("entry_price") @db.Decimal(20, 8)
  entryTimestamp    DateTime    @default(now()) @map("entry_timestamp") @db.Timestamptz
  status            String      @default("active") @db.VarChar(20)
  highestPrice      Decimal?    @map("highest_price") @db.Decimal(20, 8)
  lowestPrice       Decimal?    @map("lowest_price") @db.Decimal(20, 8)
  realizedPnl       Decimal?    @default(0) @map("realized_pnl") @db.Decimal(20, 8)
  createdAt         DateTime    @default(now()) @map("created_at") @db.Timestamptz
  updatedAt         DateTime    @updatedAt @map("updated_at") @db.Timestamptz
  closedAt          DateTime?   @map("closed_at") @db.Timestamptz

  // Relations
  exitStrategy      ExitStrategy?
  transactions      Transaction[]

  @@map("positions")
}

model ExitStrategy {
  id                String    @id @default(uuid()) @db.Uuid
  positionId        String    @unique @map("position_id") @db.Uuid
  takeProfitTiers   Json?     @map("take_profit_tiers")
  stopLoss          Json?     @map("stop_loss")
  trailingStop      Json?     @map("trailing_stop")
  moonBag           Json?     @map("moon_bag")
  isActive          Boolean   @default(true) @map("is_active")
  presetName        String?   @map("preset_name") @db.VarChar(50)
  createdAt         DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt         DateTime  @updatedAt @map("updated_at") @db.Timestamptz

  // Relations
  position          Position  @relation(fields: [positionId], references: [id], onDelete: Cascade)

  @@map("exit_strategies")
}

model Transaction {
  id              String    @id @default(uuid()) @db.Uuid
  positionId      String    @map("position_id") @db.Uuid
  type            String    @db.VarChar(10)
  signature       String    @unique @db.VarChar(100)
  amountTokens    Decimal   @map("amount_tokens") @db.Decimal(20, 8)
  pricePerToken   Decimal   @map("price_per_token") @db.Decimal(20, 8)
  totalUsd        Decimal   @map("total_usd") @db.Decimal(20, 8)
  fees            Json
  status          String    @default("pending") @db.VarChar(20)
  blockNumber     BigInt?   @map("block_number")
  executedAt      DateTime? @map("executed_at") @db.Timestamptz
  createdAt       DateTime  @default(now()) @map("created_at") @db.Timestamptz

  // Relations
  position        Position  @relation(fields: [positionId], references: [id])

  @@map("transactions")
}

model WatchlistItem {
  id             String    @id @default(uuid()) @db.Uuid
  tokenAddress   String    @unique @map("token_address") @db.VarChar(50)
  tokenSymbol    String?   @map("token_symbol") @db.VarChar(20)
  tokenName      String?   @map("token_name") @db.VarChar(100)
  customName     String?   @map("custom_name") @db.VarChar(100)
  notes          String?
  isPinned       Boolean   @default(false) @map("is_pinned")
  addedAt        DateTime  @default(now()) @map("added_at") @db.Timestamptz
  lastViewedAt   DateTime? @map("last_viewed_at") @db.Timestamptz
  createdAt      DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt      DateTime  @updatedAt @map("updated_at") @db.Timestamptz

  @@map("watchlist_items")
}

model PriceSnapshot {
  timestamp       DateTime  @db.Timestamptz
  tokenAddress    String    @map("token_address") @db.VarChar(50)
  priceUsd        Decimal   @map("price_usd") @db.Decimal(20, 8)
  volume24h       Decimal?  @map("volume_24h") @db.Decimal(20, 2)
  marketCap       Decimal?  @map("market_cap") @db.Decimal(20, 2)
  priceChange1h   Decimal?  @map("price_change_1h") @db.Decimal(8, 4)
  priceChange24h  Decimal?  @map("price_change_24h") @db.Decimal(8, 4)
  liquidity       Decimal?  @db.Decimal(20, 2)
  fdv             Decimal?  @db.Decimal(20, 2)
  source          String    @db.VarChar(20)
  createdAt       DateTime  @default(now()) @map("created_at") @db.Timestamptz

  @@id([tokenAddress, timestamp])
  @@map("price_snapshots")
}

model JobQueueState {
  id            String    @id @default(uuid()) @db.Uuid
  jobId         String    @unique @map("job_id") @db.VarChar(100)
  jobType       String    @map("job_type") @db.VarChar(50)
  status        String    @default("pending") @db.VarChar(20)
  data          Json
  priority      Int       @default(0)
  attempts      Int       @default(0)
  maxAttempts   Int       @default(3) @map("max_attempts")
  scheduledAt   DateTime? @map("scheduled_at") @db.Timestamptz
  startedAt     DateTime? @map("started_at") @db.Timestamptz
  completedAt   DateTime? @map("completed_at") @db.Timestamptz
  errorMessage  String?   @map("error_message")
  createdAt     DateTime  @default(now()) @map("created_at") @db.Timestamptz
  updatedAt     DateTime  @updatedAt @map("updated_at") @db.Timestamptz

  @@map("job_queue_state")
}

model PollingConfig {
  id              String   @id @default(uuid()) @db.Uuid
  name            String   @unique @db.VarChar(50)
  intervalSeconds Int      @map("interval_seconds")
  description     String?
  isActive        Boolean  @default(true) @map("is_active")
  createdAt       DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt       DateTime @updatedAt @map("updated_at") @db.Timestamptz

  @@map("polling_config")
}
EOF
```

### 3. Generate and Run Migrations

```bash
# Generate Prisma client
npx prisma generate

# Create and run initial migration
npx prisma migrate dev --name init

# Seed the database with initial data
cat > prisma/seed.ts << 'EOF'
import { PrismaClient } from './generated/client'

const prisma = new PrismaClient()

async function main() {
  // Seed polling configuration
  await prisma.pollingConfig.createMany({
    data: [
      {
        name: 'watchlist_default',
        intervalSeconds: 60,
        description: 'Standard watchlist monitoring'
      },
      {
        name: 'watchlist_pinned',
        intervalSeconds: 15,
        description: 'Enhanced monitoring for pinned tokens'
      },
      {
        name: 'position_active',
        intervalSeconds: 30,
        description: 'Active position monitoring (armed mode)'
      },
      {
        name: 'position_near_target',
        intervalSeconds: 5,
        description: 'Ultra-fast polling near exit triggers'
      }
    ],
    skipDuplicates: true,
  })

  console.log('Database seeded successfully')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
EOF

# Run the seed script
npx tsx prisma/seed.ts
```

### 4. Verify Database Setup

```bash
# Check database connection
npx prisma studio

# Run a simple query test
npx prisma db execute --stdin << 'EOF'
SELECT current_database(), current_user, version();
EOF

# Verify TimescaleDB extension
npx prisma db execute --stdin << 'EOF'
SELECT * FROM pg_extension WHERE extname = 'timescaledb';
EOF
```

## Application Startup (Multi-service)

### 1. Frontend Setup (Next.js)

```bash
cd apps/web

# Create package.json for frontend
cat > package.json << 'EOF'
{
  "name": "@solana-trading/web",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "next": "^14.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "@solana-trading/shared": "workspace:*",
    "zustand": "^4.4.0",
    "@tanstack/react-query": "^5.0.0",
    "axios": "^1.6.0",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^2.0.0",
    "lucide-react": "^0.290.0",
    "@radix-ui/react-dialog": "^1.0.5",
    "@radix-ui/react-button": "^1.0.3",
    "decimal.js": "^10.4.3"
  },
  "devDependencies": {
    "typescript": "^5.3.0",
    "@types/node": "^20.0.0",
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "eslint": "^8.57.0",
    "eslint-config-next": "^14.0.0",
    "tailwindcss": "^3.3.0",
    "autoprefixer": "^10.4.0",
    "postcss": "^8.4.0"
  }
}
EOF

# Install dependencies
pnpm install

# Initialize Next.js project
npx create-next-app@latest . --typescript --tailwind --eslint --app --no-src-dir

# Start development server
pnpm dev
```

### 2. Backend Setup (Express.js)

```bash
cd apps/api

# Create package.json for backend
cat > package.json << 'EOF'
{
  "name": "@solana-trading/api",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "dev": "nodemon --exec tsx src/index.ts",
    "build": "tsc",
    "start": "node dist/index.js",
    "test": "vitest",
    "lint": "eslint src --ext .ts",
    "type-check": "tsc --noEmit",
    "db:migrate": "prisma migrate dev",
    "db:studio": "prisma studio",
    "db:seed": "tsx prisma/seed.ts"
  },
  "dependencies": {
    "express": "^4.18.0",
    "cors": "^2.8.5",
    "helmet": "^7.1.0",
    "compression": "^1.7.4",
    "cookie-parser": "^1.4.6",
    "@prisma/client": "^5.7.0",
    "bullmq": "^4.15.0",
    "ioredis": "^5.3.0",
    "pino": "^8.16.0",
    "pino-http": "^8.5.0",
    "decimal.js": "^10.4.3",
    "zod": "^3.22.0",
    "@solana/web3.js": "^1.87.0",
    "@solana-trading/shared": "workspace:*",
    "axios": "^1.6.0",
    "rate-limiter-flexible": "^5.0.0"
  },
  "devDependencies": {
    "typescript": "^5.3.0",
    "@types/node": "^20.0.0",
    "@types/express": "^4.17.0",
    "@types/cors": "^2.8.0",
    "@types/compression": "^1.7.0",
    "@types/cookie-parser": "^1.4.0",
    "tsx": "^4.6.0",
    "nodemon": "^3.0.0",
    "vitest": "^1.0.0",
    "prisma": "^5.7.0",
    "eslint": "^8.57.0",
    "@typescript-eslint/parser": "^6.0.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0"
  }
}
EOF

# Install dependencies
pnpm install

# Create basic Express server
mkdir -p src
cat > src/index.ts << 'EOF'
import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import cookieParser from 'cookie-parser'
import { PrismaClient } from '../prisma/generated/client'
import { createBullBoard } from '@bull-board/api'
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter'
import { ExpressAdapter } from '@bull-board/express'

const app = express()
const port = process.env.PORT || 3001
const prisma = new PrismaClient()

// Middleware
app.use(helmet())
app.use(compression())
app.use(cors({
  origin: process.env.NODE_ENV === 'development' 
    ? ['http://localhost:3000']
    : [],
  credentials: true
}))
app.use(express.json())
app.use(express.urlencoded({ extended: true }))
app.use(cookieParser())

// Health check endpoint
app.get('/api/health', async (req, res) => {
  try {
    // Test database connection
    await prisma.$queryRaw`SELECT 1`
    
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'connected',
        redis: 'connected' // TODO: Add Redis check
      }
    })
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Database connection failed'
    })
  }
})

// Basic API routes
app.get('/api/positions', async (req, res) => {
  try {
    const positions = await prisma.position.findMany({
      include: {
        exitStrategy: true,
        transactions: {
          orderBy: { createdAt: 'desc' }
        }
      }
    })
    res.json({ data: positions })
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch positions' })
  }
})

app.get('/api/watchlist', async (req, res) => {
  try {
    const items = await prisma.watchlistItem.findMany({
      orderBy: { createdAt: 'desc' }
    })
    res.json({ data: items })
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch watchlist' })
  }
})

// Error handler
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack)
  res.status(500).json({ error: 'Something went wrong!' })
})

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Endpoint not found' })
})

// Start server
app.listen(port, () => {
  console.log(`🚀 API server running at http://localhost:${port}`)
  console.log(`📊 Health check: http://localhost:${port}/api/health`)
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔍 Bull Board: http://localhost:${port}/admin/queues`)
  }
})

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('🛑 Shutting down gracefully...')
  await prisma.$disconnect()
  process.exit(0)
})
EOF

# Start development server
pnpm dev
```

### 3. Concurrent Development Setup

Create a root-level script for running all services:

```bash
# In project root, add to package.json scripts
cat >> package.json << 'EOF'
  "scripts": {
    "dev": "pnpm --parallel --stream dev",
    "dev:web": "pnpm --filter @solana-trading/web dev",
    "dev:api": "pnpm --filter @solana-trading/api dev",
    "dev:services": "docker compose up -d",
    "dev:full": "pnpm dev:services && pnpm dev",
    "build": "pnpm --recursive build",
    "clean": "pnpm --recursive clean && docker compose down -v",
    "setup": "pnpm install && pnpm dev:services && sleep 10 && pnpm --filter @solana-trading/api db:migrate"
  }
EOF

# Create development startup script
cat > dev-setup.sh << 'EOF'
#!/bin/bash
set -e

echo "🚀 Starting Solana Trading App Development Environment..."

# Start Docker services
echo "📦 Starting Docker services..."
docker compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to initialize..."
sleep 15

# Run database migrations
echo "🗄️  Running database migrations..."
cd apps/api && pnpm db:migrate && cd ../..

# Start development servers
echo "🖥️  Starting development servers..."
pnpm dev

echo "✅ Development environment ready!"
echo "📱 Frontend: http://localhost:3000"
echo "🔧 API: http://localhost:3001"
echo "🗄️  Database UI: http://localhost:8080"
echo "📊 Redis UI: http://localhost:8081"
EOF

chmod +x dev-setup.sh
```

## Verification and Testing Steps

### 1. Service Health Checks

```bash
# Check Docker services are running
docker compose ps

# Test database connection
docker exec solana-trading-postgres pg_isready -U postgres

# Test Redis connection  
docker exec solana-trading-redis redis-cli ping

# Test API health endpoint
curl http://localhost:3001/api/health

# Test frontend is accessible
curl -I http://localhost:3000
```

### 2. API Endpoint Testing

```bash
# Test positions endpoint
curl -X GET http://localhost:3001/api/positions \
  -H "Content-Type: application/json"

# Test watchlist endpoint
curl -X GET http://localhost:3001/api/watchlist \
  -H "Content-Type: application/json"

# Test adding to watchlist
curl -X POST http://localhost:3001/api/watchlist \
  -H "Content-Type: application/json" \
  -d '{"tokenAddress": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "notes": "USDC test token"}'
```

### 3. Database Connectivity Tests

```bash
# Connect to database directly
docker exec -it solana-trading-postgres psql -U postgres -d solana_trading_db

# In psql, run test queries:
# \dt  -- List all tables
# SELECT * FROM positions LIMIT 5;
# SELECT * FROM polling_config;
# \q  -- Exit psql
```

### 4. Frontend Integration Testing

Visit these URLs in your browser:
- **Frontend**: http://localhost:3000
- **API Health**: http://localhost:3001/api/health
- **Database Admin**: http://localhost:8080 (<EMAIL> / admin)
- **Redis Admin**: http://localhost:8081 (admin / admin)

### 5. Job Queue Testing

```bash
# Test Redis connection for BullMQ
cd apps/api
cat > test-job.ts << 'EOF'
import { Queue } from 'bullmq'
import IORedis from 'ioredis'

const connection = new IORedis({
  host: 'localhost',
  port: 6379,
  maxRetriesPerRequest: 3,
})

const testQueue = new Queue('test-queue', { connection })

async function testJobQueue() {
  console.log('Adding test job...')
  
  const job = await testQueue.add('test-job', {
    message: 'Hello from BullMQ!',
    timestamp: new Date().toISOString()
  })
  
  console.log('Job added with ID:', job.id)
  
  // Clean up
  await testQueue.close()
  connection.disconnect()
}

testJobQueue().catch(console.error)
EOF

npx tsx test-job.ts
```

## Development Workflow Commands

### 1. Daily Development Commands

```bash
# Start everything (recommended for daily development)
./dev-setup.sh

# Start only specific services
pnpm dev:services    # Just Docker services
pnpm dev:web        # Just frontend
pnpm dev:api        # Just backend
pnpm dev            # Frontend + Backend (requires services running)

# Stop everything
docker compose down
```

### 2. Database Operations

```bash
cd apps/api

# Database migrations
pnpm db:migrate          # Create and apply migration
pnpm db:studio          # Open Prisma Studio GUI

# Reset database (WARNING: Destroys all data)
npx prisma migrate reset

# Seed database with test data
pnpm db:seed
```

### 3. Code Quality and Testing

```bash
# Run linting across all packages
pnpm lint

# Run type checking
pnpm type-check

# Run tests
pnpm test

# Format code
pnpm format   # (if configured)

# Build everything
pnpm build
```

### 4. Debugging and Monitoring

```bash
# View logs
docker compose logs -f postgres
docker compose logs -f redis
pnpm --filter @solana-trading/api dev  # API logs
pnpm --filter @solana-trading/web dev  # Frontend logs

# Monitor resource usage
docker stats

# Check Redis data
docker exec -it solana-trading-redis redis-cli
# In redis-cli: KEYS *, GET key_name, etc.
```

### 5. Environment Management

```bash
# Clean everything (nuclear option)
pnpm clean                    # Clean build artifacts
docker compose down -v        # Stop services and delete volumes
docker system prune -a        # Clean Docker system (optional)

# Backup database
docker exec solana-trading-postgres pg_dump -U postgres solana_trading_db > backup.sql

# Restore database
cat backup.sql | docker exec -i solana-trading-postgres psql -U postgres -d solana_trading_db
```

### 6. Troubleshooting Commands

```bash
# Check ports in use
lsof -i :3000  # Frontend
lsof -i :3001  # API
lsof -i :5432  # PostgreSQL
lsof -i :6379  # Redis

# Check Docker service health
docker compose ps
docker compose logs [service-name]

# Restart specific service
docker compose restart postgres
docker compose restart redis

# Check Node.js and pnpm versions
node --version
pnpm --version
docker --version
```

## Next Steps

Once your local development environment is running:

1. **Verify All Services**: Ensure all endpoints return healthy status
2. **Test External APIs**: Add your Helius and CoinMarketCap API keys
3. **Implement Core Features**: Start with the trading engine and position management
4. **Add Authentication**: Implement session-based authentication
5. **Configure Job Queues**: Set up BullMQ workers for trading automation
6. **Test Trading Flow**: Create a test position and verify the full workflow

## Monitoring and Observability

### Monitoring Stack

- **Frontend Monitoring:** Vercel Analytics for Core Web Vitals, Sentry for JavaScript error tracking, custom metrics for trading actions
- **Backend Monitoring:** Railway built-in metrics for infrastructure, Pino structured logging, Bull Board for job queue monitoring
- **Error Tracking:** Sentry for both frontend and backend error aggregation with trading context
- **Performance Monitoring:** Custom metrics collection for trading execution times, API response times, and database query performance

### Key Metrics

**Frontend Metrics:**
- Core Web Vitals (LCP < 2.5s, FID < 100ms, CLS < 0.1)
- JavaScript errors with trading context (position ID, token address)
- API response times for critical paths (quote fetch < 1s, buy execution < 3s)
- User interactions (clicks, form submissions, successful trades)

**Backend Metrics:**
- Request rate (target: < 1000 req/min steady state)
- Error rate (target: < 1% for trading operations)
- Response time (95th percentile < 500ms for API calls)
- Database query performance (slow query threshold: > 100ms)
- Job queue metrics (processing time, failure rate, backlog size)
- External API health (Jupiter, Helius, CMC response times and error rates)
- Trading-specific metrics (execution time < 5s, success rate > 95%, slippage tracking)

<function_calls>
<invoke name="TodoWrite">
<parameter name="todos">[{"content":"Execute create-doc task with fullstack-architecture template","status":"completed","id":"1"},{"content":"Process all architecture sections with user interaction","status":"completed","id":"2"},{"content":"Apply user tech stack refinements (Vitest, session auth, TimescaleDB)","status":"completed","id":"3"},{"content":"Update polling intervals (Armed mode: 30s)","status":"completed","id":"4"},{"content":"Update PRD with watchlist requirements","status":"completed","id":"5"},{"content":"Complete remaining architecture sections","status":"completed","id":"6"},{"content":"Finalize with monitoring section","status":"completed","id":"7"},{"content":"Generate final architecture document","status":"completed","id":"8"}]