# Story 3.1: Exit Strategy Configuration Interface

## Status
Draft

## Story
**As a** trader,
**I want** to configure multi-tier exit strategies with take profits and stop losses,
**so that** I can define how my positions should be automatically managed.

## Acceptance Criteria
1. Exit strategy form with configurable take profit tiers (up to 5 levels)
2. Each tier allows percentage of position and target price/percentage configuration
3. Stop loss configuration with percentage or fixed price options
4. Trailing stop implementation with configurable trail distance
5. Moon bag option to retain small percentage (5-10%) indefinitely
6. Strategy preset saving and loading for reuse across positions
7. Strategy validation ensuring percentages sum correctly and targets are logical
8. Default strategy configuration for quick position setup

[Source: architecture/../prd/epic-3-automated-exit-strategy-engine.md#story-31-exit-strategy-configuration-interface]

## Tasks / Subtasks
- [ ] Implement Exit Strategy form UI in frontend (AC: 1, 2, 3)
  - [ ] Create `ExitStrategyForm.tsx` in `apps/web/src/components/trading/` using shadcn/ui components (Card, Form, Input, Button, Switch) [Source: architecture/frontend-architecture.md#component-organization; architecture/tech-stack.md#technology-stack-table]
  - [ ] Use React Hook Form + Zod for form state and validation [Source: architecture/coding-standards.md#critical-fullstack-rules]
  - [ ] Support up to 5 dynamic take-profit tiers with add/remove, each with target (price or %), and percentage-to-sell inputs [Source: prd/epic-3-automated-exit-strategy-engine.md#story-31-exit-strategy-configuration-interface]
  - [ ] Add stop loss configuration with toggle for % or fixed price [Source: prd/epic-3-automated-exit-strategy-engine.md#story-31-exit-strategy-configuration-interface]
- [ ] Trailing stop configuration (AC: 4)
  - [ ] Add enable/disable and trail distance input (amount or %) with help text [Source: prd/epic-3-automated-exit-strategy-engine.md#story-31-exit-strategy-configuration-interface]
  - [ ] Show preview of current stop derived from entry price when enabled [Source: architecture/frontend-architecture.md#component-architecture]
- [ ] Moon bag option (AC: 5)
  - [ ] Add toggle and percentage input (validate 5–10%) [Source: prd/epic-3-automated-exit-strategy-engine.md#story-31-exit-strategy-configuration-interface]
- [ ] Preset management (AC: 6, 8)
  - [ ] Add save, load, delete presets UI (Dialog + Table) under Settings → Strategies and inline quick-select [Source: architecture/frontend-architecture.md#routing-architecture; architecture/frontend-architecture.md#frontend-services-layer]
  - [ ] Persist presets to localStorage initially; prepare service calls for backend sync [Source: architecture/frontend-architecture.md#state-management-architecture; architecture/api-specification.md#rest-api-specification]
  - [ ] Provide default preset(s) and “Set as default” action [Source: prd/epic-3-automated-exit-strategy-engine.md#story-31-exit-strategy-configuration-interface]
- [ ] Validation rules (AC: 7)
  - [ ] Enforce that tier percentages + moon bag ≤ 100%; tiers are logically ordered; stop loss side is sensible relative to entry [Source: prd/epic-3-automated-exit-strategy-engine.md#story-31-exit-strategy-configuration-interface]
  - [ ] Implement Zod schema(s) and inline error display patterns [Source: architecture/testing-strategy.md#frontend-tests; architecture/coding-standards.md#critical-fullstack-rules]
- [ ] Integrate with Trading interface (AC: 8)
  - [ ] Embed ExitStrategyForm in `apps/web/src/app/trading/page.tsx` as a collapsible section [Source: architecture/frontend-architecture.md#routing-architecture; architecture/frontend-architecture.md#component-architecture]
  - [ ] Wire to store: add `exitStrategy` slice to `apps/web/src/stores/tradingStore.ts` with actions for set/validate/save/load [Source: architecture/frontend-architecture.md#state-management-architecture]
  - [ ] Extend service call for buy to include `exitStrategy` payload as per API spec [Source: architecture/frontend-architecture.md#frontend-services-layer; architecture/api-specification.md#rest-api-specification]
- [ ] Unit/integration tests
  - [ ] Component tests for ExitStrategyForm (render, add/remove tiers, validation states, preset ops) at `apps/web/tests/components/trading/ExitStrategyForm.test.tsx` [Source: architecture/testing-strategy.md#frontend-tests]
  - [ ] Store tests for exitStrategy slice behavior at `apps/web/tests/stores/tradingStore.test.ts` [Source: architecture/testing-strategy.md#frontend-tests]
  - [ ] Service tests verifying payload shape with API client [Source: architecture/testing-strategy.md#frontend-tests]

## Dev Notes

The following technical guidance is extracted from architecture/PRD documents; where no explicit guidance exists, it is noted accordingly.

### Previous Story Insights
- From Watchlist and Trading groundwork (Epic 2): Trading page exists at `apps/web/src/app/trading/page.tsx` with component composition and store patterns established for buy flow and form handling. [Source: architecture/frontend-architecture.md#routing-architecture]

### Data Models
- ExitStrategy and related structures (TakeProfitTier, StopLoss, TrailingStop) are defined conceptually with attributes used to configure tiers, stop loss, and trailing stops. Use shared types from `packages/shared/src/types` where applicable for consistency. [Source: architecture/data-models.md#exitstrategy]
- Positions reference an optional ExitStrategy for automation; ensure the UI produces a configuration compatible with backend expectations. [Source: architecture/data-models.md#position]

### API Specifications
- Client should submit exit strategy with buy via POST `/api/trades/buy` including `exitStrategy` in body matching `ExitStrategyInput`. [Source: architecture/api-specification.md#rest-api-specification]
- Position modification for exit strategy later is via PATCH `/api/positions/{id}` with `exitStrategy` payload. [Source: architecture/api-specification.md#rest-api-specification]

### Component Specifications
- Location for trading components including ExitStrategyForm: `apps/web/src/components/trading/`. [Source: architecture/frontend-architecture.md#component-organization]
- Use shadcn/ui primitives (Card, Form, Input, Switch, Dialog, Table) and Tailwind CSS utilities. [Source: architecture/tech-stack.md#technology-stack-table; architecture/frontend-architecture.md#component-organization]
- Integrate into Trading page: `apps/web/src/app/trading/page.tsx` as collapsible panel that persists state during flow. [Source: architecture/frontend-architecture.md#routing-architecture]

### File Locations
- UI: `apps/web/src/components/trading/ExitStrategyForm.tsx` [Source: architecture/frontend-architecture.md#component-organization]
- Store: `apps/web/src/stores/tradingStore.ts` exitStrategy slice and actions [Source: architecture/frontend-architecture.md#state-management-architecture]
- Hooks (optional): `apps/web/src/hooks/useExitStrategy.ts` if extracting complex logic [Source: architecture/frontend-architecture.md#component-architecture]
- Validators: `apps/web/src/lib/validators.ts` for Zod schemas if present; else colocate with component [Source: architecture/unified-project-structure.md]
- Shared Types: `packages/shared/src/types/` (extend if needed, but prefer existing) [Source: architecture/unified-project-structure.md]

### Testing Requirements
- Frontend tests structure and file locations are defined under `apps/web/tests/*` with components, stores, services, hooks. Create new tests for ExitStrategyForm and store slice, and update integration tests for trading flow. [Source: architecture/testing-strategy.md#frontend-tests]
- Optional E2E: add coverage for configuring an exit strategy in buy flow later in Epic 3 integration. [Source: architecture/testing-strategy.md#e2e-tests]

### Technical Constraints
- Use TypeScript across stack; share types via packages/shared. [Source: architecture/coding-standards.md#critical-fullstack-rules]
- Use Decimal.js for financial calculations/formatting to avoid float errors. [Source: architecture/coding-standards.md#critical-fullstack-rules]
- Use service layer (`tradingService`) for API interactions; do not fetch directly. [Source: architecture/frontend-architecture.md#frontend-services-layer; architecture/coding-standards.md#critical-fullstack-rules]
- Persist critical exit strategy state to localStorage as backup; hydrate into store on load. [Source: architecture/frontend-architecture.md#state-management-architecture]
- Validation specifics such as “minimum trail distance percentage” are not explicitly defined in architecture docs; implement only PRD-mandated validations and present warnings rather than hard rules for any additional heuristics. [No specific guidance found in architecture docs]

### Project Structure Notes
- Found `docs/stories/2.7.exit-strategy-configuration-interface.md` whose content is titled “Story 3.1” with Status: Draft. This appears misfiled/misnamed. Consider either renaming to `3.1.*` or treating the new `3.1.*` as authoritative and deprecating the 2.7 file to avoid duplication.

## Testing
- Unit tests: ExitStrategyForm component (rendering, dynamic tiers, validation, preset save/load), store slice actions/selectors, API payload construction. [Source: architecture/testing-strategy.md#frontend-tests]
- Integration tests: Trading page with embedded ExitStrategyForm and buy submission including strategy payload. [Source: architecture/testing-strategy.md#frontend-tests]
- Optional E2E: user configures strategy and executes buy, verified later in Epic 3 flow. [Source: architecture/testing-strategy.md#e2e-tests]

## Change Log
| Date       | Version | Description                                             | Author |
|------------|---------|---------------------------------------------------------|--------|
| 2025-08-18 | 1.0     | Initial draft of Exit Strategy Configuration Interface | Scrum Master |

## Dev Agent Record
- Agent Model Used:
- Debug Log References:
- Completion Notes List:
- File List:

## QA Results
- TBD
