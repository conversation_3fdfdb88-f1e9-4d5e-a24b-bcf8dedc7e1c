# Story 3.1: Exit Strategy Configuration Interface

## Status
**Status:** Draft
**Agent Model Used:** N/A
**Implementation Priority:** High
**Estimated Complexity:** High
**Dependencies:** Trading Integration and Workflow Optimization (2.6)
**Validation Required:** Exit strategy form validation, preset save/load functionality, strategy calculation accuracy

## Story
**As a** trader,
**I want** to configure multi-tier exit strategies with take profits and stop losses,
**so that** I can define how my positions should be automatically managed.

## Acceptance Criteria

1. Exit strategy form with configurable take profit tiers (up to 5 levels)
2. Each tier allows percentage of position and target price/percentage configuration
3. Stop loss configuration with percentage or fixed price options
4. Trailing stop implementation with configurable trail distance
5. Moon bag option to retain small percentage (5-10%) indefinitely
6. Strategy preset saving and loading for reuse across positions
7. Strategy validation ensuring percentages sum correctly and targets are logical
8. Default strategy configuration for quick position setup

## Tasks / Subtasks

### Task 1: Exit Strategy Form Component Development (AC: 1, 2, 3)
- [ ] Create `ExitStrategyForm.tsx` component in `apps/web/src/components/trading/`
  - [ ] Implement form structure using shadcn/ui components (Card, Form, Input, Button)
  - [ ] Add form state management with React Hook Form and Zod validation
  - [ ] Include TypeScript interfaces from shared package for type safety
  - [ ] Add responsive design for both desktop and mobile views
- [ ] Implement take profit tier configuration section
  - [ ] Create dynamic tier addition/removal functionality (up to 5 tiers)
  - [ ] Add price input fields with USD and percentage toggle options
  - [ ] Implement percentage of position input with 1-100% validation
  - [ ] Include tier ordering validation ensuring logical price progression
  - [ ] Add visual indicators for tier priority and execution order
- [ ] Implement stop loss configuration section
  - [ ] Add toggle between percentage-based and fixed price stop loss
  - [ ] Create price input with validation against current market price
  - [ ] Include percentage input with reasonable defaults (10-20% range)
  - [ ] Add percentage of position to sell (typically 100% for stop loss)
  - [ ] Include emergency stop option for immediate market sell

### Task 2: Trailing Stop Configuration Interface (AC: 4)
- [ ] Create trailing stop configuration section within ExitStrategyForm
  - [ ] Add trailing stop enable/disable toggle
  - [ ] Implement trail distance configuration (USD amount or percentage)
  - [ ] Add trail distance validation with minimum viable distances
  - [ ] Include trail only upward toggle (prevents lowering stop loss)
  - [ ] Add visual explanation of how trailing stops work
- [ ] Implement trailing stop preview and calculation display
  - [ ] Show current stop price calculation based on entry price
  - [ ] Display how stop price would adjust with price movements
  - [ ] Add trail distance recommendations based on token volatility
  - [ ] Include graphical representation of trailing stop behavior
  - [ ] Add reset trail option for manual repositioning

### Task 3: Moon Bag Configuration and Advanced Options (AC: 5)
- [ ] Create moon bag configuration section
  - [ ] Add moon bag enable/disable toggle with default disabled
  - [ ] Implement percentage retention input (5-10% typical range)
  - [ ] Add moon bag validation ensuring it doesn't conflict with other tiers
  - [ ] Include explanation of moon bag strategy and implications
  - [ ] Add option to exclude moon bag from stop loss triggers
- [ ] Implement advanced strategy options
  - [ ] Add priority-based execution toggle (stop loss vs take profit in volatile conditions)
  - [ ] Include slippage tolerance configuration for exit executions
  - [ ] Add minimum execution size to prevent dust trades
  - [ ] Implement execution delay options for market condition optimization
  - [ ] Add strategy activation delay for immediate post-buy protection

### Task 4: Strategy Preset Management System (AC: 6, 8)
- [ ] Create strategy preset management interface
  - [ ] Add preset save functionality with custom naming
  - [ ] Implement preset loading with form population
  - [ ] Create preset deletion with confirmation dialog
  - [ ] Add preset export/import functionality for backup
  - [ ] Include preset sharing capability for future social features
- [ ] Implement default strategy configuration
  - [ ] Create default strategy form pre-population
  - [ ] Add ability to set user's personal default strategy
  - [ ] Implement quick setup presets (Conservative, Moderate, Aggressive)
  - [ ] Add preset templates for common trading scenarios
  - [ ] Include new user onboarding with recommended default strategies
- [ ] Create preset management storage
  - [ ] Implement localStorage persistence for preset data
  - [ ] Add preset synchronization with backend API for cross-device access
  - [ ] Create preset validation ensuring compatibility with current system
  - [ ] Add preset version management for future strategy format updates
  - [ ] Include preset backup and restore functionality

### Task 5: Strategy Validation and Error Handling (AC: 7)
- [ ] Implement comprehensive strategy validation
  - [ ] Add percentage sum validation ensuring total doesn't exceed 100%
  - [ ] Create logical price progression validation for take profit tiers
  - [ ] Implement stop loss price validation against entry price
  - [ ] Add trailing stop distance validation for feasibility
  - [ ] Include moon bag percentage validation against total strategy
- [ ] Create validation error handling and user feedback
  - [ ] Add real-time validation with inline error messages
  - [ ] Implement form submission validation with detailed error descriptions
  - [ ] Create validation warning system for potentially risky configurations
  - [ ] Add strategy optimization suggestions based on validation results
  - [ ] Include validation tooltip explanations for complex rules
- [ ] Add strategy calculation and preview functionality
  - [ ] Implement real-time calculation of potential outcomes
  - [ ] Add profit/loss projections based on strategy configuration
  - [ ] Create execution order preview showing tier progression
  - [ ] Include fee estimation for strategy execution
  - [ ] Add risk assessment display based on strategy parameters

### Task 6: Integration with Trading Interface (AC: 8)
- [ ] Integrate ExitStrategyForm with existing trading interface
  - [ ] Update `apps/web/src/app/trading/page.tsx` to include exit strategy section
  - [ ] Add exit strategy form to trading panel with collapsible design
  - [ ] Implement form state sharing between quote display and strategy configuration
  - [ ] Add exit strategy preview in quote confirmation dialog
  - [ ] Include strategy modification option during buy flow
- [ ] Create exit strategy store integration
  - [ ] Update `apps/web/src/stores/tradingStore.ts` with exit strategy state
  - [ ] Add exit strategy actions for save, load, and validation
  - [ ] Implement exit strategy persistence in store state
  - [ ] Create exit strategy subscription patterns for real-time updates
  - [ ] Add exit strategy sharing between trading and position management
- [ ] Implement exit strategy data flow to backend
  - [ ] Update trading service calls to include exit strategy payload
  - [ ] Add exit strategy validation on buy order submission
  - [ ] Create exit strategy persistence in position creation API calls
  - [ ] Implement exit strategy modification API integration
  - [ ] Add exit strategy template sharing between frontend and backend

### Task 7: Testing and Quality Assurance (AC: All)
- [ ] Create unit tests for ExitStrategyForm component
  - [ ] Test form validation with various input combinations
  - [ ] Test tier addition/removal functionality
  - [ ] Test preset save/load operations with edge cases
  - [ ] Test strategy calculation accuracy and edge cases
  - [ ] Test error handling and user feedback display
- [ ] Implement integration tests for exit strategy workflow
  - [ ] Test complete exit strategy creation and submission
  - [ ] Test exit strategy preset management operations
  - [ ] Test exit strategy integration with trading interface
  - [ ] Test exit strategy data persistence and retrieval
  - [ ] Test exit strategy validation across different scenarios
- [ ] Create E2E tests for exit strategy user experience
  - [ ] Test complete buy flow with exit strategy configuration
  - [ ] Test preset creation and reuse across multiple trades
  - [ ] Test exit strategy modification for existing positions
  - [ ] Test error scenarios and recovery workflows
  - [ ] Test mobile responsiveness and touch interactions

## Dev Notes

### Previous Story Insights
From Story 2.6 (Trading Integration and Workflow Optimization):
- Trading interface established in `apps/web/src/app/trading/page.tsx` with component integration patterns
- TradingStore pattern established with Zustand for state management consistency
- Form validation patterns using React Hook Form and Zod schemas
- Trading service layer established for API communication patterns
- URL parameter support and navigation patterns already implemented

### Frontend Architecture Patterns
**Component Organization** [Source: architecture/frontend-architecture.md#component-organization]:
- Trading components location: `apps/web/src/components/trading/`
- Form components follow shadcn/ui patterns with Card, Form, Input, Button components
- Component props interface pattern using TypeScript with proper typing
- State management using Zustand stores in `apps/web/src/stores/` directory
- Custom hooks pattern for complex logic in `apps/web/src/hooks/` directory

**State Management Architecture** [Source: architecture/frontend-architecture.md#state-management-architecture]:
- Zustand store pattern for exit strategy state management
- Exit strategy configuration state in tradingStore.ts
- Strategy presets stored in localStorage with optional API sync
- Real-time validation state management with error boundaries
- Optimistic updates with rollback patterns for form operations

**Form Handling Patterns** [Source: architecture/coding-standards.md]:
- React Hook Form integration with Zod schema validation
- TypeScript interfaces from packages/shared for type consistency
- Error handling with inline validation messages
- Form state persistence during navigation transitions
- Responsive form design using Tailwind CSS utility classes

### Exit Strategy Data Model Architecture
**ExitStrategy Interface** [Source: architecture/data-models.md#exitstrategy]:
- Core interface: `ExitStrategy` with takeProfitTiers, stopLoss, trailingStop, moonBag
- TakeProfitTier interface: tierNumber, targetPrice, percentageToSell, status
- StopLoss interface: triggerPrice, percentageToSell, status
- TrailingStop interface: trailDistance, highestPrice, currentStopPrice, isActive
- All financial calculations use Decimal.js for precision [Source: architecture/coding-standards.md#critical-fullstack-rules]

**Type Safety Requirements** [Source: architecture/coding-standards.md#critical-fullstack-rules]:
- All types defined in `packages/shared/src/types/` and imported consistently
- Exit strategy types shared between frontend and backend
- Validation schemas using Zod for runtime type safety
- Decimal.js usage for all financial calculations and price handling
- Consistent error handling patterns using standard error types

### Component Implementation Specifications
**ExitStrategyForm Component Location** [Source: architecture/unified-project-structure.md]:
- Primary component: `apps/web/src/components/trading/ExitStrategyForm.tsx`
- Supporting components: `apps/web/src/components/trading/` subdirectory
- Form validation utilities: `apps/web/src/lib/validators.ts`
- Exit strategy hooks: `apps/web/src/hooks/useExitStrategy.ts`
- Type definitions: `packages/shared/src/types/exitStrategy.ts`

**Trading Interface Integration** [Source: architecture/frontend-architecture.md#component-architecture]:
- Integration point: `apps/web/src/app/trading/page.tsx`
- Exit strategy section as collapsible panel in trading interface
- State sharing through tradingStore Zustand integration
- Form submission integration with existing buy order flow
- Navigation preservation during form completion

**shadcn/ui Component Usage** [Source: architecture/tech-stack.md]:
- Form components: Card, Form, Input, Button, Switch, Slider
- Layout components: Tabs, Accordion, Dialog for preset management
- Validation components: Alert, Badge for error display
- Data display: Table for preset management, Progress for validation feedback
- Navigation: Breadcrumb for multi-step form processes

### Validation and Business Logic Requirements
**Strategy Validation Rules**:
- Take profit tier percentages must sum to ≤ 100% minus moon bag percentage
- Take profit target prices must be logically ordered (ascending for profitable positions)
- Stop loss price must be below entry price for long positions
- Trailing stop distance must be ≥ 1% to prevent over-execution
- Moon bag percentage must be 5-10% when enabled
- Minimum execution size validation to prevent dust trades

**Preset Management Requirements**:
- Local storage key: `exitStrategyPresets` with JSON serialization
- Preset validation on load to ensure current format compatibility
- Maximum 20 saved presets per user with oldest deletion on overflow
- Preset export/import using JSON format for portability
- Default preset indicators and quick selection interface

**Integration with Trading Flow**:
- Exit strategy form appears after quote confirmation, before final buy execution
- Strategy preview display in buy confirmation dialog
- Strategy persistence in trading store during buy flow navigation
- Post-buy strategy attachment to newly created position
- Strategy modification capability for existing positions

### Testing Requirements
**Unit Testing Requirements** [Source: architecture/testing-strategy.md#frontend-tests]:
- Component tests: `apps/web/tests/components/trading/ExitStrategyForm.test.tsx`
- Hook tests: `apps/web/tests/hooks/useExitStrategy.test.ts`
- Validation tests: `apps/web/tests/utils/exitStrategyValidation.test.ts`
- Store tests: `apps/web/tests/stores/tradingStore.exitStrategy.test.ts`
- Utility tests: `packages/shared/tests/utils/exitStrategyCalculations.test.ts`

**Integration Testing Focus**:
- Form submission with complete exit strategy configuration
- Preset save/load operations with persistence validation
- Exit strategy integration with trading interface flow
- Strategy validation across various edge cases and combinations
- Error handling and recovery from invalid configurations

**E2E Testing Scenarios**:
- Complete trading flow: token selection → exit strategy configuration → buy execution
- Preset management: create → save → load → modify → delete preset cycle
- Exit strategy modification for existing positions
- Mobile responsive testing for form interactions and validation feedback
- Error scenario testing with network failures and validation errors

### Technical Implementation Notes
**Form State Management**:
- React Hook Form for form state with Zod schema validation
- Controlled components for all form inputs with proper TypeScript typing
- Dynamic form sections for tier addition/removal with array field management
- Form persistence during navigation using session storage backup
- Optimistic updates for preset operations with rollback on failure

**Calculation Engine Requirements**:
- Real-time calculation of strategy outcomes using Decimal.js
- Profit/loss projection based on strategy configuration and current price
- Fee estimation for strategy execution including network and platform fees
- Risk assessment calculation based on stop loss distance and position size
- Strategy optimization suggestions based on token volatility and market conditions

**Performance Considerations**:
- Form rendering optimization for dynamic tier management
- Calculation debouncing for real-time strategy preview updates
- Preset loading optimization with lazy loading for large preset collections
- Validation optimization with memoized validation functions
- Memory management for complex form state with proper cleanup

## Testing

### Unit Testing Requirements
- **Form Component Tests**: Validation of form rendering, input handling, and state management
- **Strategy Calculation Tests**: Accuracy of profit/loss calculations and risk assessments
- **Preset Management Tests**: Save, load, delete, and validation of strategy presets
- **Validation Logic Tests**: Comprehensive testing of all validation rules and edge cases

### Integration Testing Requirements
- **Trading Flow Integration**: Complete exit strategy configuration within buy workflow
- **API Integration**: Strategy submission and persistence with backend services
- **Store Integration**: Exit strategy state management with Zustand patterns
- **Navigation Integration**: Form state preservation during trading interface navigation

### E2E Testing Requirements
- **Complete Trading Workflow**: End-to-end testing from strategy configuration to position creation
- **Preset Management Workflow**: Full preset lifecycle testing with persistence validation
- **Error Handling Scenarios**: Testing of error states, recovery, and user feedback systems
- **Mobile Responsiveness**: Touch interaction testing and responsive design validation

### Performance Testing Requirements
- **Form Rendering Performance**: Testing with maximum tier configurations and complex strategies
- **Calculation Performance**: Real-time calculation speed with various input combinations
- **Preset Loading Performance**: Testing with large preset collections and concurrent operations
- **Memory Usage Testing**: Form state memory management during extended usage sessions

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-08-14 | 1.0 | Initial story creation for exit strategy configuration interface | Bob (Scrum Master) |

## Dev Agent Record

**Agent Model Used:** N/A

### Debug Log References
[To be populated during implementation]

### Completion Notes List
[To be populated during implementation]

### File List
[To be populated during implementation]

## QA Results
[To be populated after QA review]