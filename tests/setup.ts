import { beforeAll, afterAll } from 'vitest';
import { config } from '../apps/api/src/lib/config';
import { apiLogger } from '../apps/api/src/lib/logger';

// Override config for test environment
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = process.env.DATABASE_URL || 'postgresql://test:test@localhost:5432/bmad_test';
process.env.REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379/1';
process.env.SESSION_SECRET = 'test-session-secret-for-integration-tests-32chars';
process.env.ADMIN_PASSWORD = 'test-admin-password';
process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests

// Global test setup
beforeAll(async () => {
  apiLogger.info('Starting test environment setup');
});

// Global test cleanup
afterAll(async () => {
  apiLogger.info('Cleaning up test environment');
});

// Suppress console output during tests unless explicitly needed
global.console = {
  ...console,
  log: () => {},
  info: () => {},
  warn: () => {},
  error: (...args) => {
    // Only show actual test failures, not expected error logs
    if (process.env.VITEST_DEBUG) {
      console.error(...args);
    }
  },
};