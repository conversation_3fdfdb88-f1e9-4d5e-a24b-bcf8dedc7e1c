import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import supertest from 'supertest';
import app from '../../../apps/api/src/server';
import { initializeDatabase, disconnectDatabase } from '../../../apps/api/src/lib/database';
import { connectRedis, disconnectRedis } from '../../../apps/api/src/lib/redis';

const request = supertest.agent(app);

describe('Trade Endpoints', () => {
  beforeAll(async () => {
    await initializeDatabase();
    await connectRedis();
  });

  afterAll(async () => {
    await disconnectDatabase();
    await disconnectRedis();
  });

  beforeEach(async () => {
    // Login before each test
    await request
      .post('/api/auth/login')
      .send({ password: 'admin123' });
  });

  describe('POST /api/trades/quote', () => {
    const validQuoteRequest = {
      inputMint: 'So11111111111111111111111111111111111111112', // SOL
      outputMint: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263', // BONK
      amount: 1.0,
      slippageBps: 100,
    };

    it('should return valid quote for SOL to token swap', async () => {
      const response = await request
        .post('/api/trades/quote')
        .send(validQuoteRequest)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          inputMint: validQuoteRequest.inputMint,
          outputMint: validQuoteRequest.outputMint,
          inAmount: expect.any(String),
          outAmount: expect.any(String),
          otherAmountThreshold: expect.any(String),
          swapMode: 'ExactIn',
          slippageBps: 100,
          priceImpactPct: expect.any(String),
          routePlan: expect.any(Array),
        },
        timestamp: expect.any(String),
      });
    });

    it('should validate required fields', async () => {
      const invalidRequest = {
        inputMint: 'So11111111111111111111111111111111111111112',
        // Missing outputMint and amount
        slippageBps: 100,
      };

      const response = await request
        .post('/api/trades/quote')
        .send(invalidRequest)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid request data',
        timestamp: expect.any(String),
      });
    });

    it('should validate mint address format', async () => {
      const invalidRequest = {
        ...validQuoteRequest,
        inputMint: 'invalid-address', // Invalid format
      };

      const response = await request
        .post('/api/trades/quote')
        .send(invalidRequest)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: 'VALIDATION_ERROR',
        timestamp: expect.any(String),
      });
    });

    it('should validate amount is positive', async () => {
      const invalidRequest = {
        ...validQuoteRequest,
        amount: -1.0, // Negative amount
      };

      const response = await request
        .post('/api/trades/quote')
        .send(invalidRequest)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: 'VALIDATION_ERROR',
        timestamp: expect.any(String),
      });
    });

    it('should validate slippage bounds', async () => {
      const invalidRequest = {
        ...validQuoteRequest,
        slippageBps: 2000, // Too high slippage
      };

      const response = await request
        .post('/api/trades/quote')
        .send(invalidRequest)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: 'VALIDATION_ERROR',
        timestamp: expect.any(String),
      });
    });

    it('should require authentication', async () => {
      // Logout first
      await request.post('/api/auth/logout');

      const response = await request
        .post('/api/trades/quote')
        .send(validQuoteRequest)
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        error: 'UNAUTHORIZED',
        timestamp: expect.any(String),
      });
    });
  });

  describe('POST /api/trades/buy', () => {
    const validBuyRequest = {
      tokenAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
      amountSol: 0.1,
      slippageBps: 100,
    };

    it('should execute buy order successfully', async () => {
      const response = await request
        .post('/api/trades/buy')
        .send(validBuyRequest)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          transactionId: expect.any(String),
          tokenAddress: validBuyRequest.tokenAddress,
          amountSol: validBuyRequest.amountSol,
          tokensReceived: expect.any(String),
          executionPrice: expect.any(String),
          slippageActual: expect.any(String),
          fees: expect.objectContaining({
            jupiterFee: expect.any(String),
            networkFee: expect.any(String),
          }),
          positionId: expect.any(String),
        },
        message: 'Buy order executed successfully',
        timestamp: expect.any(String),
      });
    });

    it('should validate token address format', async () => {
      const invalidRequest = {
        ...validBuyRequest,
        tokenAddress: 'invalid-token-address',
      };

      const response = await request
        .post('/api/trades/buy')
        .send(invalidRequest)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: 'VALIDATION_ERROR',
        timestamp: expect.any(String),
      });
    });

    it('should validate minimum SOL amount', async () => {
      const invalidRequest = {
        ...validBuyRequest,
        amountSol: 0, // Zero amount
      };

      const response = await request
        .post('/api/trades/buy')
        .send(invalidRequest)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: 'VALIDATION_ERROR',
        timestamp: expect.any(String),
      });
    });

    it('should handle large buy orders', async () => {
      const largeOrderRequest = {
        ...validBuyRequest,
        amountSol: 10.0, // Large order
      };

      const response = await request
        .post('/api/trades/buy')
        .send(largeOrderRequest)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(parseFloat(response.body.data.amountSol)).toBe(10.0);
    });

    it('should create position record after successful buy', async () => {
      const response = await request
        .post('/api/trades/buy')
        .send(validBuyRequest)
        .expect(200);

      expect(response.body.data.positionId).toBeDefined();

      // Verify position was created by checking positions endpoint
      const positionsResponse = await request
        .get('/api/positions')
        .expect(200);

      const createdPosition = positionsResponse.body.data.find(
        (position: any) => position.id === response.body.data.positionId
      );

      expect(createdPosition).toBeDefined();
      expect(createdPosition.tokenAddress).toBe(validBuyRequest.tokenAddress);
    });

    it('should require authentication', async () => {
      await request.post('/api/auth/logout');

      const response = await request
        .post('/api/trades/buy')
        .send(validBuyRequest)
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        error: 'UNAUTHORIZED',
        timestamp: expect.any(String),
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle network timeouts gracefully', async () => {
      // This would require mocking external API calls
      // For now, just verify error structure
      const response = await request
        .post('/api/trades/quote')
        .send({
          inputMint: 'invalid'.repeat(10), // This should trigger validation
          outputMint: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
          amount: 1,
        })
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('timestamp');
    });

    it('should handle malformed JSON gracefully', async () => {
      const response = await request
        .post('/api/trades/quote')
        .set('Content-Type', 'application/json')
        .send('{"malformed": json}')
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('timestamp');
    });

    it('should handle missing content-type header', async () => {
      const response = await request
        .post('/api/trades/quote')
        .send('not-json-data')
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('timestamp');
    });
  });

  describe('Rate Limiting', () => {
    it('should respect rate limits on trading endpoints', async () => {
      const quoteRequest = {
        inputMint: 'So11111111111111111111111111111111111111112',
        outputMint: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
        amount: 0.1,
      };

      // Make many requests quickly
      const promises = Array(10).fill(null).map(() =>
        request.post('/api/trades/quote').send(quoteRequest)
      );

      const responses = await Promise.allSettled(promises);
      const successful = responses.filter(r => r.status === 'fulfilled');
      
      // Should have at least some successful requests
      expect(successful.length).toBeGreaterThan(0);
    });
  });
});