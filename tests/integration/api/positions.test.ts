import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import supertest from 'supertest';
import app from '../../../apps/api/src/server';
import { initializeDatabase, disconnectDatabase, prisma } from '../../../apps/api/src/lib/database';
import { connectRedis, disconnectRedis } from '../../../apps/api/src/lib/redis';
import { Decimal } from 'decimal.js';

const request = supertest.agent(app);

describe('Position Management Endpoints', () => {
  let testPositionId: string;

  beforeAll(async () => {
    await initializeDatabase();
    await connectRedis();
  });

  afterAll(async () => {
    await disconnectDatabase();
    await disconnectRedis();
  });

  beforeEach(async () => {
    // Login before each test
    await request
      .post('/api/auth/login')
      .send({ password: 'admin123' });

    // Create a test position
    const position = await prisma.position.create({
      data: {
        tokenAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
        tokenName: 'Bonk',
        tokenSymbol: 'BONK',
        quantity: new Decimal('1000000'),
        entryAmountSol: new Decimal('0.1'),
        entryPrice: new Decimal('0.0000001'),
        currentPrice: new Decimal('0.0000001'),
        currentValueSol: new Decimal('0.1'),
        pnlSol: new Decimal('0'),
        pnlPercentage: new Decimal('0'),
        status: 'ACTIVE',
      },
    });
    testPositionId = position.id;
  });

  afterEach(async () => {
    // Clean up test positions
    await prisma.exitStrategy.deleteMany();
    await prisma.position.deleteMany();
  });

  describe('GET /api/positions', () => {
    it('should return all positions', async () => {
      const response = await request
        .get('/api/positions')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: expect.arrayContaining([
          expect.objectContaining({
            id: testPositionId,
            tokenAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
            tokenName: 'Bonk',
            tokenSymbol: 'BONK',
            status: 'ACTIVE',
          }),
        ]),
        timestamp: expect.any(String),
      });
    });

    it('should filter positions by status', async () => {
      // Create a closed position
      await prisma.position.create({
        data: {
          tokenAddress: 'WENWENvqqNya429ubCdR81ZmD69brwQaaBYY6p3LCpk',
          tokenName: 'Wen',
          tokenSymbol: 'WEN',
          quantity: new Decimal('500000'),
          entryAmountSol: new Decimal('0.05'),
          entryPrice: new Decimal('0.0000001'),
          currentPrice: new Decimal('0.0000001'),
          currentValueSol: new Decimal('0.05'),
          pnlSol: new Decimal('0'),
          pnlPercentage: new Decimal('0'),
          status: 'CLOSED',
        },
      });

      const response = await request
        .get('/api/positions?status=ACTIVE')
        .expect(200);

      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].status).toBe('ACTIVE');
    });

    it('should paginate results', async () => {
      // Create multiple positions
      const positionPromises = Array(15).fill(null).map((_, i) =>
        prisma.position.create({
          data: {
            tokenAddress: `Token${i.toString().padStart(40, '0')}`,
            tokenName: `Token${i}`,
            tokenSymbol: `TK${i}`,
            quantity: new Decimal('1000'),
            entryAmountSol: new Decimal('0.01'),
            entryPrice: new Decimal('0.00001'),
            currentPrice: new Decimal('0.00001'),
            currentValueSol: new Decimal('0.01'),
            pnlSol: new Decimal('0'),
            pnlPercentage: new Decimal('0'),
            status: 'ACTIVE',
          },
        })
      );

      await Promise.all(positionPromises);

      const response = await request
        .get('/api/positions?limit=10&offset=0')
        .expect(200);

      expect(response.body.data.length).toBeLessThanOrEqual(10);
      expect(response.body).toHaveProperty('pagination');
    });

    it('should require authentication', async () => {
      await request.post('/api/auth/logout');

      const response = await request
        .get('/api/positions')
        .expect(401);

      expect(response.body).toMatchObject({
        success: false,
        error: 'UNAUTHORIZED',
        timestamp: expect.any(String),
      });
    });
  });

  describe('GET /api/positions/:id', () => {
    it('should return specific position', async () => {
      const response = await request
        .get(`/api/positions/${testPositionId}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          id: testPositionId,
          tokenAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
          tokenName: 'Bonk',
          tokenSymbol: 'BONK',
          status: 'ACTIVE',
        },
        timestamp: expect.any(String),
      });
    });

    it('should return 404 for non-existent position', async () => {
      const nonExistentId = '550e8400-e29b-41d4-a716-************';

      const response = await request
        .get(`/api/positions/${nonExistentId}`)
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        error: 'NOT_FOUND',
        message: 'Position not found',
        timestamp: expect.any(String),
      });
    });

    it('should validate UUID format', async () => {
      const response = await request
        .get('/api/positions/invalid-uuid')
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: 'VALIDATION_ERROR',
        timestamp: expect.any(String),
      });
    });
  });

  describe('PATCH /api/positions/:id', () => {
    it('should update position exit strategy', async () => {
      const updateData = {
        exitStrategy: {
          takeProfitPercentage: 100,
          stopLossPercentage: 20,
          trailingStopPercentage: 15,
        },
      };

      const response = await request
        .patch(`/api/positions/${testPositionId}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          id: testPositionId,
          exitStrategies: expect.arrayContaining([
            expect.objectContaining({
              takeProfitPercentage: expect.any(String),
              stopLossPercentage: expect.any(String),
            }),
          ]),
        },
        message: 'Position updated successfully',
        timestamp: expect.any(String),
      });
    });

    it('should validate update data', async () => {
      const invalidData = {
        exitStrategy: {
          takeProfitPercentage: -10, // Invalid negative percentage
        },
      };

      const response = await request
        .patch(`/api/positions/${testPositionId}`)
        .send(invalidData)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: 'VALIDATION_ERROR',
        timestamp: expect.any(String),
      });
    });

    it('should return 404 for non-existent position', async () => {
      const nonExistentId = '550e8400-e29b-41d4-a716-************';

      const response = await request
        .patch(`/api/positions/${nonExistentId}`)
        .send({ exitStrategy: { takeProfitPercentage: 50 } })
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        error: 'NOT_FOUND',
        timestamp: expect.any(String),
      });
    });
  });

  describe('DELETE /api/positions/:id', () => {
    it('should close position successfully', async () => {
      const response = await request
        .delete(`/api/positions/${testPositionId}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          transactionId: expect.any(String),
          positionId: testPositionId,
          exitPrice: expect.any(String),
          pnlSol: expect.any(String),
          pnlPercentage: expect.any(String),
          fees: expect.any(Object),
        },
        message: 'Position closed successfully',
        timestamp: expect.any(String),
      });

      // Verify position status changed to CLOSED
      const positionCheck = await prisma.position.findUnique({
        where: { id: testPositionId },
      });

      expect(positionCheck?.status).toBe('CLOSED');
    });

    it('should return 404 for non-existent position', async () => {
      const nonExistentId = '550e8400-e29b-41d4-a716-************';

      const response = await request
        .delete(`/api/positions/${nonExistentId}`)
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        error: 'NOT_FOUND',
        timestamp: expect.any(String),
      });
    });

    it('should handle already closed positions', async () => {
      // First close the position
      await request.delete(`/api/positions/${testPositionId}`);

      // Try to close again
      const response = await request
        .delete(`/api/positions/${testPositionId}`)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: 'INVALID_OPERATION',
        message: 'Position is already closed',
        timestamp: expect.any(String),
      });
    });
  });

  describe('Position Analytics', () => {
    it('should calculate PnL correctly', async () => {
      // Update position with different current price
      await prisma.position.update({
        where: { id: testPositionId },
        data: {
          currentPrice: new Decimal('0.0000002'), // Double the entry price
          currentValueSol: new Decimal('0.2'),
          pnlSol: new Decimal('0.1'), // 100% gain
          pnlPercentage: new Decimal('100'),
        },
      });

      const response = await request
        .get(`/api/positions/${testPositionId}`)
        .expect(200);

      expect(response.body.data.pnlPercentage).toBe('100');
      expect(response.body.data.pnlSol).toBe('0.1');
    });

    it('should track position performance over time', async () => {
      const response = await request
        .get(`/api/positions/${testPositionId}/performance`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          positionId: testPositionId,
          performanceHistory: expect.any(Array),
          currentMetrics: expect.any(Object),
        },
        timestamp: expect.any(String),
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      // This would require mocking database connection failure
      // For now, just verify error response structure
      const response = await request
        .get('/api/positions/invalid-format')
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('timestamp');
    });

    it('should validate request body size limits', async () => {
      const largePayload = {
        exitStrategy: {
          description: 'x'.repeat(10000), // Very large string
        },
      };

      const response = await request
        .patch(`/api/positions/${testPositionId}`)
        .send(largePayload)
        .expect(413);

      expect(response.body).toHaveProperty('error');
    });
  });
});