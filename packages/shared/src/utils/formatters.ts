import { Decimal } from 'decimal.js';

/**
 * Format a decimal value as a currency string
 */
export function formatCurrency(value: Decimal, decimals: number = 4): string {
  return value.toFixed(decimals);
}

/**
 * Format a percentage value
 */
export function formatPercentage(value: Decimal, decimals: number = 2): string {
  return `${value.toFixed(decimals)}%`;
}

/**
 * Format SOL amount with appropriate precision
 */
export function formatSolAmount(value: Decimal): string {
  if (value.gte(1)) {
    return `${value.toFixed(4)} SOL`;
  } else if (value.gte(0.001)) {
    return `${value.toFixed(6)} SOL`;
  } else {
    return `${value.toFixed(8)} SOL`;
  }
}

/**
 * Format token amount with appropriate precision
 */
export function formatTokenAmount(value: Decimal, symbol: string, decimals?: number): string {
  const precision = decimals ?? (value.gte(1) ? 2 : 6);
  return `${value.toFixed(precision)} ${symbol}`;
}

/**
 * Format large numbers with K, M, B suffixes
 */
export function formatLargeNumber(value: Decimal): string {
  if (value.gte(1e9)) {
    return `${value.div(1e9).toFixed(2)}B`;
  } else if (value.gte(1e6)) {
    return `${value.div(1e6).toFixed(2)}M`;
  } else if (value.gte(1e3)) {
    return `${value.div(1e3).toFixed(2)}K`;
  } else {
    return value.toFixed(2);
  }
}

/**
 * Format timestamp for display
 */
export function formatTimestamp(date: Date): string {
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

/**
 * Format duration in milliseconds to human readable
 */
export function formatDuration(ms: number): string {
  if (ms < 1000) {
    return `${ms}ms`;
  } else if (ms < 60000) {
    return `${(ms / 1000).toFixed(1)}s`;
  } else if (ms < 3600000) {
    return `${(ms / 60000).toFixed(1)}m`;
  } else {
    return `${(ms / 3600000).toFixed(1)}h`;
  }
}

/**
 * Truncate Solana address for display
 */
export function truncateAddress(address: string, startChars: number = 4, endChars: number = 4): string {
  if (address.length <= startChars + endChars) {
    return address;
  }
  return `${address.slice(0, startChars)}...${address.slice(-endChars)}`;
}