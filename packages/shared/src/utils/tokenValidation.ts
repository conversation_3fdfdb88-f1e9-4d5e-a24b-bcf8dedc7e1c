import { PublicKey } from '@solana/web3.js';

export interface TokenValidationResult {
  isValid: boolean;
  error?: string;
  normalizedAddress?: string;
}

export interface TokenStandards {
  SPL_TOKEN: 'spl-token';
  SPL_TOKEN_2022: 'spl-token-2022';
  NFT: 'nft';
}

export const TOKEN_STANDARDS: TokenStandards = {
  SPL_TOKEN: 'spl-token',
  SPL_TOKEN_2022: 'spl-token-2022',
  NFT: 'nft'
};

/**
 * Validates a Solana token address using @solana/web3.js PublicKey validation
 */
export function validateTokenAddress(address: string): TokenValidationResult {
  if (!address) {
    return {
      isValid: false,
      error: 'Token address is required'
    };
  }

  if (typeof address !== 'string') {
    return {
      isValid: false,
      error: 'Token address must be a string'
    };
  }

  const trimmedAddress = address.trim();

  if (trimmedAddress.length === 0) {
    return {
      isValid: false,
      error: 'Token address cannot be empty'
    };
  }

  try {
    const publicKey = new PublicKey(trimmedAddress);
    return {
      isValid: true,
      normalizedAddress: publicKey.toBase58()
    };
  } catch (error) {
    return {
      isValid: false,
      error: 'Invalid Solana address format'
    };
  }
}

/**
 * Validates multiple token addresses at once
 */
export function validateTokenAddresses(addresses: string[]): TokenValidationResult[] {
  return addresses.map(address => validateTokenAddress(address));
}

/**
 * Checks if an address is a known system address (SOL, WSOL, etc.)
 */
export function isSystemToken(address: string): boolean {
  const systemTokens = [
    'So11111111111111111111111111111111111111112', // SOL
    '11111111111111111111111111111111' // System Program
  ];
  
  return systemTokens.includes(address);
}

/**
 * Gets user-friendly error messages for common validation failures
 */
export function getValidationErrorMessage(error: string): string {
  const errorMap: { [key: string]: string } = {
    'Invalid Solana address format': 'Please enter a valid Solana token address (32-44 characters)',
    'Token address is required': 'Please enter a token address',
    'Token address must be a string': 'Token address format is invalid',
    'Token address cannot be empty': 'Please enter a token address'
  };

  return errorMap[error] || error;
}

/**
 * Validates and normalizes a token address with user-friendly error handling
 */
export function validateAndNormalizeTokenAddress(address: string): {
  isValid: boolean;
  address?: string;
  userFriendlyError?: string;
} {
  const result = validateTokenAddress(address);
  
  if (!result.isValid) {
    return {
      isValid: false,
      userFriendlyError: getValidationErrorMessage(result.error || 'Unknown validation error')
    };
  }

  return {
    isValid: true,
    address: result.normalizedAddress
  };
}