import { z } from 'zod';
import { Decimal } from 'decimal.js';
import { MAX_SLIPPAGE_BPS, MIN_SLIPPAGE_BPS, MIN_POSITION_VALUE_SOL, MAX_POSITION_VALUE_SOL } from '../constants';

// Custom Decimal schema for zod
const DecimalSchema = z.preprocess(
  (val) => {
    if (typeof val === 'string' || typeof val === 'number') {
      return new Decimal(val);
    }
    return val;
  },
  z.instanceof(Decimal)
);

// Solana address validation
export const SolanaAddressSchema = z.string()
  .length(44, 'Solana address must be exactly 44 characters')
  .regex(/^[A-HJ-NP-Z1-9]{44}$/, 'Invalid Solana address format');

// Trading validation schemas
export const SlippageSchema = z.number()
  .min(MIN_SLIPPAGE_BPS, `Slippage must be at least ${MIN_SLIPPAGE_BPS} BPS`)
  .max(MAX_SLIPPAGE_BPS, `Slippage must not exceed ${MAX_SLIPPAGE_BPS} BPS`);

export const PositionAmountSchema = DecimalSchema
  .refine((val) => val.gte(MIN_POSITION_VALUE_SOL), `Position must be at least ${MIN_POSITION_VALUE_SOL.toString()} SOL`)
  .refine((val) => val.lte(MAX_POSITION_VALUE_SOL), `Position must not exceed ${MAX_POSITION_VALUE_SOL.toString()} SOL`);

export const PositionCreateSchema = z.object({
  tokenAddress: SolanaAddressSchema,
  tokenSymbol: z.string().min(1).max(20),
  tokenName: z.string().min(1).max(100),
  entryPrice: DecimalSchema.refine((val) => val.gt(0), 'Entry price must be positive'),
  quantity: DecimalSchema.refine((val) => val.gt(0), 'Quantity must be positive'),
  entryAmountSol: PositionAmountSchema,
  transactionSignature: z.string().length(88, 'Transaction signature must be exactly 88 characters'),
  slippage: DecimalSchema.refine((val) => val.gte(MIN_SLIPPAGE_BPS) && val.lte(MAX_SLIPPAGE_BPS), 'Invalid slippage range'),
  jupiterQuoteId: z.string().optional()
});

export const WatchlistCreateSchema = z.object({
  tokenAddress: SolanaAddressSchema,
  tokenSymbol: z.string().min(1).max(20),
  tokenName: z.string().min(1).max(100),
  customName: z.string().max(50).optional(),
  notes: z.string().max(500).optional()
});

// Utility functions
export function isValidSolanaAddress(address: string): boolean {
  try {
    SolanaAddressSchema.parse(address);
    return true;
  } catch {
    return false;
  }
}

export function validateSlippage(slippageBps: number): boolean {
  try {
    SlippageSchema.parse(slippageBps);
    return true;
  } catch {
    return false;
  }
}