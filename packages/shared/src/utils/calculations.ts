import { Decimal } from 'decimal.js';

/**
 * Calculate PnL in SOL
 * @param entryAmountSol - Initial investment amount in SOL
 * @param currentValueSol - Current position value in SOL
 * @returns PnL amount in SOL (positive = profit, negative = loss)
 */
export function calculatePnLSol(entryAmountSol: Decimal, currentValueSol: Decimal): Decimal {
  if (entryAmountSol.isNaN() || currentValueSol.isNaN()) {
    throw new Error('Invalid input: NaN values not allowed in PnL calculations');
  }
  if (entryAmountSol.isNegative() || currentValueSol.isNegative()) {
    throw new Error('Invalid input: Negative values not allowed in PnL calculations');
  }
  return currentValueSol.minus(entryAmountSol);
}

/**
 * Calculate PnL percentage
 * @param entryAmountSol - Initial investment amount in SOL
 * @param currentValueSol - Current position value in SOL
 * @returns PnL percentage (positive = profit, negative = loss)
 */
export function calculatePnLPercentage(entryAmountSol: Decimal, currentValueSol: Decimal): Decimal {
  if (entryAmountSol.isNaN() || currentValueSol.isNaN()) {
    throw new Error('Invalid input: NaN values not allowed in PnL calculations');
  }
  if (entryAmountSol.isNegative() || currentValueSol.isNegative()) {
    throw new Error('Invalid input: Negative values not allowed in PnL calculations');
  }
  if (entryAmountSol.isZero()) {
    return new Decimal(0);
  }
  return currentValueSol.minus(entryAmountSol).div(entryAmountSol).mul(100);
}

/**
 * Calculate current position value in SOL
 */
export function calculateCurrentValue(quantity: Decimal, currentPrice: Decimal): Decimal {
  return quantity.mul(currentPrice);
}

/**
 * Calculate price impact percentage
 */
export function calculatePriceImpact(inputAmount: Decimal, outputAmount: Decimal, marketPrice: Decimal): Decimal {
  const expectedOutput = inputAmount.div(marketPrice);
  if (expectedOutput.isZero()) {
    return new Decimal(0);
  }
  return expectedOutput.minus(outputAmount).div(expectedOutput).mul(100);
}

/**
 * Calculate slippage from basis points to decimal
 */
export function bpsToDecimal(bps: number): Decimal {
  return new Decimal(bps).div(10000);
}

/**
 * Calculate slippage from decimal to basis points
 */
export function decimalToBps(decimal: Decimal): number {
  return decimal.mul(10000).toNumber();
}

/**
 * Calculate trailing stop price
 */
export function calculateTrailingStopPrice(peakPrice: Decimal, trailingDistance: Decimal): Decimal {
  return peakPrice.minus(trailingDistance);
}

/**
 * Calculate take profit target price
 */
export function calculateTakeProfitPrice(entryPrice: Decimal, profitPercentage: Decimal): Decimal {
  return entryPrice.mul(new Decimal(1).plus(profitPercentage.div(100)));
}

/**
 * Calculate stop loss target price
 */
export function calculateStopLossPrice(entryPrice: Decimal, lossPercentage: Decimal): Decimal {
  return entryPrice.mul(new Decimal(1).minus(lossPercentage.div(100)));
}

/**
 * Calculate position size based on risk amount
 */
export function calculatePositionSize(riskAmountSol: Decimal, entryPrice: Decimal, stopLossPrice: Decimal): Decimal {
  const riskPerToken = entryPrice.minus(stopLossPrice);
  if (riskPerToken.lte(0)) {
    return new Decimal(0);
  }
  return riskAmountSol.div(riskPerToken);
}