import { 
  validateTokenAddress, 
  validateTokenAddresses,
  isSystemToken,
  getValidationErrorMessage,
  validateAndNormalizeTokenAddress 
} from '../tokenValidation';

describe('tokenValidation', () => {
  describe('validateTokenAddress', () => {
    it('should validate correct Solana addresses', () => {
      const validAddresses = [
        'So11111111111111111111111111111111111111112', // SOL
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
        'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB', // USDT
        'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263', // BONK
      ];

      validAddresses.forEach(address => {
        const result = validateTokenAddress(address);
        expect(result.isValid).toBe(true);
        expect(result.normalizedAddress).toBe(address);
        expect(result.error).toBeUndefined();
      });
    });

    it('should reject invalid addresses', () => {
      const invalidAddresses = [
        'invalid',
        '123',
        'too_short',
        'InvalidChars@#$%',
        'this_is_way_too_long_for_a_solana_address_and_should_definitely_fail_validation',
      ];

      invalidAddresses.forEach(address => {
        const result = validateTokenAddress(address);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Invalid Solana address format');
        expect(result.normalizedAddress).toBeUndefined();
      });
    });

    it('should handle empty and null inputs', () => {
      const emptyInputs = ['', null, undefined];

      emptyInputs.forEach(input => {
        const result = validateTokenAddress(input as any);
        expect(result.isValid).toBe(false);
        expect(result.error).toBeDefined();
      });
    });

    it('should handle non-string inputs', () => {
      const nonStringInputs = [123, {}, [], true];

      nonStringInputs.forEach(input => {
        const result = validateTokenAddress(input as any);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe('Token address must be a string');
      });
    });

    it('should trim whitespace from addresses', () => {
      const addressWithWhitespace = '  So11111111111111111111111111111111111111112  ';
      const result = validateTokenAddress(addressWithWhitespace);
      
      expect(result.isValid).toBe(true);
      expect(result.normalizedAddress).toBe('So11111111111111111111111111111111111111112');
    });
  });

  describe('validateTokenAddresses', () => {
    it('should validate multiple addresses', () => {
      const addresses = [
        'So11111111111111111111111111111111111111112',
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
        'invalid_address'
      ];

      const results = validateTokenAddresses(addresses);
      
      expect(results).toHaveLength(3);
      expect(results[0].isValid).toBe(true);
      expect(results[1].isValid).toBe(true);
      expect(results[2].isValid).toBe(false);
    });

    it('should handle empty array', () => {
      const results = validateTokenAddresses([]);
      expect(results).toHaveLength(0);
    });
  });

  describe('isSystemToken', () => {
    it('should identify system tokens', () => {
      const systemTokens = [
        'So11111111111111111111111111111111111111112', // SOL
        '11111111111111111111111111111111' // System Program
      ];

      systemTokens.forEach(token => {
        expect(isSystemToken(token)).toBe(true);
      });
    });

    it('should reject non-system tokens', () => {
      const nonSystemTokens = [
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
        'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB', // USDT
        'invalid_address'
      ];

      nonSystemTokens.forEach(token => {
        expect(isSystemToken(token)).toBe(false);
      });
    });
  });

  describe('getValidationErrorMessage', () => {
    it('should return user-friendly error messages', () => {
      const errorMappings = [
        ['Invalid Solana address format', 'Please enter a valid Solana token address (32-44 characters)'],
        ['Token address is required', 'Please enter a token address'],
        ['Token address must be a string', 'Token address format is invalid'],
        ['Token address cannot be empty', 'Please enter a token address'],
      ];

      errorMappings.forEach(([input, expected]) => {
        expect(getValidationErrorMessage(input)).toBe(expected);
      });
    });

    it('should return original error for unmapped errors', () => {
      const unknownError = 'Some unknown error';
      expect(getValidationErrorMessage(unknownError)).toBe(unknownError);
    });
  });

  describe('validateAndNormalizeTokenAddress', () => {
    it('should return normalized address for valid inputs', () => {
      const validAddress = 'So11111111111111111111111111111111111111112';
      const result = validateAndNormalizeTokenAddress(validAddress);
      
      expect(result.isValid).toBe(true);
      expect(result.address).toBe(validAddress);
      expect(result.userFriendlyError).toBeUndefined();
    });

    it('should return user-friendly error for invalid inputs', () => {
      const invalidAddress = 'invalid';
      const result = validateAndNormalizeTokenAddress(invalidAddress);
      
      expect(result.isValid).toBe(false);
      expect(result.address).toBeUndefined();
      expect(result.userFriendlyError).toBe('Please enter a valid Solana token address (32-44 characters)');
    });

    it('should handle edge cases gracefully', () => {
      const edgeCases = ['', null, undefined];
      
      edgeCases.forEach(input => {
        const result = validateAndNormalizeTokenAddress(input as any);
        expect(result.isValid).toBe(false);
        expect(result.userFriendlyError).toBeDefined();
      });
    });
  });
});