// API Base URLs
export const JUPITER_BASE_URL = 'https://lite-api.jup.ag/swap/v1';
export const HELIUS_BASE_URL = 'https://mainnet.helius-rpc.com';
export const CMC_BASE_URL = 'https://pro-api.coinmarketcap.com/v1';

// API Endpoints
export const API_ENDPOINTS = {
  JUPITER: {
    QUOTE: '/quote',
    SWAP: '/swap',
    SWAP_INSTRUCTIONS: '/swap-instructions',
    TOKENS: '/tokens',
    PRICE: '/price'
  },
  HELIUS: {
    BALANCE: '/v0/addresses/{address}/balances',
    TRANSACTIONS: '/v0/addresses/{address}/transactions',
    WEBHOOKS: '/v0/webhooks'
  },
  CMC: {
    QUOTES: '/cryptocurrency/quotes/latest',
    LISTINGS: '/cryptocurrency/listings/latest',
    METADATA: '/cryptocurrency/info'
  }
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503
} as const;

// API Response Headers
export const API_HEADERS = {
  CONTENT_TYPE: 'Content-Type',
  AUTHORIZATION: 'Authorization',
  USER_AGENT: 'User-Agent',
  X_API_KEY: 'X-API-Key'
} as const;