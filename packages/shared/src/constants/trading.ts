import { Decimal } from 'decimal.js';

// Trading Constants
export const MAX_SLIPPAGE_BPS = 1000; // 10%
export const DEFAULT_SLIPPAGE_BPS = 100; // 1%
export const MIN_SLIPPAGE_BPS = 10; // 0.1%

export const SOL_MINT_ADDRESS = 'So11111111111111111111111111111111111111112';
export const USDC_MINT_ADDRESS = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';

// Position limits
export const MIN_POSITION_VALUE_SOL = new Decimal('0.001'); // 0.001 SOL minimum
export const MAX_POSITION_VALUE_SOL = new Decimal('1000'); // 1000 SOL maximum

// Exit strategy constants
export const MAX_TAKE_PROFIT_TIERS = 5;
export const MIN_TAKE_PROFIT_PERCENTAGE = new Decimal('0.01'); // 1%
export const MAX_TAKE_PROFIT_PERCENTAGE = new Decimal('10'); // 1000%

// Timing constants
export const PRICE_UPDATE_INTERVAL_MS = 5000; // 5 seconds
export const MAX_EXECUTION_TIME_MS = 5000; // 5 seconds max execution time
export const POSITION_CLEANUP_INTERVAL_MS = 60000; // 1 minute

// API rate limits
export const HELIUS_RATE_LIMIT_PER_SECOND = 10;
export const CMC_RATE_LIMIT_PER_MINUTE = 333; // Based on free tier
export const JUPITER_RATE_LIMIT_PER_SECOND = 50;