import { Decimal } from 'decimal.js';

export type ExitStrategyType = 'take_profit' | 'stop_loss' | 'trailing_stop' | 'time_based';

export interface TakeProfitTier {
  id: string;
  percentage: Decimal; // Percentage of position to sell
  priceTarget: Decimal; // Target price in SOL
  isExecuted: boolean;
  executedAt?: Date;
  transactionSignature?: string;
}

export interface StopLossConfig {
  priceTarget: Decimal; // Stop loss price in SOL
  isTrailing: boolean;
  trailingDistance?: Decimal; // Distance from peak price for trailing stop
  isExecuted: boolean;
  executedAt?: Date;
  transactionSignature?: string;
}

export interface ExitStrategy {
  id: string;
  positionId: string;
  type: ExitStrategyType;
  isActive: boolean;
  takeProfitTiers: TakeProfitTier[];
  stopLoss?: StopLossConfig;
  timeBasedExit?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface JupiterQuote {
  inputMint: string;
  inAmount: string;
  outputMint: string;
  outAmount: string;
  otherAmountThreshold: string;
  swapMode: 'ExactIn' | 'ExactOut';
  slippageBps: number;
  priceImpactPct: string;
  routePlan: any[];
  quoteId?: string;
}

export interface TradeExecutionRequest {
  positionId?: string; // For exits, undefined for entries
  tokenAddress: string;
  amountSol: Decimal;
  slippageBps: number;
  isEntry: boolean; // true for buy, false for sell
  jupiterQuote?: JupiterQuote;
}

// ========================================
// Trading Activity Tracking for Watchlist Integration
// ========================================

/**
 * Trading activity metrics for watchlist influence
 */
export interface TokenTradingHistory {
  tokenAddress: string;
  totalTrades: number;
  successfulTrades: number;
  failedTrades: number;
  totalVolumeUsd: Decimal;
  totalPnlUsd: Decimal;
  averageHoldTimeMinutes: number;
  lastTradeDate: Date;
  firstTradeDate: Date;
  tradingPairs: string[]; // e.g., ['SOL', 'USDC']
}

/**
 * Individual trade record for detailed analysis
 */
export interface TradeRecord {
  id: string;
  tokenAddress: string;
  tradeType: 'buy' | 'sell';
  amountIn: Decimal;
  amountOut: Decimal;
  priceUsd: Decimal;
  slippageBps: number;
  gasFeeSol: Decimal;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
  holdTimeMinutes?: number; // For sell trades
  pnlUsd?: Decimal; // For sell trades
  signature?: string; // Transaction signature
}

/**
 * Trading performance metrics
 */
export interface TradingPerformanceMetrics {
  winRate: number; // Percentage of profitable trades
  averagePnlUsd: Decimal;
  averageHoldTimeMinutes: number;
  bestTradeUsd: Decimal;
  worstTradeUsd: Decimal;
  totalFeesUsd: Decimal;
  sharpeRatio?: number; // Risk-adjusted return metric
  maxDrawdownUsd: Decimal;
  profitFactor: number; // Gross profit / gross loss
}

/**
 * Watchlist priority scoring based on trading activity
 */
export interface WatchlistPriorityScore {
  tokenAddress: string;
  baseScore: number; // 0-100, based on market metrics
  tradingInfluenceScore: number; // 0-100, based on trading history
  finalScore: number; // Weighted combination
  lastUpdated: Date;
  
  // Breakdown of trading influence factors
  tradingInfluenceFactors: {
    winRateBonus: number;
    volumeBonus: number;
    recentActivityBonus: number;
    holdTimeOptimization: number;
    profitFactorBonus: number;
  };
}

/**
 * Priority scoring algorithm configuration
 */
export interface PriorityScoreConfig {
  weights: {
    baseScore: number; // Weight for market-based metrics
    tradingInfluence: number; // Weight for trading history
  };
  tradingInfluenceWeights: {
    winRate: number;
    volume: number;
    recentActivity: number;
    holdTime: number;
    profitFactor: number;
  };
  
  // Decay factors for older trading activity
  timeDecayConfig: {
    recentDays: number; // Days considered "recent"
    decayRate: number; // How much older activity is discounted
  };
}

/**
 * Trading activity callbacks for real-time updates
 */
export interface TradingActivityCallbacks {
  onTradeExecuted: (trade: TradeRecord) => void;
  onTradeCompleted: (trade: TradeRecord) => void;
  onTradeFailed: (trade: Partial<TradeRecord> & { errorMessage: string }) => void;
  onPositionClosed: (trade: TradeRecord) => void;
}

/**
 * Integration hooks for trading-watchlist communication
 */
export interface TradingWatchlistIntegration {
  // Event emitters
  emitTradeEvent: (event: string, data: any) => void;
  
  // Event listeners
  onWatchlistUpdate: (callback: (tokens: string[]) => void) => void;
  onPriorityChange: (callback: (scores: WatchlistPriorityScore[]) => void) => void;
  
  // Data fetchers
  getTradingHistory: (tokenAddress: string) => Promise<TokenTradingHistory | null>;
  getPerformanceMetrics: (tokenAddress: string) => Promise<TradingPerformanceMetrics | null>;
  getPriorityScore: (tokenAddress: string) => Promise<WatchlistPriorityScore | null>;
  
  // Analytics
  getConversionMetrics: () => Promise<{
    watchlistToTradingRate: number;
    averageTimeToTrade: number;
    mostTradedFromWatchlist: string[];
  }>;
}

// ========================================
// Analytics and Optimization Framework
// ========================================

/**
 * Analytics tracking for workflow optimization
 */
export interface UserJourneyMetrics {
  sessionId: string;
  userId: string;
  journeyStart: Date;
  journeyEnd?: Date;
  
  // Workflow steps
  steps: UserJourneyStep[];
  
  // Outcome metrics
  conversionRate: number; // Did they complete a trade?
  timeToConversion?: number; // Milliseconds from start to trade
  dropOffPoint?: string; // Where they left if no conversion
}

export interface UserJourneyStep {
  stepId: string;
  stepName: string;
  timestamp: Date;
  duration: number; // Milliseconds spent on this step
  metadata?: Record<string, any>;
}

/**
 * A/B testing framework types
 */
export interface ABTestVariant {
  id: string;
  name: string;
  description: string;
  isControl: boolean;
  weight: number; // Percentage of users to receive this variant
  config: Record<string, any>; // Variant-specific configuration
}

export interface ABTestExperiment {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'active' | 'paused' | 'completed';
  startDate: Date;
  endDate?: Date;
  variants: ABTestVariant[];
  targetMetric: string; // e.g., 'conversion_rate', 'time_to_trade'
  minimumSampleSize: number;
  confidenceLevel: number; // e.g., 0.95 for 95% confidence
}

export interface ABTestAssignment {
  userId: string;
  experimentId: string;
  variantId: string;
  assignedAt: Date;
}

// ========================================
// Future API Endpoint Specifications
// ========================================

/**
 * Future API endpoint specifications (for design reference)
 */
export interface TradingHistoryAPI {
  // GET /api/trading/history/:tokenAddress
  getTokenTradingHistory: (tokenAddress: string) => Promise<TokenTradingHistory>;
  
  // GET /api/trading/performance/:tokenAddress
  getTokenPerformance: (tokenAddress: string) => Promise<TradingPerformanceMetrics>;
  
  // POST /api/trading/activity
  recordTradingActivity: (trade: TradeRecord) => Promise<void>;
  
  // GET /api/watchlist/priority-scores
  getPriorityScores: (tokenAddresses: string[]) => Promise<WatchlistPriorityScore[]>;
  
  // PUT /api/watchlist/priority-config
  updatePriorityConfig: (config: PriorityScoreConfig) => Promise<void>;
  
  // GET /api/analytics/user-journey
  getUserJourneyMetrics: (filters: {
    startDate: Date;
    endDate: Date;
    userId?: string;
  }) => Promise<UserJourneyMetrics[]>;
  
  // GET /api/analytics/conversion
  getConversionMetrics: (filters: {
    startDate: Date;
    endDate: Date;
  }) => Promise<{
    watchlistToTradingRate: number;
    averageTimeToTrade: number;
    topPerformingTokens: string[];
  }>;
}