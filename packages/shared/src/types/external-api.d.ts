import { Decimal } from 'decimal.js';
export interface CMCTokenData {
    id: number;
    name: string;
    symbol: string;
    slug: string;
    cmc_rank?: number;
    circulating_supply?: number;
    total_supply?: number;
    max_supply?: number;
    quote: {
        USD: {
            price: number;
            volume_24h?: number;
            market_cap?: number;
            percent_change_1h?: number;
            percent_change_24h?: number;
            percent_change_7d?: number;
            last_updated: string;
        };
    };
}
export interface HeliusTransaction {
    signature: string;
    slot: number;
    timestamp: number;
    fee: number;
    feePayer: string;
    success: boolean;
}
export interface HeliusBalance {
    tokenAccount: string;
    mint: string;
    owner: string;
    amount: string;
    decimals: number;
}
export interface PriceSnapshot {
    id: string;
    tokenAddress: string;
    price: Decimal;
    volume24h?: Decimal;
    marketCap?: Decimal;
    priceChange24h: Decimal;
    timestamp: Date;
    source: 'coinmarketcap' | 'jupiter' | 'helius';
}
//# sourceMappingURL=external-api.d.ts.map