import { Decimal } from 'decimal.js';
export type PositionStatus = 'active' | 'closed' | 'pending_exit' | 'error';
export interface Position {
    id: string;
    tokenAddress: string;
    tokenSymbol: string;
    tokenName: string;
    entryPrice: Decimal;
    currentPrice: Decimal;
    quantity: Decimal;
    entryAmountSol: Decimal;
    currentValueSol: Decimal;
    pnlSol: Decimal;
    pnlPercentage: Decimal;
    status: PositionStatus;
    entryTimestamp: Date;
    lastUpdateTimestamp: Date;
    exitTimestamp?: Date;
    transactionSignature: string;
    slippage: Decimal;
    jupiterQuoteId?: string;
}
export interface PositionCreateInput {
    tokenAddress: string;
    tokenSymbol: string;
    tokenName: string;
    entryPrice: Decimal;
    quantity: Decimal;
    entryAmountSol: Decimal;
    transactionSignature: string;
    slippage: Decimal;
    jupiterQuoteId?: string;
}
//# sourceMappingURL=position.d.ts.map