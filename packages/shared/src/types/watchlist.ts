import { z } from 'zod';
import { Decimal } from 'decimal.js';

// Base WatchlistItem interface matching Prisma model
export interface WatchlistItem {
  id: string;
  tokenAddress: string;
  tokenSymbol: string;
  tokenName: string;
  customName?: string | null;
  notes?: string | null;
  isPinned: boolean;
  addedAt: Date | string;
  updatedAt: Date | string;
  isActive: boolean;
}

// TokenSnapshot interface for market data
export interface TokenSnapshot {
  tokenAddress: string;
  priceUsd: Decimal;
  priceChange1h?: Decimal;
  priceChange24h?: Decimal;
  volume24h?: Decimal;
  liquidity?: Decimal;
  fdv?: Decimal; // Fully diluted valuation
  ageInDays?: number;
  lastUpdated: Date;
  source: string;
}

// TokenMetrics interface for extended market data
export interface TokenMetrics {
  marketCap?: Decimal;
  totalSupply?: Decimal;
  circulatingSupply?: Decimal;
  holders?: number;
  transactions24h?: number;
  priceHigh24h?: Decimal;
  priceLow24h?: Decimal;
}

// Combined type for watchlist items with market metrics
export interface WatchlistItemWithMetrics extends WatchlistItem {
  snapshot?: TokenSnapshot;
  metrics?: TokenMetrics;
}

// DTO for creating a new watchlist item
export interface CreateWatchlistItemDto {
  tokenAddress: string;
  tokenSymbol: string;
  tokenName: string;
  customName?: string;
  notes?: string;
}

// DTO for updating a watchlist item
export interface UpdateWatchlistItemDto {
  customName?: string;
  notes?: string;
  isPinned?: boolean;
}

// Legacy aliases for backward compatibility
export type WatchlistCreateInput = CreateWatchlistItemDto;
export type WatchlistUpdateInput = UpdateWatchlistItemDto;

// Response type for watchlist API endpoints
export interface WatchlistResponse {
  items: WatchlistItem[];
  total: number;
  timestamp: string;
}

// Response type for watchlist statistics
export interface WatchlistStatsResponse {
  totalItems: number;
  pinnedItems: number;
  timestamp: string;
}

// Zod validation schemas
export const createWatchlistItemSchema = z.object({
  tokenAddress: z.string().min(32).max(44), // Solana address length
  tokenSymbol: z.string().min(1).max(20),
  tokenName: z.string().min(1).max(100),
  customName: z.string().max(100).optional(),
  notes: z.string().max(1000).optional()
});

export const updateWatchlistItemSchema = z.object({
  customName: z.string().max(100).optional(),
  notes: z.string().max(1000).optional(),
  isPinned: z.boolean().optional()
});

// Schema for bulk import input
export const bulkImportItemSchema = z.object({
  tokenAddress: z.string().min(32).max(44),
  tokenSymbol: z.string().min(1).max(20).optional(),
  tokenName: z.string().min(1).max(100).optional(),
  customName: z.string().max(100).optional(),
  notes: z.string().max(1000).optional()
});

export const bulkImportSchema = z.object({
  tokens: z.array(bulkImportItemSchema).min(1).max(50) // Limit bulk operations to 50 items
});

// Schema for bulk import response
export const bulkImportResponseSchema = z.object({
  successful: z.array(z.any()), // WatchlistItem[]
  failed: z.array(z.object({
    tokenAddress: z.string(),
    error: z.string()
  })),
  summary: z.object({
    total: z.number(),
    successful: z.number(),
    failed: z.number(),
    skippedDuplicates: z.number()
  })
});

// Zod schema for TokenSnapshot validation
export const tokenSnapshotSchema = z.object({
  tokenAddress: z.string().min(32).max(44),
  priceUsd: z.instanceof(Decimal).or(z.number().transform(val => new Decimal(val))),
  priceChange1h: z.instanceof(Decimal).or(z.number().transform(val => new Decimal(val))).optional(),
  priceChange24h: z.instanceof(Decimal).or(z.number().transform(val => new Decimal(val))).optional(),
  volume24h: z.instanceof(Decimal).or(z.number().transform(val => new Decimal(val))).optional(),
  liquidity: z.instanceof(Decimal).or(z.number().transform(val => new Decimal(val))).optional(),
  fdv: z.instanceof(Decimal).or(z.number().transform(val => new Decimal(val))).optional(),
  ageInDays: z.number().optional(),
  lastUpdated: z.date().or(z.string().transform(val => new Date(val))),
  source: z.string().min(1).max(50)
});

// Zod schema for TokenMetrics validation
export const tokenMetricsSchema = z.object({
  marketCap: z.instanceof(Decimal).or(z.number().transform(val => new Decimal(val))).optional(),
  totalSupply: z.instanceof(Decimal).or(z.number().transform(val => new Decimal(val))).optional(),
  circulatingSupply: z.instanceof(Decimal).or(z.number().transform(val => new Decimal(val))).optional(),
  holders: z.number().optional(),
  transactions24h: z.number().optional(),
  priceHigh24h: z.instanceof(Decimal).or(z.number().transform(val => new Decimal(val))).optional(),
  priceLow24h: z.instanceof(Decimal).or(z.number().transform(val => new Decimal(val))).optional()
});

// Type guards
export const isWatchlistItem = (obj: any): obj is WatchlistItem => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    typeof obj.id === 'string' &&
    typeof obj.tokenAddress === 'string' &&
    typeof obj.tokenSymbol === 'string' &&
    typeof obj.tokenName === 'string' &&
    typeof obj.isPinned === 'boolean' &&
    typeof obj.isActive === 'boolean' &&
    (obj.addedAt instanceof Date || typeof obj.addedAt === 'string') &&
    (obj.updatedAt instanceof Date || typeof obj.updatedAt === 'string')
  );
};

// Type guard for TokenSnapshot
export const isTokenSnapshot = (obj: any): obj is TokenSnapshot => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    typeof obj.tokenAddress === 'string' &&
    obj.priceUsd instanceof Decimal &&
    obj.lastUpdated instanceof Date &&
    typeof obj.source === 'string'
  );
};

// Type guard for TokenMetrics
export const isTokenMetrics = (obj: any): obj is TokenMetrics => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    (obj.marketCap === undefined || obj.marketCap instanceof Decimal) &&
    (obj.totalSupply === undefined || obj.totalSupply instanceof Decimal) &&
    (obj.circulatingSupply === undefined || obj.circulatingSupply instanceof Decimal)
  );
};

// Type guard for WatchlistItemWithMetrics
export const isWatchlistItemWithMetrics = (obj: any): obj is WatchlistItemWithMetrics => {
  return (
    isWatchlistItem(obj) &&
    ('snapshot' in obj ? (obj.snapshot === undefined || isTokenSnapshot(obj.snapshot)) : true) &&
    ('metrics' in obj ? (obj.metrics === undefined || isTokenMetrics(obj.metrics)) : true)
  );
};

// Constants
export const WATCHLIST_LIMITS = {
  MAX_ITEMS: 100,
  MAX_PINNED: 10,
  MAX_CUSTOM_NAME_LENGTH: 100,
  MAX_NOTES_LENGTH: 1000,
  MAX_SYMBOL_LENGTH: 20,
  MAX_NAME_LENGTH: 100
} as const;

// Error messages
export const WATCHLIST_ERRORS = {
  INVALID_TOKEN_ADDRESS: 'Invalid Solana token address format',
  DUPLICATE_TOKEN: 'Token already exists in watchlist',
  ITEM_NOT_FOUND: 'Watchlist item not found',
  MAX_ITEMS_REACHED: 'Maximum number of watchlist items reached',
  MAX_PINNED_REACHED: 'Maximum number of pinned items reached',
  INVALID_UPDATE_DATA: 'Invalid update data provided'
} as const;