import { z } from 'zod';
import { Decimal } from 'decimal.js';
export interface WatchlistItem {
    id: string;
    tokenAddress: string;
    tokenSymbol: string;
    tokenName: string;
    customName?: string | null;
    notes?: string | null;
    isPinned: boolean;
    addedAt: Date | string;
    updatedAt: Date | string;
    isActive: boolean;
}
export interface TokenSnapshot {
    tokenAddress: string;
    priceUsd: Decimal;
    priceChange1h?: Decimal;
    priceChange24h?: Decimal;
    volume24h?: Decimal;
    liquidity?: Decimal;
    fdv?: Decimal;
    ageInDays?: number;
    lastUpdated: Date;
    source: string;
}
export interface TokenMetrics {
    marketCap?: Decimal;
    totalSupply?: Decimal;
    circulatingSupply?: Decimal;
    holders?: number;
    transactions24h?: number;
    priceHigh24h?: Decimal;
    priceLow24h?: Decimal;
}
export interface WatchlistItemWithMetrics extends WatchlistItem {
    snapshot?: TokenSnapshot;
    metrics?: TokenMetrics;
}
export interface CreateWatchlistItemDto {
    tokenAddress: string;
    tokenSymbol: string;
    tokenName: string;
    customName?: string;
    notes?: string;
}
export interface UpdateWatchlistItemDto {
    customName?: string;
    notes?: string;
    isPinned?: boolean;
}
export type WatchlistCreateInput = CreateWatchlistItemDto;
export type WatchlistUpdateInput = UpdateWatchlistItemDto;
export interface WatchlistResponse {
    items: WatchlistItem[];
    total: number;
    timestamp: string;
}
export interface WatchlistStatsResponse {
    totalItems: number;
    pinnedItems: number;
    timestamp: string;
}
export declare const createWatchlistItemSchema: z.ZodObject<{
    tokenAddress: z.ZodString;
    tokenSymbol: z.ZodString;
    tokenName: z.ZodString;
    customName: z.ZodOptional<z.ZodString>;
    notes: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    tokenAddress: string;
    tokenSymbol: string;
    tokenName: string;
    customName?: string | undefined;
    notes?: string | undefined;
}, {
    tokenAddress: string;
    tokenSymbol: string;
    tokenName: string;
    customName?: string | undefined;
    notes?: string | undefined;
}>;
export declare const updateWatchlistItemSchema: z.ZodObject<{
    customName: z.ZodOptional<z.ZodString>;
    notes: z.ZodOptional<z.ZodString>;
    isPinned: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    customName?: string | undefined;
    notes?: string | undefined;
    isPinned?: boolean | undefined;
}, {
    customName?: string | undefined;
    notes?: string | undefined;
    isPinned?: boolean | undefined;
}>;
export declare const bulkImportItemSchema: z.ZodObject<{
    tokenAddress: z.ZodString;
    tokenSymbol: z.ZodOptional<z.ZodString>;
    tokenName: z.ZodOptional<z.ZodString>;
    customName: z.ZodOptional<z.ZodString>;
    notes: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    tokenAddress: string;
    tokenSymbol?: string | undefined;
    tokenName?: string | undefined;
    customName?: string | undefined;
    notes?: string | undefined;
}, {
    tokenAddress: string;
    tokenSymbol?: string | undefined;
    tokenName?: string | undefined;
    customName?: string | undefined;
    notes?: string | undefined;
}>;
export declare const bulkImportSchema: z.ZodObject<{
    tokens: z.ZodArray<z.ZodObject<{
        tokenAddress: z.ZodString;
        tokenSymbol: z.ZodOptional<z.ZodString>;
        tokenName: z.ZodOptional<z.ZodString>;
        customName: z.ZodOptional<z.ZodString>;
        notes: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        tokenAddress: string;
        tokenSymbol?: string | undefined;
        tokenName?: string | undefined;
        customName?: string | undefined;
        notes?: string | undefined;
    }, {
        tokenAddress: string;
        tokenSymbol?: string | undefined;
        tokenName?: string | undefined;
        customName?: string | undefined;
        notes?: string | undefined;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    tokens: {
        tokenAddress: string;
        tokenSymbol?: string | undefined;
        tokenName?: string | undefined;
        customName?: string | undefined;
        notes?: string | undefined;
    }[];
}, {
    tokens: {
        tokenAddress: string;
        tokenSymbol?: string | undefined;
        tokenName?: string | undefined;
        customName?: string | undefined;
        notes?: string | undefined;
    }[];
}>;
export declare const bulkImportResponseSchema: z.ZodObject<{
    successful: z.ZodArray<z.ZodAny, "many">;
    failed: z.ZodArray<z.ZodObject<{
        tokenAddress: z.ZodString;
        error: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        error: string;
        tokenAddress: string;
    }, {
        error: string;
        tokenAddress: string;
    }>, "many">;
    summary: z.ZodObject<{
        total: z.ZodNumber;
        successful: z.ZodNumber;
        failed: z.ZodNumber;
        skippedDuplicates: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        total: number;
        failed: number;
        successful: number;
        skippedDuplicates: number;
    }, {
        total: number;
        failed: number;
        successful: number;
        skippedDuplicates: number;
    }>;
}, "strip", z.ZodTypeAny, {
    failed: {
        error: string;
        tokenAddress: string;
    }[];
    successful: any[];
    summary: {
        total: number;
        failed: number;
        successful: number;
        skippedDuplicates: number;
    };
}, {
    failed: {
        error: string;
        tokenAddress: string;
    }[];
    successful: any[];
    summary: {
        total: number;
        failed: number;
        successful: number;
        skippedDuplicates: number;
    };
}>;
export declare const tokenSnapshotSchema: z.ZodObject<{
    tokenAddress: z.ZodString;
    priceUsd: z.ZodUnion<[z.ZodType<Decimal, z.ZodTypeDef, Decimal>, z.ZodEffects<z.ZodNumber, Decimal, number>]>;
    priceChange1h: z.ZodOptional<z.ZodUnion<[z.ZodType<Decimal, z.ZodTypeDef, Decimal>, z.ZodEffects<z.ZodNumber, Decimal, number>]>>;
    priceChange24h: z.ZodOptional<z.ZodUnion<[z.ZodType<Decimal, z.ZodTypeDef, Decimal>, z.ZodEffects<z.ZodNumber, Decimal, number>]>>;
    volume24h: z.ZodOptional<z.ZodUnion<[z.ZodType<Decimal, z.ZodTypeDef, Decimal>, z.ZodEffects<z.ZodNumber, Decimal, number>]>>;
    liquidity: z.ZodOptional<z.ZodUnion<[z.ZodType<Decimal, z.ZodTypeDef, Decimal>, z.ZodEffects<z.ZodNumber, Decimal, number>]>>;
    fdv: z.ZodOptional<z.ZodUnion<[z.ZodType<Decimal, z.ZodTypeDef, Decimal>, z.ZodEffects<z.ZodNumber, Decimal, number>]>>;
    ageInDays: z.ZodOptional<z.ZodNumber>;
    lastUpdated: z.ZodUnion<[z.ZodDate, z.ZodEffects<z.ZodString, Date, string>]>;
    source: z.ZodString;
}, "strip", z.ZodTypeAny, {
    tokenAddress: string;
    source: string;
    priceUsd: Decimal;
    lastUpdated: Date;
    volume24h?: Decimal | undefined;
    priceChange24h?: Decimal | undefined;
    priceChange1h?: Decimal | undefined;
    liquidity?: Decimal | undefined;
    fdv?: Decimal | undefined;
    ageInDays?: number | undefined;
}, {
    tokenAddress: string;
    source: string;
    priceUsd: number | Decimal;
    lastUpdated: string | Date;
    volume24h?: number | Decimal | undefined;
    priceChange24h?: number | Decimal | undefined;
    priceChange1h?: number | Decimal | undefined;
    liquidity?: number | Decimal | undefined;
    fdv?: number | Decimal | undefined;
    ageInDays?: number | undefined;
}>;
export declare const tokenMetricsSchema: z.ZodObject<{
    marketCap: z.ZodOptional<z.ZodUnion<[z.ZodType<Decimal, z.ZodTypeDef, Decimal>, z.ZodEffects<z.ZodNumber, Decimal, number>]>>;
    totalSupply: z.ZodOptional<z.ZodUnion<[z.ZodType<Decimal, z.ZodTypeDef, Decimal>, z.ZodEffects<z.ZodNumber, Decimal, number>]>>;
    circulatingSupply: z.ZodOptional<z.ZodUnion<[z.ZodType<Decimal, z.ZodTypeDef, Decimal>, z.ZodEffects<z.ZodNumber, Decimal, number>]>>;
    holders: z.ZodOptional<z.ZodNumber>;
    transactions24h: z.ZodOptional<z.ZodNumber>;
    priceHigh24h: z.ZodOptional<z.ZodUnion<[z.ZodType<Decimal, z.ZodTypeDef, Decimal>, z.ZodEffects<z.ZodNumber, Decimal, number>]>>;
    priceLow24h: z.ZodOptional<z.ZodUnion<[z.ZodType<Decimal, z.ZodTypeDef, Decimal>, z.ZodEffects<z.ZodNumber, Decimal, number>]>>;
}, "strip", z.ZodTypeAny, {
    marketCap?: Decimal | undefined;
    totalSupply?: Decimal | undefined;
    circulatingSupply?: Decimal | undefined;
    holders?: number | undefined;
    transactions24h?: number | undefined;
    priceHigh24h?: Decimal | undefined;
    priceLow24h?: Decimal | undefined;
}, {
    marketCap?: number | Decimal | undefined;
    totalSupply?: number | Decimal | undefined;
    circulatingSupply?: number | Decimal | undefined;
    holders?: number | undefined;
    transactions24h?: number | undefined;
    priceHigh24h?: number | Decimal | undefined;
    priceLow24h?: number | Decimal | undefined;
}>;
export declare const isWatchlistItem: (obj: any) => obj is WatchlistItem;
export declare const isTokenSnapshot: (obj: any) => obj is TokenSnapshot;
export declare const isTokenMetrics: (obj: any) => obj is TokenMetrics;
export declare const isWatchlistItemWithMetrics: (obj: any) => obj is WatchlistItemWithMetrics;
export declare const WATCHLIST_LIMITS: {
    readonly MAX_ITEMS: 100;
    readonly MAX_PINNED: 10;
    readonly MAX_CUSTOM_NAME_LENGTH: 100;
    readonly MAX_NOTES_LENGTH: 1000;
    readonly MAX_SYMBOL_LENGTH: 20;
    readonly MAX_NAME_LENGTH: 100;
};
export declare const WATCHLIST_ERRORS: {
    readonly INVALID_TOKEN_ADDRESS: "Invalid Solana token address format";
    readonly DUPLICATE_TOKEN: "Token already exists in watchlist";
    readonly ITEM_NOT_FOUND: "Watchlist item not found";
    readonly MAX_ITEMS_REACHED: "Maximum number of watchlist items reached";
    readonly MAX_PINNED_REACHED: "Maximum number of pinned items reached";
    readonly INVALID_UPDATE_DATA: "Invalid update data provided";
};
//# sourceMappingURL=watchlist.d.ts.map