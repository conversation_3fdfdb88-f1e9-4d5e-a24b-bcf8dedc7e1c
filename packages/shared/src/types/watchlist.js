"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WATCHLIST_ERRORS = exports.WATCHLIST_LIMITS = exports.isWatchlistItemWithMetrics = exports.isTokenMetrics = exports.isTokenSnapshot = exports.isWatchlistItem = exports.tokenMetricsSchema = exports.tokenSnapshotSchema = exports.bulkImportResponseSchema = exports.bulkImportSchema = exports.bulkImportItemSchema = exports.updateWatchlistItemSchema = exports.createWatchlistItemSchema = void 0;
const zod_1 = require("zod");
const decimal_js_1 = require("decimal.js");
// Zod validation schemas
exports.createWatchlistItemSchema = zod_1.z.object({
    tokenAddress: zod_1.z.string().min(32).max(44), // Solana address length
    tokenSymbol: zod_1.z.string().min(1).max(20),
    tokenName: zod_1.z.string().min(1).max(100),
    customName: zod_1.z.string().max(100).optional(),
    notes: zod_1.z.string().max(1000).optional()
});
exports.updateWatchlistItemSchema = zod_1.z.object({
    customName: zod_1.z.string().max(100).optional(),
    notes: zod_1.z.string().max(1000).optional(),
    isPinned: zod_1.z.boolean().optional()
});
// Schema for bulk import input
exports.bulkImportItemSchema = zod_1.z.object({
    tokenAddress: zod_1.z.string().min(32).max(44),
    tokenSymbol: zod_1.z.string().min(1).max(20).optional(),
    tokenName: zod_1.z.string().min(1).max(100).optional(),
    customName: zod_1.z.string().max(100).optional(),
    notes: zod_1.z.string().max(1000).optional()
});
exports.bulkImportSchema = zod_1.z.object({
    tokens: zod_1.z.array(exports.bulkImportItemSchema).min(1).max(50) // Limit bulk operations to 50 items
});
// Schema for bulk import response
exports.bulkImportResponseSchema = zod_1.z.object({
    successful: zod_1.z.array(zod_1.z.any()), // WatchlistItem[]
    failed: zod_1.z.array(zod_1.z.object({
        tokenAddress: zod_1.z.string(),
        error: zod_1.z.string()
    })),
    summary: zod_1.z.object({
        total: zod_1.z.number(),
        successful: zod_1.z.number(),
        failed: zod_1.z.number(),
        skippedDuplicates: zod_1.z.number()
    })
});
// Zod schema for TokenSnapshot validation
exports.tokenSnapshotSchema = zod_1.z.object({
    tokenAddress: zod_1.z.string().min(32).max(44),
    priceUsd: zod_1.z.instanceof(decimal_js_1.Decimal).or(zod_1.z.number().transform(val => new decimal_js_1.Decimal(val))),
    priceChange1h: zod_1.z.instanceof(decimal_js_1.Decimal).or(zod_1.z.number().transform(val => new decimal_js_1.Decimal(val))).optional(),
    priceChange24h: zod_1.z.instanceof(decimal_js_1.Decimal).or(zod_1.z.number().transform(val => new decimal_js_1.Decimal(val))).optional(),
    volume24h: zod_1.z.instanceof(decimal_js_1.Decimal).or(zod_1.z.number().transform(val => new decimal_js_1.Decimal(val))).optional(),
    liquidity: zod_1.z.instanceof(decimal_js_1.Decimal).or(zod_1.z.number().transform(val => new decimal_js_1.Decimal(val))).optional(),
    fdv: zod_1.z.instanceof(decimal_js_1.Decimal).or(zod_1.z.number().transform(val => new decimal_js_1.Decimal(val))).optional(),
    ageInDays: zod_1.z.number().optional(),
    lastUpdated: zod_1.z.date().or(zod_1.z.string().transform(val => new Date(val))),
    source: zod_1.z.string().min(1).max(50)
});
// Zod schema for TokenMetrics validation
exports.tokenMetricsSchema = zod_1.z.object({
    marketCap: zod_1.z.instanceof(decimal_js_1.Decimal).or(zod_1.z.number().transform(val => new decimal_js_1.Decimal(val))).optional(),
    totalSupply: zod_1.z.instanceof(decimal_js_1.Decimal).or(zod_1.z.number().transform(val => new decimal_js_1.Decimal(val))).optional(),
    circulatingSupply: zod_1.z.instanceof(decimal_js_1.Decimal).or(zod_1.z.number().transform(val => new decimal_js_1.Decimal(val))).optional(),
    holders: zod_1.z.number().optional(),
    transactions24h: zod_1.z.number().optional(),
    priceHigh24h: zod_1.z.instanceof(decimal_js_1.Decimal).or(zod_1.z.number().transform(val => new decimal_js_1.Decimal(val))).optional(),
    priceLow24h: zod_1.z.instanceof(decimal_js_1.Decimal).or(zod_1.z.number().transform(val => new decimal_js_1.Decimal(val))).optional()
});
// Type guards
const isWatchlistItem = (obj) => {
    return (typeof obj === 'object' &&
        obj !== null &&
        typeof obj.id === 'string' &&
        typeof obj.tokenAddress === 'string' &&
        typeof obj.tokenSymbol === 'string' &&
        typeof obj.tokenName === 'string' &&
        typeof obj.isPinned === 'boolean' &&
        typeof obj.isActive === 'boolean' &&
        (obj.addedAt instanceof Date || typeof obj.addedAt === 'string') &&
        (obj.updatedAt instanceof Date || typeof obj.updatedAt === 'string'));
};
exports.isWatchlistItem = isWatchlistItem;
// Type guard for TokenSnapshot
const isTokenSnapshot = (obj) => {
    return (typeof obj === 'object' &&
        obj !== null &&
        typeof obj.tokenAddress === 'string' &&
        obj.priceUsd instanceof decimal_js_1.Decimal &&
        obj.lastUpdated instanceof Date &&
        typeof obj.source === 'string');
};
exports.isTokenSnapshot = isTokenSnapshot;
// Type guard for TokenMetrics
const isTokenMetrics = (obj) => {
    return (typeof obj === 'object' &&
        obj !== null &&
        (obj.marketCap === undefined || obj.marketCap instanceof decimal_js_1.Decimal) &&
        (obj.totalSupply === undefined || obj.totalSupply instanceof decimal_js_1.Decimal) &&
        (obj.circulatingSupply === undefined || obj.circulatingSupply instanceof decimal_js_1.Decimal));
};
exports.isTokenMetrics = isTokenMetrics;
// Type guard for WatchlistItemWithMetrics
const isWatchlistItemWithMetrics = (obj) => {
    return ((0, exports.isWatchlistItem)(obj) &&
        ('snapshot' in obj ? (obj.snapshot === undefined || (0, exports.isTokenSnapshot)(obj.snapshot)) : true) &&
        ('metrics' in obj ? (obj.metrics === undefined || (0, exports.isTokenMetrics)(obj.metrics)) : true));
};
exports.isWatchlistItemWithMetrics = isWatchlistItemWithMetrics;
// Constants
exports.WATCHLIST_LIMITS = {
    MAX_ITEMS: 100,
    MAX_PINNED: 10,
    MAX_CUSTOM_NAME_LENGTH: 100,
    MAX_NOTES_LENGTH: 1000,
    MAX_SYMBOL_LENGTH: 20,
    MAX_NAME_LENGTH: 100
};
// Error messages
exports.WATCHLIST_ERRORS = {
    INVALID_TOKEN_ADDRESS: 'Invalid Solana token address format',
    DUPLICATE_TOKEN: 'Token already exists in watchlist',
    ITEM_NOT_FOUND: 'Watchlist item not found',
    MAX_ITEMS_REACHED: 'Maximum number of watchlist items reached',
    MAX_PINNED_REACHED: 'Maximum number of pinned items reached',
    INVALID_UPDATE_DATA: 'Invalid update data provided'
};
//# sourceMappingURL=watchlist.js.map