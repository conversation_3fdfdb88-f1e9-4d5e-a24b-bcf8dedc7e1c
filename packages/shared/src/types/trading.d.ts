import { Decimal } from 'decimal.js';
export type ExitStrategyType = 'take_profit' | 'stop_loss' | 'trailing_stop' | 'time_based';
export interface TakeProfitTier {
    id: string;
    percentage: Decimal;
    priceTarget: Decimal;
    isExecuted: boolean;
    executedAt?: Date;
    transactionSignature?: string;
}
export interface StopLossConfig {
    priceTarget: Decimal;
    isTrailing: boolean;
    trailingDistance?: Decimal;
    isExecuted: boolean;
    executedAt?: Date;
    transactionSignature?: string;
}
export interface ExitStrategy {
    id: string;
    positionId: string;
    type: ExitStrategyType;
    isActive: boolean;
    takeProfitTiers: TakeProfitTier[];
    stopLoss?: StopLossConfig;
    timeBasedExit?: Date;
    createdAt: Date;
    updatedAt: Date;
}
export interface JupiterQuote {
    inputMint: string;
    inAmount: string;
    outputMint: string;
    outAmount: string;
    otherAmountThreshold: string;
    swapMode: 'ExactIn' | 'ExactOut';
    slippageBps: number;
    priceImpactPct: string;
    routePlan: any[];
    quoteId?: string;
}
export interface TradeExecutionRequest {
    positionId?: string;
    tokenAddress: string;
    amountSol: Decimal;
    slippageBps: number;
    isEntry: boolean;
    jupiterQuote?: JupiterQuote;
}
/**
 * Trading activity metrics for watchlist influence
 */
export interface TokenTradingHistory {
    tokenAddress: string;
    totalTrades: number;
    successfulTrades: number;
    failedTrades: number;
    totalVolumeUsd: Decimal;
    totalPnlUsd: Decimal;
    averageHoldTimeMinutes: number;
    lastTradeDate: Date;
    firstTradeDate: Date;
    tradingPairs: string[];
}
/**
 * Individual trade record for detailed analysis
 */
export interface TradeRecord {
    id: string;
    tokenAddress: string;
    tradeType: 'buy' | 'sell';
    amountIn: Decimal;
    amountOut: Decimal;
    priceUsd: Decimal;
    slippageBps: number;
    gasFeeSol: Decimal;
    timestamp: Date;
    success: boolean;
    errorMessage?: string;
    holdTimeMinutes?: number;
    pnlUsd?: Decimal;
    signature?: string;
}
/**
 * Trading performance metrics
 */
export interface TradingPerformanceMetrics {
    winRate: number;
    averagePnlUsd: Decimal;
    averageHoldTimeMinutes: number;
    bestTradeUsd: Decimal;
    worstTradeUsd: Decimal;
    totalFeesUsd: Decimal;
    sharpeRatio?: number;
    maxDrawdownUsd: Decimal;
    profitFactor: number;
}
/**
 * Watchlist priority scoring based on trading activity
 */
export interface WatchlistPriorityScore {
    tokenAddress: string;
    baseScore: number;
    tradingInfluenceScore: number;
    finalScore: number;
    lastUpdated: Date;
    tradingInfluenceFactors: {
        winRateBonus: number;
        volumeBonus: number;
        recentActivityBonus: number;
        holdTimeOptimization: number;
        profitFactorBonus: number;
    };
}
/**
 * Priority scoring algorithm configuration
 */
export interface PriorityScoreConfig {
    weights: {
        baseScore: number;
        tradingInfluence: number;
    };
    tradingInfluenceWeights: {
        winRate: number;
        volume: number;
        recentActivity: number;
        holdTime: number;
        profitFactor: number;
    };
    timeDecayConfig: {
        recentDays: number;
        decayRate: number;
    };
}
/**
 * Trading activity callbacks for real-time updates
 */
export interface TradingActivityCallbacks {
    onTradeExecuted: (trade: TradeRecord) => void;
    onTradeCompleted: (trade: TradeRecord) => void;
    onTradeFailed: (trade: Partial<TradeRecord> & {
        errorMessage: string;
    }) => void;
    onPositionClosed: (trade: TradeRecord) => void;
}
/**
 * Integration hooks for trading-watchlist communication
 */
export interface TradingWatchlistIntegration {
    emitTradeEvent: (event: string, data: any) => void;
    onWatchlistUpdate: (callback: (tokens: string[]) => void) => void;
    onPriorityChange: (callback: (scores: WatchlistPriorityScore[]) => void) => void;
    getTradingHistory: (tokenAddress: string) => Promise<TokenTradingHistory | null>;
    getPerformanceMetrics: (tokenAddress: string) => Promise<TradingPerformanceMetrics | null>;
    getPriorityScore: (tokenAddress: string) => Promise<WatchlistPriorityScore | null>;
    getConversionMetrics: () => Promise<{
        watchlistToTradingRate: number;
        averageTimeToTrade: number;
        mostTradedFromWatchlist: string[];
    }>;
}
/**
 * Analytics tracking for workflow optimization
 */
export interface UserJourneyMetrics {
    sessionId: string;
    userId: string;
    journeyStart: Date;
    journeyEnd?: Date;
    steps: UserJourneyStep[];
    conversionRate: number;
    timeToConversion?: number;
    dropOffPoint?: string;
}
export interface UserJourneyStep {
    stepId: string;
    stepName: string;
    timestamp: Date;
    duration: number;
    metadata?: Record<string, any>;
}
/**
 * A/B testing framework types
 */
export interface ABTestVariant {
    id: string;
    name: string;
    description: string;
    isControl: boolean;
    weight: number;
    config: Record<string, any>;
}
export interface ABTestExperiment {
    id: string;
    name: string;
    description: string;
    status: 'draft' | 'active' | 'paused' | 'completed';
    startDate: Date;
    endDate?: Date;
    variants: ABTestVariant[];
    targetMetric: string;
    minimumSampleSize: number;
    confidenceLevel: number;
}
export interface ABTestAssignment {
    userId: string;
    experimentId: string;
    variantId: string;
    assignedAt: Date;
}
/**
 * Future API endpoint specifications (for design reference)
 */
export interface TradingHistoryAPI {
    getTokenTradingHistory: (tokenAddress: string) => Promise<TokenTradingHistory>;
    getTokenPerformance: (tokenAddress: string) => Promise<TradingPerformanceMetrics>;
    recordTradingActivity: (trade: TradeRecord) => Promise<void>;
    getPriorityScores: (tokenAddresses: string[]) => Promise<WatchlistPriorityScore[]>;
    updatePriorityConfig: (config: PriorityScoreConfig) => Promise<void>;
    getUserJourneyMetrics: (filters: {
        startDate: Date;
        endDate: Date;
        userId?: string;
    }) => Promise<UserJourneyMetrics[]>;
    getConversionMetrics: (filters: {
        startDate: Date;
        endDate: Date;
    }) => Promise<{
        watchlistToTradingRate: number;
        averageTimeToTrade: number;
        topPerformingTokens: string[];
    }>;
}
//# sourceMappingURL=trading.d.ts.map