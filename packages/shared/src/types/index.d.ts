export * from './position';
export * from './trading';
export * from './watchlist';
export * from './external-api';
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}
export interface ApiError {
    code: string;
    message: string;
    details?: any;
}
export interface HealthCheckResponse {
    status: 'healthy' | 'unhealthy';
    timestamp: string;
    uptime: number;
    version: string;
    services?: {
        database?: 'connected' | 'disconnected' | 'error';
        redis?: 'connected' | 'disconnected' | 'error';
        external_apis?: {
            jupiter?: 'connected' | 'disconnected' | 'error';
            helius?: 'connected' | 'disconnected' | 'error';
            coinmarketcap?: 'connected' | 'disconnected' | 'error';
        };
    };
}
//# sourceMappingURL=index.d.ts.map