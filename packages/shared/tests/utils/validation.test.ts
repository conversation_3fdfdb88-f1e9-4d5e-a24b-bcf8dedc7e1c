import { describe, it, expect } from 'vitest';
import { Decimal } from 'decimal.js';
import {
  SolanaAddressSchema,
  SlippageSchema,
  PositionCreateSchema,
  WatchlistCreateSchema,
  isValidSolanaAddress,
  validateSlippage
} from '../../src/utils/validation';

describe('Validation Utilities', () => {
  describe('SolanaAddressSchema', () => {
    it('should validate correct Solana addresses', () => {
      const validAddress = 'So11111111111111111111111111111111111111112';
      expect(() => SolanaAddressSchema.parse(validAddress)).not.toThrow();
    });

    it('should reject invalid length addresses', () => {
      const shortAddress = 'So11111111111111111111111111111111111111';
      expect(() => SolanaAddressSchema.parse(shortAddress)).toThrow('must be exactly 44 characters');
    });

    it('should reject addresses with invalid characters', () => {
      const invalidAddress = 'So11111111111111111111111111111111111111#2';
      expect(() => SolanaAddressSchema.parse(invalidAddress)).toThrow('Invalid Solana address format');
    });
  });

  describe('SlippageSchema', () => {
    it('should accept valid slippage values', () => {
      expect(() => SlippageSchema.parse(100)).not.toThrow(); // 1%
      expect(() => SlippageSchema.parse(50)).not.toThrow();  // 0.5%
      expect(() => SlippageSchema.parse(1000)).not.toThrow(); // 10%
    });

    it('should reject slippage below minimum', () => {
      expect(() => SlippageSchema.parse(5)).toThrow('must be at least');
    });

    it('should reject slippage above maximum', () => {
      expect(() => SlippageSchema.parse(1500)).toThrow('must not exceed');
    });
  });

  describe('PositionCreateSchema', () => {
    const validPositionData = {
      tokenAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
      tokenSymbol: 'BONK',
      tokenName: 'Bonk',
      entryPrice: new Decimal('0.00002340'),
      quantity: new Decimal('42735042.73'),
      entryAmountSol: new Decimal('1.0'),
      transactionSignature: '5VfyKRmfRfyZmBUfcY7xXMtpqwJ3sLqP8V4v5hCtZzPb6Z9Nw2xBkW7qpJ2Qf6XPm1Y4sK3vRmBnCtXzNhQs8rK',
      slippage: new Decimal('100')
    };

    it('should validate correct position data', () => {
      expect(() => PositionCreateSchema.parse(validPositionData)).not.toThrow();
    });

    it('should reject negative entry price', () => {
      const invalidData = { ...validPositionData, entryPrice: new Decimal('-1') };
      expect(() => PositionCreateSchema.parse(invalidData)).toThrow('Entry price must be positive');
    });

    it('should reject zero quantity', () => {
      const invalidData = { ...validPositionData, quantity: new Decimal('0') };
      expect(() => PositionCreateSchema.parse(invalidData)).toThrow('Quantity must be positive');
    });

    it('should reject invalid transaction signature length', () => {
      const invalidData = { ...validPositionData, transactionSignature: 'short' };
      expect(() => PositionCreateSchema.parse(invalidData)).toThrow('must be exactly 88 characters');
    });
  });

  describe('WatchlistCreateSchema', () => {
    const validWatchlistData = {
      tokenAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
      tokenSymbol: 'BONK',
      tokenName: 'Bonk',
      customName: 'My Bonk',
      notes: 'Testing token'
    };

    it('should validate correct watchlist data', () => {
      expect(() => WatchlistCreateSchema.parse(validWatchlistData)).not.toThrow();
    });

    it('should accept optional fields as undefined', () => {
      const { customName, notes, ...requiredData } = validWatchlistData;
      expect(() => WatchlistCreateSchema.parse(requiredData)).not.toThrow();
    });

    it('should reject custom name that is too long', () => {
      const longName = 'a'.repeat(51);
      const invalidData = { ...validWatchlistData, customName: longName };
      expect(() => WatchlistCreateSchema.parse(invalidData)).toThrow();
    });
  });

  describe('Utility Functions', () => {
    describe('isValidSolanaAddress', () => {
      it('should return true for valid addresses', () => {
        expect(isValidSolanaAddress('So11111111111111111111111111111111111111112')).toBe(true);
      });

      it('should return false for invalid addresses', () => {
        expect(isValidSolanaAddress('invalid')).toBe(false);
      });
    });

    describe('validateSlippage', () => {
      it('should return true for valid slippage', () => {
        expect(validateSlippage(100)).toBe(true);
      });

      it('should return false for invalid slippage', () => {
        expect(validateSlippage(5)).toBe(false);
        expect(validateSlippage(1500)).toBe(false);
      });
    });
  });
});