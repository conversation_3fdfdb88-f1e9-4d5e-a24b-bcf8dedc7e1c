import { describe, it, expect } from 'vitest';
import { Decimal } from 'decimal.js';
import {
  calculatePnLSol,
  calculatePnLPercentage,
  calculateCurrentValue,
  bpsToDecimal,
  decimalToBps
} from '../../src/utils/calculations';

describe('Calculations Utilities', () => {
  describe('calculatePnLSol', () => {
    it('should calculate positive PnL correctly', () => {
      const entry = new Decimal('1.0');
      const current = new Decimal('1.5');
      const result = calculatePnLSol(entry, current);
      expect(result.toString()).toBe('0.5');
    });

    it('should calculate negative PnL correctly', () => {
      const entry = new Decimal('1.0');
      const current = new Decimal('0.8');
      const result = calculatePnLSol(entry, current);
      expect(result.toString()).toBe('-0.2');
    });

    it('should handle zero current value', () => {
      const entry = new Decimal('1.0');
      const current = new Decimal('0');
      const result = calculatePnLSol(entry, current);
      expect(result.toString()).toBe('-1');
    });

    it('should throw error for NaN values', () => {
      const entry = new Decimal(NaN);
      const current = new Decimal('1.0');
      expect(() => calculatePnLSol(entry, current)).toThrow('Invalid input: NaN values not allowed');
    });

    it('should throw error for negative values', () => {
      const entry = new Decimal('-1.0');
      const current = new Decimal('1.0');
      expect(() => calculatePnLSol(entry, current)).toThrow('Invalid input: Negative values not allowed');
    });
  });

  describe('calculatePnLPercentage', () => {
    it('should calculate positive percentage correctly', () => {
      const entry = new Decimal('1.0');
      const current = new Decimal('1.5');
      const result = calculatePnLPercentage(entry, current);
      expect(result.toString()).toBe('50');
    });

    it('should calculate negative percentage correctly', () => {
      const entry = new Decimal('1.0');
      const current = new Decimal('0.8');
      const result = calculatePnLPercentage(entry, current);
      expect(result.toString()).toBe('-20');
    });

    it('should return zero for zero entry amount', () => {
      const entry = new Decimal('0');
      const current = new Decimal('1.0');
      const result = calculatePnLPercentage(entry, current);
      expect(result.toString()).toBe('0');
    });

    it('should handle 100% loss', () => {
      const entry = new Decimal('1.0');
      const current = new Decimal('0');
      const result = calculatePnLPercentage(entry, current);
      expect(result.toString()).toBe('-100');
    });
  });

  describe('calculateCurrentValue', () => {
    it('should calculate position value correctly', () => {
      const quantity = new Decimal('1000');
      const price = new Decimal('0.001');
      const result = calculateCurrentValue(quantity, price);
      expect(result.toString()).toBe('1');
    });
  });

  describe('bpsToDecimal', () => {
    it('should convert basis points to decimal', () => {
      expect(bpsToDecimal(100).toString()).toBe('0.01'); // 1%
      expect(bpsToDecimal(50).toString()).toBe('0.005'); // 0.5%
      expect(bpsToDecimal(10000).toString()).toBe('1'); // 100%
    });
  });

  describe('decimalToBps', () => {
    it('should convert decimal to basis points', () => {
      expect(decimalToBps(new Decimal('0.01'))).toBe(100); // 1%
      expect(decimalToBps(new Decimal('0.005'))).toBe(50); // 0.5%
      expect(decimalToBps(new Decimal('1'))).toBe(10000); // 100%
    });
  });
});