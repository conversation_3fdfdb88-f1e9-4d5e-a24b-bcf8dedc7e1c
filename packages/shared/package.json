{"name": "@shared/types", "version": "1.0.0", "private": true, "main": "src/index.ts", "types": "src/index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/**/*.ts", "typecheck": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "clean": "rm -rf dist node_modules"}, "dependencies": {"decimal.js": "^10.4.3", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.11.0", "typescript": "^5.3.3", "vitest": "^1.2.2"}}