{"name": "@ui/components", "version": "1.0.0", "private": true, "main": "src/index.ts", "types": "src/index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/**/*.{ts,tsx}", "typecheck": "tsc --noEmit", "test": "vitest", "clean": "rm -rf dist node_modules"}, "dependencies": {"react": "^18.2.0", "@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "tailwind-merge": "^2.2.0"}, "devDependencies": {"@types/react": "^18.2.0", "@shared/types": "*", "typescript": "^5.3.3", "vitest": "^1.2.2"}}