{"name": "@config/eslint", "version": "1.0.0", "description": "Shared ESLint configuration for the monorepo", "main": "base.js", "files": ["base.js", "react.js", "node.js"], "dependencies": {"@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0"}, "peerDependencies": {"eslint": "^8.56.0"}}