module.exports = {
  extends: ['./base.js'],
  env: {
    node: true,
    es6: true
  },
  rules: {
    // Allow console in Node.js applications (for logging)
    'no-console': 'warn',
    
    // Node.js specific rules
    '@typescript-eslint/no-var-requires': 'off',
    'no-process-env': 'error', // Prevent direct process.env access
    'no-process-exit': 'warn',
    'no-sync': 'warn',
    
    // Buffer and global rules for Node.js
    'no-buffer-constructor': 'error',
    'no-new-require': 'error',
    'no-path-concat': 'error',
    
    // Security rules for server-side code
    'no-eval': 'error',
    'no-implied-eval': 'error',
    'no-new-func': 'error',
    'no-script-url': 'error'
  },
  overrides: [
    {
      files: ['**/config.ts', '**/config.js'],
      rules: {
        'no-process-env': 'off' // Config files need process.env access
      }
    },
    {
      files: ['**/*.test.ts', '**/*.spec.ts'],
      rules: {
        'no-console': 'off',
        '@typescript-eslint/no-explicit-any': 'off'
      }
    }
  ]
};