{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "declaration": true, "declarationMap": true, "sourceMap": true, "baseUrl": ".", "paths": {"@shared/*": ["../../packages/shared/src/*"], "@ui/*": ["../../packages/ui/src/*"], "@config/*": ["../../packages/config/*"]}}, "exclude": ["node_modules", "dist", "build"]}