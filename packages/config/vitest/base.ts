import { defineConfig } from 'vitest/config';
import path from 'path';

export const baseConfig = defineConfig({
  test: {
    globals: true,
    environment: 'node',
    setupFiles: [],
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/.next/**',
      '**/coverage/**',
      '**/.nyc_output/**'
    ],
    testTimeout: 10000,
    hookTimeout: 10000,
    teardownTimeout: 5000,
    isolate: true,
    passWithNoTests: true,
    watch: false,
    reporter: process.env.CI ? 'junit' : 'verbose',
    outputFile: {
      junit: './coverage/junit.xml'
    },
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov', 'json-summary'],
      reportsDirectory: './coverage',
      include: [
        'src/**/*.{js,ts,jsx,tsx}'
      ],
      exclude: [
        'coverage/**',
        'dist/**',
        'build/**',
        '.next/**',
        'node_modules/**',
        '**/*.d.ts',
        '**/*.config.*',
        '**/vitest.config.*',
        '**/.eslintrc.*',
        '**/test/**',
        '**/tests/**',
        '**/__tests__/**',
        '**/*.test.*',
        '**/*.spec.*',
        '**/mock*',
        '**/__mocks__/**',
        '**/fixtures/**',
        '**/setup.*',
        '**/*types.*',
        '**/*.types.*'
      ],
      thresholds: {
        global: {
          branches: 75,
          functions: 85,
          lines: 80,
          statements: 80
        },
        // Per-file thresholds for critical files
        './src/lib/config.*': {
          branches: 90,
          functions: 95,
          lines: 90,
          statements: 90
        },
        './src/middleware/auth.*': {
          branches: 85,
          functions: 90,
          lines: 85,
          statements: 85
        },
        './src/middleware/errorHandler.*': {
          branches: 80,
          functions: 90,
          lines: 80,
          statements: 80
        }
      },
      all: true,
      clean: true,
      cleanOnRerun: true
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(process.cwd(), 'src'),
      '@shared': path.resolve(process.cwd(), '../../packages/shared/src'),
      '@config': path.resolve(process.cwd(), '../../packages/config')
    }
  }
});

export default baseConfig;