import { defineConfig, mergeConfig } from 'vitest/config';
import { baseConfig } from './base';

export const backendConfig = defineConfig({
  test: {
    environment: 'node',
    setupFiles: ['./tests/setup.ts'],
    include: [
      'src/**/*.{test,spec}.{js,mjs,cjs,ts}',
      'tests/**/*.{test,spec}.{js,mjs,cjs,ts}'
    ],
    testTimeout: 15000, // Longer timeout for database operations
    hookTimeout: 15000,
    pool: 'forks', // Better for database tests
    poolOptions: {
      forks: {
        singleFork: true // Prevent database connection conflicts
      }
    },
    sequence: {
      hooks: 'stack' // Better for database setup/teardown
    }
  },
  esbuild: {
    target: 'node20'
  }
});

export default mergeConfig(baseConfig, backendConfig);