import { defineConfig, mergeConfig } from 'vitest/config';
import { baseConfig } from './base';

export const frontendConfig = defineConfig({
  test: {
    environment: 'happy-dom',
    setupFiles: ['./tests/setup.ts'],
    include: [
      'src/**/*.{test,spec}.{js,mjs,cjs,ts,tsx}',
      'tests/**/*.{test,spec}.{js,mjs,cjs,ts,tsx}'
    ],
    globals: true,
    css: true // Enable CSS processing in tests
  },
  esbuild: {
    target: 'esnext'
  }
});

export default mergeConfig(baseConfig, frontendConfig);