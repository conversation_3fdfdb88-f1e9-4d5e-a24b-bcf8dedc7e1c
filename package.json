{"name": "solana-trading-app", "version": "1.0.0", "description": "A sophisticated Solana trading application with automated exit strategies", "private": true, "workspaces": ["apps/*", "packages/*"], "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "scripts": {"build": "npm run build --workspaces --if-present", "build:web": "npm run build --workspace=apps/web", "build:api": "npm run build --workspace=apps/api", "dev": "concurrently --kill-others-on-fail \"npm run dev --workspace=apps/api\" \"npm run dev --workspace=apps/web\"", "dev:web": "npm run dev --workspace=apps/web", "dev:api": "npm run dev --workspace=apps/api", "dev:services": "npm run docker:up", "test": "npm run test --workspaces --if-present", "test:unit": "npm run test:unit --workspaces --if-present", "test:integration": "npm run test:integration --workspaces --if-present", "test:e2e": "npm run test:e2e --workspace=apps/web", "test:coverage": "npm run test:coverage --workspaces --if-present", "test:coverage:report": "npm run test:coverage && npm run coverage:merge", "coverage:merge": "node scripts/merge-coverage.js", "coverage:badges": "node scripts/generate-coverage-badge.js", "lint": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "lint:check": "eslint . --ext .ts,.tsx,.js,.jsx", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md,yml,yaml}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md,yml,yaml}\"", "typecheck": "npm run typecheck --workspaces --if-present", "clean": "npm run clean --workspaces --if-present && rm -rf node_modules", "db:migrate": "npm run db:migrate --workspace=apps/api", "db:seed": "npm run db:seed --workspace=apps/api", "db:studio": "npm run db:studio --workspace=apps/api", "db:reset": "npm run db:reset --workspace=apps/api", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "setup": "npm install && npm run docker:up && npm run db:migrate && npm run db:seed", "setup:fresh": "npm run clean && npm install && npm run docker:down && npm run docker:up && npm run db:reset && npm run db:seed", "check": "npm run lint:check && npm run format:check && npm run typecheck && npm run test", "fix": "npm run lint && npm run format", "start": "npm run build && concurrently \"npm run start --workspace=apps/api\" \"npm run start --workspace=apps/web\"", "start:api": "npm run build:api && npm run start --workspace=apps/api", "start:web": "npm run build:web && npm run start --workspace=apps/web", "prepare": "husky install", "postinstall": "husky install", "validate:env": "./scripts/validate-env.sh", "wallet:create": "node scripts/wallet-manager.js create", "wallet:secrets": "node scripts/wallet-manager.js secrets", "wallet:info": "node scripts/wallet-manager.js info", "wallet:export": "node scripts/wallet-manager.js export", "wallet:validate": "node scripts/wallet-manager.js validate"}, "devDependencies": {"@types/node": "^20.11.0", "concurrently": "^8.2.2", "typescript": "^5.3.3", "eslint": "^8.56.0", "prettier": "^3.2.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "husky": "^8.0.3", "lint-staged": "^15.2.0", "@commitlint/cli": "^18.6.0", "@commitlint/config-conventional": "^18.6.0"}, "keywords": ["solana", "trading", "defi", "crypto", "jupiter", "meme-coins"], "author": "Developer", "license": "MIT", "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}