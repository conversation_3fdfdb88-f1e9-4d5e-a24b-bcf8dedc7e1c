#!/bin/bash

# Environment validation script
set -e

echo "🔍 Validating environment configuration..."

# Check if .env file exists
if [ ! -f ".env" ]; then
  echo "❌ .env file not found!"
  echo "📝 Copy .env.example to .env and configure your environment:"
  echo "   cp .env.example .env"
  exit 1
fi

# Source the environment file
source .env

# Track validation errors
ERRORS=0

# Function to check required variable
check_required() {
  local var_name=$1
  local var_value=$2
  local description=$3
  
  if [ -z "$var_value" ]; then
    echo "❌ $var_name is required ($description)"
    ((ERRORS++))
  else
    echo "✅ $var_name is set"
  fi
}

# Function to check optional variable
check_optional() {
  local var_name=$1
  local var_value=$2
  local description=$3
  
  if [ -z "$var_value" ]; then
    echo "⚠️  $var_name is not set ($description)"
  else
    echo "✅ $var_name is set"
  fi
}

# Function to validate minimum length
check_min_length() {
  local var_name=$1
  local var_value=$2
  local min_length=$3
  local description=$4
  
  if [ -z "$var_value" ]; then
    echo "❌ $var_name is required ($description)"
    ((ERRORS++))
  elif [ ${#var_value} -lt $min_length ]; then
    echo "❌ $var_name must be at least $min_length characters long"
    ((ERRORS++))
  else
    echo "✅ $var_name meets length requirements"
  fi
}

echo ""
echo "📋 Checking core configuration..."
check_required "NODE_ENV" "$NODE_ENV" "runtime environment"
check_required "PORT" "$PORT" "server port"
check_required "HOST" "$HOST" "server host"

echo ""
echo "🗄️  Checking database configuration..."
check_required "DATABASE_URL" "$DATABASE_URL" "PostgreSQL connection string"

echo ""
echo "🔄 Checking Redis configuration..."
if [ -n "$REDIS_URL" ]; then
  echo "✅ REDIS_URL is set (using URL configuration)"
else
  check_required "REDIS_HOST" "$REDIS_HOST" "Redis host"
  check_required "REDIS_PORT" "$REDIS_PORT" "Redis port"
  check_optional "REDIS_PASSWORD" "$REDIS_PASSWORD" "Redis authentication"
fi

echo ""
echo "🔒 Checking security configuration..."
check_min_length "SESSION_SECRET" "$SESSION_SECRET" 32 "session encryption key"
check_min_length "ADMIN_PASSWORD" "$ADMIN_PASSWORD" 8 "admin dashboard password"

echo ""
echo "🔑 Checking external API keys..."
check_optional "HELIUS_API_KEY" "$HELIUS_API_KEY" "Helius RPC API for better reliability"
check_optional "COINMARKETCAP_API_KEY" "$COINMARKETCAP_API_KEY" "price data and token metadata"

echo ""
echo "💰 Checking wallet configuration..."
if [ "$NODE_ENV" = "production" ]; then
  check_required "WALLET_SECRET_KEY" "$WALLET_SECRET_KEY" "wallet private key for signing"
  check_required "WALLET_PUBLIC_KEY" "$WALLET_PUBLIC_KEY" "wallet public key"
else
  check_optional "WALLET_SECRET_KEY" "$WALLET_SECRET_KEY" "wallet private key for development"
  check_optional "WALLET_PUBLIC_KEY" "$WALLET_PUBLIC_KEY" "wallet public key"
fi

echo ""
echo "⚙️  Checking trading configuration..."
check_optional "DEFAULT_SLIPPAGE_BPS" "$DEFAULT_SLIPPAGE_BPS" "default slippage tolerance"
check_optional "MAX_POSITION_SIZE_SOL" "$MAX_POSITION_SIZE_SOL" "maximum position size"

echo ""
echo "🔧 Checking job queue configuration..."
check_optional "JOB_QUEUE_CONCURRENCY" "$JOB_QUEUE_CONCURRENCY" "job processing concurrency"
check_optional "PRICE_MONITOR_INTERVAL_MS" "$PRICE_MONITOR_INTERVAL_MS" "price monitoring frequency"

# Production-specific validations
if [ "$NODE_ENV" = "production" ]; then
  echo ""
  echo "🏭 Checking production-specific configuration..."
  
  # Check for insecure default values in production
  if [[ "$SESSION_SECRET" == *"your-super-secure"* ]]; then
    echo "❌ SESSION_SECRET contains default placeholder value in production"
    ((ERRORS++))
  fi
  
  if [[ "$ADMIN_PASSWORD" == *"your-secure"* ]]; then
    echo "❌ ADMIN_PASSWORD contains default placeholder value in production"
    ((ERRORS++))
  fi
  
  if [[ "$WALLET_SECRET_KEY" == *"your_base58"* ]]; then
    echo "❌ WALLET_SECRET_KEY contains default placeholder value in production"
    ((ERRORS++))
  fi
fi

echo ""
if [ $ERRORS -eq 0 ]; then
  echo "🎉 Environment validation passed! All required variables are properly configured."
  exit 0
else
  echo "❌ Environment validation failed with $ERRORS error(s)."
  echo ""
  echo "📋 Next steps:"
  echo "1. Review the .env.example file for guidance"
  echo "2. Update your .env file with the missing or invalid values"
  echo "3. Run this validation script again: npm run validate:env"
  echo ""
  exit 1
fi