#!/bin/bash

# Comprehensive test script
set -e

echo "🧪 Running comprehensive test suite..."

# Parse command line arguments
RUN_UNIT=true
RUN_INTEGRATION=true
RUN_E2E=true
RUN_LINT=true
RUN_TYPE=true
COVERAGE=false

while [[ $# -gt 0 ]]; do
  case $1 in
    --unit-only)
      RUN_INTEGRATION=false
      RUN_E2E=false
      shift
      ;;
    --integration-only)
      RUN_UNIT=false
      RUN_E2E=false
      shift
      ;;
    --e2e-only)
      RUN_UNIT=false
      RUN_INTEGRATION=false
      shift
      ;;
    --no-lint)
      RUN_LINT=false
      shift
      ;;
    --no-typecheck)
      RUN_TYPE=false
      shift
      ;;
    --coverage)
      COVERAGE=true
      shift
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 [--unit-only|--integration-only|--e2e-only] [--no-lint] [--no-typecheck] [--coverage]"
      exit 1
      ;;
  esac
done

# Ensure Docker services are running for integration tests
if [ "$RUN_INTEGRATION" = true ] || [ "$RUN_E2E" = true ]; then
  echo "🐳 Ensuring Docker services are running..."
  if ! docker-compose ps | grep -q "Up"; then
    echo "Starting Docker services..."
    npm run docker:up
    echo "⏱️  Waiting for services to be ready..."
    sleep 10
  fi
fi

# Run linting
if [ "$RUN_LINT" = true ]; then
  echo ""
  echo "🔍 Running linting checks..."
  npm run lint:check
  npm run format:check
  echo "✅ Linting passed"
fi

# Run type checking
if [ "$RUN_TYPE" = true ]; then
  echo ""
  echo "📝 Running type checks..."
  npm run typecheck
  echo "✅ Type checking passed"
fi

# Run unit tests
if [ "$RUN_UNIT" = true ]; then
  echo ""
  echo "🧪 Running unit tests..."
  if [ "$COVERAGE" = true ]; then
    npm run test:unit -- --coverage
  else
    npm run test:unit
  fi
  echo "✅ Unit tests passed"
fi

# Run integration tests
if [ "$RUN_INTEGRATION" = true ]; then
  echo ""
  echo "🔗 Running integration tests..."
  if [ "$COVERAGE" = true ]; then
    npm run test:integration -- --coverage
  else
    npm run test:integration
  fi
  echo "✅ Integration tests passed"
fi

# Run E2E tests
if [ "$RUN_E2E" = true ]; then
  echo ""
  echo "🎭 Running E2E tests..."
  npm run test:e2e
  echo "✅ E2E tests passed"
fi

echo ""
echo "🎉 All tests completed successfully!"

if [ "$COVERAGE" = true ]; then
  echo ""
  echo "📊 Coverage reports generated in coverage/ directories"
fi