#!/bin/bash

# Development environment setup script
set -e

echo "🚀 Setting up BMad Solana Trading App development environment..."

# Check for required tools
echo "📋 Checking prerequisites..."

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2)
REQUIRED_NODE="20.0.0"
if [ "$(printf '%s\n' "$REQUIRED_NODE" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_NODE" ]; then
  echo "❌ Node.js version $REQUIRED_NODE or higher is required. Found: $NODE_VERSION"
  exit 1
fi
echo "✅ Node.js version: $NODE_VERSION"

# Check npm version
NPM_VERSION=$(npm --version)
REQUIRED_NPM="10.0.0"
if [ "$(printf '%s\n' "$REQUIRED_NPM" "$NPM_VERSION" | sort -V | head -n1)" != "$REQUIRED_NPM" ]; then
  echo "❌ npm version $REQUIRED_NPM or higher is required. Found: $NPM_VERSION"
  exit 1
fi
echo "✅ npm version: $NPM_VERSION"

# Check Docker
if ! command -v docker &> /dev/null; then
  echo "❌ Docker is required but not installed"
  exit 1
fi
echo "✅ Docker is available"

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
  echo "❌ Docker Compose is required but not installed"
  exit 1
fi
echo "✅ Docker Compose is available"

echo ""
echo "📦 Installing dependencies..."
npm install

echo ""
echo "🐳 Starting Docker services..."
npm run docker:up

echo ""
echo "⏱️  Waiting for services to be ready..."
sleep 10

echo ""
echo "🗄️  Setting up database..."
npm run db:migrate
npm run db:seed

echo ""
echo "🔍 Running health checks..."
if npm run test:integration > /dev/null 2>&1; then
  echo "✅ Services are healthy"
else
  echo "⚠️  Health check warnings (services may still be starting)"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "To start development:"
echo "  npm run dev          # Start both API and Web"
echo "  npm run dev:api      # Start API only"
echo "  npm run dev:web      # Start Web only"
echo ""
echo "Other useful commands:"
echo "  npm run check        # Run all quality checks"
echo "  npm run fix          # Fix linting and formatting"
echo "  npm run test         # Run all tests"
echo "  npm run docker:logs  # View service logs"
echo ""