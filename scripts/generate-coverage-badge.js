#!/usr/bin/env node

/**
 * Coverage Badge Generator
 * Generates coverage badges for README and CI/CD pipelines
 */

const fs = require('fs');
const path = require('path');

/**
 * Generate badge color based on coverage percentage
 */
function getBadgeColor(percentage) {
  if (percentage >= 90) return 'brightgreen';
  if (percentage >= 80) return 'green';
  if (percentage >= 70) return 'yellowgreen';
  if (percentage >= 60) return 'yellow';
  if (percentage >= 50) return 'orange';
  return 'red';
}

/**
 * Generate shield.io badge URL
 */
function generateBadgeUrl(label, value, color) {
  return `https://img.shields.io/badge/${encodeURIComponent(label)}-${encodeURIComponent(value)}-${color}`;
}

/**
 * Generate coverage badges
 */
function generateCoverageBadges() {
  const summaryFile = path.join('coverage', 'coverage-summary.json');
  
  if (!fs.existsSync(summaryFile)) {
    console.log('❌ No coverage summary found. Run tests with coverage first.');
    return false;
  }

  try {
    const summary = JSON.parse(fs.readFileSync(summaryFile, 'utf8'));
    const totals = summary.total;

    const badges = {
      lines: {
        label: 'Coverage-Lines',
        value: `${totals.lines.pct}%`,
        color: getBadgeColor(totals.lines.pct),
        url: generateBadgeUrl('Coverage-Lines', `${totals.lines.pct}%`, getBadgeColor(totals.lines.pct))
      },
      functions: {
        label: 'Coverage-Functions',
        value: `${totals.functions.pct}%`,
        color: getBadgeColor(totals.functions.pct),
        url: generateBadgeUrl('Coverage-Functions', `${totals.functions.pct}%`, getBadgeColor(totals.functions.pct))
      },
      statements: {
        label: 'Coverage-Statements',
        value: `${totals.statements.pct}%`,
        color: getBadgeColor(totals.statements.pct),
        url: generateBadgeUrl('Coverage-Statements', `${totals.statements.pct}%`, getBadgeColor(totals.statements.pct))
      },
      branches: {
        label: 'Coverage-Branches',
        value: `${totals.branches.pct}%`,
        color: getBadgeColor(totals.branches.pct),
        url: generateBadgeUrl('Coverage-Branches', `${totals.branches.pct}%`, getBadgeColor(totals.branches.pct))
      },
      overall: {
        label: 'Coverage',
        value: `${Math.round((totals.lines.pct + totals.functions.pct + totals.statements.pct + totals.branches.pct) / 4)}%`,
        color: getBadgeColor(Math.round((totals.lines.pct + totals.functions.pct + totals.statements.pct + totals.branches.pct) / 4)),
        url: generateBadgeUrl('Coverage', `${Math.round((totals.lines.pct + totals.functions.pct + totals.statements.pct + totals.branches.pct) / 4)}%`, getBadgeColor(Math.round((totals.lines.pct + totals.functions.pct + totals.statements.pct + totals.branches.pct) / 4)))
      }
    };

    // Write badges JSON
    fs.writeFileSync(
      path.join('coverage', 'badges.json'),
      JSON.stringify(badges, null, 2)
    );

    // Generate markdown badges
    const markdownBadges = Object.entries(badges)
      .map(([key, badge]) => `![${badge.label}](${badge.url})`)
      .join(' ');

    fs.writeFileSync(
      path.join('coverage', 'badges.md'),
      `# Coverage Badges\n\n${markdownBadges}\n\n## Individual Metrics\n\n${
        Object.entries(badges)
          .map(([key, badge]) => `- **${badge.label}**: ${badge.value} ![${badge.label}](${badge.url})`)
          .join('\n')
      }\n`
    );

    console.log('🏆 Coverage badges generated:');
    console.log('─'.repeat(60));
    Object.entries(badges).forEach(([key, badge]) => {
      console.log(`${badge.label}: ${badge.value} (${badge.color})`);
    });
    console.log('─'.repeat(60));
    console.log('📄 Badges saved to:');
    console.log('   coverage/badges.json (JSON format)');
    console.log('   coverage/badges.md (Markdown format)');

    return true;
  } catch (error) {
    console.error('❌ Error generating coverage badges:', error.message);
    return false;
  }
}

/**
 * Main function
 */
function main() {
  console.log('🎯 Generating coverage badges...\n');
  
  const success = generateCoverageBadges();

  if (!success) {
    console.log('\n💡 To generate badges, first run:');
    console.log('   npm run test:coverage:report');
    process.exit(1);
  }

  console.log('\n✅ Coverage badges generated successfully!');
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { generateCoverageBadges, getBadgeColor };