#!/usr/bin/env node

/**
 * Coverage Report Merger
 * Combines coverage reports from multiple workspaces into a unified report
 */

const fs = require('fs');
const path = require('path');

// Coverage report paths
const COVERAGE_DIRS = [
  'apps/api/coverage',
  'apps/web/coverage'
];

const OUTPUT_DIR = 'coverage';

/**
 * Merge coverage summary reports
 */
function mergeCoverageSummary() {
  const mergedSummary = {
    total: {
      lines: { total: 0, covered: 0, skipped: 0, pct: 0 },
      functions: { total: 0, covered: 0, skipped: 0, pct: 0 },
      statements: { total: 0, covered: 0, skipped: 0, pct: 0 },
      branches: { total: 0, covered: 0, skipped: 0, pct: 0 }
    }
  };

  let hasData = false;

  COVERAGE_DIRS.forEach(dir => {
    const summaryFile = path.join(dir, 'coverage-summary.json');
    
    if (fs.existsSync(summaryFile)) {
      try {
        const summary = JSON.parse(fs.readFileSync(summaryFile, 'utf8'));
        
        if (summary.total) {
          hasData = true;
          
          // Merge totals
          ['lines', 'functions', 'statements', 'branches'].forEach(metric => {
            if (summary.total[metric]) {
              mergedSummary.total[metric].total += summary.total[metric].total || 0;
              mergedSummary.total[metric].covered += summary.total[metric].covered || 0;
              mergedSummary.total[metric].skipped += summary.total[metric].skipped || 0;
            }
          });
        }
        
        console.log(`✅ Merged coverage from ${dir}`);
      } catch (error) {
        console.warn(`⚠️ Failed to read coverage summary from ${dir}:`, error.message);
      }
    } else {
      console.log(`📁 No coverage summary found in ${dir}`);
    }
  });

  if (hasData) {
    // Calculate percentages
    ['lines', 'functions', 'statements', 'branches'].forEach(metric => {
      const total = mergedSummary.total[metric].total;
      const covered = mergedSummary.total[metric].covered;
      mergedSummary.total[metric].pct = total > 0 ? Math.round((covered / total) * 100 * 100) / 100 : 0;
    });

    // Ensure output directory exists
    if (!fs.existsSync(OUTPUT_DIR)) {
      fs.mkdirSync(OUTPUT_DIR, { recursive: true });
    }

    // Write merged summary
    fs.writeFileSync(
      path.join(OUTPUT_DIR, 'coverage-summary.json'),
      JSON.stringify(mergedSummary, null, 2)
    );

    console.log('📊 Merged Coverage Summary:');
    console.log('─'.repeat(60));
    console.log(`Lines:      ${mergedSummary.total.lines.pct.toFixed(2)}% (${mergedSummary.total.lines.covered}/${mergedSummary.total.lines.total})`);
    console.log(`Functions:  ${mergedSummary.total.functions.pct.toFixed(2)}% (${mergedSummary.total.functions.covered}/${mergedSummary.total.functions.total})`);
    console.log(`Statements: ${mergedSummary.total.statements.pct.toFixed(2)}% (${mergedSummary.total.statements.covered}/${mergedSummary.total.statements.total})`);
    console.log(`Branches:   ${mergedSummary.total.branches.pct.toFixed(2)}% (${mergedSummary.total.branches.covered}/${mergedSummary.total.branches.total})`);
    console.log('─'.repeat(60));

    // Check thresholds
    checkThresholds(mergedSummary.total);
    
    return true;
  } else {
    console.log('❌ No coverage data found to merge');
    return false;
  }
}

/**
 * Check coverage thresholds
 */
function checkThresholds(totals) {
  const thresholds = {
    lines: 80,
    functions: 85,
    statements: 80,
    branches: 75
  };

  let allPassed = true;
  const results = [];

  Object.entries(thresholds).forEach(([metric, threshold]) => {
    const pct = totals[metric].pct;
    const passed = pct >= threshold;
    
    if (!passed) {
      allPassed = false;
    }
    
    results.push({
      metric,
      pct,
      threshold,
      passed,
      status: passed ? '✅' : '❌'
    });
  });

  console.log('\n🎯 Coverage Thresholds:');
  console.log('─'.repeat(60));
  results.forEach(({ metric, pct, threshold, status }) => {
    console.log(`${status} ${metric.padEnd(11)}: ${pct.toFixed(2).padStart(6)}% (threshold: ${threshold}%)`);
  });
  console.log('─'.repeat(60));

  if (allPassed) {
    console.log('🎉 All coverage thresholds passed!');
  } else {
    console.log('⚠️ Some coverage thresholds not met');
  }

  return allPassed;
}

/**
 * Generate HTML report link
 */
function generateReportInfo() {
  const htmlReports = [];
  
  COVERAGE_DIRS.forEach(dir => {
    const htmlDir = path.join(dir, 'index.html');
    if (fs.existsSync(htmlDir)) {
      htmlReports.push(htmlDir);
    }
  });

  if (htmlReports.length > 0) {
    console.log('\n📋 HTML Coverage Reports:');
    htmlReports.forEach(report => {
      const fullPath = path.resolve(report);
      console.log(`🔗 file://${fullPath}`);
    });
  }
}

/**
 * Main function
 */
function main() {
  console.log('🔄 Merging coverage reports...\n');
  
  const success = mergeCoverageSummary();
  generateReportInfo();

  if (!success) {
    console.log('\n💡 To generate coverage reports, run:');
    console.log('   npm run test:coverage');
    process.exit(1);
  }

  console.log('\n✅ Coverage reports merged successfully!');
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { mergeCoverageSummary, checkThresholds };