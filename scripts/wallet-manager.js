#!/usr/bin/env node

/**
 * Wallet Management CLI
 * Utility for generating and managing development wallets securely
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Simple base58 implementation for Node.js
function base58Encode(buffer) {
  const alphabet = '*********************************************************';
  const base = alphabet.length;
  
  if (buffer.length === 0) return '';
  
  let result = [];
  let carry = 0;
  
  for (let byte of buffer) {
    carry = byte;
    for (let i = 0; i < result.length; i++) {
      carry += result[i] * 256;
      result[i] = carry % base;
      carry = Math.floor(carry / base);
    }
    while (carry > 0) {
      result.push(carry % base);
      carry = Math.floor(carry / base);
    }
  }
  
  // Count leading zeros
  let leadingZeros = 0;
  for (let byte of buffer) {
    if (byte === 0) leadingZeros++;
    else break;
  }
  
  // Add leading zeros
  for (let i = 0; i < leadingZeros; i++) {
    result.push(0);
  }
  
  return result.reverse().map(i => alphabet[i]).join('');
}

function generateKeypair() {
  // Generate 64-byte keypair (32 bytes secret key + 32 bytes public key)
  const secretKey = crypto.randomBytes(32);
  
  // For demo purposes, we'll use a simplified key derivation
  // In a real implementation, you'd use proper Ed25519 key generation
  const keyHash = crypto.createHash('sha256').update(secretKey).digest();
  const publicKey = keyHash.slice(0, 32);
  
  const fullKeypair = Buffer.concat([secretKey, publicKey]);
  
  return {
    secretKey: Array.from(secretKey),
    publicKey: Array.from(publicKey),
    secretKeyBase58: base58Encode(secretKey),
    publicKeyBase58: base58Encode(publicKey),
    fullKeypair: Array.from(fullKeypair)
  };
}

function generateSecretKey() {
  const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz123456789';
  let result = '';
  for (let i = 0; i < 88; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

function generateSessionSecret(length = 64) {
  return crypto.randomBytes(length).toString('base64');
}

function createWallet() {
  console.log('🔐 Generating new development wallet...\n');
  
  // Generate mock Solana-style addresses for development
  const secretKey = generateSecretKey();
  const publicKey = generateSecretKey().substring(0, 44);
  
  const walletData = {
    secretKey,
    publicKey,
    created: new Date().toISOString(),
    warning: 'FOR DEVELOPMENT USE ONLY - DO NOT USE IN PRODUCTION'
  };
  
  // Save wallet to dev file
  const devWalletPath = path.join(process.cwd(), '.dev-wallet.json');
  fs.writeFileSync(devWalletPath, JSON.stringify(walletData, null, 2));
  fs.chmodSync(devWalletPath, 0o600);
  
  console.log('✅ Development wallet created:');
  console.log('─'.repeat(60));
  console.log(`Secret Key: ${secretKey}`);
  console.log(`Public Key: ${publicKey}`);
  console.log(`Saved to:   ${devWalletPath}`);
  console.log('─'.repeat(60));
  console.log('\n📝 Add these to your .env file:');
  console.log(`WALLET_SECRET_KEY=${secretKey}`);
  console.log(`WALLET_PUBLIC_KEY=${publicKey}`);
  console.log('\n⚠️  WARNING: This is for development only!');
  console.log('   Never use these keys with real funds or in production.');
}

function createSecuritySecrets() {
  console.log('🔑 Generating security secrets...\n');
  
  const sessionSecret = generateSessionSecret();
  const adminPassword = crypto.randomBytes(12).toString('base64').replace(/[+/=]/g, '').substring(0, 16);
  
  console.log('✅ Security secrets generated:');
  console.log('─'.repeat(60));
  console.log(`Session Secret: ${sessionSecret}`);
  console.log(`Admin Password: ${adminPassword}`);
  console.log('─'.repeat(60));
  console.log('\n📝 Add these to your .env file:');
  console.log(`SESSION_SECRET=${sessionSecret}`);
  console.log(`ADMIN_PASSWORD=${adminPassword}`);
  console.log('\n🔒 These meet the security requirements:');
  console.log(`   - Session secret: ${sessionSecret.length} characters (min: 32)`);
  console.log(`   - Admin password: ${adminPassword.length} characters (min: 8)`);
}

function showWalletInfo() {
  const devWalletPath = path.join(process.cwd(), '.dev-wallet.json');
  
  if (!fs.existsSync(devWalletPath)) {
    console.log('❌ No development wallet found.');
    console.log('   Run: npm run wallet:create');
    return;
  }
  
  try {
    const walletData = JSON.parse(fs.readFileSync(devWalletPath, 'utf8'));
    
    console.log('💼 Development Wallet Info:');
    console.log('─'.repeat(60));
    console.log(`Public Key: ${walletData.publicKey}`);
    console.log(`Created:    ${walletData.created}`);
    console.log(`File:       ${devWalletPath}`);
    console.log('─'.repeat(60));
    console.log('\n🔍 To view secret key (use with caution):');
    console.log('   npm run wallet:export');
  } catch (error) {
    console.error('❌ Error reading wallet file:', error.message);
  }
}

function exportWallet() {
  const devWalletPath = path.join(process.cwd(), '.dev-wallet.json');
  
  if (!fs.existsSync(devWalletPath)) {
    console.log('❌ No development wallet found.');
    return;
  }
  
  try {
    const walletData = JSON.parse(fs.readFileSync(devWalletPath, 'utf8'));
    
    console.log('⚠️  SENSITIVE INFORMATION - Handle with care!\n');
    console.log('💼 Development Wallet Export:');
    console.log('─'.repeat(60));
    console.log(`Secret Key: ${walletData.secretKey}`);
    console.log(`Public Key: ${walletData.publicKey}`);
    console.log('─'.repeat(60));
    console.log('\n📝 Environment variables:');
    console.log(`WALLET_SECRET_KEY=${walletData.secretKey}`);
    console.log(`WALLET_PUBLIC_KEY=${walletData.publicKey}`);
    console.log('\n⚠️  Keep these keys secure and never share them!');
  } catch (error) {
    console.error('❌ Error reading wallet file:', error.message);
  }
}

function validateEnvWallet() {
  const envPath = path.join(process.cwd(), '.env');
  
  if (!fs.existsSync(envPath)) {
    console.log('❌ No .env file found.');
    return;
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  const secretKeyMatch = envContent.match(/^WALLET_SECRET_KEY=(.+)$/m);
  const publicKeyMatch = envContent.match(/^WALLET_PUBLIC_KEY=(.+)$/m);
  const sessionSecretMatch = envContent.match(/^SESSION_SECRET=(.+)$/m);
  const adminPasswordMatch = envContent.match(/^ADMIN_PASSWORD=(.+)$/m);
  
  console.log('🔍 Environment Wallet Validation:');
  console.log('─'.repeat(60));
  
  // Check wallet keys
  if (secretKeyMatch && secretKeyMatch[1] !== 'your_base58_private_key_here') {
    console.log('✅ WALLET_SECRET_KEY is set');
  } else {
    console.log('❌ WALLET_SECRET_KEY not configured properly');
  }
  
  if (publicKeyMatch && publicKeyMatch[1] !== 'your_wallet_public_key_here') {
    console.log('✅ WALLET_PUBLIC_KEY is set');
  } else {
    console.log('❌ WALLET_PUBLIC_KEY not configured properly');
  }
  
  // Check security secrets
  if (sessionSecretMatch && sessionSecretMatch[1].length >= 32) {
    console.log(`✅ SESSION_SECRET is set (${sessionSecretMatch[1].length} chars)`);
  } else {
    console.log('❌ SESSION_SECRET not set or too short (min: 32 chars)');
  }
  
  if (adminPasswordMatch && adminPasswordMatch[1].length >= 8) {
    console.log(`✅ ADMIN_PASSWORD is set (${adminPasswordMatch[1].length} chars)`);
  } else {
    console.log('❌ ADMIN_PASSWORD not set or too short (min: 8 chars)');
  }
  
  console.log('─'.repeat(60));
}

function showHelp() {
  console.log('🔐 BMad Wallet Manager\n');
  console.log('Commands:');
  console.log('  create      Generate new development wallet');
  console.log('  secrets     Generate security secrets (session, admin)');
  console.log('  info        Show wallet information');
  console.log('  export      Export wallet keys (sensitive!)');
  console.log('  validate    Validate environment configuration');
  console.log('  help        Show this help message');
  console.log('\nExamples:');
  console.log('  npm run wallet:create');
  console.log('  npm run wallet:secrets');
  console.log('  npm run wallet:validate');
}

// Parse command line arguments
const command = process.argv[2];

switch (command) {
  case 'create':
    createWallet();
    break;
  case 'secrets':
    createSecuritySecrets();
    break;
  case 'info':
    showWalletInfo();
    break;
  case 'export':
    exportWallet();
    break;
  case 'validate':
    validateEnvWallet();
    break;
  case 'help':
  default:
    showHelp();
    break;
}