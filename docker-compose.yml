services:
  # PostgreSQL with TimescaleDB extension
  postgres:
    image: timescale/timescaledb:latest-pg15
    container_name: solana-trading-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: trading
      POSTGRES_USER: trader
      POSTGRES_PASSWORD: dev_password_123
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
      # Grant superuser privileges to trader for migrations
      POSTGRES_HOST_AUTH_METHOD: scram-sha-256
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/docker/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - trading-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U trader -d trading"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis for caching and job queues
  redis:
    image: redis:7-alpine
    container_name: solana-trading-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - trading-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s

  # pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: solana-trading-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: "False"
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: "False"
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
      - ./infrastructure/docker/pgadmin-servers.json:/pgadmin4/servers.json:ro
    networks:
      - trading-network
    depends_on:
      postgres:
        condition: service_healthy

  # Redis Commander for cache management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: solana-trading-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379:0
    ports:
      - "8081:8081"
    networks:
      - trading-network
    depends_on:
      redis:
        condition: service_healthy

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  trading-network:
    driver: bridge
    name: solana-trading-network