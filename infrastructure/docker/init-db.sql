-- Make trader user a superuser for migrations
ALTER USER trader SUPERUSER;

-- Initialize TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Create test database for tests
CREATE DATABASE trading_test OWNER trader;

-- Grant all necessary privileges to trader
GRANT ALL PRIVILEGES ON DATABASE trading TO trader;
GRANT ALL PRIVILEGES ON DATABASE trading_test TO trader;

-- Connect to main database and setup schema
\c trading;

-- Create public schema permissions for trader
GRANT ALL PRIVILEGES ON SCHEMA public TO trader;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO trader;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO trader;
GRANT USAGE ON SCHEMA public TO trader;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO trader;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON SEQUENCES TO trader;

-- Connect to test database and setup schema
\c trading_test;

-- Initialize test database with same permissions
GRANT ALL PRIVILEGES ON SCHEMA public TO trader;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO trader;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO trader;
GRANT USAGE ON SCHEMA public TO trader;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO trader;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON SEQUENCES TO trader;

-- Set timezone for both databases
SET timezone = 'UTC';

-- Back to main database
\c trading;
SET timezone = 'UTC';