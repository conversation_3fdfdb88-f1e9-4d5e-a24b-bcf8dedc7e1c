#!/bin/bash

# Check how many price history records we have
echo "Checking price history status..."
echo ""

# Run a SQL query to get counts
npx prisma db execute --stdin <<EOF 2>/dev/null | grep -A 10 "ROW" || echo "No data yet"
SELECT 
    token_symbol,
    COUNT(*) as snapshot_count,
    MIN(recorded_at) as first_snapshot,
    MAX(recorded_at) as last_snapshot,
    EXTRACT(EPOCH FROM (MAX(recorded_at) - MIN(recorded_at)))/60 as minutes_of_data
FROM price_history
GROUP BY token_symbol
ORDER BY token_symbol;
EOF

echo ""
echo "Price metrics availability:"

# Check current metrics from API
METRICS=$(curl -s http://localhost:3001/api/watchlist/metrics/history | jq -r '.items[0].priceMetrics.changes | keys[]' 2>/dev/null)

if [ -z "$METRICS" ]; then
    echo "No metrics available yet. Keep collecting data..."
else
    echo "Available time periods:"
    echo "$METRICS" | while read -r period; do
        echo "  ✓ $period"
    done
fi

echo ""
echo "Background collection status:"
ps aux | grep -E "curl.*snapshot" | grep -v grep > /dev/null && echo "  ✓ Collection is running" || echo "  ✗ Collection is NOT running"