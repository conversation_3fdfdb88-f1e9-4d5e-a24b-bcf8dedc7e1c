# =============================
# OS-specific files
# =============================
.DS_Store
Thumbs.db
Desktop.ini

# =============================
# Editor & IDE files
# =============================
.vscode/
.idea/
*.swp
*.swo
*.sublime-workspace
*.sublime-project
*.code-workspace
*.bak
*.tmp

# =============================
# Build artifacts & dependencies
# =============================
node_modules/
dist/
build/
out/
coverage/
*.egg-info/
__pycache__/
*.pyc
*.pyo
*.pyd
*.class
*.jar
*.war
*.ear

# =============================
# Log & temporary files
# =============================
*.log
*.log.*
*.tmp
*.temp
*.cache
*.pid
*.seed
*.pid.lock
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# =============================
# Environment & config files
# =============================
.env
.env.*
.envrc
*.local
*.secret
*.config
*.sqlite3
*.db
*.db-journal

# =============================
# Development specific
# =============================
# Next.js
.next/
.vercel/

# TypeScript
*.tsbuildinfo

# Testing
.nyc_output

# Prisma
prisma/migrations/

# Docker volumes
postgres_data/
redis_data/
pgadmin_data/

# =============================
# Custom ignored folders
# =============================
.ignore/
