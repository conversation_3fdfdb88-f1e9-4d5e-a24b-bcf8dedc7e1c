/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    serverActions: {
      bodySizeLimit: '2mb',
    },
  },
  transpilePackages: ['@shared/types', '@ui/components'],
  images: {
    unoptimized: true, // For Railway deployment
  },
  output: 'standalone', // For containerized deployment
  // Remove env exposure - use environment variables directly
  
  // API proxy for development - route /api/* to backend server
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://localhost:3001/api/:path*',
      },
    ];
  },
}

module.exports = nextConfig