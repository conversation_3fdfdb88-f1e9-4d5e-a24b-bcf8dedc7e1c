{"name": "@solana-trading-app/web", "version": "1.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "typecheck": "tsc --noEmit", "test": "vitest", "test:unit": "vitest run --dir src", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "clean": "rm -rf .next node_modules dist"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "decimal.js": "^10.4.3", "lucide-react": "^0.539.0", "next": "14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.2.0", "zustand": "^4.5.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.1", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/node": "^20.11.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "happy-dom": "^13.3.1", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.3", "vitest": "^1.2.2"}}