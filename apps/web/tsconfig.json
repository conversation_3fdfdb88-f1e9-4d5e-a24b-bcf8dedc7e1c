{"extends": "../../packages/config/typescript/nextjs.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/hooks/*": ["./src/hooks/*"], "@/services/*": ["./src/services/*"], "@/stores/*": ["./src/stores/*"], "@/lib/*": ["./src/lib/*"], "@/styles/*": ["./src/styles/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}