import { beforeAll, afterAll, vi } from 'vitest';
import '@testing-library/jest-dom';
import React from 'react';

// Make React globally available for JSX
(globalThis as any).React = React;

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    prefetch: vi.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
  useParams: () => ({}),
}));

// Mock Next.js Image component
vi.mock('next/image', () => ({
  default: ({ src, alt, ...props }: any) => {
    // eslint-disable-next-line @next/next/no-img-element
    return React.createElement('img', { src, alt, ...props });
  },
}));

beforeAll(() => {
  // Setup test environment - using Object.assign for readonly properties
  Object.assign(process.env, {
    NODE_ENV: 'test',
    NEXT_PUBLIC_API_URL: 'http://localhost:3001'
  });
});

afterAll(() => {
  // Cleanup if needed
});