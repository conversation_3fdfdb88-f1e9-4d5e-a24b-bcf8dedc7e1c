import { render, RenderOptions } from '@testing-library/react'
import { ReactElement, ReactNode } from 'react'
import { vi } from 'vitest'

// Mock Zustand store provider
interface MockStoreProviderProps {
  children: ReactNode
}

function MockStoreProvider({ children }: MockStoreProviderProps) {
  return (
    <div data-testid="mock-store-provider">
      {children}
    </div>
  )
}

// Custom render function with providers
const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
): ReturnType<typeof render> =>
  render(ui, {
    wrapper: MockStoreProvider,
    ...options,
  })

// Re-export everything
export * from '@testing-library/react'
export { customRender as render }

// Common test utilities
export const mockPositionData = {
  id: 'pos_123',
  symbol: 'BONK',
  currentPrice: 0.000025,
  entryPrice: 0.000020,
  quantity: 1000000,
  pnl: 50.0,
  pnlPercentage: 25.0,
  status: 'active' as const
}

export const createMockApiResponse = <T,>(data: T, delay = 0) => {
  return new Promise<T>((resolve) => {
    setTimeout(() => resolve(data), delay)
  })
}

export const createMockApiError = (message = 'API Error', status = 500) => {
  const error = new Error(message) as any
  error.status = status
  return Promise.reject(error)
}