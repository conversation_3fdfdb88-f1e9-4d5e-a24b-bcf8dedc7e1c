import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '../../utils/test-utils'
import { Button } from '../../../src/components/ui/button'

describe('Button', () => {
  it('renders with default variant and size', () => {
    render(<Button>Click me</Button>)
    
    const button = screen.getByRole('button', { name: /click me/i })
    expect(button).toBeInTheDocument()
    expect(button).toHaveClass('bg-primary')
  })

  it('renders with buy variant for trading', () => {
    render(<Button variant="buy">Buy Token</Button>)
    
    const button = screen.getByRole('button', { name: /buy token/i })
    expect(button).toHaveClass('bg-success')
  })

  it('renders with sell variant for trading', () => {
    render(<Button variant="sell">Sell Token</Button>)
    
    const button = screen.getByRole('button', { name: /sell token/i })
    expect(button).toHaveClass('bg-error')
  })

  it('renders with touch size for mobile', () => {
    render(<Button size="touch">Touch Friendly</Button>)
    
    const button = screen.getByRole('button', { name: /touch friendly/i })
    expect(button).toHaveClass('min-w-[44px]')
  })

  it('handles click events', () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    const button = screen.getByRole('button', { name: /click me/i })
    button.click()
    
    expect(handleClick).toHaveBeenCalledOnce()
  })

  it('is disabled when disabled prop is true', () => {
    render(<Button disabled>Disabled</Button>)
    
    const button = screen.getByRole('button', { name: /disabled/i })
    expect(button).toBeDisabled()
  })
})