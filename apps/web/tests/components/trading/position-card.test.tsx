import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '../../utils/test-utils'
import { PositionCard } from '../../../src/components/trading/position-card'

describe('PositionCard', () => {
  const mockProps = {
    symbol: 'BONK',
    currentPrice: 0.000025,
    entryPrice: 0.000020,
    quantity: 1000000,
    pnl: 50.0,
    pnlPercentage: 25.0
  }

  it('displays position information correctly', () => {
    render(<PositionCard {...mockProps} />)
    
    expect(screen.getByText('BONK')).toBeInTheDocument()
    expect(screen.getByText('$0.00')).toBeInTheDocument() // Current price formatted
    expect(screen.getByText('1,000,000')).toBeInTheDocument() // Quantity formatted
  })

  it('shows profit with green color', () => {
    render(<PositionCard {...mockProps} />)
    
    const pnlElement = screen.getByText(/\$50\.00.*\(25\.00%\)/)
    expect(pnlElement).toHaveClass('text-success')
  })

  it('shows loss with red color', () => {
    const lossProps = {
      ...mockProps,
      pnl: -25.0,
      pnlPercentage: -12.5
    }
    
    render(<PositionCard {...lossProps} />)
    
    const pnlElement = screen.getByText(/\$-25\.00.*\(-12\.50%\)/)
    expect(pnlElement).toHaveClass('text-error')
  })

  it('renders close button when onClose is provided', () => {
    const handleClose = vi.fn()
    render(<PositionCard {...mockProps} onClose={handleClose} />)
    
    const closeButton = screen.getByRole('button', { name: /close position/i })
    expect(closeButton).toBeInTheDocument()
  })

  it('calls onClose when close button is clicked', () => {
    const handleClose = vi.fn()
    render(<PositionCard {...mockProps} onClose={handleClose} />)
    
    const closeButton = screen.getByRole('button', { name: /close position/i })
    closeButton.click()
    
    expect(handleClose).toHaveBeenCalledOnce()
  })

  it('does not render close button when onClose is not provided', () => {
    render(<PositionCard {...mockProps} />)
    
    expect(screen.queryByRole('button', { name: /close position/i })).not.toBeInTheDocument()
  })
})