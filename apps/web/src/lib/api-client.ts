class ApiError extends <PERSON>rror {
  constructor(
    message: string,
    public status: number,
    public context?: string
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

interface RequestConfig {
  timeout?: number
  retries?: number
  retryDelay?: number
}

class ApiClient {
  private baseURL: string
  private defaultTimeout = 30000 // 30 seconds
  
  constructor(baseURL?: string) {
    // TODO: Access through config object following CLAUDE.md coding standards
    // Should use validated config, not direct process.env access
    this.baseURL = baseURL || process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001'
  }
  
  private async fetchWithRetry(
    url: string,
    options: RequestInit = {},
    config: RequestConfig = {}
  ): Promise<Response> {
    const { timeout = this.defaultTimeout, retries = 3, retryDelay = 1000 } = config
    
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)
    
    const requestOptions: RequestInit = {
      ...options,
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    }
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const response = await fetch(url, requestOptions)
        clearTimeout(timeoutId)
        
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          throw new ApiError(
            errorData.message || `HTTP ${response.status}`,
            response.status,
            errorData.context || url
          )
        }
        
        return response
      } catch (error) {
        clearTimeout(timeoutId)
        
        if (attempt === retries) {
          if (error instanceof ApiError) {
            throw error
          }
          throw new ApiError(
            error instanceof Error ? error.message : 'Network error',
            0,
            url
          )
        }
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt)))
      }
    }
    
    throw new ApiError('Max retries reached', 0, url)
  }
  
  async get<T>(endpoint: string, config?: RequestConfig): Promise<T> {
    const response = await this.fetchWithRetry(`${this.baseURL}${endpoint}`, {
      method: 'GET',
    }, config)
    
    return response.json()
  }
  
  async post<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<T> {
    const response = await this.fetchWithRetry(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    }, config)
    
    return response.json()
  }
  
  async put<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<T> {
    const response = await this.fetchWithRetry(`${this.baseURL}${endpoint}`, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    }, config)
    
    return response.json()
  }
  
  async delete<T>(endpoint: string, config?: RequestConfig): Promise<T> {
    const response = await this.fetchWithRetry(`${this.baseURL}${endpoint}`, {
      method: 'DELETE',
    }, config)
    
    return response.json()
  }
  
  // Health check method
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.get('/api/health')
  }
}

// Export singleton instance
export const apiClient = new ApiClient()
export { ApiError }
export type { RequestConfig }