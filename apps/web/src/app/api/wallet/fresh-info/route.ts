import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:3001';

// Force disable caching completely for this endpoint
export const dynamic = 'force-dynamic';
export const revalidate = 0;
export const fetchCache = 'force-no-store';

export async function GET(request: NextRequest) {
  try {
    // Add multiple cache busting parameters
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(7);
    
    const response = await fetch(`${BACKEND_URL}/api/wallet/info?t=${timestamp}&r=${random}&nocache=1`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'X-Requested-With': 'XMLHttpRequest'
      },
      cache: 'no-cache'
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    // Return response with aggressive no-cache headers
    return NextResponse.json(
      {
        ...data,
        _timestamp: timestamp,
        _random: random,
        _fresh: true
      },
      {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate, max-age=0, s-maxage=0, proxy-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
          'Last-Modified': new Date().toUTCString(),
          'ETag': `"${timestamp}-${random}"`,
          'Vary': '*',
          'X-No-Cache': '1'
        }
      }
    );
  } catch (error) {
    console.error('Error fetching fresh wallet info:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch fresh wallet info', 
        message: error instanceof Error ? error.message : 'Unknown error',
        _timestamp: Date.now()
      },
      { 
        status: 500,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      }
    );
  }
}