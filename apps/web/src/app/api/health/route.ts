import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const headers = {
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
  }

  try {
    // Basic frontend health check
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'memetrader-web',
      version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
      environment: process.env.NEXT_PUBLIC_ENVIRONMENT || 'development',
      uptime: process.uptime(),
    }

    return NextResponse.json(health, {
      status: 200,
      headers,
    })
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        service: 'memetrader-web',
        error: 'Health check failed',
      },
      {
        status: 503,
        headers,
      }
    )
  }
}