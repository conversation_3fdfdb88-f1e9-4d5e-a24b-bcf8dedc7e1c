'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Plus, Trash2, Save, Settings, TrendingUp, TrendingDown, Target, ChevronDown, Copy, Edit, Eye, BarChart3, Zap, Shield, Moon, AlertTriangle, CheckCircle, Info, ArrowRight } from 'lucide-react';

// Types for exit strategy configuration
interface TakeProfitTier {
  id: string;
  percentage: number;
  targetPercentage: number;
  isActive: boolean;
}

interface ExitStrategyConfig {
  takeProfitTiers: TakeProfitTier[];
  stopLossEnabled: boolean;
  stopLossPercentage: number;
  trailingStopEnabled: boolean;
  trailingStopDistancePercentage: number;
  moonBagEnabled: boolean;
  moonBagPercentage: number;
}

interface ExitStrategyPreset {
  id: string;
  name: string;
  description: string;
  isDefault: boolean;
  configuration: ExitStrategyConfig;
}

// Default presets with different configurations - each preset has independent settings
const DEFAULT_PRESETS: ExitStrategyPreset[] = [
  {
    id: 'conservative',
    name: 'Conservative',
    description: 'Low risk, steady gains with protective stops',
    isDefault: true,
    configuration: {
      takeProfitTiers: [
        { id: 'conservative-1', percentage: 40, targetPercentage: 20, isActive: true },
        { id: 'conservative-2', percentage: 35, targetPercentage: 40, isActive: true },
        { id: 'conservative-3', percentage: 25, targetPercentage: 75, isActive: true }
      ],
      stopLossEnabled: true,
      stopLossPercentage: 8,
      trailingStopEnabled: true,
      trailingStopDistancePercentage: 12,
      moonBagEnabled: false,
      moonBagPercentage: 0
    }
  },
  {
    id: 'aggressive',
    name: 'Aggressive',
    description: 'High risk, high reward with moon bag potential',
    isDefault: true,
    configuration: {
      takeProfitTiers: [
        { id: 'aggressive-1', percentage: 25, targetPercentage: 50, isActive: true },
        { id: 'aggressive-2', percentage: 35, targetPercentage: 150, isActive: true },
        { id: 'aggressive-3', percentage: 25, targetPercentage: 400, isActive: true }
      ],
      stopLossEnabled: true,
      stopLossPercentage: 15,
      trailingStopEnabled: false,
      trailingStopDistancePercentage: 20,
      moonBagEnabled: true,
      moonBagPercentage: 15
    }
  },
  {
    id: 'quick-flip',
    name: 'Quick Flip',
    description: 'Fast scalping with tight stops',
    isDefault: true,
    configuration: {
      takeProfitTiers: [
        { id: 'quick-flip-1', percentage: 70, targetPercentage: 8, isActive: true },
        { id: 'quick-flip-2', percentage: 30, targetPercentage: 18, isActive: true }
      ],
      stopLossEnabled: true,
      stopLossPercentage: 4,
      trailingStopEnabled: true,
      trailingStopDistancePercentage: 6,
      moonBagEnabled: false,
      moonBagPercentage: 0
    }
  },
  {
    id: 'hodl-dca',
    name: 'HODL & DCA',
    description: 'Long-term holding with large moon bag',
    isDefault: true,
    configuration: {
      takeProfitTiers: [
        { id: 'hodl-dca-1', percentage: 20, targetPercentage: 100, isActive: true },
        { id: 'hodl-dca-2', percentage: 30, targetPercentage: 300, isActive: true }
      ],
      stopLossEnabled: false,
      stopLossPercentage: 25,
      trailingStopEnabled: true,
      trailingStopDistancePercentage: 30,
      moonBagEnabled: true,
      moonBagPercentage: 50
    }
  },
  {
    id: 'swing-trade',
    name: 'Swing Trade',
    description: 'Medium-term positions with balanced risk',
    isDefault: true,
    configuration: {
      takeProfitTiers: [
        { id: 'swing-trade-1', percentage: 30, targetPercentage: 30, isActive: true },
        { id: 'swing-trade-2', percentage: 40, targetPercentage: 80, isActive: true },
        { id: 'swing-trade-3', percentage: 20, targetPercentage: 200, isActive: true }
      ],
      stopLossEnabled: true,
      stopLossPercentage: 12,
      trailingStopEnabled: true,
      trailingStopDistancePercentage: 18,
      moonBagEnabled: true,
      moonBagPercentage: 10
    }
  }
];

export default function ExitStrategiesPage() {
  const [selectedPreset, setSelectedPreset] = useState<ExitStrategyPreset>(DEFAULT_PRESETS[2]); // Quick Flip default
  const [configuration, setConfiguration] = useState<ExitStrategyConfig>(DEFAULT_PRESETS[2].configuration);
  const [activeSection, setActiveSection] = useState<'overview' | 'presets' | 'take-profit' | 'stop-loss' | 'trailing-stop' | 'moon-bag' | 'advanced'>('overview');
  const [isEditing, setIsEditing] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);
  const [savedStrategies, setSavedStrategies] = useState<ExitStrategyPreset[]>(DEFAULT_PRESETS);
  const [showCreateModal, setShowCreateModal] = useState(false);

  const handlePresetChange = (presetId: string) => {
    const preset = DEFAULT_PRESETS.find(p => p.id === presetId);
    if (preset) {
      setSelectedPreset(preset);
      setConfiguration(preset.configuration);
      setIsEditing(false);
    }
  };

  const handleCreateNewPreset = () => {
    // For now, just show an alert - in a real implementation, this would open a modal
    alert('Create New Preset functionality will be implemented in the next iteration. For now, you can edit existing presets.');
  };

  const addTakeProfitTier = () => {
    if (configuration.takeProfitTiers.length < 5) {
      const newTier: TakeProfitTier = {
        id: Date.now().toString(),
        percentage: 25,
        targetPercentage: 20,
        isActive: true
      };
      setConfiguration(prev => ({
        ...prev,
        takeProfitTiers: [...prev.takeProfitTiers, newTier]
      }));
    }
  };

  const removeTakeProfitTier = (id: string) => {
    setConfiguration(prev => ({
      ...prev,
      takeProfitTiers: prev.takeProfitTiers.filter(tier => tier.id !== id)
    }));
  };

  const updateTakeProfitTier = (id: string, updates: Partial<TakeProfitTier>) => {
    setConfiguration(prev => ({
      ...prev,
      takeProfitTiers: prev.takeProfitTiers.map(tier =>
        tier.id === id ? { ...tier, ...updates } : tier
      )
    }));
  };

  const handleSave = () => {
    console.log('Saving exit strategy configuration:', configuration);
    // TODO: Implement API call to save configuration
  };

  return (
    <div className="min-h-screen bg-gray-950 text-white">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-white mb-2 flex items-center gap-3">
                <div className="p-2 bg-green-500/20 rounded-lg">
                  <Target className="w-8 h-8 text-green-400" />
                </div>
                Exit Strategy Configuration
              </h1>
              <p className="text-gray-400 text-lg">Design and manage automated exit strategies for optimal trading performance</p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                onClick={() => setPreviewMode(!previewMode)}
                className="bg-gray-800 hover:bg-gray-700 border-gray-600"
              >
                <Eye className="w-4 h-4 mr-2" />
                {previewMode ? 'Edit Mode' : 'Preview Mode'}
              </Button>
              <Button
                onClick={handleSave}
                className="bg-green-500 hover:bg-green-600 text-white"
              >
                <Save className="w-4 h-4 mr-2" />
                Save Strategy
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card className="bg-gradient-to-br from-green-900/20 to-green-800/10 border-green-700/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-400 text-sm font-medium">Active Strategies</p>
                  <p className="text-2xl font-bold text-white">3</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-400" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-br from-blue-900/20 to-blue-800/10 border-blue-700/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-400 text-sm font-medium">Avg Success Rate</p>
                  <p className="text-2xl font-bold text-white">87.3%</p>
                </div>
                <BarChart3 className="w-8 h-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-br from-purple-900/20 to-purple-800/10 border-purple-700/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-400 text-sm font-medium">Total Profit</p>
                  <p className="text-2xl font-bold text-white">+24.7 SOL</p>
                </div>
                <TrendingUp className="w-8 h-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-br from-orange-900/20 to-orange-800/10 border-orange-700/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-400 text-sm font-medium">Risk Score</p>
                  <p className="text-2xl font-bold text-white">Medium</p>
                </div>
                <Shield className="w-8 h-8 text-orange-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2 p-1 bg-gray-900 rounded-lg border border-gray-800">
            {[
              { id: 'overview', label: 'Overview', icon: BarChart3, description: 'Strategy summary and performance' },
              { id: 'presets', label: 'Presets', icon: Settings, description: 'Pre-configured strategies' },
              { id: 'take-profit', label: 'Take Profit', icon: TrendingUp, description: 'Profit-taking tiers' },
              { id: 'stop-loss', label: 'Stop Loss', icon: TrendingDown, description: 'Risk management' },
              { id: 'trailing-stop', label: 'Trailing Stop', icon: Zap, description: 'Dynamic stop adjustment' },
              { id: 'moon-bag', label: 'Moon Bag', icon: Moon, description: 'Long-term holdings' },
              { id: 'advanced', label: 'Advanced', icon: Settings, description: 'Advanced settings' }
            ].map(({ id, label, icon: Icon, description }) => (
              <Button
                key={id}
                variant={activeSection === id ? 'default' : 'ghost'}
                onClick={() => setActiveSection(id as any)}
                className={`flex-1 min-w-0 ${
                  activeSection === id 
                    ? 'bg-green-500 hover:bg-green-600 text-white shadow-lg' 
                    : 'bg-transparent hover:bg-gray-800 text-gray-300'
                } transition-all duration-200`}
                title={description}
              >
                <Icon className="w-4 h-4 mr-2 flex-shrink-0" />
                <span className="truncate">{label}</span>
              </Button>
            ))}
          </div>
        </div>

        {/* Main Content Area */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Configuration */}
          <div className="lg:col-span-2 space-y-6">
            
            {/* Overview Section */}
            {activeSection === 'overview' && (
              <div className="space-y-6">
                <Card className="bg-gray-900 border-gray-800">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="w-5 h-5 text-green-400" />
                      Strategy Overview
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-4 bg-gray-800 rounded-lg">
                        <h4 className="font-semibold text-white mb-2">Current Strategy</h4>
                        <p className="text-2xl font-bold text-green-400">{selectedPreset.name}</p>
                        <p className="text-sm text-gray-400">{selectedPreset.description}</p>
                      </div>
                      <div className="p-4 bg-gray-800 rounded-lg">
                        <h4 className="font-semibold text-white mb-2">Risk Level</h4>
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                          <span className="text-orange-400 font-medium">Medium Risk</span>
                        </div>
                        <p className="text-sm text-gray-400 mt-1">Balanced profit/risk ratio</p>
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <h4 className="font-semibold text-white">Strategy Breakdown</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="p-3 bg-green-900/20 border border-green-700/30 rounded-lg">
                          <div className="flex items-center gap-2 mb-2">
                            <TrendingUp className="w-4 h-4 text-green-400" />
                            <span className="text-green-400 font-medium">Take Profit</span>
                          </div>
                          <p className="text-white font-bold">{configuration.takeProfitTiers.length} Tiers</p>
                          <p className="text-sm text-gray-400">
                            {configuration.takeProfitTiers.reduce((sum, tier) => sum + tier.percentage, 0)}% of position
                          </p>
                        </div>
                        
                        <div className="p-3 bg-red-900/20 border border-red-700/30 rounded-lg">
                          <div className="flex items-center gap-2 mb-2">
                            <TrendingDown className="w-4 h-4 text-red-400" />
                            <span className="text-red-400 font-medium">Stop Loss</span>
                          </div>
                          <p className="text-white font-bold">
                            {configuration.stopLossEnabled ? `${configuration.stopLossPercentage}%` : 'Disabled'}
                          </p>
                          <p className="text-sm text-gray-400">Risk protection</p>
                        </div>
                        
                        <div className="p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg">
                          <div className="flex items-center gap-2 mb-2">
                            <Zap className="w-4 h-4 text-blue-400" />
                            <span className="text-blue-400 font-medium">Trailing Stop</span>
                          </div>
                          <p className="text-white font-bold">
                            {configuration.trailingStopEnabled ? `${configuration.trailingStopDistancePercentage}%` : 'Disabled'}
                          </p>
                          <p className="text-sm text-gray-400">Dynamic adjustment</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Preset Selection */}
            {activeSection === 'presets' && (
              <div className="space-y-6">
                <Card className="bg-gray-900 border-gray-800">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="w-5 h-5 text-green-400" />
                      Strategy Presets
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {DEFAULT_PRESETS.map((preset) => (
                        <div
                          key={preset.id}
                          className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                            selectedPreset.id === preset.id
                              ? 'border-green-500 bg-green-900/20'
                              : 'border-gray-700 bg-gray-800 hover:border-gray-600'
                          }`}
                          onClick={() => handlePresetChange(preset.id)}
                        >
                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <h3 className="text-lg font-semibold text-white">{preset.name}</h3>
                              {selectedPreset.id === preset.id && (
                                <CheckCircle className="w-5 h-5 text-green-400" />
                              )}
                            </div>
                            <p className="text-gray-400 text-sm">{preset.description}</p>
                            
                            {/* Preset Configuration Summary */}
                            <div className="space-y-2">
                              <div className="flex items-center justify-between text-sm">
                                <span className="text-gray-300">Take Profit:</span>
                                <span className="text-green-400 font-medium">
                                  {preset.configuration.takeProfitTiers.length} tiers
                                </span>
                              </div>
                              <div className="flex items-center justify-between text-sm">
                                <span className="text-gray-300">Stop Loss:</span>
                                <span className={`font-medium ${preset.configuration.stopLossEnabled ? 'text-red-400' : 'text-gray-500'}`}>
                                  {preset.configuration.stopLossEnabled ? `${preset.configuration.stopLossPercentage}%` : 'Off'}
                                </span>
                              </div>
                              <div className="flex items-center justify-between text-sm">
                                <span className="text-gray-300">Trailing Stop:</span>
                                <span className={`font-medium ${preset.configuration.trailingStopEnabled ? 'text-blue-400' : 'text-gray-500'}`}>
                                  {preset.configuration.trailingStopEnabled ? `${preset.configuration.trailingStopDistancePercentage}%` : 'Off'}
                                </span>
                              </div>
                              <div className="flex items-center justify-between text-sm">
                                <span className="text-gray-300">Moon Bag:</span>
                                <span className={`font-medium ${preset.configuration.moonBagEnabled ? 'text-purple-400' : 'text-gray-500'}`}>
                                  {preset.configuration.moonBagEnabled ? `${preset.configuration.moonBagPercentage}%` : 'Off'}
                                </span>
                              </div>
                            </div>

                            {/* Take Profit Tiers Preview */}
                            <div className="space-y-1">
                              <span className="text-xs text-gray-400">Take Profit Targets:</span>
                              <div className="flex flex-wrap gap-1">
                                {preset.configuration.takeProfitTiers.map((tier, index) => (
                                  <Badge 
                                    key={index} 
                                    className="text-xs bg-green-500/20 text-green-400 border border-green-500/30"
                                  >
                                    {tier.percentage}% @ +{tier.targetPercentage}%
                                  </Badge>
                                ))}
                              </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex gap-2 pt-2">
                              <Button
                                size="sm"
                                variant="outline"
                                className="flex-1 bg-gray-700 hover:bg-gray-600 border-gray-600 text-xs"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // TODO: Implement edit functionality
                                }}
                              >
                                <Edit className="w-3 h-3 mr-1" />
                                Edit
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                className="flex-1 bg-gray-700 hover:bg-gray-600 border-gray-600 text-xs"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // TODO: Implement duplicate functionality
                                }}
                              >
                                <Copy className="w-3 h-3 mr-1" />
                                Copy
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Create New Preset Button */}
                    <div className="mt-6">
                      <Button
                        className="w-full bg-green-500 hover:bg-green-600 text-white"
                        onClick={handleCreateNewPreset}
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Create New Strategy Preset
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

        {/* Take Profit Configuration */}
        {activeSection === 'take-profit' && (
          <Card className="bg-gray-900 border-gray-800 mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-green-400" />
                Take Profit Tiers
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {configuration.takeProfitTiers.map((tier, index) => (
                <div key={tier.id} className="p-4 bg-gray-800 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-white">Tier {index + 1}</h4>
                    <div className="flex items-center gap-2">
                      <Checkbox
                        checked={tier.isActive}
                        onCheckedChange={(checked) => 
                          updateTakeProfitTier(tier.id, { isActive: !!checked })
                        }
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeTakeProfitTier(tier.id)}
                        className="text-red-400 hover:text-red-300"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm text-gray-300">Sell Percentage</Label>
                      <Input
                        type="number"
                        value={tier.percentage}
                        onChange={(e) => 
                          updateTakeProfitTier(tier.id, { percentage: Number(e.target.value) })
                        }
                        className="bg-gray-700 border-gray-600 text-white"
                        min="1"
                        max="100"
                      />
                    </div>
                    <div>
                      <Label className="text-sm text-gray-300">Target Profit %</Label>
                      <Input
                        type="number"
                        value={tier.targetPercentage}
                        onChange={(e) => 
                          updateTakeProfitTier(tier.id, { targetPercentage: Number(e.target.value) })
                        }
                        className="bg-gray-700 border-gray-600 text-white"
                        min="1"
                      />
                    </div>
                  </div>
                </div>
              ))}
              
              {configuration.takeProfitTiers.length < 5 && (
                <Button
                  onClick={addTakeProfitTier}
                  variant="outline"
                  className="w-full bg-gray-800 hover:bg-gray-700 text-gray-300 border-gray-700"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Take Profit Tier
                </Button>
              )}
            </CardContent>
          </Card>
        )}

        {/* Stop Loss Configuration */}
        {activeSection === 'stop-loss' && (
          <Card className="bg-gray-900 border-gray-800 mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingDown className="w-5 h-5 text-red-400" />
                Stop Loss
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-gray-300">Enable Stop Loss</Label>
                <Checkbox
                  checked={configuration.stopLossEnabled}
                  onCheckedChange={(checked) => 
                    setConfiguration(prev => ({ ...prev, stopLossEnabled: !!checked }))
                  }
                />
              </div>
              
              {configuration.stopLossEnabled && (
                <div>
                  <Label className="text-sm text-gray-300 mb-2 block">
                    Stop Loss Percentage: {configuration.stopLossPercentage}%
                  </Label>
                  <Input
                    type="range"
                    min="1"
                    max="50"
                    value={configuration.stopLossPercentage}
                    onChange={(e) => 
                      setConfiguration(prev => ({ 
                        ...prev, 
                        stopLossPercentage: Number(e.target.value) 
                      }))
                    }
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-400 mt-1">
                    <span>1%</span>
                    <span>50%</span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Trailing Stop Configuration */}
        {activeSection === 'trailing-stop' && (
          <Card className="bg-gray-900 border-gray-800 mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-blue-400" />
                Trailing Stop
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-gray-300">Enable Trailing Stop</Label>
                <Checkbox
                  checked={configuration.trailingStopEnabled}
                  onCheckedChange={(checked) => 
                    setConfiguration(prev => ({ ...prev, trailingStopEnabled: !!checked }))
                  }
                />
              </div>
              
              {configuration.trailingStopEnabled && (
                <div>
                  <Label className="text-sm text-gray-300 mb-2 block">
                    Trailing Distance: {configuration.trailingStopDistancePercentage}%
                  </Label>
                  <Input
                    type="range"
                    min="5"
                    max="50"
                    value={configuration.trailingStopDistancePercentage}
                    onChange={(e) => 
                      setConfiguration(prev => ({ 
                        ...prev, 
                        trailingStopDistancePercentage: Number(e.target.value) 
                      }))
                    }
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-400 mt-1">
                    <span>5%</span>
                    <span>50%</span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Moon Bag Configuration */}
        {activeSection === 'moon-bag' && (
          <Card className="bg-gray-900 border-gray-800 mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5 text-yellow-400" />
                Moon Bag
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-gray-300">Enable Moon Bag</Label>
                <Checkbox
                  checked={configuration.moonBagEnabled}
                  onCheckedChange={(checked) => 
                    setConfiguration(prev => ({ ...prev, moonBagEnabled: !!checked }))
                  }
                />
              </div>
              
              {configuration.moonBagEnabled && (
                <div>
                  <Label className="text-sm text-gray-300 mb-2 block">
                    Moon Bag Percentage: {configuration.moonBagPercentage}%
                  </Label>
                  <Input
                    type="range"
                    min="5"
                    max="50"
                    value={configuration.moonBagPercentage}
                    onChange={(e) => 
                      setConfiguration(prev => ({ 
                        ...prev, 
                        moonBagPercentage: Number(e.target.value) 
                      }))
                    }
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-400 mt-1">
                    <span>5%</span>
                    <span>50%</span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

            {/* Advanced Configuration */}
            {activeSection === 'advanced' && (
              <Card className="bg-gray-900 border-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-5 h-5 text-purple-400" />
                    Advanced Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm text-gray-300">Max Slippage (%)</Label>
                      <Input
                        type="number"
                        value="1.5"
                        className="bg-gray-700 border-gray-600 text-white"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm text-gray-300">Priority Fee (SOL)</Label>
                      <Input
                        type="number"
                        value="0.01"
                        className="bg-gray-700 border-gray-600 text-white"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label className="text-gray-300">MEV Protection</Label>
                      <Checkbox checked={true} />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label className="text-gray-300">Auto-Compound Profits</Label>
                      <Checkbox checked={false} />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label className="text-gray-300">Emergency Stop</Label>
                      <Checkbox checked={true} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Column - Strategy Summary & Actions */}
          <div className="space-y-6">
            {/* Current Strategy Card */}
            <Card className="bg-gradient-to-br from-gray-900 to-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <Target className="w-5 h-5 text-green-400" />
                    Active Strategy
                  </span>
                  <Badge className="bg-green-500 text-white">
                    {selectedPreset.isDefault ? 'Default' : 'Custom'}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-xl font-bold text-white">{selectedPreset.name}</h3>
                  <p className="text-gray-400 text-sm">{selectedPreset.description}</p>
                </div>
                
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Take Profit Tiers</span>
                    <span className="text-white font-medium">{configuration.takeProfitTiers.length}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Stop Loss</span>
                    <span className="text-white font-medium">
                      {configuration.stopLossEnabled ? `${configuration.stopLossPercentage}%` : 'Off'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Trailing Stop</span>
                    <span className="text-white font-medium">
                      {configuration.trailingStopEnabled ? `${configuration.trailingStopDistancePercentage}%` : 'Off'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Moon Bag</span>
                    <span className="text-white font-medium">
                      {configuration.moonBagEnabled ? `${configuration.moonBagPercentage}%` : 'Off'}
                    </span>
                  </div>
                </div>

                <div className="pt-4 border-t border-gray-700">
                  <div className="space-y-2">
                    <Button
                      onClick={handleSave}
                      className="w-full bg-green-500 hover:bg-green-600 text-white"
                    >
                      <Save className="w-4 h-4 mr-2" />
                      Save Strategy
                    </Button>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        className="bg-gray-800 hover:bg-gray-700 border-gray-600"
                      >
                        <Copy className="w-4 h-4 mr-2" />
                        Duplicate
                      </Button>
                      <Button
                        variant="outline"
                        className="bg-gray-800 hover:bg-gray-700 border-gray-600"
                      >
                        <Edit className="w-4 h-4 mr-2" />
                        Edit
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Performance Metrics */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5 text-blue-400" />
                  Performance
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Success Rate</span>
                    <span className="text-green-400 font-bold">87.3%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Avg Profit</span>
                    <span className="text-green-400 font-bold">+12.4%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Max Drawdown</span>
                    <span className="text-red-400 font-bold">-3.2%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Total Trades</span>
                    <span className="text-white font-bold">247</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Risk Assessment */}
            <Card className="bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5 text-orange-400" />
                  Risk Assessment
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Risk Level</span>
                    <Badge className="bg-orange-500 text-white">Medium</Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Conservative</span>
                      <span className="text-gray-400">Aggressive</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div className="bg-orange-500 h-2 rounded-full" style={{ width: '60%' }}></div>
                    </div>
                  </div>
                  <div className="text-xs text-gray-400 mt-2">
                    <div className="flex items-center gap-1">
                      <Info className="w-3 h-3" />
                      Balanced approach with moderate risk exposure
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
