import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import '@/styles/globals.css'
import { Navigation } from '@/components/layout/navigation'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { StoreProvider } from '@/components/providers/store-provider'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'MemeTrader Pro - Solana Trading App',
  description: 'Advanced automated meme coin trading platform for Solana',
  keywords: 'solana, trading, meme coins, cryptocurrency, automated trading',
  authors: [{ name: 'MemeTrader Pro Team' }],
  viewport: 'width=device-width, initial-scale=1',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <StoreProvider>
          <div className="min-h-screen bg-background flex flex-col">
            <Navigation />
            <Header />
            <main className="flex-1">
              {children}
            </main>
            <Footer />
          </div>
        </StoreProvider>
      </body>
    </html>
  )
}