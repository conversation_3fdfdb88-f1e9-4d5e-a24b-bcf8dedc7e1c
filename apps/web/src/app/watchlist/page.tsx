'use client'

import { useState, useEffect, useMemo } from 'react'
import { useWatchlistStore } from '../../stores/watchlist-store'
import { useWatchlistPolling } from '../../hooks/useWatchlistPolling'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'
import { 
  Plus, 
  RefreshCw, 
  Upload, 
  Search, 
  Filter, 
  ChevronDown,
  Star,
  MoreHorizontal,
  TrendingUp,
  TrendingDown
} from 'lucide-react'
import { WatchlistItemWithMetrics } from '../../../../../packages/shared/src/types/watchlist'
import { AddTokenDialog } from '../../components/watchlist/AddTokenDialog'
import { watchlistService } from '../../services/watchlistService'
import { priceHistoryService } from '../../services/priceHistoryService'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../../components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../components/ui/table"

export default function WatchlistPage() {
  const {
    items,
    selectedItem,
    sortBy,
    sortOrder,
    filterBy,
    searchQuery,
    getFilteredItems,
    selectItem,
    togglePinned,
    removeItem,
    setSortBy,
    setSortOrder,
    setFilterBy,
    setSearchQuery,
    updateMarketData,
    setItems,
  } = useWatchlistStore()

  const polling = useWatchlistPolling({
    enabled: true,
    onError: (error) => {
      console.error('Watchlist polling error:', error)
    }
  })

  const [showAddDialog, setShowAddDialog] = useState(false)
  const [isClient, setIsClient] = useState(false)
  
  // Reactive filteredItems that recalculates when store changes
  const filteredItems = useMemo(() => {
    return getFilteredItems()
  }, [items, searchQuery, filterBy, sortBy, sortOrder, getFilteredItems])

  // Fix hydration issues by ensuring we're on client
  useEffect(() => {
    setIsClient(true)
    
    // Clear any corrupted watchlist data from localStorage (one-time cleanup)
    const CACHE_VERSION = '1.2.0'
    const storedVersion = localStorage.getItem('watchlist-cache-version')
    if (storedVersion !== CACHE_VERSION) {
      localStorage.removeItem('watchlist-store')
      localStorage.setItem('watchlist-cache-version', CACHE_VERSION)
    }
  }, [])

  // Load initial watchlist data on page mount
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Try to get data with price history first
        const historyResponse = await priceHistoryService.getItemsWithPriceHistory()
        if (historyResponse.items) {
          setItems(historyResponse.items)
          return
        }
        
        // Fallback to regular metrics if history not available
        const response = await watchlistService.getItemsWithMetrics()
        if (response.items) {
          setItems(response.items)
        }
      } catch (error) {
        console.error('Failed to load initial watchlist data:', error)
      }
    }
    
    loadInitialData()
  }, [setItems])

  const formatPrice = (price: string | number | undefined | any) => {
    if (!price && price !== 0) return '$0.0000'
    
    // Handle Decimal objects
    let num: number
    if (typeof price === 'object' && price !== null && 'toNumber' in price) {
      num = price.toNumber()
    } else if (typeof price === 'string') {
      num = parseFloat(price)
    } else {
      num = price
    }
    if (num === 0) return '$0.0000'
    if (num >= 1) return `$${num.toFixed(4)}`
    if (num >= 0.01) return `$${num.toFixed(4)}`
    if (num >= 0.0001) return `$${num.toFixed(6)}`
    if (num >= 0.000001) return `$${num.toFixed(8)}`
    // For very small numbers, use exponential notation
    return `$${num.toExponential(2)}`
  }

  const formatPercentage = (change: string | number | undefined) => {
    if (!change) return '0.00%'
    const num = typeof change === 'string' ? parseFloat(change) : change
    // Handle very large percentages
    if (Math.abs(num) >= 1000) {
      return `${num >= 0 ? '+' : '-'}${Math.abs(num).toFixed(0)}%`
    }
    const formatted = Math.abs(num).toFixed(2)
    return `${num >= 0 ? '+' : '-'}${formatted}%`
  }

  const formatVolume = (volume: string | number | undefined) => {
    if (volume === undefined || volume === null) return 'N/A'
    if (!volume) return '$0'
    const num = typeof volume === 'string' ? parseFloat(volume) : volume
    if (num >= 1e9) return `$${(num / 1e9).toFixed(1)}B`
    if (num >= 1e6) return `$${(num / 1e6).toFixed(1)}M`
    if (num >= 1e3) return `$${(num / 1e3).toFixed(1)}K`
    return `$${num.toFixed(0)}`
  }

  const formatHolders = (holders: number | undefined) => {
    if (holders === undefined || holders === null) return 'N/A'
    if (!holders) return '0'
    if (holders >= 1e6) return `${(holders / 1e6).toFixed(2)}M`
    if (holders >= 1e3) return `${(holders / 1e3).toFixed(1)}K`
    return holders.toLocaleString()
  }

  const formatHolderChange = (change: number | undefined) => {
    if (change === undefined || change === null) return ''
    if (change === 0) return '0'
    const sign = change > 0 ? '+' : ''
    if (Math.abs(change) >= 1000) return `${sign}${(change / 1000).toFixed(1)}K`
    return `${sign}${change}`
  }

  const getChangeColor = (change: string | number | undefined) => {
    if (!change) return 'text-muted-foreground'
    const num = typeof change === 'string' ? parseFloat(change) : change
    return num >= 0 ? 'text-green-500' : 'text-red-500'
  }

  const getChangeIcon = (change: string | number | undefined) => {
    if (!change) return null
    const num = typeof change === 'string' ? parseFloat(change) : change
    return num >= 0 ? 
      <TrendingUp className="w-3 h-3 text-green-500" /> : 
      <TrendingDown className="w-3 h-3 text-red-500" />
  }

  const getLastUpdatedTime = () => {
    if (!polling.lastUpdateTimestamp) return 'Never'
    const now = new Date()
    const lastUpdate = new Date(polling.lastUpdateTimestamp)
    const diff = Math.floor((now.getTime() - lastUpdate.getTime()) / 1000)
    
    if (diff < 60) return 'less than a minute ago'
    if (diff < 3600) return `${Math.floor(diff / 60)} minutes ago`
    return lastUpdate.toLocaleTimeString()
  }

  const handleRefresh = () => {
    polling.forceRefresh()
  }

  const handleDeleteToken = async (tokenId: string) => {
    try {
      // Call the API to delete the token
      const response = await fetch(`/api/watchlist/${tokenId}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) {
        throw new Error('Failed to delete token')
      }
      
      // Remove from local store
      removeItem(tokenId)
    } catch (error) {
      console.error('Failed to delete token:', error)
      // TODO: Show error toast
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-card/50 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold tracking-tight">Watchlist</h1>
              <p className="text-sm text-muted-foreground">
                Monitor tokens and discover trading opportunities
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                Last updated: {getLastUpdatedTime()}
              </p>
            </div>
            
            <div className="flex items-center gap-3">
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleRefresh}
                disabled={polling.isPolling}
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${polling.isPolling ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              
              <Button variant="outline" size="sm">
                <Upload className="w-4 h-4 mr-2" />
                Bulk Add
              </Button>
              
              <Button onClick={() => setShowAddDialog(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Token
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-6">
        {/* Stats Section */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-lg font-semibold">Your Watchlist ({items.length} tokens)</h2>
              <p className="text-sm text-muted-foreground">
                Monitor token prices and market data. Pinned tokens update every 15 seconds.
              </p>
              <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                <span className="flex items-center gap-1">
                  <Badge variant="outline" className="text-xs px-1 py-0 text-green-600 border-green-600">✓</Badge> Verified
                </span>
                <span className="flex items-center gap-1">
                  <Badge className="text-xs px-1 py-0 bg-purple-600 text-white">DEX</Badge> Paid Profile
                </span>
                <span className="flex items-center gap-1">
                  <Badge className="text-xs px-1 py-0 bg-orange-500 text-white">🚀</Badge> Boosted
                </span>
                <span className="flex items-center gap-1">
                  <Badge variant="outline" className="text-xs px-1 py-0 text-pink-600 border-pink-600">🔥</Badge> Trending
                </span>
              </div>
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <Badge 
                  variant={polling.isPolling ? "default" : "secondary"}
                  className="flex items-center gap-1"
                >
                  <div className={`w-2 h-2 rounded-full ${polling.isPolling ? 'bg-green-500 animate-pulse' : 'bg-gray-500'}`} />
                  {polling.isPolling ? 'Live' : 'Paused'}
                </Badge>
                
                <Badge variant="outline" className="text-xs">
                  Jupiter Data
                </Badge>
                
                <span className="text-sm text-muted-foreground">
                  Updates every 1m
                </span>
                
                <span className="text-sm text-muted-foreground">
                  Last: {getLastUpdatedTime()}
                </span>
                
                <span className="text-sm text-muted-foreground">
                  {polling.currentInterval ? Math.floor(polling.currentInterval / 1000) : 60}s avg
                </span>
                
                <Button variant="ghost" size="sm">
                  <RefreshCw className="w-3 h-3" />
                </Button>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search tokens..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Filter className="w-4 h-4 mr-2" />
                    All
                    <ChevronDown className="w-4 h-4 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => setFilterBy('all')}>All Tokens</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setFilterBy('pinned')}>Pinned Only</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setFilterBy('alerts')}>With Alerts</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    Added
                    <ChevronDown className="w-4 h-4 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => setSortBy('addedAt')}>Recently Added</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortBy('tokenSymbol')}>Alphabetical</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortBy('priceChange24h')}>24h Change</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortBy('volume24h')}>Volume</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {/* Watchlist Table */}
        {!isClient ? (
          <div className="border rounded-lg p-12 text-center">
            <div className="text-muted-foreground">Loading...</div>
          </div>
        ) : filteredItems.length === 0 ? (
          <div className="border rounded-lg p-12 text-center">
            <div className="text-muted-foreground">
              {items.length === 0 ? (
                <>
                  <h3 className="text-lg font-medium mb-2">Your watchlist is empty</h3>
                  <p className="text-sm mb-4">Add tokens to start tracking their prices and performance</p>
                  <Button onClick={() => setShowAddDialog(true)}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Your First Token
                  </Button>
                </>
              ) : (
                <>
                  <h3 className="text-lg font-medium mb-2">No tokens match your filters</h3>
                  <p className="text-sm">Try adjusting your search or filter settings</p>
                </>
              )}
            </div>
          </div>
        ) : (
          <div className="border rounded-lg overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow className="hover:bg-transparent border-b">
                  <TableHead className="w-12"></TableHead>
                  <TableHead className="font-medium">Token</TableHead>
                  <TableHead className="text-right font-medium">Price</TableHead>
                  <TableHead className="text-right font-medium">5m</TableHead>
                  <TableHead className="text-right font-medium">1h</TableHead>
                  <TableHead className="text-right font-medium">24h</TableHead>
                  <TableHead className="text-right font-medium">Volume</TableHead>
                  <TableHead className="text-right font-medium">Market Cap</TableHead>
                  <TableHead className="text-right font-medium">Holders</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredItems.map((item, index) => {
                  const snapshot = item.snapshot
                  const isPinned = item.isPinned
                  // Get price from either priceMetrics (history endpoint) or snapshot (regular endpoint)
                  const currentPrice = item.priceMetrics?.currentPrice || snapshot?.priceUsd
                  // Get 24h change from either priceMetrics or snapshot
                  const change24h = item.priceMetrics?.changes?.['24h']?.changePercent || snapshot?.priceChange24h
                  // Get volume and market cap from either source
                  const volume24h = item.priceMetrics?.volume24h || snapshot?.volume24h
                  const marketCap = item.priceMetrics?.marketCap || snapshot?.fdv
                  // Get holder data from metadata
                  const holderCount = (snapshot as any)?.metadata?.holderCount
                  const holder5mChange = (snapshot as any)?.metadata?.holderChange5m
                  // Get verification and CEX data
                  const isVerified = (snapshot as any)?.metadata?.isVerified
                  const cexes = (snapshot as any)?.metadata?.cexes || []
                  const tags = (snapshot as any)?.metadata?.tags || []
                  // Get DEX Screener data
                  const hasDexPaid = (snapshot as any)?.metadata?.hasDexPaid
                  const isBoosted = (snapshot as any)?.metadata?.isBoosted
                  const boostAmount = (snapshot as any)?.metadata?.boostAmount
                  const trendingScore = (snapshot as any)?.metadata?.trendingScore
                  // Get token age and trending rank
                  const tokenAge = (snapshot as any)?.metadata?.tokenAge
                  const trendingRank = (snapshot as any)?.metadata?.trendingRank
                  
                  return (
                    <TableRow 
                      key={item.id} 
                      className="hover:bg-muted/50 cursor-pointer border-b last:border-b-0"
                      onClick={() => selectItem(item)}
                    >
                      <TableCell className="p-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="w-6 h-6 p-0"
                          onClick={(e) => {
                            e.stopPropagation()
                            togglePinned(item.id)
                          }}
                        >
                          <Star className={`w-3 h-3 ${isPinned ? 'fill-yellow-400 text-yellow-400' : 'text-muted-foreground'}`} />
                        </Button>
                      </TableCell>
                      
                      <TableCell className="p-4">
                        <div className="flex flex-col">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{(snapshot as any)?.metadata?.tokenSymbol || item.tokenSymbol}</span>
                            {tokenAge && (
                              <span className="text-xs text-muted-foreground">
                                {tokenAge}
                              </span>
                            )}
                            {trendingRank && (
                              <Badge variant="outline" className="text-xs px-1.5 py-0 text-yellow-600 border-yellow-600">
                                #{trendingRank}
                              </Badge>
                            )}
                            {isPinned && (
                              <Badge variant="secondary" className="text-xs px-1.5 py-0">
                                Pinned
                              </Badge>
                            )}
                            {isVerified && (
                              <Badge variant="outline" className="text-xs px-1.5 py-0 text-green-600 border-green-600">
                                ✓
                              </Badge>
                            )}
                            {cexes.length > 0 && (
                              <Badge variant="outline" className="text-xs px-1.5 py-0 text-blue-600 border-blue-600">
                                CEX {cexes.length}
                              </Badge>
                            )}
                            {hasDexPaid && (
                              <Badge className="text-xs px-1.5 py-0 bg-purple-600 text-white">
                                DEX PAID
                              </Badge>
                            )}
                            {isBoosted && (
                              <Badge className="text-xs px-1.5 py-0 bg-orange-500 text-white">
                                🚀 {boostAmount ? `BOOST ${boostAmount}` : 'BOOST'}
                              </Badge>
                            )}
                            {trendingScore && trendingScore > 0 && (
                              <Badge variant="outline" className="text-xs px-1.5 py-0 text-pink-600 border-pink-600">
                                🔥 {trendingScore.toFixed(0)}
                              </Badge>
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {item.customName || (snapshot as any)?.metadata?.tokenName || item.tokenName}
                          </div>
                          {item.notes && (
                            <div className="text-xs text-muted-foreground mt-1 truncate max-w-48">
                              {item.notes}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      
                      <TableCell className="text-right p-4">
                        <span className="font-mono">
                          {formatPrice(currentPrice)}
                        </span>
                      </TableCell>
                      
                      {/* 5m Change */}
                      <TableCell className="text-right p-4">
                        <div className={`flex items-center justify-end gap-1 ${getChangeColor(item.priceMetrics?.changes?.['5m']?.changePercent)}`}>
                          {getChangeIcon(item.priceMetrics?.changes?.['5m']?.changePercent)}
                          <span className="font-mono text-sm">
                            {formatPercentage(item.priceMetrics?.changes?.['5m']?.changePercent || 0)}
                          </span>
                        </div>
                      </TableCell>
                      
                      {/* 1h Change */}
                      <TableCell className="text-right p-4">
                        <div className={`flex items-center justify-end gap-1 ${getChangeColor(item.priceMetrics?.changes?.['1h']?.changePercent)}`}>
                          {getChangeIcon(item.priceMetrics?.changes?.['1h']?.changePercent)}
                          <span className="font-mono text-sm">
                            {formatPercentage(item.priceMetrics?.changes?.['1h']?.changePercent || 0)}
                          </span>
                        </div>
                      </TableCell>
                      
                      {/* 24h Change */}
                      <TableCell className="text-right p-4">
                        <div className={`flex items-center justify-end gap-1 ${getChangeColor(change24h)}`}>
                          {getChangeIcon(change24h)}
                          <span className="font-mono text-sm">
                            {formatPercentage(change24h)}
                          </span>
                        </div>
                      </TableCell>
                      
                      <TableCell className="text-right p-4">
                        <span className="font-mono text-sm">
                          {formatVolume(volume24h)}
                        </span>
                      </TableCell>
                      
                      <TableCell className="text-right p-4">
                        <span className="font-mono text-sm">
                          {formatVolume(marketCap)}
                        </span>
                      </TableCell>
                      
                      <TableCell className="text-right p-4">
                        <div className="flex flex-col items-end">
                          <span className="font-mono text-sm">
                            {formatHolders(holderCount)}
                          </span>
                          {holder5mChange !== undefined && holder5mChange !== 0 && (
                            <span className={`text-xs ${holder5mChange > 0 ? 'text-green-500' : 'text-red-500'}`}>
                              {formatHolderChange(holder5mChange)}
                            </span>
                          )}
                        </div>
                      </TableCell>
                      
                      <TableCell className="p-4">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="w-6 h-6 p-0"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <MoreHorizontal className="w-3 h-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => togglePinned(item.id)}>
                              {isPinned ? 'Unpin' : 'Pin'} Token
                            </DropdownMenuItem>
                            <DropdownMenuItem>View Details</DropdownMenuItem>
                            <DropdownMenuItem>Set Alert</DropdownMenuItem>
                            <DropdownMenuItem 
                              className="text-red-600"
                              onClick={() => handleDeleteToken(item.id)}
                            >
                              Remove
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          </div>
        )}
      </div>

      {/* Add Token Dialog */}
      <AddTokenDialog 
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
        onSuccess={(newItem) => {
          // Token added successfully - polling will update the list
        }}
      />
    </div>
  )
}