'use client'

import { useState } from 'react'
import { useWatchlistStore } from '../../stores/watchlist-store'
import { useWatchlistPolling } from '../../hooks/useWatchlistPolling'
import { WatchlistStatus } from '../../components/watchlist/WatchlistStatus'
import { WatchlistItem } from '../../components/watchlist/WatchlistItem'
import { Button } from '../../components/ui/button'
import { Card } from '../../components/ui/card'
import { Input } from '../../components/ui/input'
import { Badge } from '../../components/ui/badge'
import { Plus, Search, Filter, SortAsc } from 'lucide-react'
import { WatchlistItemWithMetrics } from '../../../packages/shared/src/types/watchlist'

export default function WatchlistPage() {
  const {
    items,
    selectedItem,
    sortBy,
    sortOrder,
    filterBy,
    searchQuery,
    getFilteredItems,
    selectItem,
    togglePinned,
    setSortBy,
    setSortOrder,
    setFilterBy,
    setSearchQuery,
  } = useWatchlistStore()

  const polling = useWatchlistPolling({
    enabled: true,
    onError: (error) => {
      console.error('Watchlist polling error:', error)
      // Could show toast notification here
    }
  })

  const [showAddForm, setShowAddForm] = useState(false)

  const filteredItems = getFilteredItems()
  const pinnedItems = items.filter(item => item.isPinned)

  const handleTogglePin = (id: string) => {
    togglePinned(id)
  }

  const handleSelectItem = (item: WatchlistItemWithMetrics) => {
    selectItem(item)
  }

  const handleSortToggle = () => {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
  }

  const getSortLabel = () => {
    const labels = {
      tokenSymbol: 'Symbol',
      priceUsd: 'Price',
      priceChange24h: 'Change',
      volume24h: 'Volume',
      addedAt: 'Added'
    }
    return labels[sortBy] || 'Symbol'
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Watchlist</h1>
          <p className="text-muted-foreground">
            Track your favorite tokens with real-time updates
          </p>
        </div>
        
        <Button onClick={() => setShowAddForm(!showAddForm)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Token
        </Button>
      </div>

      {/* Polling Status */}
      <WatchlistStatus polling={polling} />

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4 my-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search tokens..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setFilterBy(filterBy === 'all' ? 'pinned' : filterBy === 'pinned' ? 'alerts' : 'all')}
            className="flex items-center gap-1"
          >
            <Filter className="h-3 w-3" />
            {filterBy === 'all' ? 'All' : filterBy === 'pinned' ? 'Pinned' : 'Alerts'}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleSortToggle}
            className="flex items-center gap-1"
          >
            <SortAsc className="h-3 w-3" />
            {getSortLabel()} {sortOrder === 'asc' ? '↑' : '↓'}
          </Button>
        </div>
      </div>

      {/* Summary Stats */}
      {items.length > 0 && (
        <div className="flex gap-4 mb-6">
          <Badge variant="outline" className="flex items-center gap-1">
            Total: {items.length}
          </Badge>
          {pinnedItems.length > 0 && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Pinned: {pinnedItems.length}
            </Badge>
          )}
          {polling.isPolling && (
            <Badge className="flex items-center gap-1">
              Live Updates
            </Badge>
          )}
        </div>
      )}

      {/* Add Token Form */}
      {showAddForm && (
        <Card className="p-4 mb-6">
          <h3 className="font-medium mb-3">Add New Token</h3>
          <p className="text-sm text-muted-foreground">
            Token addition form would go here - requires integration with token lookup service
          </p>
        </Card>
      )}

      {/* Watchlist Items */}
      {filteredItems.length === 0 ? (
        <Card className="p-8 text-center">
          <div className="text-muted-foreground">
            {items.length === 0 ? (
              <>
                <p className="mb-2">Your watchlist is empty</p>
                <p className="text-sm">Add tokens to start tracking their prices and performance</p>
              </>
            ) : (
              <>
                <p className="mb-2">No tokens match your current filters</p>
                <p className="text-sm">Try adjusting your search or filter settings</p>
              </>
            )}
          </div>
        </Card>
      ) : (
        <div className="space-y-3">
          {filteredItems.map((item) => (
            <WatchlistItem
              key={item.id}
              item={item}
              onTogglePin={handleTogglePin}
              onSelect={handleSelectItem}
            />
          ))}
        </div>
      )}

      {/* Selected Item Details */}
      {selectedItem && (
        <Card className="mt-6 p-4">
          <h3 className="font-medium mb-2">Selected Token Details</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Name:</span> {selectedItem.tokenName}
            </div>
            <div>
              <span className="text-muted-foreground">Symbol:</span> {selectedItem.tokenSymbol}
            </div>
            <div className="col-span-2">
              <span className="text-muted-foreground">Address:</span> 
              <span className="font-mono ml-1">{selectedItem.tokenAddress}</span>
            </div>
            {selectedItem.notes && (
              <div className="col-span-2">
                <span className="text-muted-foreground">Notes:</span> {selectedItem.notes}
              </div>
            )}
          </div>
        </Card>
      )}
    </div>
  )
}