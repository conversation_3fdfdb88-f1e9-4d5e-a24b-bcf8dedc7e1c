'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useWallet } from '@/hooks/useWallet';
import { usePrice } from '@/hooks/usePrice';
import { jupiterTradingService, type Quote, type TradeResult } from '@/services/jupiterTrading';
import { tokenMetadataService, type TokenMetadata } from '@/services/tokenMetadata';
import { TokenInput } from '@/components/trading/TokenInput';
import { InlineTokenBalance } from '@/components/trading/TokenBalanceDisplay';
import { tokenBalanceService } from '@/services/tokenBalance';
import { ArrowUpDown, RotateCc<PERSON>, <PERSON>ting<PERSON>, Zap, ArrowLeft } from 'lucide-react';
import { parseSwapParams } from '@/utils/urlHelpers';
import { validateAndNormalizeTokenAddress } from '@packages/shared/utils/tokenValidation';
import { useSelectedTokenStore } from '@/stores/selectedTokenStore';
import { Breadcrumb, createWatchlistTradingBreadcrumb, createTradingBreadcrumb } from '@/components/navigation/Breadcrumb';
import { setupKeyboardNavigation, saveNavigationContext } from '@/utils/navigation';
import { ExitStrategySelector } from '@/components/exit-strategy/ExitStrategySelector';
import { exitStrategyService, type ExitStrategyPreset } from '@/services/exitStrategy';

// Trading presets
const TRADING_PRESETS = [
  { id: 'default', name: 'DEFAULT', color: 'bg-green-500', active: true },
  { id: 'vol', name: 'VOL', color: 'bg-gray-600', active: false },
  { id: 'dead', name: 'DEAD', color: 'bg-gray-600', active: false },
  { id: 'nun', name: 'NUN', color: 'bg-gray-600', active: false },
  { id: 'p5', name: 'P5', color: 'bg-gray-600', active: false },
];

// Amount presets
const AMOUNT_PRESETS = [
  { id: 'lite', name: 'LITE', percentage: 0.1 },  // 10%
  { id: 'half', name: 'HALF', percentage: 0.5 },  // 50%
  { id: 'pump', name: 'PUMP', percentage: 0.75 }, // 75%
  { id: 'max', name: 'MAX', percentage: 1.0 },    // 100%
];

export default function TradingPage() {
  // URL parameters and navigation
  const searchParams = useSearchParams();
  const router = useRouter();
  
  // Selected token store
  const { 
    selectedToken, 
    watchlistContext, 
    hasWatchlistContext, 
    getDisplayName,
    setNavigationSource 
  } = useSelectedTokenStore();
  
  // Wallet state
  const { wallet, isConnected, hasError, clearError, reconnect, hardReset } = useWallet();
  
  // Price state
  const { formatSolAsUsd } = usePrice();

  // Trading form state
  const [activePreset, setActivePreset] = useState('default');
  const [inputMint, setInputMint] = useState('So11111111111111111111111111111111111111112'); // SOL
  const [outputMint, setOutputMint] = useState('');
  const [amountSol, setAmountSol] = useState('');
  const [slippageBps, setSlippageBps] = useState(100); // 1%
  
  // Token metadata state
  const [inputTokenMetadata, setInputTokenMetadata] = useState<TokenMetadata | null>(null);
  const [outputTokenMetadata, setOutputTokenMetadata] = useState<TokenMetadata | null>(null);

  // UI state
  const [quote, setQuote] = useState<Quote | null>(null);
  const [isGettingQuote, setIsGettingQuote] = useState(false);
  const [quoteError, setQuoteError] = useState<string | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [tradeResult, setTradeResult] = useState<TradeResult | null>(null);
  const [isSwapping, setIsSwapping] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  
  // URL parameter parsing state
  const [urlParseErrors, setUrlParseErrors] = useState<string[]>([]);
  const [localNavigationSource, setLocalNavigationSource] = useState<string | null>(null);
  
  // Exit strategy state
  const [exitStrategyEnabled, setExitStrategyEnabled] = useState(false);
  const [selectedExitStrategy, setSelectedExitStrategy] = useState<ExitStrategyPreset | null>(null);
  const [isAttachingStrategy, setIsAttachingStrategy] = useState(false);
  
  // Process URL parameters on initial load
  useEffect(() => {
    if (!isInitialLoad) return;
    
    const parsedParams = parseSwapParams(searchParams);
    
    if (parsedParams.errors.length > 0) {
      setUrlParseErrors(parsedParams.errors);
    }
    
    // Set navigation source both locally and in store
    if (parsedParams.source) {
      setNavigationSource(parsedParams.source);
      setLocalNavigationSource(parsedParams.source);
    }
    
    // Pre-populate token address if provided
    if (parsedParams.mint) {
      setOutputMint(parsedParams.mint);
    }
    
    // Pre-populate amount if provided
    if (parsedParams.amount !== null) {
      setAmountSol(parsedParams.amount.toString());
    }
    
    // Pre-populate slippage if provided
    if (parsedParams.slippage !== null) {
      setSlippageBps(parsedParams.slippage);
    }
    
    setIsInitialLoad(false);
  }, [searchParams, isInitialLoad]);
  
  // Setup keyboard navigation
  useEffect(() => {
    const cleanup = setupKeyboardNavigation({
      onEscape: () => {
        if (localNavigationSource === 'watchlist') {
          handleBackToWatchlist();
        }
      },
      onBack: () => {
        if (localNavigationSource === 'watchlist') {
          handleBackToWatchlist();
        }
      }
    });
    
    return cleanup;
  }, [localNavigationSource]);
  
  // Load token metadata when addresses change
  useEffect(() => {
    if (inputMint) {
      tokenMetadataService.getTokenMetadata(inputMint)
        .then(setInputTokenMetadata)
        .catch(console.error);
    } else {
      setInputTokenMetadata(null);
    }
  }, [inputMint]);
  
  useEffect(() => {
    if (outputMint) {
      tokenMetadataService.getTokenMetadata(outputMint)
        .then(setOutputTokenMetadata)
        .catch(console.error);
    } else {
      setOutputTokenMetadata(null);
    }
  }, [outputMint]);

  // Get quote when form changes
  useEffect(() => {
    if (inputMint && outputMint && amountSol && parseFloat(amountSol) > 0 && isConnected) {
      const debounceTimer = setTimeout(() => {
        getQuote();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [inputMint, outputMint, amountSol, slippageBps, isConnected]);

  const getQuote = async () => {
    if (!inputMint || !outputMint || !amountSol || parseFloat(amountSol) <= 0) return;

    // Validate token addresses before making API call
    if (!isValidSolanaAddress(outputMint)) {
      setQuoteError('Invalid token address format');
      return;
    }

    setIsGettingQuote(true);
    setQuoteError(null);

    try {
      const quoteData = await jupiterTradingService.getQuote({
        inputMint,
        outputMint,
        amountSol: parseFloat(amountSol),
        slippageBps,
      });

      setQuote(quoteData);
    } catch (error) {
      setQuoteError(error instanceof Error ? error.message : 'Failed to get quote');
      setQuote(null);
    } finally {
      setIsGettingQuote(false);
    }
  };

  const executeTrade = async () => {
    if (!quote || !isConnected) return;

    setIsExecuting(true);
    setTradeResult(null);

    try {
      const result = await jupiterTradingService.executeTrade({
        inputMint,
        outputMint,
        amountSol: parseFloat(amountSol),
        slippageBps,
        maxPriceImpact: 10,
        simulate: false,
      });

      setTradeResult(result);
      
      if (result.success) {
        setAmountSol('');
        setQuote(null);
        
        // Attach exit strategy if enabled and selected
        if (exitStrategyEnabled && selectedExitStrategy && result.positionId) {
          try {
            setIsAttachingStrategy(true);
            await exitStrategyService.attachStrategyToPosition({
              positionId: result.positionId,
              presetId: selectedExitStrategy.id
            });
            console.log('Exit strategy attached successfully to position:', result.positionId);
          } catch (error) {
            console.error('Failed to attach exit strategy:', error);
            // Don't fail the trade if exit strategy attachment fails
          } finally {
            setIsAttachingStrategy(false);
          }
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Trade execution failed';
      setTradeResult({
        success: false,
        error: errorMessage,
      });
      setQuoteError(errorMessage);
    } finally {
      setIsExecuting(false);
    }
  };

  const setAmountByPreset = async (preset: typeof AMOUNT_PRESETS[0]) => {
    try {
      let balance = 0;
      
      if (inputMint === 'So11111111111111111111111111111111111111112') {
        // SOL balance
        balance = wallet.balance || 0;
      } else {
        // Token balance - fetch from API
        const tokenBalance = await tokenBalanceService.getTokenBalance(inputMint);
        balance = tokenBalance?.uiAmount || 0;
      }
      
      if (balance > 0) {
        const amount = balance * preset.percentage;
        // Use appropriate decimal places based on the amount size
        const decimals = amount < 0.01 ? 8 : amount < 1 ? 6 : amount < 1000 ? 4 : 2;
        setAmountSol(amount.toFixed(decimals));
      }
    } catch (error) {
      console.error('Error setting amount by preset:', error);
    }
  };

  const handlePresetChange = (presetId: string) => {
    setActivePreset(presetId);
    // In a real implementation, this would change trading parameters
  };

  const handleAmountChange = (value: string) => {
    const sanitized = value.replace(/[^0-9.]/g, '');
    setAmountSol(sanitized);
  };

  const handleTokenAddressChange = (value: string) => {
    const cleaned = value.trim();
    setOutputMint(cleaned);
    
    // Clear previous errors when user starts typing
    if (quoteError && quoteError.includes('Invalid token address')) {
      setQuoteError(null);
    }
    
    // Clear URL parse errors when user manually changes address
    if (urlParseErrors.length > 0) {
      setUrlParseErrors([]);
    }
  };

  const isValidSolanaAddress = (address: string): boolean => {
    const validation = validateAndNormalizeTokenAddress(address);
    return validation.isValid;
  };

  const swapTokens = () => {
    // Prevent swapping if output token is not set
    if (!outputMint) {
      setQuoteError('Please select a token to swap to first');
      return;
    }
    
    // Prevent swapping if input and output are the same
    if (inputMint === outputMint) {
      setQuoteError('Cannot swap the same token');
      return;
    }
    
    // Show brief swap animation
    setIsSwapping(true);
    
    // Swap the mint addresses
    const tempMint = inputMint;
    setInputMint(outputMint);
    setOutputMint(tempMint);
    
    // Swap the token metadata
    const tempMetadata = inputTokenMetadata;
    setInputTokenMetadata(outputTokenMetadata);
    setOutputTokenMetadata(tempMetadata);
    
    // Clear amount since it was for the previous "from" token
    setAmountSol('');
    
    // Clear quote and errors
    setQuote(null);
    setQuoteError(null);
    
    // Hide swap animation after a brief moment
    setTimeout(() => {
      setIsSwapping(false);
    }, 300);
  };

  const handleBackToWatchlist = () => {
    // Save current trading context before navigating back
    saveNavigationContext('/trading', {
      filters: { inputMint, outputMint, amountSol, slippageBps: slippageBps.toString() },
      timestamp: Date.now()
    });
    
    router.push('/watchlist');
  };

  return (
    <div className="min-h-screen bg-gray-950 text-white">
      <div className="max-w-2xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          {/* Breadcrumb Navigation */}
          <div className="mb-4">
            <Breadcrumb 
              items={localNavigationSource === 'watchlist' 
                ? createWatchlistTradingBreadcrumb('trading')
                : createTradingBreadcrumb()
              }
              className="mb-4"
            />
          </div>
          
          {/* Quick Back Navigation for Watchlist */}
          {localNavigationSource === 'watchlist' && (
            <div className="mb-4">
              <Button
                variant="ghost"
                onClick={handleBackToWatchlist}
                className="text-gray-400 hover:text-white hover:bg-gray-800 p-2"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Watchlist
              </Button>
            </div>
          )}
          
          <div className="text-center">
            <h1 className="text-3xl font-bold text-white mb-2">BMad Trading</h1>
            <p className="text-gray-400">Professional Solana trading platform</p>
            {localNavigationSource === 'watchlist' && (
              <Badge className="mt-2 bg-blue-600 text-white">From Watchlist</Badge>
            )}
          </div>
        </div>

        {/* Trading Presets */}
        <Card className="bg-gray-900 border-gray-800 mb-6">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg text-white">Trading Presets</CardTitle>
              <Badge className="bg-green-500 text-white">Active: {activePreset.toUpperCase()}</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-5 gap-2">
              {TRADING_PRESETS.map((preset) => (
                <Button
                  key={preset.id}
                  variant={activePreset === preset.id ? 'default' : 'outline'}
                  className={`${
                    activePreset === preset.id 
                      ? 'bg-green-500 hover:bg-green-600 text-white border-green-500' 
                      : 'bg-gray-800 hover:bg-gray-700 text-gray-300 border-gray-700'
                  } font-medium`}
                  onClick={() => handlePresetChange(preset.id)}
                >
                  {preset.name}
                </Button>
              ))}
            </div>

            <div className="grid grid-cols-3 gap-4 mt-4 text-sm">
              <div>
                <span className="text-gray-400">Priority Fee</span>
                <div className="text-white font-medium">0.01 SOL</div>
              </div>
              <div>
                <span className="text-gray-400">Slippage Limit</span>
                <div className="text-white font-medium">1.0%</div>
              </div>
              <div>
                <span className="text-gray-400">MEV Protection</span>
                <div className="text-white font-medium">Standard</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Trading Interface */}
        <Card className="bg-gray-900 border-gray-800 mb-6">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg text-white flex items-center">
                <Zap className="w-5 h-5 mr-2 text-green-500" />
                Instant Trading
              </CardTitle>
              <Badge className="bg-green-500 text-white">Live</Badge>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            {/* From Token */}
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <Label className="text-gray-400 text-sm">From</Label>
                <div className="text-sm text-gray-400">
                  {inputMint === 'So11111111111111111111111111111111111111112' ? (
                    `Balance: ${isConnected && wallet.balance !== null ? wallet.balance.toFixed(6) : '0'} SOL`
                  ) : (
                    <InlineTokenBalance 
                      tokenMint={inputMint}
                      tokenSymbol={inputTokenMetadata?.symbol}
                      className="flex items-center gap-1"
                    />
                  )}
                </div>
              </div>
              
              <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 rounded-full bg-purple-600 flex items-center justify-center">
                    <span className="text-white font-bold text-sm">
                      {inputTokenMetadata?.symbol || 'SOL'}
                    </span>
                  </div>
                  <div className="flex-1">
                    <div className="text-white font-medium">{inputTokenMetadata?.symbol || 'SOL'}</div>
                    <div className="text-gray-400 text-sm">{inputTokenMetadata?.name || 'Solana'}</div>
                  </div>
                </div>
                
                {/* Amount Input */}
                <div className="mt-4 text-right">
                  <Input
                    type="text"
                    value={amountSol}
                    onChange={(e) => handleAmountChange(e.target.value)}
                    placeholder="0.0"
                    className="bg-transparent border-0 text-2xl font-bold text-white placeholder-gray-500 text-right focus-visible:ring-0"
                    disabled={!isConnected}
                  />
                  <div className="text-gray-400 text-sm mt-1">
                    {inputMint === 'So11111111111111111111111111111111111111112' 
                      ? formatSolAsUsd(parseFloat(amountSol) || 0)
                      : `${parseFloat(amountSol).toLocaleString(undefined, { 
                          minimumFractionDigits: 0,
                          maximumFractionDigits: 4 
                        })} ${inputTokenMetadata?.symbol || 'tokens'}`
                    }
                  </div>
                </div>

                {/* Amount Presets */}
                <div className="flex space-x-2 mt-4">
                  {AMOUNT_PRESETS.map((preset) => (
                    <Button
                      key={preset.id}
                      variant="outline"
                      size="sm"
                      className="bg-gray-700 hover:bg-gray-600 text-gray-300 border-gray-600 text-xs px-3"
                      onClick={() => setAmountByPreset(preset)}
                      disabled={!isConnected}
                    >
                      {preset.name}
                    </Button>
                  ))}
                </div>
              </div>
            </div>

            {/* Swap Button */}
            <div className="flex justify-center my-4">
              <Button
                variant="ghost"
                size="sm"
                className={`rounded-full w-10 h-10 bg-gray-800 hover:bg-gray-700 border border-gray-700 transition-transform duration-300 ${
                  isSwapping ? 'rotate-180 scale-110' : ''
                }`}
                onClick={swapTokens}
                disabled={isSwapping}
              >
                <ArrowUpDown className={`w-4 h-4 ${isSwapping ? 'text-green-400' : 'text-gray-400'} transition-colors duration-300`} />
              </Button>
            </div>

            {/* To Token */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <Label className="text-gray-400 text-sm">To</Label>
                <div className="text-sm text-gray-400">
                  {outputMint ? (
                    outputMint === 'So11111111111111111111111111111111111111112' ? (
                      `Balance: ${isConnected && wallet.balance !== null ? wallet.balance.toFixed(6) : '0'} SOL`
                    ) : (
                      <InlineTokenBalance 
                        tokenMint={outputMint}
                        tokenSymbol={outputTokenMetadata?.symbol}
                        className="flex items-center gap-1"
                      />
                    )
                  ) : (
                    'Balance: 0.00 UNKNOWN'
                  )}
                </div>
              </div>
              
              <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center">
                    <span className="text-white font-bold text-sm">
                      {outputTokenMetadata?.symbol?.[0] || '?'}
                    </span>
                  </div>
                  <div className="flex-1">
                    <div className="text-gray-400 text-sm mb-1">
                      {outputTokenMetadata ? (
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <span>{outputTokenMetadata.name}</span>
                            {outputTokenMetadata.verified && (
                              <span className="text-green-500 text-xs">✓ Verified</span>
                            )}
                          </div>
                          {/* Watchlist Context */}
                          {localNavigationSource === 'watchlist' && hasWatchlistContext() && (
                            <div className="space-y-1">
                              {watchlistContext?.customName && (
                                <div className="text-blue-400 text-xs">
                                  Custom: {watchlistContext.customName}
                                </div>
                              )}
                              {watchlistContext?.notes && (
                                <div className="text-gray-500 text-xs">
                                  Notes: {watchlistContext.notes}
                                </div>
                              )}
                              {watchlistContext?.isPinned && (
                                <Badge className="bg-yellow-600 text-white text-xs">📌 Pinned</Badge>
                              )}
                            </div>
                          )}
                        </div>
                      ) : (
                        'Unknown Token'
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-white">
                      {quote ? (
                        outputMint === 'So11111111111111111111111111111111111111112' ? (
                          // SOL output - convert from lamports
                          (parseFloat(quote.outAmount) / Math.pow(10, 9)).toLocaleString(undefined, { 
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 6 
                          })
                        ) : outputTokenMetadata ? (
                          // Token output
                          tokenMetadataService.formatTokenAmount(quote.outAmount, outputTokenMetadata, 2)
                        ) : (
                          // Fallback
                          parseFloat(quote.outAmount).toLocaleString(undefined, { maximumFractionDigits: 6 })
                        )
                      ) : '0'}
                    </div>
                    <div className="text-gray-400 text-sm">
                      {outputTokenMetadata?.symbol || 'tokens'}
                    </div>
                  </div>
                </div>
                
                {/* Token Address Input */}
                <Input
                  type="text"
                  value={outputMint}
                  onChange={(e) => handleTokenAddressChange(e.target.value)}
                  placeholder="Enter token address..."
                  className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 font-mono text-sm"
                  disabled={!isConnected}
                />
                
                {/* Token shortcuts */}
                <div className="flex flex-wrap gap-2 mt-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setOutputMint('So11111111111111111111111111111111111111112')}
                    className="text-xs h-6 bg-purple-700 hover:bg-purple-600 text-white border-purple-600"
                    disabled={!isConnected}
                  >
                    SOL
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setOutputMint('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v')}
                    className="text-xs h-6 bg-gray-700 hover:bg-gray-600 text-gray-300 border-gray-600"
                    disabled={!isConnected}
                  >
                    USDC
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setOutputMint('Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB')}
                    className="text-xs h-6 bg-gray-700 hover:bg-gray-600 text-gray-300 border-gray-600"
                    disabled={!isConnected}
                  >
                    USDT
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setOutputMint('DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263')}
                    className="text-xs h-6 bg-orange-700 hover:bg-orange-600 text-white border-orange-600"
                    disabled={!isConnected}
                  >
                    BONK
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setOutputMint('EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm')}
                    className="text-xs h-6 bg-blue-700 hover:bg-blue-600 text-white border-blue-600"
                    disabled={!isConnected}
                  >
                    WIF
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setOutputMint('JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN')}
                    className="text-xs h-6 bg-green-700 hover:bg-green-600 text-white border-green-600"
                    disabled={!isConnected}
                  >
                    JUP
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setOutputMint('FYXnJhgLmhcELqsKyAfdKZExFfA7gaskQa5TuEZE9RZT')}
                    className="text-xs h-6 bg-yellow-700 hover:bg-yellow-600 text-white border-yellow-600"
                    disabled={!isConnected}
                  >
                    TEST
                  </Button>
                </div>
              </div>
            </div>

            {/* Exchange Rate */}
            {quote && (
              <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-sm text-gray-400">Exchange Rate</div>
                  <Button variant="ghost" size="sm" onClick={getQuote} disabled={isGettingQuote}>
                    <RotateCcw className={`w-4 h-4 ${isGettingQuote ? 'animate-spin' : ''}`} />
                  </Button>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">1 {inputTokenMetadata?.symbol || 'Token'} ≈ {
                      inputMint === 'So11111111111111111111111111111111111111112' ? (
                        // SOL to Token rate
                        outputTokenMetadata ? 
                          tokenMetadataService.formatTokenAmount(
                            (parseFloat(quote.outAmount) / parseFloat(amountSol || '1')).toString(),
                            outputTokenMetadata,
                            2
                          ) : 
                          ((parseFloat(quote.outAmount) / Math.pow(10, 9)) / parseFloat(amountSol || '1')).toLocaleString(undefined, { maximumFractionDigits: 2 })
                      ) : (
                        // Token to SOL rate  
                        (parseFloat(quote.outAmount) / Math.pow(10, 9) / parseFloat(amountSol || '1')).toFixed(6)
                      )
                    } {outputTokenMetadata?.symbol || 'tokens'}</span>
                    <span className="text-green-500">Min. Received: {
                      outputMint === 'So11111111111111111111111111111111111111112' ? (
                        // SOL output
                        ((parseFloat(quote.outAmount) / Math.pow(10, 9)) * 0.99).toFixed(6)
                      ) : outputTokenMetadata ? (
                        // Token output 
                        tokenMetadataService.formatTokenAmount(
                          (parseFloat(quote.outAmount) * 0.99).toString(),
                          outputTokenMetadata,
                          2
                        )
                      ) : (
                        // Fallback
                        ((parseFloat(quote.outAmount) / Math.pow(10, 9)) * 0.99).toLocaleString(undefined, { maximumFractionDigits: 2 })
                      )
                    } {outputTokenMetadata?.symbol || ''}</span>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Price Impact</span>
                    <span className={`${parseFloat(quote.priceImpactPct) > 3 ? 'text-red-500' : 'text-green-500'}`}>
                      {quote.priceImpactPct}%
                    </span>
                  </div>

                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Network Fee</span>
                    <span className="text-gray-300">~0.00025 SOL</span>
                  </div>
                </div>
              </div>
            )}

            {/* Execute Button */}
            <Button
              className="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-4 text-lg disabled:opacity-50"
              onClick={executeTrade}
              disabled={!isConnected || !quote || isExecuting || isGettingQuote}
            >
              {isExecuting ? (
                'Executing Trade...'
              ) : isGettingQuote ? (
                'Getting Quote...'
              ) : (
                <>
                  <Zap className="w-5 h-5 mr-2" />
                  Swap {inputTokenMetadata?.symbol || 'TOKEN'} → {outputTokenMetadata?.symbol || 'TOKEN'}
                </>
              )}
            </Button>

            {/* Advanced Settings */}
            <Button
              variant="ghost"
              className="w-full mt-4 text-gray-400 hover:text-white"
            >
              <Settings className="w-4 h-4 mr-2" />
              Advanced Settings
            </Button>
          </CardContent>
        </Card>

        {/* Exit Strategy Configuration */}
        <ExitStrategySelector
          onStrategySelect={setSelectedExitStrategy}
          selectedStrategy={selectedExitStrategy}
          isEnabled={exitStrategyEnabled}
          onEnabledChange={setExitStrategyEnabled}
          className="mb-6"
        />

        {/* Trade Result */}
        {tradeResult && (
          <Card className={`mb-6 ${
            tradeResult.success 
              ? 'bg-green-900/20 border-green-700' 
              : 'bg-red-900/20 border-red-700'
          }`}>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  tradeResult.success ? 'bg-green-500' : 'bg-red-500'
                }`}>
                  {tradeResult.success ? '✓' : '✗'}
                </div>
                <div>
                  <h3 className={`font-bold ${
                    tradeResult.success ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {tradeResult.success ? 'Trade Successful!' : 'Trade Failed'}
                  </h3>
                  {tradeResult.success && tradeResult.signature && (
                    <p className="text-sm text-gray-400">
                      TX: {tradeResult.signature.slice(0, 20)}...
                    </p>
                  )}
                  {tradeResult.error && (
                    <p className="text-sm text-red-400">{tradeResult.error}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* URL Parameter Errors */}
        {urlParseErrors.length > 0 && (
          <Card className="bg-yellow-900/20 border-yellow-700 mb-6">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-full bg-yellow-500 flex items-center justify-center">
                  ⚠
                </div>
                <div className="flex-1">
                  <h3 className="font-bold text-yellow-400">URL Parameter Issues</h3>
                  {urlParseErrors.map((error, index) => (
                    <p key={index} className="text-sm text-yellow-300">{error}</p>
                  ))}
                  <p className="text-xs text-yellow-400 mt-2">
                    Please check the URL parameters or manually enter the token information below.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quote Error Display */}
        {quoteError && (
          <Card className="bg-red-900/20 border-red-700 mb-6">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-full bg-red-500 flex items-center justify-center">
                  ⚠
                </div>
                <div className="flex-1">
                  <h3 className="font-bold text-red-400">Quote Error</h3>
                  <p className="text-sm text-red-300">{quoteError}</p>
                  {quoteError.includes('not tradable') && (
                    <p className="text-xs text-red-400 mt-2">
                      This token may not be available on Jupiter DEX or may be a scam token.
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Connection Warning */}
        {!isConnected && (
          <Card className="bg-yellow-900/20 border-yellow-700">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-full bg-yellow-500 flex items-center justify-center">
                  ⚠
                </div>
                <div className="flex-1">
                  <h3 className="font-bold text-yellow-400">Wallet Not Connected</h3>
                  <p className="text-sm text-yellow-300 mb-3">
                    Connect your wallet to start trading. Visit the wallet page to check your connection.
                  </p>
                  <div className="space-y-2">
                    <div className="flex space-x-2">
                      <Button 
                        onClick={reconnect}
                        variant="outline" 
                        size="sm"
                        className="bg-yellow-600 hover:bg-yellow-700 border-yellow-600 text-white"
                      >
                        Retry Connection
                      </Button>
                      <Button 
                        onClick={hardReset}
                        variant="outline" 
                        size="sm"
                        className="bg-red-600 hover:bg-red-700 border-red-600 text-white"
                      >
                        Hard Reset
                      </Button>
                    </div>
                    <div className="text-xs text-yellow-400 space-y-1">
                      <div>Address: {wallet.address || 'None'}</div>
                      <div>Balance: {wallet.balance !== null ? wallet.balance : 'Unknown'}</div>
                      <div>Network: {wallet.network || 'Unknown'}</div>
                      <div>Error: {wallet.error || 'None'}</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Debug Panel */}
        <Card className="p-4 bg-gray-800 border-gray-700 mt-4">
          <CardHeader>
            <CardTitle className="text-sm text-gray-400">Debug Info</CardTitle>
          </CardHeader>
          <CardContent className="text-xs text-gray-300 space-y-1">
            <div>Connected: {isConnected ? 'Yes' : 'No'}</div>
            <div>Has Error: {hasError ? 'Yes' : 'No'}</div>
            <div>Address: {wallet.address || 'None'}</div>
            <div>Balance: {wallet.balance !== null ? wallet.balance : 'Unknown'}</div>
            <div>Network: {wallet.network || 'Unknown'}</div>
            <div>Last Updated: {wallet.lastUpdated ? wallet.lastUpdated.toString() : 'Never'}</div>
            <div>Error: {wallet.error || 'None'}</div>
            <div className="flex space-x-2 mt-2">
              <Button onClick={reconnect} size="sm">
                Reconnect
              </Button>
              <Button onClick={hardReset} size="sm" variant="outline">
                Hard Reset
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}