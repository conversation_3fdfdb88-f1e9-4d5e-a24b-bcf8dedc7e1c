'use client';

import { WalletStatus } from '@/components/wallet/WalletStatus';
import { WalletBalance } from '@/components/wallet/WalletBalance';
import { WalletError } from '@/components/wallet/WalletError';
import { TransactionTest } from '@/components/wallet/TransactionTest';
import { TokenBalances } from '@/components/wallet/TokenBalances';
import { useWallet } from '@/hooks/useWallet';

export default function WalletPage() {
  const { wallet, hasError, clearError, refreshBalance } = useWallet();

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">Wallet Management</h1>
        <p className="text-muted-foreground">
          Manage your Solana wallet connection and monitor your balance.
        </p>
      </div>

      {/* Error Display */}
      {hasError && wallet.error && (
        <WalletError
          error={wallet.error}
          onRetry={() => window.location.reload()}
          onDismiss={clearError}
          type="connection"
        />
      )}

      {/* Main Wallet Information Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Wallet Status */}
        <WalletStatus onRefresh={refreshBalance} />

        {/* Wallet Balance */}
        <WalletBalance 
          onRefresh={(balance) => {
            console.log('Balance refreshed:', balance);
          }}
          showAutoRefresh={true}
        />
      </div>

      {/* Token Balances */}
      {wallet.isConnected && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Token Balances</h2>
          <TokenBalances 
            onTokenSelect={(token) => {
              console.log('Selected token:', token);
            }}
            showAutoRefresh={true}
          />
        </div>
      )}

      {/* Additional Information */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Wallet Information</h2>
        
        <div className="bg-gray-50 p-4 rounded-lg space-y-3 text-sm">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <span className="font-medium text-gray-700">Address:</span>
              <div className="font-mono text-gray-900 break-all">
                {wallet.address || 'Not connected'}
              </div>
            </div>
            
            <div>
              <span className="font-medium text-gray-700">Network:</span>
              <div className="flex items-center gap-2">
                <span className={wallet.isMainnet ? 'text-red-600' : 'text-yellow-600'}>
                  {wallet.isMainnet ? '🔴' : '🟡'} {wallet.network || 'Unknown'}
                </span>
                {wallet.isMainnet && (
                  <span className="text-xs bg-red-100 text-red-800 px-1 rounded font-bold">
                    LIVE TRADING
                  </span>
                )}
              </div>
            </div>

            <div>
              <span className="font-medium text-gray-700">Status:</span>
              <span className={`${wallet.isConnected ? 'text-green-600' : 'text-red-600'}`}>
                {wallet.isConnected ? '✅ Connected' : '❌ Disconnected'}
              </span>
            </div>

            <div>
              <span className="font-medium text-gray-700">Last Updated:</span>
              <span className="text-gray-900">
                {wallet.lastUpdated 
                  ? new Date(wallet.lastUpdated).toLocaleString()
                  : 'Never'
                }
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Safety Information */}
      {wallet.isMainnet && (
        <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
          <h3 className="text-red-800 font-semibold flex items-center gap-2">
            ⚠️ Mainnet Trading Safety Information
          </h3>
          <div className="text-red-700 text-sm mt-2 space-y-2">
            <p>
              You are connected to Solana mainnet-beta, which means you are trading with real money.
            </p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Transaction limits are in place to protect you from large losses</li>
              <li>Default maximum transaction: 0.05 SOL</li>
              <li>Always test with small amounts first</li>
              <li>Never trade more than you can afford to lose completely</li>
            </ul>
            <p className="font-medium">
              💡 Consider testing on devnet first if you're new to the platform.
            </p>
          </div>
        </div>
      )}

      {/* Transaction Testing */}
      {wallet.isConnected && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Transaction Testing</h2>
          <TransactionTest 
            walletAddress={wallet.address}
            walletBalance={wallet.balance}
            onTransactionComplete={(result) => {
              console.log('Transaction completed:', result);
              // Refresh balance after successful transaction
              if (result.success) {
                refreshBalance();
              }
            }}
          />
        </div>
      )}

      {/* Development Information */}
      <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
        <h3 className="text-blue-800 font-semibold flex items-center gap-2">
          🔧 Development Information
        </h3>
        <div className="text-blue-700 text-sm mt-2 space-y-2">
          <p>
            This wallet page demonstrates the wallet integration components. 
            In development mode, some features may show errors if API keys are not configured.
          </p>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs">
            <div>✅ Wallet connection and validation</div>
            <div>✅ Balance fetching (requires Helius API key)</div>
            <div>✅ Network detection and warnings</div>
            <div>✅ Error handling and user feedback</div>
            <div>✅ Transaction signing with safety limits</div>
            <div>✅ Transaction validation and simulation</div>
            <div>✅ Daily transaction limits</div>
            <div>✅ SOL transfer testing capability</div>
          </div>
        </div>
      </div>
    </div>
  );
}