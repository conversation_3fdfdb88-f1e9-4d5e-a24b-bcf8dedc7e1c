'use client'

import { ReactNode } from 'react'
import { cn } from '@/lib/utils'

interface ResponsiveGridProps {
  children: ReactNode
  className?: string
}

export function ResponsiveGrid({ children, className }: ResponsiveGridProps) {
  return (
    <div 
      className={cn(
        // Mobile-first responsive grid
        "grid gap-4",
        // Mobile (default): Single column
        "grid-cols-1",
        // Tablet: Two columns
        "md:grid-cols-2",
        // Desktop: Three columns  
        "lg:grid-cols-3",
        // Large desktop: Four columns
        "xl:grid-cols-4",
        className
      )}
    >
      {children}
    </div>
  )
}

// Mobile-specific touch-friendly button component
interface TouchButtonProps {
  children: ReactNode
  onClick?: () => void
  variant?: 'buy' | 'sell' | 'default'
  className?: string
}

export function TouchButton({ 
  children, 
  onClick, 
  variant = 'default', 
  className 
}: TouchButtonProps) {
  return (
    <button
      onClick={onClick}
      className={cn(
        // Base touch-friendly styles - minimum 44px height
        "min-h-[44px] px-6 py-3 min-w-[44px]",
        "rounded-lg font-medium text-sm",
        "transition-colors duration-200",
        "active:scale-95 transition-transform",
        
        // Variant styles
        variant === 'buy' && "bg-success text-success-foreground hover:bg-success/90",
        variant === 'sell' && "bg-error text-error-foreground hover:bg-error/90",
        variant === 'default' && "bg-primary text-primary-foreground hover:bg-primary/90",
        
        className
      )}
    >
      {children}
    </button>
  )
}

// Swipeable card wrapper for mobile
interface SwipeableCardProps {
  children: ReactNode
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  className?: string
}

export function SwipeableCard({ 
  children, 
  onSwipeLeft, 
  onSwipeRight, 
  className 
}: SwipeableCardProps) {
  // Simple touch handling - in production, use a library like react-swipeable
  return (
    <div 
      className={cn(
        "touch-pan-x select-none",
        // Mobile specific styles
        "lg:touch-auto lg:select-text", // Reset on desktop
        className
      )}
      onTouchStart={(e) => {
        // Store initial touch position
        const touch = e.touches[0]
        e.currentTarget.dataset.startX = touch.clientX.toString()
      }}
      onTouchEnd={(e) => {
        const startX = parseFloat(e.currentTarget.dataset.startX || '0')
        const endX = e.changedTouches[0].clientX
        const deltaX = endX - startX
        
        // Detect swipe (minimum 50px movement)
        if (Math.abs(deltaX) > 50) {
          if (deltaX > 0 && onSwipeRight) {
            onSwipeRight()
          } else if (deltaX < 0 && onSwipeLeft) {
            onSwipeLeft()
          }
        }
      }}
    >
      {children}
    </div>
  )
}