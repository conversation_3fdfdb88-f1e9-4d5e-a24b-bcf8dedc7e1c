'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { WalletConnectionIndicator } from '@/components/wallet/WalletConnectionIndicator'
import { useWallet } from '@/hooks/useWallet'

export function Header() {
  const { reconnect, hardReset, isLoading } = useWallet()
  const [refreshTrigger, setRefreshTrigger] = useState(Date.now()) // Start with timestamp
  
  const handleRefresh = async () => {
    // Trigger a force refresh of the WalletConnectionIndicator
    setRefreshTrigger(prev => prev + 1)
    await reconnect()
  }
  
  const handleHardReset = async () => {
    // Trigger a force refresh of the WalletConnectionIndicator  
    setRefreshTrigger(Date.now())
    await hardReset()
  }
  
  // Force immediate refresh on component mount
  useEffect(() => {
    const timer = setTimeout(() => {
      setRefreshTrigger(Date.now())
    }, 100)
    return () => clearTimeout(timer)
  }, [])
  
  return (
    <header className="bg-background border-b shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-3">
          {/* Logo/Brand */}
          <div className="flex items-center">
            <h1 className="text-xl font-bold text-primary">BMad Solana Trading</h1>
          </div>

          {/* Status indicators */}
          <div className="flex items-center space-x-4">
            <WalletConnectionIndicator 
              key={`wallet-indicator-${refreshTrigger}`}
              forceRefresh={refreshTrigger} 
            />
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              {isLoading ? 'Connecting...' : 'Refresh Wallet'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleHardReset}
              disabled={isLoading}
              className="text-red-600 border-red-600 hover:bg-red-50"
            >
              Hard Reset
            </Button>
          </div>
        </div>
      </div>
    </header>
  )
}