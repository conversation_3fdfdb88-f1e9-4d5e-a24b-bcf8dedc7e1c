import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { PinToggle } from '../PinToggle';
import { useWatchlistStore } from '@/stores/watchlist-store';
import { vi } from 'vitest';

// Mock the store
vi.mock('@/stores/watchlist-store', () => ({
  useWatchlistStore: vi.fn(),
}));

describe('PinToggle', () => {
  const mockTogglePinned = vi.fn();
  const mockGetPinnedItems = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useWatchlistStore as any).mockReturnValue({
      togglePinned: mockTogglePinned,
      getPinnedItems: mockGetPinnedItems,
    });
    mockGetPinnedItems.mockReturnValue([]);
  });

  it('renders unpinned state correctly', () => {
    render(<PinToggle isPinned={false} tokenId="test-id" />);
    
    const button = screen.getByRole('button');
    const star = button.querySelector('svg');
    
    expect(button).toHaveAttribute('title', 'Pin token');
    expect(star).toHaveClass('fill-transparent');
  });

  it('renders pinned state correctly', () => {
    render(<PinToggle isPinned={true} tokenId="test-id" />);
    
    const button = screen.getByRole('button');
    const star = button.querySelector('svg');
    
    expect(button).toHaveAttribute('title', 'Unpin token');
    expect(star).toHaveClass('fill-current');
  });

  it('calls togglePinned when clicked', async () => {
    render(<PinToggle isPinned={false} tokenId="test-id" />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    await waitFor(() => {
      expect(mockTogglePinned).toHaveBeenCalledWith('test-id');
    });
  });

  it('prevents pinning when maximum pinned items reached', async () => {
    // Mock 10 pinned items (max limit)
    const mockPinnedItems = Array.from({ length: 10 }, (_, i) => ({ id: `item-${i}`, isPinned: true }));
    mockGetPinnedItems.mockReturnValue(mockPinnedItems);

    render(<PinToggle isPinned={false} tokenId="test-id" />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    await waitFor(() => {
      expect(mockTogglePinned).not.toHaveBeenCalled();
    });
  });

  it('allows unpinning even when at maximum', async () => {
    const mockPinnedItems = Array.from({ length: 10 }, (_, i) => ({ id: `item-${i}`, isPinned: true }));
    mockGetPinnedItems.mockReturnValue(mockPinnedItems);

    render(<PinToggle isPinned={true} tokenId="test-id" />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    await waitFor(() => {
      expect(mockTogglePinned).toHaveBeenCalledWith('test-id');
    });
  });

  it('is disabled when loading', () => {
    render(<PinToggle isPinned={false} tokenId="test-id" disabled={true} />);
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
  });

  it('prevents event propagation', () => {
    const mockParentClick = vi.fn();
    
    render(
      <div onClick={mockParentClick}>
        <PinToggle isPinned={false} tokenId="test-id" />
      </div>
    );
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(mockParentClick).not.toHaveBeenCalled();
  });
});