import { render, screen } from '@testing-library/react';
import { WatchlistTable } from '../WatchlistTable';
import { WatchlistItemWithMetrics } from '../../../../../packages/shared/src/types/watchlist';
import { vi } from 'vitest';

// Mock the child components
vi.mock('../WatchlistItem', () => ({
  WatchlistItem: ({ item }: { item: WatchlistItemWithMetrics }) => (
    <tr data-testid={`watchlist-item-${item.id}`}>
      <td>{item.tokenSymbol}</td>
      <td>{item.tokenName}</td>
    </tr>
  ),
}));

describe('WatchlistTable', () => {
  const mockItems: WatchlistItemWithMetrics[] = [
    {
      id: '1',
      tokenAddress: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
      tokenSymbol: 'USDC',
      tokenName: 'USD Coin',
      customName: null,
      notes: null,
      isPinned: false,
      addedAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      isActive: true,
    },
    {
      id: '2',
      tokenAddress: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
      tokenSymbol: 'USDT',
      tokenName: 'Tether USD',
      customName: 'My USDT',
      notes: 'Stable coin',
      isPinned: true,
      addedAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02'),
      isActive: true,
    },
  ];

  it('renders table headers correctly', () => {
    render(<WatchlistTable items={mockItems} />);
    
    expect(screen.getByText('Pin')).toBeInTheDocument();
    expect(screen.getByText('Token')).toBeInTheDocument();
    expect(screen.getByText('Price')).toBeInTheDocument();
    expect(screen.getByText('1h Change')).toBeInTheDocument();
    expect(screen.getByText('24h Change')).toBeInTheDocument();
    expect(screen.getByText('Volume')).toBeInTheDocument();
    expect(screen.getByText('Market Cap')).toBeInTheDocument();
    expect(screen.getByText('Actions')).toBeInTheDocument();
  });

  it('renders watchlist items', () => {
    render(<WatchlistTable items={mockItems} />);
    
    expect(screen.getByTestId('watchlist-item-1')).toBeInTheDocument();
    expect(screen.getByTestId('watchlist-item-2')).toBeInTheDocument();
  });

  it('shows empty state when no items', () => {
    render(<WatchlistTable items={[]} />);
    
    expect(screen.getByText('No tokens in watchlist')).toBeInTheDocument();
    expect(screen.getByText('Start by adding tokens to monitor their prices and trading opportunities.')).toBeInTheDocument();
  });

  it('shows loading skeleton when isLoading is true', () => {
    render(<WatchlistTable items={[]} isLoading={true} />);
    
    // Should show loading skeleton rows
    const skeletonElements = screen.getAllByText((content, element) => {
      return element?.classList.contains('animate-pulse') ?? false;
    });
    
    expect(skeletonElements.length).toBeGreaterThan(0);
  });

  it('displays last updated timestamp', () => {
    const lastUpdated = new Date('2024-01-01T12:00:00Z');
    render(<WatchlistTable items={mockItems} lastUpdated={lastUpdated} />);
    
    expect(screen.getByText(/Last updated:/)).toBeInTheDocument();
  });

  it('shows correct status indicator colors', () => {
    const { container } = render(<WatchlistTable items={mockItems} isLoading={false} lastUpdated={new Date()} />);
    
    // Should show green dot when not loading
    const statusDot = container.querySelector('.bg-green-500');
    expect(statusDot).toBeInTheDocument();
  });

  it('shows loading indicator when refreshing', () => {
    const { container } = render(<WatchlistTable items={mockItems} isLoading={true} lastUpdated={new Date()} />);
    
    // Should show yellow pulsing dot when loading
    const loadingDot = container.querySelector('.bg-yellow-500.animate-pulse');
    expect(loadingDot).toBeInTheDocument();
  });
});