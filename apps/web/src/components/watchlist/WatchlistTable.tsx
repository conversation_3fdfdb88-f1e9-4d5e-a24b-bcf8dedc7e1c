"use client";

import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { WatchlistItemWithMetrics } from "../../../../packages/shared/src/types/watchlist";
import { WatchlistItem } from "./WatchlistItem";
import { cn } from "@/lib/utils";

interface WatchlistTableProps {
  items: WatchlistItemWithMetrics[];
  isLoading?: boolean;
  lastUpdated?: Date | null;
  className?: string;
}

export function WatchlistTable({ 
  items, 
  isLoading = false, 
  lastUpdated,
  className 
}: WatchlistTableProps) {
  if (items.length === 0 && !isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="text-muted-foreground mb-4">
          <svg
            className="mx-auto h-12 w-12 mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
            />
          </svg>
          <h3 className="text-lg font-medium">No tokens in watchlist</h3>
          <p className="text-sm mt-2">Start by adding tokens to monitor their prices and trading opportunities.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("w-full", className)}>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">Pin</TableHead>
              <TableHead>Token</TableHead>
              <TableHead className="text-right">Price</TableHead>
              <TableHead className="text-right hidden md:table-cell">1h Change</TableHead>
              <TableHead className="text-right">24h Change</TableHead>
              <TableHead className="text-right hidden lg:table-cell">Volume</TableHead>
              <TableHead className="text-right hidden xl:table-cell">Market Cap</TableHead>
              <TableHead className="text-center w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              // Loading skeleton rows
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <div className="h-4 w-4 bg-muted rounded animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="space-y-2">
                      <div className="h-4 w-16 bg-muted rounded animate-pulse" />
                      <div className="h-3 w-24 bg-muted rounded animate-pulse" />
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="h-4 w-20 bg-muted rounded animate-pulse ml-auto" />
                  </TableCell>
                  <TableCell className="text-right hidden md:table-cell">
                    <div className="h-4 w-16 bg-muted rounded animate-pulse ml-auto" />
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="h-4 w-16 bg-muted rounded animate-pulse ml-auto" />
                  </TableCell>
                  <TableCell className="text-right hidden lg:table-cell">
                    <div className="h-4 w-20 bg-muted rounded animate-pulse ml-auto" />
                  </TableCell>
                  <TableCell className="text-right hidden xl:table-cell">
                    <div className="h-4 w-20 bg-muted rounded animate-pulse ml-auto" />
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="h-6 w-6 bg-muted rounded animate-pulse mx-auto" />
                  </TableCell>
                </TableRow>
              ))
            ) : (
              items.map((item) => (
                <WatchlistItem key={item.id} item={item} />
              ))
            )}
          </TableBody>
        </Table>
      </div>
      
      {/* Status indicator */}
      {lastUpdated && (
        <div className="flex items-center justify-center mt-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <div className={cn(
              "h-2 w-2 rounded-full",
              isLoading ? "bg-yellow-500 animate-pulse" : "bg-green-500"
            )} />
            <span>
              Last updated: {lastUpdated.toLocaleTimeString()}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}