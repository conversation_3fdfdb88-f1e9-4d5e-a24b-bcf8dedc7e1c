"use client";

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Star } from "lucide-react";
import { cn } from "@/lib/utils";
import { useWatchlistStore } from "@/stores/watchlist-store";

interface PinToggleProps {
  isPinned: boolean;
  tokenId: string;
  className?: string;
  disabled?: boolean;
}

export function PinToggle({ 
  isPinned, 
  tokenId, 
  className,
  disabled = false 
}: PinToggleProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { togglePinned, getPinnedItems } = useWatchlistStore();

  const handleTogglePinned = async (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (isLoading || disabled) return;

    // Check if we're trying to pin and already at maximum
    const currentPinnedItems = getPinnedItems();
    const maxPinnedItems = 10; // From WATCHLIST_LIMITS.MAX_PINNED
    
    if (!isPinned && currentPinnedItems.length >= maxPinnedItems) {
      // TODO: Show toast notification about maximum pinned items reached
      console.warn(`Maximum pinned items (${maxPinnedItems}) reached`);
      return;
    }

    setIsLoading(true);
    
    try {
      // Optimistic update
      togglePinned(tokenId);
      
      // TODO: Make API call to update pin status
      // await watchlistApi.updateItem(tokenId, { isPinned: !isPinned });
      
    } catch (error) {
      // Revert optimistic update on failure
      togglePinned(tokenId);
      console.error('Failed to toggle pin status:', error);
      // TODO: Show error toast
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleTogglePinned}
      disabled={disabled || isLoading}
      className={cn(
        "h-8 w-8 p-0 hover:scale-110 transition-all duration-200",
        isPinned 
          ? "text-yellow-500 hover:text-yellow-600" 
          : "text-muted-foreground hover:text-yellow-500",
        isLoading && "opacity-50 cursor-not-allowed",
        className
      )}
      title={isPinned ? "Unpin token" : "Pin token"}
    >
      <Star 
        className={cn(
          "h-4 w-4 transition-all duration-200",
          isPinned ? "fill-current" : "fill-transparent",
          isLoading && "animate-pulse"
        )} 
      />
    </Button>
  );
}