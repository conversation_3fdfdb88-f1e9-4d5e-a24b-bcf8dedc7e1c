"use client";

import React, { useState, useRef, useEffect } from 'react';
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { WatchlistItemWithMetrics, WATCHLIST_LIMITS } from "../../../../packages/shared/src/types/watchlist";
import { useWatchlistStore } from "@/stores/watchlist-store";
import { Edit2, Check, X, Plus } from "lucide-react";
import { cn } from "@/lib/utils";

interface EditableNotesProps {
  item: WatchlistItemWithMetrics;
  className?: string;
}

export function EditableNotes({ item, className }: EditableNotesProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { updateItem } = useWatchlistStore();
  
  const hasNotes = item.notes && item.notes.trim().length > 0;

  // Focus textarea when entering edit mode
  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
      textareaRef.current.setSelectionRange(editValue.length, editValue.length);
    }
  }, [isEditing, editValue.length]);

  const startEditing = () => {
    setEditValue(item.notes || '');
    setIsEditing(true);
    setError(null);
  };

  const cancelEditing = () => {
    setIsEditing(false);
    setEditValue('');
    setError(null);
  };

  const saveChanges = async () => {
    if (isLoading) return;

    // Validate input
    if (editValue.length > WATCHLIST_LIMITS.MAX_NOTES_LENGTH) {
      setError(`Notes must be ${WATCHLIST_LIMITS.MAX_NOTES_LENGTH} characters or less`);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Optimistic update
      const newNotes = editValue.trim() || null;
      updateItem(item.id, { notes: newNotes });

      // TODO: Make API call to update the item
      // await watchlistApi.updateItem(item.id, { notes: newNotes });

      setIsEditing(false);
      setEditValue('');

    } catch (error) {
      // Revert optimistic update on failure
      updateItem(item.id, { notes: item.notes });
      setError('Failed to update notes');
      console.error('Failed to update notes:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      e.preventDefault();
      cancelEditing();
    }
    // Allow Shift+Enter for line breaks, but not plain Enter to save (would be confusing in textarea)
  };

  // Auto-save on blur
  const handleBlur = () => {
    if (isEditing && !isLoading) {
      saveChanges();
    }
  };

  if (isEditing) {
    return (
      <div className={cn("space-y-2", className)}>
        <div className="flex items-start gap-2">
          <Textarea
            ref={textareaRef}
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleKeyDown}
            onBlur={handleBlur}
            className="text-xs min-h-[60px] resize-none"
            placeholder="Add notes about this token..."
            disabled={isLoading}
            rows={3}
            maxLength={WATCHLIST_LIMITS.MAX_NOTES_LENGTH}
          />
          <div className="flex flex-col gap-1 pt-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={saveChanges}
              disabled={isLoading}
              className="h-7 w-7 p-0"
              title="Save"
            >
              <Check className="h-3 w-3 text-green-600" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={cancelEditing}
              disabled={isLoading}
              className="h-7 w-7 p-0"
              title="Cancel (Escape)"
            >
              <X className="h-3 w-3 text-red-600" />
            </Button>
          </div>
        </div>
        
        {error && (
          <p className="text-xs text-red-500">{error}</p>
        )}
        
        <p className="text-xs text-muted-foreground">
          {editValue.length}/{WATCHLIST_LIMITS.MAX_NOTES_LENGTH} characters • Auto-saves on blur • Press Escape to cancel
        </p>
      </div>
    );
  }

  if (hasNotes) {
    return (
      <div 
        className={cn(
          "group cursor-pointer hover:bg-accent/50 rounded px-1 py-0.5 transition-colors",
          className
        )}
        onClick={startEditing}
        title="Click to edit notes"
      >
        <div className="flex items-start gap-2">
          <p className="text-xs text-muted-foreground/70 line-clamp-2 flex-1">
            {item.notes}
          </p>
          <Edit2 className="h-3 w-3 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0 mt-0.5" />
        </div>
      </div>
    );
  }

  // No notes - show add button
  return (
    <div 
      className={cn(
        "group cursor-pointer hover:bg-accent/50 rounded px-1 py-0.5 transition-colors",
        className
      )}
      onClick={startEditing}
      title="Click to add notes"
    >
      <div className="flex items-center gap-2 text-xs text-muted-foreground">
        <Plus className="h-3 w-3" />
        <span>Add notes</span>
      </div>
    </div>
  );
}