"use client";

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { WatchlistItemWithMetrics } from "../../../../packages/shared/src/types/watchlist";
import { useWatchlistStore } from "@/stores/watchlist-store";
import { Trash2, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface RemoveTokenButtonProps {
  item: WatchlistItemWithMetrics;
  className?: string;
  variant?: 'icon' | 'button' | 'dropdown';
  onRemoved?: (item: WatchlistItemWithMetrics) => void;
}

interface RemovedTokenInfo {
  id: string;
  item: WatchlistItemWithMetrics;
  timeoutId: NodeJS.Timeout;
}

// Global state for undo functionality
let removedTokensRegistry: Map<string, RemovedTokenInfo> = new Map();

export function RemoveTokenButton({ 
  item, 
  className,
  variant = 'icon',
  onRemoved 
}: RemoveTokenButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [dontAskAgain, setDontAskAgain] = useState(false);
  
  const { removeItem, addItem } = useWatchlistStore();

  const handleRemove = async () => {
    if (isLoading) return;

    setIsLoading(true);

    try {
      // Soft delete - remove from store but keep reference for recovery
      removeItem(item.id);

      // Set up recovery timeout (10 seconds)
      const timeoutId = setTimeout(() => {
        // Permanently delete after timeout
        removedTokensRegistry.delete(item.id);
        // TODO: Make API call to permanently delete
        // await watchlistApi.deleteItem(item.id);
      }, 10000);

      // Store for potential recovery
      removedTokensRegistry.set(item.id, {
        id: item.id,
        item,
        timeoutId
      });

      // TODO: Show undo toast notification
      console.log('Token removed. You can undo this action for 10 seconds.');
      
      onRemoved?.(item);
      setIsOpen(false);

    } catch (error) {
      console.error('Failed to remove token:', error);
      // TODO: Show error toast
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkipConfirmation = () => {
    if (dontAskAgain) {
      // TODO: Store user preference to skip confirmations
      localStorage.setItem('skipRemoveConfirmation', 'true');
    }
    handleRemove();
  };

  // Check if user has opted to skip confirmations
  const shouldSkipConfirmation = () => {
    return localStorage.getItem('skipRemoveConfirmation') === 'true';
  };

  const handleClick = () => {
    if (shouldSkipConfirmation()) {
      handleRemove();
    } else {
      setIsOpen(true);
    }
  };

  const displayName = item.customName || item.tokenName;
  const addedDate = new Date(item.addedAt).toLocaleDateString();

  // Render different variants
  const renderTrigger = () => {
    switch (variant) {
      case 'button':
        return (
          <Button
            variant="destructive"
            size="sm"
            onClick={handleClick}
            disabled={isLoading}
            className={className}
          >
            {isLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="mr-2 h-4 w-4" />
            )}
            Remove Token
          </Button>
        );
      
      case 'dropdown':
        return (
          <div
            onClick={handleClick}
            className={cn(
              "flex items-center px-2 py-1.5 text-sm cursor-pointer hover:bg-accent rounded-sm text-red-600 hover:text-red-600",
              className
            )}
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Remove Token
          </div>
        );
      
      case 'icon':
      default:
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClick}
            disabled={isLoading}
            className={cn(
              "h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50",
              className
            )}
            title="Remove token"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
          </Button>
        );
    }
  };

  if (shouldSkipConfirmation()) {
    return renderTrigger();
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      {renderTrigger()}
      
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Remove Token from Watchlist</DialogTitle>
          <DialogDescription>
            Are you sure you want to remove <strong>{item.tokenSymbol}</strong> from your watchlist?
          </DialogDescription>
        </DialogHeader>

        {/* Token Details */}
        <div className="bg-muted/50 p-4 rounded-lg space-y-2">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Token:</span>
              <div className="font-medium">{item.tokenSymbol}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Name:</span>
              <div className="font-medium">{displayName}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Added:</span>
              <div className="font-medium">{addedDate}</div>
            </div>
            {item.isPinned && (
              <div>
                <span className="text-muted-foreground">Status:</span>
                <div className="font-medium text-blue-600">Pinned</div>
              </div>
            )}
          </div>
          
          {item.notes && (
            <div>
              <span className="text-muted-foreground text-sm">Notes:</span>
              <div className="text-sm mt-1 p-2 bg-background rounded border">
                {item.notes}
              </div>
            </div>
          )}
        </div>

        {/* Don't ask again option */}
        <div className="flex items-center space-x-2">
          <Checkbox
            id="dontAskAgain"
            checked={dontAskAgain}
            onCheckedChange={(checked) => setDontAskAgain(!!checked)}
          />
          <label
            htmlFor="dontAskAgain"
            className="text-sm cursor-pointer select-none"
          >
            Don't ask again for token removals
          </label>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleSkipConfirmation}
            disabled={isLoading}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Remove Token
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Utility function to recover a removed token
export function recoverRemovedToken(tokenId: string): boolean {
  const removedToken = removedTokensRegistry.get(tokenId);
  
  if (!removedToken) {
    return false; // Token not found or already permanently deleted
  }

  // Clear the timeout
  clearTimeout(removedToken.timeoutId);
  
  // Remove from registry
  removedTokensRegistry.delete(tokenId);
  
  // Re-add to store
  const { addItem } = useWatchlistStore.getState();
  addItem(removedToken.item);
  
  return true;
}