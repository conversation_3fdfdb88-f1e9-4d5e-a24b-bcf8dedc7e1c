"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { AddTokenDialog } from "./AddTokenDialog";
import { BulkAddDialog } from "./BulkAddDialog";
import { formatRelativeTime } from "@/utils/priceFormatting";
import { RefreshCw, Plus, Upload, ArrowRightLeft } from "lucide-react";
import { cn } from "@/lib/utils";
import { useSelectedTokenStore } from "@/stores/selectedTokenStore";

interface WatchlistItemForBulk {
  id: string;
  tokenAddress: string;
  tokenSymbol: string;
  tokenName: string;
  customName?: string;
  notes?: string;
  isPinned: boolean;
  priority?: number;
  addedAt?: Date;
  verified?: boolean;
}

interface WatchlistHeaderProps {
  lastUpdated?: Date | null;
  isRefreshing?: boolean;
  onRefresh?: () => void;
  itemCount?: number;
  pinnedCount?: number;
  pinnedItems?: WatchlistItemForBulk[];
  className?: string;
}

export function WatchlistHeader({
  lastUpdated,
  isRefreshing = false,
  onRefresh,
  itemCount = 0,
  pinnedCount = 0,
  pinnedItems = [],
  className
}: WatchlistHeaderProps) {
  const router = useRouter();
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showBulkDialog, setShowBulkDialog] = useState(false);
  const { setSelectedToken, setWatchlistContext, setNavigationSource } = useSelectedTokenStore();

  const handleRefresh = () => {
    if (onRefresh && !isRefreshing) {
      onRefresh();
    }
  };

  const handleBulkTrading = () => {
    if (pinnedItems.length === 0) return;
    
    // For now, just navigate to trading with the first pinned token
    // In a full implementation, this could open a multi-token trading interface
    const firstPinned = pinnedItems[0];
    
    // Set selected token context
    setSelectedToken(
      {
        mint: firstPinned.tokenAddress,
        symbol: firstPinned.tokenSymbol,
        name: firstPinned.tokenName,
        verified: firstPinned.verified
      },
      {
        customName: firstPinned.customName,
        notes: firstPinned.notes,
        isPinned: firstPinned.isPinned,
        priority: firstPinned.priority,
        addedAt: firstPinned.addedAt
      }
    );
    
    setNavigationSource('watchlist');
    router.push(`/trading?mint=${firstPinned.tokenAddress}&source=watchlist`);
  };

  return (
    <div className={cn("container mx-auto px-4 py-4 border-b border-border", className)}>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        {/* Title and Description */}
        <div className="space-y-1">
          <h1 className="text-2xl font-bold">Watchlist</h1>
          <p className="text-muted-foreground">
            Monitor tokens and discover trading opportunities
          </p>
          
          {/* Stats */}
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>{itemCount} token{itemCount === 1 ? '' : 's'}</span>
            {pinnedCount > 0 && (
              <span>{pinnedCount} pinned</span>
            )}
            {lastUpdated && (
              <span>
                Last updated: {formatRelativeTime(lastUpdated)}
              </span>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2">
          {/* Bulk Trading Button - Only show if there are pinned items */}
          {pinnedCount > 0 && (
            <Button
              variant="default"
              size="sm"
              onClick={handleBulkTrading}
              className="min-w-[44px] bg-blue-600 hover:bg-blue-700"
              title={`Send ${pinnedCount} pinned token${pinnedCount === 1 ? '' : 's'} to trading`}
            >
              <ArrowRightLeft className="h-4 w-4" />
              <span className="hidden sm:inline ml-2">Trade Pinned</span>
            </Button>
          )}
          
          {/* Manual Refresh Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="min-w-[44px]"
            title="Refresh market data"
          >
            <RefreshCw className={cn(
              "h-4 w-4",
              isRefreshing && "animate-spin"
            )} />
            <span className="hidden sm:inline ml-2">
              {isRefreshing ? "Updating..." : "Refresh"}
            </span>
          </Button>

          {/* Bulk Add Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowBulkDialog(true)}
            className="min-w-[44px]"
            title="Bulk add tokens"
          >
            <Upload className="h-4 w-4" />
            <span className="hidden sm:inline ml-2">Bulk Add</span>
          </Button>

          {/* Add Token Button */}
          <Button
            size="sm"
            onClick={() => setShowAddDialog(true)}
            className="min-w-[44px]"
            title="Add single token"
          >
            <Plus className="h-4 w-4" />
            <span className="hidden sm:inline ml-2">Add Token</span>
          </Button>
        </div>
      </div>

      {/* Status Indicators */}
      {(isRefreshing || lastUpdated) && (
        <div className="mt-4 flex items-center justify-center">
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <div className={cn(
              "h-2 w-2 rounded-full",
              isRefreshing ? "bg-yellow-500 animate-pulse" : "bg-green-500"
            )} />
            <span>
              {isRefreshing 
                ? "Updating market data..." 
                : lastUpdated 
                  ? `Updated ${lastUpdated.toLocaleTimeString()}`
                  : "Ready"
              }
            </span>
          </div>
        </div>
      )}

      {/* Dialogs */}
      <AddTokenDialog
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
        onSuccess={() => {
          // TODO: Show success toast
          console.log('Token added successfully');
        }}
      />

      <BulkAddDialog
        open={showBulkDialog}
        onOpenChange={setShowBulkDialog}
        onSuccess={(items) => {
          // TODO: Show success toast
          console.log(`${items.length} tokens added successfully`);
        }}
      />
    </div>
  );
}