"use client";

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { WatchlistItemWithMetrics, CreateWatchlistItemDto, WATCHLIST_LIMITS } from "../../../../packages/shared/src/types/watchlist";
import { useWatchlistStore } from "@/stores/watchlist-store";
import { Loader2, AlertCircle, CheckCircle2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface AddTokenDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: (item: WatchlistItemWithMetrics) => void;
}

interface ValidationState {
  tokenAddress?: string;
  tokenSymbol?: string;
  tokenName?: string;
  customName?: string;
  notes?: string;
}

export function AddTokenDialog({ 
  open, 
  onOpenChange, 
  onSuccess 
}: AddTokenDialogProps) {
  const [formData, setFormData] = useState<CreateWatchlistItemDto>({
    tokenAddress: '',
    tokenSymbol: '',
    tokenName: '',
    customName: '',
    notes: ''
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [isValidatingAddress, setIsValidatingAddress] = useState(false);
  const [validationErrors, setValidationErrors] = useState<ValidationState>({});
  const [addressValidated, setAddressValidated] = useState(false);
  
  const { addItem, items } = useWatchlistStore();

  // Reset form when dialog opens/closes
  React.useEffect(() => {
    if (!open) {
      setFormData({
        tokenAddress: '',
        tokenSymbol: '',
        tokenName: '',
        customName: '',
        notes: ''
      });
      setValidationErrors({});
      setAddressValidated(false);
      setIsLoading(false);
      setIsValidatingAddress(false);
    }
  }, [open]);

  const validateSolanaAddress = (address: string): boolean => {
    // Basic Solana address validation (32-44 characters, base58)
    if (!address || address.length < 32 || address.length > 44) {
      return false;
    }
    
    // Check for valid base58 characters
    const base58Regex = /^[1-9A-HJ-NP-Za-km-z]+$/;
    return base58Regex.test(address);
  };

  const checkForDuplicate = (address: string): boolean => {
    return items.some(item => item.tokenAddress === address);
  };

  const validateTokenAddress = async (address: string) => {
    if (!address) {
      setValidationErrors(prev => ({ ...prev, tokenAddress: undefined }));
      setAddressValidated(false);
      return;
    }

    setIsValidatingAddress(true);
    setValidationErrors(prev => ({ ...prev, tokenAddress: undefined }));

    try {
      // Basic format validation
      if (!validateSolanaAddress(address)) {
        setValidationErrors(prev => ({ 
          ...prev, 
          tokenAddress: 'Invalid Solana address format' 
        }));
        setAddressValidated(false);
        return;
      }

      // Check for duplicates
      if (checkForDuplicate(address)) {
        setValidationErrors(prev => ({ 
          ...prev, 
          tokenAddress: 'Token already exists in watchlist' 
        }));
        setAddressValidated(false);
        return;
      }

      // TODO: Fetch token metadata from Solana/Jupiter
      // This would be replaced with actual API call
      await new Promise(resolve => setTimeout(resolve, 800)); // Simulate API delay
      
      // Mock successful validation - in real implementation, this would fetch metadata
      const mockMetadata = {
        symbol: 'TOKEN',
        name: 'Token Name'
      };

      setFormData(prev => ({
        ...prev,
        tokenSymbol: mockMetadata.symbol,
        tokenName: mockMetadata.name
      }));
      
      setAddressValidated(true);
      
    } catch (error) {
      setValidationErrors(prev => ({ 
        ...prev, 
        tokenAddress: 'Failed to validate token address' 
      }));
      setAddressValidated(false);
    } finally {
      setIsValidatingAddress(false);
    }
  };

  const handleAddressChange = (value: string) => {
    setFormData(prev => ({ ...prev, tokenAddress: value }));
    
    // Debounce validation
    const timeoutId = setTimeout(() => {
      validateTokenAddress(value);
    }, 500);

    return () => clearTimeout(timeoutId);
  };

  const validateForm = (): boolean => {
    const errors: ValidationState = {};

    if (!formData.tokenAddress) {
      errors.tokenAddress = 'Token address is required';
    } else if (!validateSolanaAddress(formData.tokenAddress)) {
      errors.tokenAddress = 'Invalid Solana address format';
    } else if (checkForDuplicate(formData.tokenAddress)) {
      errors.tokenAddress = 'Token already exists in watchlist';
    }

    if (!formData.tokenSymbol) {
      errors.tokenSymbol = 'Token symbol is required';
    } else if (formData.tokenSymbol.length > WATCHLIST_LIMITS.MAX_SYMBOL_LENGTH) {
      errors.tokenSymbol = `Symbol must be ${WATCHLIST_LIMITS.MAX_SYMBOL_LENGTH} characters or less`;
    }

    if (!formData.tokenName) {
      errors.tokenName = 'Token name is required';
    } else if (formData.tokenName.length > WATCHLIST_LIMITS.MAX_NAME_LENGTH) {
      errors.tokenName = `Name must be ${WATCHLIST_LIMITS.MAX_NAME_LENGTH} characters or less`;
    }

    if (formData.customName && formData.customName.length > WATCHLIST_LIMITS.MAX_CUSTOM_NAME_LENGTH) {
      errors.customName = `Custom name must be ${WATCHLIST_LIMITS.MAX_CUSTOM_NAME_LENGTH} characters or less`;
    }

    if (formData.notes && formData.notes.length > WATCHLIST_LIMITS.MAX_NOTES_LENGTH) {
      errors.notes = `Notes must be ${WATCHLIST_LIMITS.MAX_NOTES_LENGTH} characters or less`;
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // TODO: Make API call to add token to watchlist
      // await watchlistApi.addItem(formData);

      // For now, create a mock item and add to store
      const newItem: WatchlistItemWithMetrics = {
        id: Math.random().toString(36).substr(2, 9),
        tokenAddress: formData.tokenAddress,
        tokenSymbol: formData.tokenSymbol,
        tokenName: formData.tokenName,
        customName: formData.customName || null,
        notes: formData.notes || null,
        isPinned: false,
        addedAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        // Mock snapshot data
        snapshot: {
          tokenAddress: formData.tokenAddress,
          priceUsd: { toNumber: () => Math.random() * 100 } as any,
          priceChange1h: { toNumber: () => (Math.random() - 0.5) * 10 } as any,
          priceChange24h: { toNumber: () => (Math.random() - 0.5) * 20 } as any,
          volume24h: { toNumber: () => Math.random() * 1000000 } as any,
          liquidity: { toNumber: () => Math.random() * 500000 } as any,
          lastUpdated: new Date(),
          source: 'mock'
        }
      };

      addItem(newItem);
      onSuccess?.(newItem);
      onOpenChange(false);

      // TODO: Show success toast
      console.log('Token added successfully:', newItem);

    } catch (error) {
      console.error('Failed to add token:', error);
      // TODO: Show error toast
    } finally {
      setIsLoading(false);
    }
  };

  const isFormValid = addressValidated && !Object.keys(validationErrors).length && formData.tokenSymbol && formData.tokenName;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Token to Watchlist</DialogTitle>
          <DialogDescription>
            Enter a Solana token address to add it to your watchlist for monitoring.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Token Address Input */}
          <div className="space-y-2">
            <Label htmlFor="tokenAddress">
              Token Address <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Input
                id="tokenAddress"
                placeholder="Enter Solana token address (32-44 characters)"
                value={formData.tokenAddress}
                onChange={(e) => handleAddressChange(e.target.value)}
                className={cn(
                  validationErrors.tokenAddress && "border-red-500",
                  addressValidated && "border-green-500"
                )}
                disabled={isLoading}
              />
              {isValidatingAddress && (
                <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-muted-foreground" />
              )}
              {addressValidated && !isValidatingAddress && (
                <CheckCircle2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
              )}
              {validationErrors.tokenAddress && !isValidatingAddress && (
                <AlertCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-red-500" />
              )}
            </div>
            {validationErrors.tokenAddress && (
              <p className="text-sm text-red-500">{validationErrors.tokenAddress}</p>
            )}
          </div>

          {/* Auto-populated fields (when address is validated) */}
          {addressValidated && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tokenSymbol">Symbol</Label>
                  <Input
                    id="tokenSymbol"
                    value={formData.tokenSymbol}
                    onChange={(e) => setFormData(prev => ({ ...prev, tokenSymbol: e.target.value }))}
                    className={cn(validationErrors.tokenSymbol && "border-red-500")}
                    disabled={isLoading}
                  />
                  {validationErrors.tokenSymbol && (
                    <p className="text-sm text-red-500">{validationErrors.tokenSymbol}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tokenName">Name</Label>
                  <Input
                    id="tokenName"
                    value={formData.tokenName}
                    onChange={(e) => setFormData(prev => ({ ...prev, tokenName: e.target.value }))}
                    className={cn(validationErrors.tokenName && "border-red-500")}
                    disabled={isLoading}
                  />
                  {validationErrors.tokenName && (
                    <p className="text-sm text-red-500">{validationErrors.tokenName}</p>
                  )}
                </div>
              </div>

              {/* Optional fields */}
              <div className="space-y-2">
                <Label htmlFor="customName">Custom Name (optional)</Label>
                <Input
                  id="customName"
                  placeholder="Enter a custom display name"
                  value={formData.customName || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, customName: e.target.value }))}
                  className={cn(validationErrors.customName && "border-red-500")}
                  disabled={isLoading}
                />
                {validationErrors.customName && (
                  <p className="text-sm text-red-500">{validationErrors.customName}</p>
                )}
                <p className="text-xs text-muted-foreground">
                  {(formData.customName || '').length}/{WATCHLIST_LIMITS.MAX_CUSTOM_NAME_LENGTH}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes (optional)</Label>
                <Textarea
                  id="notes"
                  placeholder="Add notes about this token..."
                  value={formData.notes || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  className={cn(validationErrors.notes && "border-red-500")}
                  disabled={isLoading}
                  rows={3}
                />
                {validationErrors.notes && (
                  <p className="text-sm text-red-500">{validationErrors.notes}</p>
                )}
                <p className="text-xs text-muted-foreground">
                  {(formData.notes || '').length}/{WATCHLIST_LIMITS.MAX_NOTES_LENGTH}
                </p>
              </div>
            </>
          )}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
              Cancel
            </Button>
            <Button type="submit" disabled={!isFormValid || isLoading || isValidatingAddress}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Add Token
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}