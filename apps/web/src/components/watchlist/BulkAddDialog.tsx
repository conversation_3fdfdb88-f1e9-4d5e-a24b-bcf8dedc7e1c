"use client";

import React, { useState, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { WatchlistItemWithMetrics, WATCHLIST_LIMITS } from "../../../../packages/shared/src/types/watchlist";
import { useWatchlistStore } from "@/stores/watchlist-store";
import { Loader2, AlertCircle, CheckCircle2, Info } from "lucide-react";
import { cn } from "@/lib/utils";

interface BulkAddDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: (items: WatchlistItemWithMetrics[]) => void;
}

interface ParsedToken {
  tokenAddress: string;
  customName?: string;
  notes?: string;
  isValid: boolean;
  error?: string;
  lineNumber: number;
}

interface BulkImportResult {
  successful: WatchlistItemWithMetrics[];
  failed: Array<{
    tokenAddress: string;
    error: string;
    lineNumber: number;
  }>;
  skippedDuplicates: number;
}

export function BulkAddDialog({ 
  open, 
  onOpenChange, 
  onSuccess 
}: BulkAddDialogProps) {
  const [inputText, setInputText] = useState('');
  const [openSwapAfterSave, setOpenSwapAfterSave] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [importResult, setImportResult] = useState<BulkImportResult | null>(null);
  
  const { addItem, items } = useWatchlistStore();

  // Reset form when dialog opens/closes
  React.useEffect(() => {
    if (!open) {
      setInputText('');
      setOpenSwapAfterSave(false);
      setIsLoading(false);
      setImportResult(null);
    }
  }, [open]);

  const parsedTokens = useMemo(() => {
    if (!inputText.trim()) return [];

    const lines = inputText.split('\n').filter(line => line.trim());
    const tokens: ParsedToken[] = [];

    lines.forEach((line, index) => {
      const trimmed = line.trim();
      if (!trimmed) return;

      const lineNumber = index + 1;

      // Support multiple formats:
      // 1. address only: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
      // 2. address,name: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v,USDC"
      // 3. address|name: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v|USD Coin"
      
      let tokenAddress = '';
      let customName = '';
      let error = '';

      if (trimmed.includes(',')) {
        const parts = trimmed.split(',').map(p => p.trim());
        tokenAddress = parts[0];
        customName = parts.slice(1).join(',').trim();
      } else if (trimmed.includes('|')) {
        const parts = trimmed.split('|').map(p => p.trim());
        tokenAddress = parts[0];
        customName = parts.slice(1).join('|').trim();
      } else {
        tokenAddress = trimmed;
      }

      // Validate token address format
      if (!tokenAddress) {
        error = 'Missing token address';
      } else if (tokenAddress.length < 32 || tokenAddress.length > 44) {
        error = 'Invalid address length (must be 32-44 characters)';
      } else if (!/^[1-9A-HJ-NP-Za-km-z]+$/.test(tokenAddress)) {
        error = 'Invalid address format (must be base58)';
      } else if (items.some(item => item.tokenAddress === tokenAddress)) {
        error = 'Token already exists in watchlist';
      }

      // Validate custom name length
      if (customName && customName.length > WATCHLIST_LIMITS.MAX_CUSTOM_NAME_LENGTH) {
        error = `Custom name too long (max ${WATCHLIST_LIMITS.MAX_CUSTOM_NAME_LENGTH} characters)`;
      }

      tokens.push({
        tokenAddress,
        customName: customName || undefined,
        isValid: !error,
        error,
        lineNumber
      });
    });

    return tokens;
  }, [inputText, items]);

  const validationSummary = useMemo(() => {
    const total = parsedTokens.length;
    const valid = parsedTokens.filter(t => t.isValid).length;
    const invalid = total - valid;
    const duplicates = parsedTokens.filter(t => t.error?.includes('already exists')).length;
    
    return { total, valid, invalid, duplicates };
  }, [parsedTokens]);

  const handleSubmit = async () => {
    const validTokens = parsedTokens.filter(token => token.isValid);
    
    if (validTokens.length === 0) {
      return;
    }

    if (validTokens.length > 50) {
      // TODO: Show error toast about limit
      console.error('Cannot import more than 50 tokens at once');
      return;
    }

    setIsLoading(true);

    try {
      const successful: WatchlistItemWithMetrics[] = [];
      const failed: BulkImportResult['failed'] = [];
      let skippedDuplicates = 0;

      // Process each valid token
      for (const token of validTokens) {
        try {
          // TODO: Fetch token metadata from API
          // For now, create mock data
          const mockMetadata = {
            symbol: 'TOKEN' + Math.floor(Math.random() * 1000),
            name: 'Token Name ' + Math.floor(Math.random() * 1000)
          };

          const newItem: WatchlistItemWithMetrics = {
            id: Math.random().toString(36).substr(2, 9),
            tokenAddress: token.tokenAddress,
            tokenSymbol: mockMetadata.symbol,
            tokenName: mockMetadata.name,
            customName: token.customName || null,
            notes: token.notes || null,
            isPinned: false,
            addedAt: new Date(),
            updatedAt: new Date(),
            isActive: true,
            snapshot: {
              tokenAddress: token.tokenAddress,
              priceUsd: { toNumber: () => Math.random() * 100 } as any,
              priceChange1h: { toNumber: () => (Math.random() - 0.5) * 10 } as any,
              priceChange24h: { toNumber: () => (Math.random() - 0.5) * 20 } as any,
              volume24h: { toNumber: () => Math.random() * 1000000 } as any,
              liquidity: { toNumber: () => Math.random() * 500000 } as any,
              lastUpdated: new Date(),
              source: 'mock'
            }
          };

          addItem(newItem);
          successful.push(newItem);

        } catch (error) {
          failed.push({
            tokenAddress: token.tokenAddress,
            error: error instanceof Error ? error.message : 'Unknown error',
            lineNumber: token.lineNumber
          });
        }
      }

      const result: BulkImportResult = {
        successful,
        failed,
        skippedDuplicates
      };

      setImportResult(result);

      if (successful.length > 0) {
        onSuccess?.(successful);

        // Handle "Open swap after save" option
        if (openSwapAfterSave && successful.length > 0) {
          const lastToken = successful[successful.length - 1];
          setTimeout(() => {
            window.open(`/swap?mint=${lastToken.tokenAddress}`, '_blank');
          }, 1000);
        }

        setTimeout(() => {
          onOpenChange(false);
        }, 2000);
      }

    } catch (error) {
      console.error('Failed to import tokens:', error);
      // TODO: Show error toast
    } finally {
      setIsLoading(false);
    }
  };

  const handleCloseDialog = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Bulk Add Tokens</DialogTitle>
          <DialogDescription>
            Import multiple tokens to your watchlist. Enter one token per line.
          </DialogDescription>
        </DialogHeader>

        {!importResult ? (
          <div className="space-y-4">
            {/* Format Examples */}
            <div className="bg-muted/50 p-4 rounded-lg space-y-2">
              <h4 className="text-sm font-medium flex items-center gap-2">
                <Info className="h-4 w-4" />
                Supported Formats
              </h4>
              <div className="text-xs space-y-1 font-mono">
                <div>Address only: <code>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</code></div>
                <div>Address + name: <code>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v,USDC</code></div>
                <div>Address | name: <code>EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v|USD Coin</code></div>
              </div>
            </div>

            {/* Input Area */}
            <div className="space-y-2">
              <Label htmlFor="bulkInput">
                Token Addresses (one per line, max 50)
              </Label>
              <Textarea
                id="bulkInput"
                placeholder={`Enter token addresses, one per line:\n\nExample:\nEPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v,USDC\nEs9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB|USDT\n4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R`}
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                rows={8}
                className="resize-none"
                disabled={isLoading}
              />
            </div>

            {/* Validation Summary */}
            {parsedTokens.length > 0 && (
              <div className="bg-muted/50 p-4 rounded-lg space-y-2">
                <h4 className="text-sm font-medium">Validation Summary</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                    <span>{validationSummary.valid} valid tokens</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-red-500" />
                    <span>{validationSummary.invalid} invalid tokens</span>
                  </div>
                </div>

                {/* Show validation errors */}
                {parsedTokens.some(t => !t.isValid) && (
                  <div className="mt-3 max-h-32 overflow-y-auto">
                    <h5 className="text-xs font-medium text-muted-foreground mb-2">Errors:</h5>
                    <div className="space-y-1 text-xs">
                      {parsedTokens.filter(t => !t.isValid).map((token, index) => (
                        <div key={index} className="flex gap-2 text-red-600">
                          <span>Line {token.lineNumber}:</span>
                          <span>{token.error}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Options */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="openSwapAfterSave"
                  checked={openSwapAfterSave}
                  onCheckedChange={(checked) => setOpenSwapAfterSave(!!checked)}
                  disabled={isLoading}
                />
                <Label
                  htmlFor="openSwapAfterSave"
                  className="text-sm font-normal cursor-pointer"
                >
                  Open swap interface with last valid token after save
                </Label>
              </div>
            </div>

            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={handleCloseDialog} 
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button 
                onClick={handleSubmit} 
                disabled={validationSummary.valid === 0 || isLoading}
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Add {validationSummary.valid} Token{validationSummary.valid === 1 ? '' : 's'}
              </Button>
            </DialogFooter>
          </div>
        ) : (
          /* Import Results */
          <div className="space-y-4">
            <div className="bg-muted/50 p-4 rounded-lg space-y-3">
              <h4 className="text-sm font-medium">Import Complete</h4>
              
              <div className="grid grid-cols-1 gap-2 text-sm">
                {importResult.successful.length > 0 && (
                  <div className="flex items-center gap-2 text-green-600">
                    <CheckCircle2 className="h-4 w-4" />
                    <span>{importResult.successful.length} tokens added successfully</span>
                  </div>
                )}
                
                {importResult.failed.length > 0 && (
                  <div className="flex items-center gap-2 text-red-600">
                    <AlertCircle className="h-4 w-4" />
                    <span>{importResult.failed.length} tokens failed to import</span>
                  </div>
                )}
                
                {importResult.skippedDuplicates > 0 && (
                  <div className="flex items-center gap-2 text-yellow-600">
                    <Info className="h-4 w-4" />
                    <span>{importResult.skippedDuplicates} duplicates skipped</span>
                  </div>
                )}
              </div>

              {openSwapAfterSave && importResult.successful.length > 0 && (
                <div className="text-sm text-blue-600">
                  Opening swap interface with {importResult.successful[importResult.successful.length - 1].tokenSymbol}...
                </div>
              )}
            </div>

            {importResult.failed.length > 0 && (
              <div className="max-h-32 overflow-y-auto">
                <h5 className="text-sm font-medium mb-2">Failed Imports:</h5>
                <div className="space-y-1 text-xs">
                  {importResult.failed.map((failure, index) => (
                    <div key={index} className="text-red-600">
                      Line {failure.lineNumber}: {failure.error}
                    </div>
                  ))}
                </div>
              </div>
            )}

            <DialogFooter>
              <Button onClick={handleCloseDialog}>
                Close
              </Button>
            </DialogFooter>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}