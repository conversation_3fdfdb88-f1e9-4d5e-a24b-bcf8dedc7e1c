"use client";

import React, { useState, useRef, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { WatchlistItemWithMetrics, WATCHLIST_LIMITS } from "../../../../packages/shared/src/types/watchlist";
import { useWatchlistStore } from "@/stores/watchlist-store";
import { Edit2, Check, X } from "lucide-react";
import { cn } from "@/lib/utils";

interface EditableTokenNameProps {
  item: WatchlistItemWithMetrics;
  className?: string;
}

export function EditableTokenName({ item, className }: EditableTokenNameProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const { updateItem } = useWatchlistStore();
  
  const displayName = item.customName || item.tokenName;

  // Focus input when entering edit mode
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const startEditing = () => {
    setEditValue(item.customName || '');
    setIsEditing(true);
    setError(null);
  };

  const cancelEditing = () => {
    setIsEditing(false);
    setEditValue('');
    setError(null);
  };

  const saveChanges = async () => {
    if (isLoading) return;

    // Validate input
    if (editValue.length > WATCHLIST_LIMITS.MAX_CUSTOM_NAME_LENGTH) {
      setError(`Name must be ${WATCHLIST_LIMITS.MAX_CUSTOM_NAME_LENGTH} characters or less`);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Optimistic update
      const newCustomName = editValue.trim() || null;
      updateItem(item.id, { customName: newCustomName });

      // TODO: Make API call to update the item
      // await watchlistApi.updateItem(item.id, { customName: newCustomName });

      setIsEditing(false);
      setEditValue('');

    } catch (error) {
      // Revert optimistic update on failure
      updateItem(item.id, { customName: item.customName });
      setError('Failed to update name');
      console.error('Failed to update token name:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      saveChanges();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      cancelEditing();
    }
  };

  if (isEditing) {
    return (
      <div className={cn("space-y-1", className)}>
        <div className="flex items-center gap-2">
          <Input
            ref={inputRef}
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleKeyDown}
            className="text-xs h-7"
            placeholder={item.tokenName}
            disabled={isLoading}
            maxLength={WATCHLIST_LIMITS.MAX_CUSTOM_NAME_LENGTH}
          />
          <div className="flex items-center gap-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={saveChanges}
              disabled={isLoading}
              className="h-7 w-7 p-0"
              title="Save (Enter)"
            >
              <Check className="h-3 w-3 text-green-600" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={cancelEditing}
              disabled={isLoading}
              className="h-7 w-7 p-0"
              title="Cancel (Escape)"
            >
              <X className="h-3 w-3 text-red-600" />
            </Button>
          </div>
        </div>
        
        {error && (
          <p className="text-xs text-red-500">{error}</p>
        )}
        
        <p className="text-xs text-muted-foreground">
          {editValue.length}/{WATCHLIST_LIMITS.MAX_CUSTOM_NAME_LENGTH} characters
        </p>
      </div>
    );
  }

  return (
    <div 
      className={cn(
        "group flex items-center gap-2 cursor-pointer hover:bg-accent/50 rounded px-1 py-0.5 transition-colors",
        className
      )}
      onClick={startEditing}
      title="Click to edit name"
    >
      <div className="text-xs text-muted-foreground truncate max-w-[200px]">
        {displayName}
      </div>
      <Edit2 className="h-3 w-3 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity" />
    </div>
  );
}