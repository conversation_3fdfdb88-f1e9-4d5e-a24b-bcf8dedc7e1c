'use client'

import { formatDistanceToNow } from 'date-fns'
import { Badge } from '../ui/badge'
import { Card } from '../ui/card'
import { Button } from '../ui/button'
import { RefreshCw, Wifi, WifiOff, AlertTriangle } from 'lucide-react'
import { UseWatchlistPollingReturn } from '../../hooks/useWatchlistPolling'

interface WatchlistStatusProps {
  polling: UseWatchlistPollingReturn
}

export function WatchlistStatus({ polling }: WatchlistStatusProps) {
  const {
    isPolling,
    pollingStatus,
    currentInterval,
    lastUpdateTimestamp,
    errorCount,
    metrics,
    forceRefresh
  } = polling

  const getStatusColor = () => {
    switch (pollingStatus) {
      case 'active':
        return 'default'
      case 'error':
        return 'destructive'
      case 'stopped':
        return 'secondary'
      default:
        return 'secondary'
    }
  }

  const getStatusIcon = () => {
    switch (pollingStatus) {
      case 'active':
        return <Wifi className="h-3 w-3" />
      case 'error':
        return <AlertTriangle className="h-3 w-3" />
      case 'stopped':
        return <WifiOff className="h-3 w-3" />
      default:
        return <WifiOff className="h-3 w-3" />
    }
  }

  const formatInterval = (intervalMs: number) => {
    const seconds = Math.round(intervalMs / 1000)
    if (seconds < 60) {
      return `${seconds}s`
    }
    const minutes = Math.round(seconds / 60)
    return `${minutes}m`
  }

  const handleRefresh = async () => {
    try {
      await forceRefresh()
    } catch (error) {
      console.error('Manual refresh failed:', error)
    }
  }

  return (
    <Card className="p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Badge variant={getStatusColor()} className="flex items-center gap-1">
            {getStatusIcon()}
            {pollingStatus === 'active' ? 'Live' : pollingStatus === 'error' ? 'Error' : 'Stopped'}
          </Badge>
          
          <div className="text-sm text-muted-foreground">
            Updates every {formatInterval(currentInterval)}
          </div>
          
          {lastUpdateTimestamp && (
            <div className="text-xs text-muted-foreground">
              Last: {formatDistanceToNow(new Date(lastUpdateTimestamp), { addSuffix: true })}
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          {errorCount > 0 && (
            <Badge variant="destructive" className="text-xs">
              {errorCount} error{errorCount > 1 ? 's' : ''}
            </Badge>
          )}
          
          {metrics && (
            <div className="text-xs text-muted-foreground">
              {metrics.successfulRequests}/{metrics.totalRequests} requests
              {metrics.averageResponseTime > 0 && (
                <span> · {Math.round(metrics.averageResponseTime)}ms avg</span>
              )}
            </div>
          )}
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={!isPolling}
            className="h-8 px-2"
          >
            <RefreshCw className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </Card>
  )
}