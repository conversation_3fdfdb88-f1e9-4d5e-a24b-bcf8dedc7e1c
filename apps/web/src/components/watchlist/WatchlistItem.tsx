"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { TableCell, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { WatchlistItemWithMetrics } from "../../../../packages/shared/src/types/watchlist";
import { PinToggle } from "./PinToggle";
import { TokenActionsDropdown } from "./TokenActionsDropdown";
import { EditableTokenName } from "./EditableTokenName";
import { EditableNotes } from "./EditableNotes";
import { formatPrice, formatPercentage, formatVolume, formatMarketCap } from "@/utils/priceFormatting";
import { TrendingUp, TrendingDown, ExternalLink, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import Decimal from 'decimal.js';
import { buildSwapUrl } from "@/utils/urlHelpers";
import { useSelectedTokenStore } from "@/stores/selectedTokenStore";

interface WatchlistItemProps {
  item: WatchlistItemWithMetrics;
  className?: string;
}

export function WatchlistItem({ item, className }: WatchlistItemProps) {
  const router = useRouter();
  const [isNavigating, setIsNavigating] = useState(false);
  const { setSelectedToken, setWatchlistContext, setNavigationSource } = useSelectedTokenStore();
  
  const price = item.snapshot?.priceUsd ? new Decimal(item.snapshot.priceUsd) : null;
  const priceChange1h = item.snapshot?.priceChange1h ? new Decimal(item.snapshot.priceChange1h) : null;
  const priceChange24h = item.snapshot?.priceChange24h ? new Decimal(item.snapshot.priceChange24h) : null;
  const volume24h = item.snapshot?.volume24h ? new Decimal(item.snapshot.volume24h) : null;
  const marketCap = item.metrics?.marketCap ? new Decimal(item.metrics.marketCap) : null;

  const displayName = item.customName || item.tokenName;
  const isPositive24h = priceChange24h && priceChange24h.gt(0);
  const isNegative24h = priceChange24h && priceChange24h.lt(0);
  const isPositive1h = priceChange1h && priceChange1h.gt(0);
  const isNegative1h = priceChange1h && priceChange1h.lt(0);

  const handleSendToSwap = async () => {
    setIsNavigating(true);
    
    try {
      // Set selected token in store with watchlist context
      setSelectedToken(
        {
          mint: item.tokenAddress,
          symbol: item.tokenSymbol,
          name: item.tokenName,
          verified: item.verified
        },
        {
          customName: item.customName,
          notes: item.notes,
          isPinned: item.isPinned,
          priority: item.priority,
          addedAt: item.addedAt
        }
      );
      
      // Set navigation source
      setNavigationSource('watchlist');
      
      // Build trading URL with source parameter
      const tradingUrl = `/trading?mint=${item.tokenAddress}&source=watchlist`;
      
      // Navigate to trading interface
      router.push(tradingUrl);
    } catch (error) {
      console.error('Failed to navigate to trading interface:', error);
      setIsNavigating(false);
    }
  };

  return (
    <TableRow className={cn(
      "hover:bg-muted/50 transition-colors",
      item.isPinned && "bg-accent/20",
      className
    )}>
      {/* Pin Toggle */}
      <TableCell>
        <PinToggle 
          isPinned={item.isPinned} 
          tokenId={item.id}
          className="mx-auto"
        />
      </TableCell>

      {/* Token Info */}
      <TableCell>
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm">{item.tokenSymbol}</span>
            {item.isPinned && (
              <div className="h-1.5 w-1.5 rounded-full bg-blue-500" title="Pinned token" />
            )}
          </div>
          <EditableTokenName item={item} />
          <EditableNotes item={item} />
        </div>
      </TableCell>

      {/* Price */}
      <TableCell className="text-right">
        <div className="font-mono text-sm">
          {price ? formatPrice(price.toNumber()) : "—"}
        </div>
      </TableCell>

      {/* 1h Change (hidden on small screens) */}
      <TableCell className="text-right hidden md:table-cell">
        {priceChange1h ? (
          <div className={cn(
            "flex items-center justify-end gap-1 text-sm font-medium",
            isPositive1h && "text-green-600",
            isNegative1h && "text-red-600"
          )}>
            {isPositive1h && <TrendingUp className="h-3 w-3" />}
            {isNegative1h && <TrendingDown className="h-3 w-3" />}
            <span className="font-mono">
              {formatPercentage(priceChange1h.toNumber())}
            </span>
          </div>
        ) : (
          <span className="text-muted-foreground">—</span>
        )}
      </TableCell>

      {/* 24h Change */}
      <TableCell className="text-right">
        {priceChange24h ? (
          <div className={cn(
            "flex items-center justify-end gap-1 text-sm font-medium",
            isPositive24h && "text-green-600",
            isNegative24h && "text-red-600"
          )}>
            {isPositive24h && <TrendingUp className="h-3 w-3" />}
            {isNegative24h && <TrendingDown className="h-3 w-3" />}
            <span className="font-mono">
              {formatPercentage(priceChange24h.toNumber())}
            </span>
          </div>
        ) : (
          <span className="text-muted-foreground">—</span>
        )}
      </TableCell>

      {/* Volume 24h (hidden on medium and smaller screens) */}
      <TableCell className="text-right hidden lg:table-cell">
        <div className="font-mono text-sm">
          {volume24h ? formatVolume(volume24h.toNumber()) : "—"}
        </div>
      </TableCell>

      {/* Market Cap (hidden on large and smaller screens) */}
      <TableCell className="text-right hidden xl:table-cell">
        <div className="font-mono text-sm">
          {marketCap ? formatMarketCap(marketCap.toNumber()) : "—"}
        </div>
      </TableCell>

      {/* Actions */}
      <TableCell className="text-center">
        <div className="flex items-center justify-center gap-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={handleSendToSwap}
            disabled={isNavigating}
            className="h-8 w-8 p-0"
            title="Send to Trading"
          >
            {isNavigating ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <ExternalLink className="h-4 w-4" />
            )}
          </Button>
          <TokenActionsDropdown item={item} />
        </div>
      </TableCell>
    </TableRow>
  );
}