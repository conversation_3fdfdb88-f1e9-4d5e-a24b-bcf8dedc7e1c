"use client";

import React, { useEffect, useState } from 'react';
import { WatchlistHeader } from "./WatchlistHeader";
import { WatchlistTable } from "./WatchlistTable";
import { WatchlistErrorBoundary } from "./WatchlistErrorBoundary";
import { useWatchlistStore } from "@/stores/watchlist-store";
import { useToast } from "@/components/ui/toast";
import { WatchlistItemWithMetrics } from "../../../../../packages/shared/src/types/watchlist";

export function WatchlistPage() {
  const { 
    items, 
    getFilteredItems, 
    polling,
    setLastUpdateTimestamp,
    setIsPolling,
    getPinnedItems
  } = useWatchlistStore();
  
  const { toast } = useToast();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Get filtered and sorted items
  const displayItems = getFilteredItems();
  const pinnedItems = getPinnedItems();

  // Tab visibility detection for polling pause (AC 11)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Tab became inactive - pause polling
        setIsPolling(false);
      } else {
        // Tab became active - resume polling
        setIsPolling(true);
        // Trigger immediate refresh when tab becomes active
        handleRefresh();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [setIsPolling]);

  // Mock market data refresh function
  const handleRefresh = async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    
    try {
      // TODO: Replace with actual API call to get market data
      // await watchlistApi.getMarketData(items.map(item => item.tokenAddress));
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update timestamps
      const now = new Date();
      setLastUpdated(now);
      setLastUpdateTimestamp(now.getTime());

      // TODO: Update items with new market data
      toast({
        title: "Market data updated",
        description: "Prices and metrics have been refreshed",
        type: "success",
        duration: 3000
      });
      
    } catch (error) {
      console.error('Failed to refresh market data:', error);
      toast({
        title: "Failed to refresh data",
        description: error instanceof Error ? error.message : "Please try again",
        type: "error",
        duration: 5000
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Auto-refresh based on polling intervals (AC 11)
  useEffect(() => {
    if (!polling.isPolling) return;

    const intervalMs = polling.currentInterval;
    
    const interval = setInterval(() => {
      if (!document.hidden) { // Only refresh if tab is active
        handleRefresh();
      }
    }, intervalMs);

    return () => clearInterval(interval);
  }, [polling.currentInterval, polling.isPolling]);

  // Initialize polling on mount
  useEffect(() => {
    setIsPolling(true);
    handleRefresh(); // Initial data load
  }, []);

  return (
    <WatchlistErrorBoundary>
      <div className="min-h-screen bg-background">
        <WatchlistHeader 
          lastUpdated={lastUpdated}
          isRefreshing={isRefreshing}
          onRefresh={handleRefresh}
          itemCount={items.length}
          pinnedCount={pinnedItems.length}
        />
        
        <main className="container mx-auto px-4 py-8">
          <WatchlistTable 
            items={displayItems}
            isLoading={isRefreshing}
            lastUpdated={lastUpdated}
          />
        </main>
      </div>
    </WatchlistErrorBoundary>
  );
}