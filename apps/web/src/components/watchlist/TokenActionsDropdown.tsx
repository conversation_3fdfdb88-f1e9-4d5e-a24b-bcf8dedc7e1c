"use client";

import React, { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { WatchlistItemWithMetrics } from "../../../../packages/shared/src/types/watchlist";
import { RemoveTokenButton } from "./RemoveTokenButton";
import { MoreHorizontal, Edit, ExternalLink } from "lucide-react";
import { cn } from "@/lib/utils";

interface TokenActionsDropdownProps {
  item: WatchlistItemWithMetrics;
  className?: string;
  onEditNotes?: (item: WatchlistItemWithMetrics) => void;
  onRemove?: (item: WatchlistItemWithMetrics) => void;
}

export function TokenActionsDropdown({ 
  item, 
  className,
  onEditNotes,
  onRemove 
}: TokenActionsDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleViewOnSolscan = () => {
    const solscanUrl = `https://solscan.io/token/${item.tokenAddress}`;
    window.open(solscanUrl, '_blank', 'noopener,noreferrer');
    setIsOpen(false);
  };

  const handleEditNotes = () => {
    onEditNotes?.(item);
    setIsOpen(false);
  };


  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "h-8 w-8 p-0 hover:bg-muted",
            className
          )}
          title="More actions"
        >
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-[160px]">
        <DropdownMenuItem onClick={handleEditNotes}>
          <Edit className="mr-2 h-4 w-4" />
          Edit Notes
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={handleViewOnSolscan}>
          <ExternalLink className="mr-2 h-4 w-4" />
          View on Solscan
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <RemoveTokenButton 
          item={item}
          variant="dropdown"
          onRemoved={() => setIsOpen(false)}
        />
      </DropdownMenuContent>
    </DropdownMenu>
  );
}