'use client';

import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ChevronRight, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';

export interface BreadcrumbItem {
  label: string;
  href?: string;
  onClick?: () => void;
  isActive?: boolean;
  icon?: React.ReactNode;
}

export interface BreadcrumbProps {
  items: BreadcrumbItem[];
  showHome?: boolean;
  className?: string;
  separator?: React.ReactNode;
}

export function Breadcrumb({ 
  items, 
  showHome = true, 
  className = '', 
  separator = <ChevronRight className="w-4 h-4 text-gray-500" />
}: BreadcrumbProps) {
  const router = useRouter();

  const handleItemClick = (item: BreadcrumbItem) => {
    if (item.onClick) {
      item.onClick();
    } else if (item.href) {
      router.push(item.href);
    }
  };

  const allItems = showHome 
    ? [
        {
          label: 'Home',
          href: '/',
          icon: <Home className="w-4 h-4" />
        },
        ...items
      ]
    : items;

  return (
    <nav className={`flex items-center space-x-2 text-sm ${className}`} aria-label="Breadcrumb">
      {allItems.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <span className="flex items-center" aria-hidden="true">
              {separator}
            </span>
          )}
          
          <div className="flex items-center">
            {item.isActive ? (
              <span className="flex items-center space-x-1 text-white font-medium">
                {item.icon && <span>{item.icon}</span>}
                <span>{item.label}</span>
              </span>
            ) : (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleItemClick(item)}
                className="flex items-center space-x-1 text-gray-400 hover:text-white hover:bg-gray-800 h-auto p-1"
                disabled={!item.href && !item.onClick}
              >
                {item.icon && <span>{item.icon}</span>}
                <span>{item.label}</span>
              </Button>
            )}
          </div>
        </React.Fragment>
      ))}
    </nav>
  );
}

// Predefined navigation paths for common use cases
export const createWatchlistTradingBreadcrumb = (
  currentPage: 'watchlist' | 'trading' = 'trading'
): BreadcrumbItem[] => {
  return [
    {
      label: 'Watchlist',
      href: '/watchlist',
      isActive: currentPage === 'watchlist'
    },
    {
      label: 'Trading',
      href: '/trading',
      isActive: currentPage === 'trading'
    }
  ];
};

export const createTradingBreadcrumb = (): BreadcrumbItem[] => {
  return [
    {
      label: 'Trading',
      href: '/trading',
      isActive: true
    }
  ];
};

// Utility function to generate breadcrumbs based on current path
export const generateBreadcrumbsFromPath = (pathname: string): BreadcrumbItem[] => {
  const segments = pathname.split('/').filter(Boolean);
  
  return segments.map((segment, index) => {
    const href = '/' + segments.slice(0, index + 1).join('/');
    const isLast = index === segments.length - 1;
    
    // Convert segment to readable label
    const label = segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
    
    return {
      label,
      href: isLast ? undefined : href,
      isActive: isLast
    };
  });
};