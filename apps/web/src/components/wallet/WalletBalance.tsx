'use client';

import { useEffect, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface WalletBalance {
  sol: number;
  lamports: number;
  lastUpdated: string;
}

interface WalletBalanceProps {
  onRefresh?: (balance: WalletBalance) => void;
  className?: string;
  showAutoRefresh?: boolean;
}

export function WalletBalance({ onRefresh, className, showAutoRefresh = true }: WalletBalanceProps) {
  const [balance, setBalance] = useState<WalletBalance | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(false);

  const fetchBalance = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/wallet/balance');
      const data = await response.json();

      if (data.success) {
        const balanceData = {
          ...data.data,
          lastUpdated: new Date(data.data.lastUpdated).toLocaleString(),
        };
        setBalance(balanceData);
        onRefresh?.(balanceData);
      } else {
        setError(data.error || 'Failed to fetch balance');
      }
    } catch (err) {
      setError('Network error - check if Helius API key is configured');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBalance();
  }, []);

  // Auto-refresh effect
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchBalance, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [autoRefresh]);

  const formatBalance = (sol: number) => {
    return sol.toFixed(6);
  };

  const formatLamports = (lamports: number) => {
    return lamports.toLocaleString();
  };

  const getBalanceColor = (sol: number) => {
    if (sol < 0.01) return 'text-red-600';
    if (sol < 0.1) return 'text-yellow-600';
    return 'text-green-600';
  };

  return (
    <Card className={`p-4 ${className}`}>
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Wallet Balance</h3>
          <div className="flex items-center gap-2">
            {showAutoRefresh && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setAutoRefresh(!autoRefresh)}
                className={`h-8 px-2 ${autoRefresh ? 'bg-blue-50 text-blue-600' : ''}`}
                title="Auto-refresh every 30 seconds"
              >
                🔄
              </Button>
            )}
            <Button 
              variant="outline" 
              size="sm"
              onClick={fetchBalance}
              disabled={loading}
            >
              {loading ? 'Loading...' : 'Refresh'}
            </Button>
          </div>
        </div>

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-800 text-sm">{error}</p>
            {error.includes('Helius') && (
              <p className="text-red-700 text-xs mt-1">
                💡 This error is expected in development without API keys
              </p>
            )}
          </div>
        )}

        {balance ? (
          <div className="space-y-3">
            {/* SOL Balance */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">SOL Balance:</span>
              <div className="text-right">
                <div className={`text-lg font-mono font-bold ${getBalanceColor(balance.sol)}`}>
                  {formatBalance(balance.sol)} SOL
                </div>
                {balance.sol < 0.01 && (
                  <p className="text-xs text-red-600">⚠️ Low balance</p>
                )}
              </div>
            </div>

            {/* Lamports */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Lamports:</span>
              <span className="text-sm font-mono text-gray-800">
                {formatLamports(balance.lamports)}
              </span>
            </div>

            {/* Last Updated */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Last Updated:</span>
              <span className="text-xs text-gray-500">
                {balance.lastUpdated}
              </span>
            </div>

            {/* Estimated USD (placeholder) */}
            <div className="flex items-center justify-between border-t pt-2">
              <span className="text-sm text-gray-600">Est. Value:</span>
              <span className="text-sm text-gray-500">
                ~${(balance.sol * 100).toFixed(2)} USD
                <span className="text-xs ml-1">(est.)</span>
              </span>
            </div>

            {/* Auto-refresh indicator */}
            {autoRefresh && (
              <div className="flex items-center gap-2 text-xs text-blue-600">
                <div className="h-2 w-2 rounded-full bg-blue-500 animate-pulse" />
                Auto-refreshing every 30s
              </div>
            )}
          </div>
        ) : loading ? (
          <div className="space-y-2">
            <div className="h-8 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
          </div>
        ) : (
          <div className="text-center py-6">
            <p className="text-gray-500 text-sm">Click refresh to load balance</p>
          </div>
        )}
      </div>
    </Card>
  );
}