'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';

interface WalletInfo {
  isValid: boolean;
  address: string;
  network: string;
  isMainnet: boolean;
}

interface WalletConnectionIndicatorProps {
  className?: string;
  showAddress?: boolean;
  showNetwork?: boolean;
  onClick?: () => void;
  forceRefresh?: number; // Trigger for forced refresh
}

export function WalletConnectionIndicator({
  className,
  showAddress = true,
  showNetwork = true,
  onClick,
  forceRefresh,
}: WalletConnectionIndicatorProps) {
  const [walletInfo, setWalletInfo] = useState<WalletInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchWalletStatus = async () => {
      try {
        // Force fresh fetch with cache-busting timestamp and fresh endpoint
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(7);
        const response = await fetch(`/api/wallet/fresh-info?t=${timestamp}&r=${random}`, {
          cache: 'no-cache',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
            'X-Requested-With': 'XMLHttpRequest'
          }
        });
        const data = await response.json();
        
        if (data.success) {
          setWalletInfo(data.data);
        }
      } catch (error) {
        // Silently fail for indicator
      } finally {
        setLoading(false);
      }
    };

    fetchWalletStatus();
    
    // Refresh every 10 seconds for faster updates
    const interval = setInterval(fetchWalletStatus, 10000);
    return () => clearInterval(interval);
  }, [forceRefresh]); // Re-run when forceRefresh changes
  
  // Force refresh when forceRefresh prop changes
  useEffect(() => {
    if (forceRefresh) {
      setLoading(true);
      const fetchWalletStatus = async () => {
        try {
          const timestamp = Date.now();
          const random = Math.random().toString(36).substring(7);
          const response = await fetch(`/api/wallet/fresh-info?t=${timestamp}&r=${random}`, {
            cache: 'no-cache',
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0',
              'X-Requested-With': 'XMLHttpRequest'
            }
          });
          const data = await response.json();
          
          if (data.success) {
            setWalletInfo(data.data);
          }
        } catch (error) {
          // Silently fail for indicator
        } finally {
          setLoading(false);
        }
      };
      fetchWalletStatus();
    }
  }, [forceRefresh]);

  const formatAddress = (address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  const getStatusColor = (isValid: boolean) => {
    return isValid ? 'text-green-600' : 'text-red-600';
  };

  const getStatusIcon = (isValid: boolean) => {
    return isValid ? '🟢' : '🔴';
  };

  const getNetworkIcon = (isMainnet: boolean) => {
    return isMainnet ? '🔴' : '🟡';
  };

  if (loading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="h-2 w-2 rounded-full bg-gray-400 animate-pulse" />
        <span className="text-sm text-gray-500">Loading...</span>
      </div>
    );
  }

  if (!walletInfo) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <span className="text-lg">🔴</span>
        <span className="text-sm text-red-600">Disconnected</span>
      </div>
    );
  }

  const content = (
    <div className="flex items-center gap-2">
      {/* Connection Status */}
      <span className="text-sm">{getStatusIcon(walletInfo.isValid)}</span>
      
      {/* Network Indicator */}
      {showNetwork && (
        <div className="flex items-center gap-1">
          <span className="text-sm">{getNetworkIcon(walletInfo.isMainnet)}</span>
          <span className={`text-xs font-medium ${
            walletInfo.isMainnet ? 'text-red-600' : 'text-yellow-600'
          }`}>
            {walletInfo.network}
          </span>
          {walletInfo.isMainnet && (
            <span className="text-xs bg-red-100 text-red-800 px-1 rounded font-bold">
              LIVE
            </span>
          )}
        </div>
      )}

      {/* Address */}
      {showAddress && walletInfo.isValid && (
        <code className="text-xs bg-gray-100 px-2 py-1 rounded font-mono">
          {formatAddress(walletInfo.address)}
        </code>
      )}

      {/* Status Text */}
      <span className={`text-sm font-medium ${getStatusColor(walletInfo.isValid)}`}>
        {walletInfo.isValid ? 'Connected' : 'Disconnected'}
      </span>
    </div>
  );

  if (onClick) {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={onClick}
        className={`h-auto p-2 hover:bg-gray-50 ${className}`}
      >
        {content}
      </Button>
    );
  }

  return (
    <div className={`flex items-center gap-2 p-2 ${className}`}>
      {content}
    </div>
  );
}