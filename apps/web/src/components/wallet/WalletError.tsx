'use client';

import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface WalletErrorProps {
  error: string;
  onRetry?: () => void;
  onDismiss?: () => void;
  className?: string;
  type?: 'connection' | 'transaction' | 'balance' | 'validation';
}

export function WalletError({ 
  error, 
  onRetry, 
  onDismiss, 
  className,
  type = 'connection' 
}: WalletErrorProps) {
  const getErrorIcon = (errorType: string) => {
    switch (errorType) {
      case 'connection': return '🔌';
      case 'transaction': return '💳';
      case 'balance': return '💰';
      case 'validation': return '⚠️';
      default: return '❌';
    }
  };

  const getErrorTitle = (errorType: string) => {
    switch (errorType) {
      case 'connection': return 'Connection Error';
      case 'transaction': return 'Transaction Error';
      case 'balance': return 'Balance Error';
      case 'validation': return 'Validation Error';
      default: return 'Error';
    }
  };

  const getErrorSuggestions = (errorType: string, errorMessage: string) => {
    const suggestions = [];

    if (errorMessage.includes('Helius') || errorMessage.includes('RPC')) {
      suggestions.push('Check if Helius API key is configured correctly');
      suggestions.push('Verify network connection');
    }

    if (errorMessage.includes('private key') || errorMessage.includes('wallet')) {
      suggestions.push('Check if SOLANA_PRIVATE_KEY is set in environment');
      suggestions.push('Verify private key format (base58 encoded)');
    }

    if (errorMessage.includes('balance') || errorMessage.includes('insufficient')) {
      suggestions.push('Make sure wallet has sufficient SOL balance');
      suggestions.push('Consider adding funds to your wallet');
    }

    if (errorMessage.includes('transaction') || errorMessage.includes('failed')) {
      suggestions.push('Try reducing transaction amount');
      suggestions.push('Check slippage tolerance settings');
    }

    if (errorMessage.includes('mainnet') && errorMessage.includes('limit')) {
      suggestions.push('Transaction amount exceeds safety limit');
      suggestions.push('Use smaller amounts for testing');
    }

    // Default suggestions
    if (suggestions.length === 0) {
      suggestions.push('Try refreshing the page');
      suggestions.push('Check your internet connection');
    }

    return suggestions;
  };

  const isDevError = error.includes('development') || error.includes('API key') || error.includes('Helius');

  return (
    <Card className={`p-4 border-red-200 bg-red-50 ${className}`}>
      <div className="space-y-3">
        {/* Error Header */}
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <span className="text-lg">{getErrorIcon(type)}</span>
            <h3 className="text-lg font-semibold text-red-800">
              {getErrorTitle(type)}
            </h3>
          </div>
          {onDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismiss}
              className="h-6 w-6 p-0 text-red-600 hover:text-red-800"
            >
              ×
            </Button>
          )}
        </div>

        {/* Error Message */}
        <div className="p-3 bg-red-100 border border-red-200 rounded-md">
          <p className="text-red-800 text-sm">{error}</p>
        </div>

        {/* Development Note */}
        {isDevError && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-yellow-800 text-xs">
              💡 <strong>Development Note:</strong> This error is expected in development 
              environment without proper API keys configured.
            </p>
          </div>
        )}

        {/* Suggestions */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-red-800">Possible Solutions:</h4>
          <ul className="space-y-1">
            {getErrorSuggestions(type, error).map((suggestion, index) => (
              <li key={index} className="text-xs text-red-700 flex items-start gap-2">
                <span className="text-red-400 mt-0.5">•</span>
                {suggestion}
              </li>
            ))}
          </ul>
        </div>

        {/* Actions */}
        {onRetry && (
          <div className="flex gap-2 pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onRetry}
              className="text-red-600 border-red-300 hover:bg-red-50"
            >
              Try Again
            </Button>
          </div>
        )}

        {/* Additional Help Links */}
        <div className="border-t border-red-200 pt-3 space-y-2">
          <p className="text-xs text-red-600 font-medium">Need Help?</p>
          <div className="flex flex-wrap gap-2 text-xs">
            <a 
              href="#" 
              className="text-red-600 hover:text-red-800 underline"
              onClick={(e) => e.preventDefault()}
            >
              Troubleshooting Guide
            </a>
            <span className="text-red-400">•</span>
            <a 
              href="#" 
              className="text-red-600 hover:text-red-800 underline"
              onClick={(e) => e.preventDefault()}
            >
              Configuration Help
            </a>
          </div>
        </div>
      </div>
    </Card>
  );
}