'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  transactionService, 
  type TransactionParams, 
  type TransactionResult, 
  type TransactionLimits,
  type DailyStats 
} from '@/services/transaction';

interface TransactionTestProps {
  walletAddress?: string;
  walletBalance?: number;
  onTransactionComplete?: (result: TransactionResult) => void;
}

export function TransactionTest({ 
  walletAddress, 
  walletBalance,
  onTransactionComplete 
}: TransactionTestProps) {
  // Form state
  const [recipient, setRecipient] = useState(walletAddress || '');
  const [amountSol, setAmountSol] = useState('');
  const [memo, setMemo] = useState('Test transaction');

  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<TransactionResult | null>(null);
  const [limits, setLimits] = useState<TransactionLimits | null>(null);
  const [dailyStats, setDailyStats] = useState<DailyStats | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [validationResult, setValidationResult] = useState<any>(null);

  // Load limits on component mount
  React.useEffect(() => {
    loadLimitsAndStats();
  }, []);

  const loadLimitsAndStats = async () => {
    try {
      const data = await transactionService.getLimitsAndStats();
      setLimits(data.limits);
      setDailyStats(data.dailyStats);
    } catch (error) {
      console.warn('Failed to load transaction limits:', error);
    }
  };

  const validateTransaction = async () => {
    if (!recipient || !amountSol || parseFloat(amountSol) <= 0) {
      setError('Please enter valid recipient and amount');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const params: TransactionParams = {
        recipient,
        amountSol: parseFloat(amountSol),
        memo: memo || undefined,
      };

      const validation = await transactionService.validateTransaction(params);
      setValidationResult(validation);

      if (!validation.isValid) {
        setError(`Validation failed: ${validation.errors.join(', ')}`);
      } else {
        setShowConfirmation(true);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Validation failed');
    } finally {
      setIsLoading(false);
    }
  };

  const simulateTransaction = async () => {
    if (!validationResult) {
      await validateTransaction();
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const params: TransactionParams = {
        recipient,
        amountSol: parseFloat(amountSol),
        memo: memo || undefined,
      };

      const result = await transactionService.simulateTransaction(params);
      setResult(result);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Simulation failed');
    } finally {
      setIsLoading(false);
    }
  };

  const executeTransaction = async () => {
    if (!validationResult || !showConfirmation) {
      setError('Please validate transaction first');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const params: TransactionParams = {
        recipient,
        amountSol: parseFloat(amountSol),
        memo: memo || undefined,
      };

      const result = await transactionService.sendTransaction(params);
      setResult(result);
      setShowConfirmation(false);
      
      if (result.success) {
        // Clear form on success
        setAmountSol('');
        setMemo('Test transaction');
        setValidationResult(null);
        
        // Reload limits and stats
        await loadLimitsAndStats();
      }

      onTransactionComplete?.(result);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Transaction failed');
    } finally {
      setIsLoading(false);
    }
  };

  const testWalletConnection = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const testResult = await transactionService.testWalletConnection();
      setResult({
        signature: 'CONNECTION_TEST',
        success: testResult.success,
        confirmationStatus: 'processed',
        error: testResult.error,
      });
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Connection test failed');
    } finally {
      setIsLoading(false);
    }
  };

  const setSelfTransfer = () => {
    if (walletAddress) {
      setRecipient(walletAddress);
    }
  };

  const setSmallAmount = () => {
    setAmountSol('0.001'); // 0.001 SOL for testing
  };

  return (
    <div className="space-y-6">
      {/* Transaction Limits */}
      {limits && dailyStats && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Transaction Limits & Stats</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <Label className="text-xs text-gray-600">Max Per Transaction</Label>
                <p className="font-mono">{limits.maxPerTransaction} SOL</p>
              </div>
              <div>
                <Label className="text-xs text-gray-600">Daily Limit</Label>
                <p className="font-mono">{limits.maxDaily} SOL</p>
              </div>
              <div>
                <Label className="text-xs text-gray-600">Used Today</Label>
                <p className="font-mono">
                  {dailyStats.amountToday.toFixed(6)} SOL ({dailyStats.transactionsToday} tx)
                </p>
              </div>
              <div>
                <Label className="text-xs text-gray-600">Remaining</Label>
                <p className="font-mono">
                  {dailyStats.remainingAmount.toFixed(6)} SOL ({dailyStats.remainingCount} tx)
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Transaction Form */}
      <Card>
        <CardHeader>
          <CardTitle>Test SOL Transfer</CardTitle>
          <CardDescription>
            Test wallet signing capability with a simple SOL transfer
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Recipient */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>Recipient Address</Label>
              <Button
                variant="ghost"
                size="sm"
                onClick={setSelfTransfer}
                className="h-6 px-2 text-xs"
              >
                Self Transfer
              </Button>
            </div>
            <Input
              value={recipient}
              onChange={(e) => setRecipient(e.target.value)}
              placeholder="Enter Solana address..."
              className="font-mono text-sm"
            />
            {recipient && !transactionService.isValidSolanaAddress(recipient) && (
              <p className="text-xs text-red-600">Invalid Solana address format</p>
            )}
          </div>

          {/* Amount */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>Amount (SOL)</Label>
              <div className="flex items-center gap-2">
                {walletBalance && (
                  <span className="text-xs text-gray-600">
                    Balance: {walletBalance.toFixed(6)} SOL
                  </span>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={setSmallAmount}
                  className="h-6 px-2 text-xs"
                >
                  Test Amount
                </Button>
              </div>
            </div>
            <Input
              type="number"
              step="0.001"
              min="0.001"
              max={limits?.maxPerTransaction || 1}
              value={amountSol}
              onChange={(e) => setAmountSol(e.target.value)}
              placeholder="0.001"
              className="font-mono"
            />
          </div>

          {/* Memo */}
          <div className="space-y-2">
            <Label>Memo (Optional)</Label>
            <Input
              value={memo}
              onChange={(e) => setMemo(e.target.value)}
              placeholder="Test transaction"
              maxLength={100}
            />
          </div>

          {/* Validation Result */}
          {validationResult && (
            <Alert>
              <AlertDescription>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant={validationResult.isValid ? "secondary" : "destructive"}>
                      {validationResult.isValid ? "Valid" : "Invalid"}
                    </Badge>
                    <span className="text-sm">
                      Estimated fee: {validationResult.estimatedFee?.toFixed(6)} SOL
                    </span>
                  </div>
                  
                  {validationResult.errors?.length > 0 && (
                    <div className="text-red-600 text-sm">
                      {validationResult.errors.map((error: string, i: number) => (
                        <div key={i}>❌ {error}</div>
                      ))}
                    </div>
                  )}
                  
                  {validationResult.warnings?.length > 0 && (
                    <div className="text-yellow-600 text-sm">
                      {validationResult.warnings.map((warning: string, i: number) => (
                        <div key={i}>⚠️ {warning}</div>
                      ))}
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Confirmation Dialog */}
          {showConfirmation && validationResult?.isValid && (
            <Alert>
              <AlertDescription>
                <div className="space-y-3">
                  <p className="font-semibold">⚠️ Confirm Transaction</p>
                  <div className="text-sm space-y-1">
                    <div>To: {transactionService.formatSignature(recipient, 6)}</div>
                    <div>Amount: {amountSol} SOL</div>
                    <div>Fee: ~{validationResult.estimatedFee?.toFixed(6)} SOL</div>
                    <div>Total: ~{validationResult.estimatedTotal?.toFixed(6)} SOL</div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      onClick={executeTransaction}
                      disabled={isLoading}
                      size="sm"
                    >
                      {isLoading ? 'Sending...' : 'Confirm & Send'}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setShowConfirmation(false)}
                      size="sm"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={testWalletConnection}
              disabled={isLoading}
              variant="outline"
              size="sm"
            >
              Test Connection
            </Button>
            
            <Button
              onClick={validateTransaction}
              disabled={isLoading || !recipient || !amountSol}
              variant="outline"
              size="sm"
            >
              {isLoading ? 'Validating...' : 'Validate'}
            </Button>
            
            <Button
              onClick={simulateTransaction}
              disabled={isLoading || !validationResult?.isValid}
              variant="outline"
              size="sm"
            >
              {isLoading ? 'Simulating...' : 'Simulate'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Transaction Result */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm flex items-center gap-2">
              Transaction Result
              <Badge variant={result.success ? "secondary" : "destructive"}>
                {result.success ? "Success" : "Failed"}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm font-mono">
              {result.signature && (
                <div>
                  <Label className="text-xs text-gray-600">Signature:</Label>
                  <p className="break-all">{result.signature}</p>
                </div>
              )}
              
              {result.confirmationStatus && (
                <div>
                  <Label className="text-xs text-gray-600">Status:</Label>
                  <p>{result.confirmationStatus}</p>
                </div>
              )}
              
              {result.fee && (
                <div>
                  <Label className="text-xs text-gray-600">Fee:</Label>
                  <p>{result.fee.toFixed(6)} SOL</p>
                </div>
              )}
              
              {result.slot && (
                <div>
                  <Label className="text-xs text-gray-600">Slot:</Label>
                  <p>{result.slot}</p>
                </div>
              )}
              
              {result.error && (
                <div>
                  <Label className="text-xs text-gray-600">Error:</Label>
                  <p className="text-red-600">{result.error}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}