'use client';

import { useEffect, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface WalletInfo {
  publicKey: string;
  address: string;
  isValid: boolean;
  network: string;
  rpcUrl: string;
  isMainnet: boolean;
}

interface WalletStatusProps {
  onRefresh?: () => void;
  className?: string;
}

export function WalletStatus({ onRefresh, className }: WalletStatusProps) {
  const [walletInfo, setWalletInfo] = useState<WalletInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchWalletInfo = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/wallet/info');
      const data = await response.json();

      if (data.success) {
        setWalletInfo(data.data);
      } else {
        setError('Failed to fetch wallet info');
      }
    } catch (err) {
      setError('Network error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWalletInfo();
  }, []);

  const handleRefresh = () => {
    fetchWalletInfo();
    onRefresh?.();
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  const getNetworkColor = (isMainnet: boolean) => {
    return isMainnet ? 'text-red-600' : 'text-yellow-600';
  };

  const getNetworkIcon = (isMainnet: boolean) => {
    return isMainnet ? '🔴' : '🟡';
  };

  return (
    <Card className={`p-4 ${className}`}>
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Wallet Status</h3>
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleRefresh}
            disabled={loading}
          >
            {loading ? 'Refreshing...' : 'Refresh'}
          </Button>
        </div>

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        {walletInfo ? (
          <div className="space-y-3">
            {/* Connection Status */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Status:</span>
              <div className="flex items-center gap-2">
                <div className={`h-2 w-2 rounded-full ${
                  walletInfo.isValid ? 'bg-green-500' : 'bg-red-500'
                }`} />
                <span className={`text-sm font-medium ${
                  walletInfo.isValid ? 'text-green-600' : 'text-red-600'
                }`}>
                  {walletInfo.isValid ? 'Connected' : 'Disconnected'}
                </span>
              </div>
            </div>

            {/* Network */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Network:</span>
              <div className="flex items-center gap-2">
                <span>{getNetworkIcon(walletInfo.isMainnet)}</span>
                <span className={`text-sm font-medium ${getNetworkColor(walletInfo.isMainnet)}`}>
                  {walletInfo.network}
                  {walletInfo.isMainnet && (
                    <span className="ml-1 text-xs bg-red-100 text-red-800 px-1 rounded">
                      LIVE
                    </span>
                  )}
                </span>
              </div>
            </div>

            {/* Wallet Address */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Address:</span>
              <div className="flex items-center gap-2">
                <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                  {formatAddress(walletInfo.address)}
                </code>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigator.clipboard.writeText(walletInfo.address)}
                  className="h-6 w-6 p-0"
                  title="Copy full address"
                >
                  📋
                </Button>
              </div>
            </div>

            {/* Safety Warning for Mainnet */}
            {walletInfo.isMainnet && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-red-800 text-xs">
                  ⚠️ <strong>MAINNET TRADING:</strong> You are trading with real money. 
                  Only use amounts you can afford to lose completely.
                </p>
              </div>
            )}
          </div>
        ) : loading ? (
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
          </div>
        ) : (
          <div className="text-center py-4">
            <p className="text-gray-500 text-sm">Click refresh to load wallet info</p>
          </div>
        )}
      </div>
    </Card>
  );
}