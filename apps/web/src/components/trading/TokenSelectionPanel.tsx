'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { InlineTokenBalance } from '@/components/trading/TokenBalanceDisplay';
import { TokenMetadata } from '@/services/tokenMetadata';

interface TokenSelectionPanelProps {
  label: string;
  tokenMint: string;
  tokenMetadata: TokenMetadata | null;
  balance: number | null;
  amount?: string;
  showAmountInput?: boolean;
  showAddressInput?: boolean;
  isConnected: boolean;
  onTokenMintChange?: (mint: string) => void;
  onAmountChange?: (amount: string) => void;
  onPresetClick?: (preset: { percentage: number }) => void;
  className?: string;
}

const AMOUNT_PRESETS = [
  { id: 'lite', name: 'LITE', percentage: 0.1 },
  { id: 'half', name: 'HAL<PERSON>', percentage: 0.5 },
  { id: 'pump', name: 'PUMP', percentage: 0.75 },
  { id: 'max', name: 'MAX', percentage: 1.0 },
];

const COMMON_TOKENS = [
  { mint: 'So11111111111111111111111111111111111111112', symbol: 'SOL', bg: 'bg-purple-700' },
  { mint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', symbol: 'USDC', bg: 'bg-gray-700' },
  { mint: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB', symbol: 'USDT', bg: 'bg-gray-700' },
  { mint: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263', symbol: 'BONK', bg: 'bg-orange-700' },
];

export function TokenSelectionPanel({
  label,
  tokenMint,
  tokenMetadata,
  balance,
  amount = '',
  showAmountInput = false,
  showAddressInput = false,
  isConnected,
  onTokenMintChange,
  onAmountChange,
  onPresetClick,
  className = ''
}: TokenSelectionPanelProps) {
  const handleAddressChange = (value: string) => {
    if (onTokenMintChange) {
      onTokenMintChange(value.trim());
    }
  };

  const handleAmountChange = (value: string) => {
    if (onAmountChange) {
      const sanitized = value.replace(/[^0-9.]/g, '');
      onAmountChange(sanitized);
    }
  };

  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-2">
        <Label className="text-gray-400 text-sm">{label}</Label>
        <div className="text-sm text-gray-400">
          {tokenMint === 'So11111111111111111111111111111111111111112' ? (
            `Balance: ${balance !== null ? balance.toFixed(6) : '0'} SOL`
          ) : (
            <InlineTokenBalance 
              tokenMint={tokenMint}
              tokenSymbol={tokenMetadata?.symbol}
              className="flex items-center gap-1"
            />
          )}
        </div>
      </div>
      
      <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 rounded-full bg-purple-600 flex items-center justify-center">
            <span className="text-white font-bold text-sm">
              {tokenMetadata?.symbol || (tokenMint.includes('So1111') ? 'SOL' : '?')}
            </span>
          </div>
          <div className="flex-1">
            <div className="text-white font-medium">
              {tokenMetadata?.symbol || (tokenMint.includes('So1111') ? 'SOL' : 'Unknown')}
            </div>
            <div className="text-gray-400 text-sm">
              {tokenMetadata?.name || (tokenMint.includes('So1111') ? 'Solana' : 'Unknown Token')}
            </div>
          </div>
        </div>
        
        {showAmountInput && (
          <div className="mt-4 text-right">
            <Input
              type="text"
              value={amount}
              onChange={(e) => handleAmountChange(e.target.value)}
              placeholder="0.0"
              className="bg-transparent border-0 text-2xl font-bold text-white placeholder-gray-500 text-right focus-visible:ring-0"
              disabled={!isConnected}
            />
            <div className="text-gray-400 text-sm mt-1">
              {tokenMint === 'So11111111111111111111111111111111111111112' 
                ? `≈ $${(parseFloat(amount || '0') * 200).toFixed(2)}` // Placeholder USD conversion
                : `${parseFloat(amount || '0').toLocaleString(undefined, { 
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 4 
                  })} ${tokenMetadata?.symbol || 'tokens'}`
              }
            </div>
            
            {/* Amount Presets */}
            <div className="flex space-x-2 mt-4">
              {AMOUNT_PRESETS.map((preset) => (
                <Button
                  key={preset.id}
                  variant="outline"
                  size="sm"
                  className="bg-gray-700 hover:bg-gray-600 text-gray-300 border-gray-600 text-xs px-3"
                  onClick={() => onPresetClick?.(preset)}
                  disabled={!isConnected}
                >
                  {preset.name}
                </Button>
              ))}
            </div>
          </div>
        )}

        {showAddressInput && (
          <div className="mt-4">
            <Input
              type="text"
              value={tokenMint}
              onChange={(e) => handleAddressChange(e.target.value)}
              placeholder="Enter token address..."
              className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 font-mono text-sm"
              disabled={!isConnected}
            />
            
            {/* Token shortcuts */}
            <div className="flex flex-wrap gap-2 mt-2">
              {COMMON_TOKENS.map((token) => (
                <Button
                  key={token.mint}
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => onTokenMintChange?.(token.mint)}
                  className={`text-xs h-6 ${token.bg} hover:opacity-80 text-white border-transparent`}
                  disabled={!isConnected}
                >
                  {token.symbol}
                </Button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}