'use client';

import React, { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Decimal } from 'decimal.js';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Alert } from '@/components/ui/alert';
import { Plus, Trash2, Save, Settings, Loader2 } from 'lucide-react';
import { ExitStrategyPreset, exitStrategyService } from '@/services/exitStrategy';

// Form validation schema using Zod
const takeProfitTierSchema = z.object({
  percentage: z.number().min(0.1).max(100, 'Percentage must be between 0.1% and 100%'),
  targetPercentage: z.number().min(0.1, 'Target percentage must be at least 0.1%').optional(),
  targetPrice: z.number().min(0, 'Target price must be positive').optional(),
  isActive: z.boolean(),
});

const exitStrategySchema = z.object({
  takeProfitTiers: z.array(takeProfitTierSchema).max(5, 'Maximum 5 take profit tiers allowed'),
  stopLossEnabled: z.boolean(),
  stopLossPercentage: z.number().min(0.1).max(100).optional(),
  stopLossPrice: z.number().min(0).optional(),
  trailingStopEnabled: z.boolean(),
  trailingStopDistancePercentage: z.number().min(0.1).max(50).optional(),
  moonBagEnabled: z.boolean(),
  moonBagPercentage: z.number().min(5).max(10, 'Moon bag must be between 5% and 10%').optional(),
}).refine((data) => {
  // Validate total percentages don't exceed 100%
  const tierPercentages = data.takeProfitTiers.reduce((sum, tier) => sum + tier.percentage, 0);
  const moonBagPercentage = data.moonBagEnabled ? (data.moonBagPercentage || 0) : 0;
  return tierPercentages + moonBagPercentage <= 100;
}, {
  message: 'Total tier percentages plus moon bag cannot exceed 100%',
  path: ['takeProfitTiers'],
}).refine((data) => {
  // Validate stop loss configuration
  if (data.stopLossEnabled) {
    return data.stopLossPercentage || data.stopLossPrice;
  }
  return true;
}, {
  message: 'Stop loss percentage or price is required when stop loss is enabled',
  path: ['stopLossPercentage'],
}).refine((data) => {
  // Validate trailing stop configuration
  if (data.trailingStopEnabled) {
    return data.trailingStopDistancePercentage;
  }
  return true;
}, {
  message: 'Trailing distance is required when trailing stop is enabled',
  path: ['trailingStopDistancePercentage'],
}).refine((data) => {
  // Validate moon bag configuration
  if (data.moonBagEnabled) {
    return data.moonBagPercentage;
  }
  return true;
}, {
  message: 'Moon bag percentage is required when moon bag is enabled',
  path: ['moonBagPercentage'],
});

type ExitStrategyFormData = z.infer<typeof exitStrategySchema>;

interface ExitStrategyFormProps {
  entryPrice?: number;
  onStrategyChange?: (strategy: ExitStrategyFormData | null) => void;
  className?: string;
}

export const ExitStrategyForm: React.FC<ExitStrategyFormProps> = ({
  entryPrice,
  onStrategyChange,
  className,
}) => {
  // Form state
  const form = useForm<ExitStrategyFormData>({
    resolver: zodResolver(exitStrategySchema),
    defaultValues: {
      takeProfitTiers: [
        { percentage: 25, targetPercentage: 50, isActive: true },
        { percentage: 25, targetPercentage: 100, isActive: true },
        { percentage: 25, targetPercentage: 200, isActive: true },
      ],
      stopLossEnabled: true,
      stopLossPercentage: 20,
      trailingStopEnabled: false,
      moonBagEnabled: true,
      moonBagPercentage: 5,
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'takeProfitTiers',
  });

  // Component state
  const [presets, setPresets] = useState<ExitStrategyPreset[]>([]);
  const [isLoadingPresets, setIsLoadingPresets] = useState(false);
  const [selectedPreset, setSelectedPreset] = useState<ExitStrategyPreset | null>(null);
  const [isSavingPreset, setIsSavingPreset] = useState(false);
  const [presetName, setPresetName] = useState('');
  const [presetDescription, setPresetDescription] = useState('');
  const [showPresetDialog, setShowPresetDialog] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  // Watch form values for real-time validation and preview
  const watchedValues = form.watch();

  // Load presets on component mount
  useEffect(() => {
    loadPresets();
  }, []);

  // Notify parent of strategy changes
  useEffect(() => {
    const isValid = form.formState.isValid;
    if (isValid && onStrategyChange) {
      onStrategyChange(watchedValues);
    } else if (!isValid && onStrategyChange) {
      onStrategyChange(null);
    }
  }, [watchedValues, form.formState.isValid, onStrategyChange]);

  const loadPresets = async () => {
    try {
      setIsLoadingPresets(true);
      const presetsData = await exitStrategyService.getPresets();
      setPresets(presetsData);
    } catch (error) {
      console.error('Failed to load exit strategy presets:', error);
      setErrors(['Failed to load exit strategy presets']);
    } finally {
      setIsLoadingPresets(false);
    }
  };

  const addTier = () => {
    if (fields.length < 5) {
      append({ percentage: 25, targetPercentage: 50, isActive: true });
    }
  };

  const removeTier = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  const loadPreset = (preset: ExitStrategyPreset) => {
    const config = preset.configuration;
    form.reset({
      takeProfitTiers: config.takeProfitTiers.map(tier => ({
        percentage: tier.percentage,
        targetPercentage: tier.targetPercentage,
        targetPrice: tier.targetPrice,
        isActive: tier.isActive,
      })),
      stopLossEnabled: config.stopLossEnabled,
      stopLossPercentage: config.stopLossPercentage,
      stopLossPrice: config.stopLossPrice,
      trailingStopEnabled: config.trailingStopEnabled,
      trailingStopDistancePercentage: config.trailingStopDistancePercentage,
      moonBagEnabled: config.moonBagEnabled,
      moonBagPercentage: config.moonBagPercentage,
    });
    setSelectedPreset(preset);
  };

  const savePreset = async () => {
    if (!presetName.trim()) {
      setErrors(['Preset name is required']);
      return;
    }

    try {
      setIsSavingPreset(true);
      const preset: Omit<ExitStrategyPreset, 'id' | 'createdAt' | 'updatedAt'> = {
        name: presetName,
        description: presetDescription,
        isDefault: false,
        configuration: watchedValues,
      };

      await exitStrategyService.createPreset(preset);
      await loadPresets();
      setShowPresetDialog(false);
      setPresetName('');
      setPresetDescription('');
      setErrors([]);
    } catch (error) {
      console.error('Failed to save preset:', error);
      setErrors(['Failed to save preset']);
    } finally {
      setIsSavingPreset(false);
    }
  };

  const calculateCurrentStopPrice = () => {
    if (!entryPrice || !watchedValues.trailingStopEnabled || !watchedValues.trailingStopDistancePercentage) {
      return null;
    }
    const distanceDecimal = new Decimal(watchedValues.trailingStopDistancePercentage).div(100);
    const stopPrice = new Decimal(entryPrice).mul(new Decimal(1).minus(distanceDecimal));
    return stopPrice.toFixed(6);
  };

  const calculateTotalPercentage = () => {
    const tierTotal = watchedValues.takeProfitTiers.reduce((sum, tier) => sum + (tier.percentage || 0), 0);
    const moonBagTotal = watchedValues.moonBagEnabled ? (watchedValues.moonBagPercentage || 0) : 0;
    return tierTotal + moonBagTotal;
  };

  const getTotalPercentageColor = () => {
    const total = calculateTotalPercentage();
    if (total > 100) return 'text-red-500';
    if (total === 100) return 'text-green-500';
    return 'text-yellow-500';
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-lg font-semibold">Exit Strategy Configuration</CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className={getTotalPercentageColor()}>
              Total: {calculateTotalPercentage()}%
            </Badge>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" disabled={isLoadingPresets}>
                  {isLoadingPresets ? <Loader2 className="h-4 w-4 animate-spin" /> : <Settings className="h-4 w-4" />}
                  Presets
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                {presets.map((preset) => (
                  <DropdownMenuItem
                    key={preset.id}
                    onClick={() => loadPreset(preset)}
                    className="cursor-pointer"
                  >
                    <div className="flex flex-col">
                      <span className="font-medium">{preset.name}</span>
                      {preset.description && (
                        <span className="text-xs text-muted-foreground">{preset.description}</span>
                      )}
                    </div>
                  </DropdownMenuItem>
                ))}
                <DropdownMenuItem onClick={() => setShowPresetDialog(true)} className="cursor-pointer">
                  <Save className="mr-2 h-4 w-4" />
                  Save Current as Preset
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {errors.length > 0 && (
            <Alert variant="destructive">
              <ul className="list-disc list-inside">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </Alert>
          )}

          {/* Take Profit Tiers */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">Take Profit Tiers</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addTier}
                disabled={fields.length >= 5}
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Tier
              </Button>
            </div>
            
            <div className="space-y-3">
              {fields.map((field, index) => (
                <div key={field.id} className="flex items-center gap-3 p-3 border rounded-lg">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      {...form.register(`takeProfitTiers.${index}.isActive`)}
                    />
                    <Label className="text-sm font-medium">Tier {index + 1}</Label>
                  </div>
                  
                  <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-2">
                    <div>
                      <Label className="text-xs text-muted-foreground">Sell %</Label>
                      <Input
                        type="number"
                        step="0.1"
                        min="0.1"
                        max="100"
                        {...form.register(`takeProfitTiers.${index}.percentage`, { valueAsNumber: true })}
                        className="h-8"
                      />
                    </div>
                    <div>
                      <Label className="text-xs text-muted-foreground">Target % Gain</Label>
                      <Input
                        type="number"
                        step="0.1"
                        min="0.1"
                        {...form.register(`takeProfitTiers.${index}.targetPercentage`, { valueAsNumber: true })}
                        className="h-8"
                      />
                    </div>
                    <div>
                      <Label className="text-xs text-muted-foreground">Target Price</Label>
                      <Input
                        type="number"
                        step="0.000001"
                        min="0"
                        {...form.register(`takeProfitTiers.${index}.targetPrice`, { valueAsNumber: true })}
                        className="h-8"
                        placeholder="Optional"
                      />
                    </div>
                  </div>
                  
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeTier(index)}
                    disabled={fields.length <= 1}
                    className="p-2"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          </div>

          {/* Stop Loss Configuration */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                {...form.register('stopLossEnabled')}
              />
              <Label className="text-base font-medium">Stop Loss</Label>
            </div>
            
            {watchedValues.stopLossEnabled && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ml-6">
                <div>
                  <Label className="text-sm">Stop Loss Percentage</Label>
                  <Input
                    type="number"
                    step="0.1"
                    min="0.1"
                    max="100"
                    {...form.register('stopLossPercentage', { valueAsNumber: true })}
                    placeholder="e.g., 20 for -20%"
                  />
                </div>
                <div>
                  <Label className="text-sm">Stop Loss Price (Optional)</Label>
                  <Input
                    type="number"
                    step="0.000001"
                    min="0"
                    {...form.register('stopLossPrice', { valueAsNumber: true })}
                    placeholder="Fixed price"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Trailing Stop Configuration */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                {...form.register('trailingStopEnabled')}
              />
              <Label className="text-base font-medium">Trailing Stop</Label>
            </div>
            
            {watchedValues.trailingStopEnabled && (
              <div className="ml-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm">Trail Distance (%)</Label>
                    <Input
                      type="number"
                      step="0.1"
                      min="0.1"
                      max="50"
                      {...form.register('trailingStopDistancePercentage', { valueAsNumber: true })}
                      placeholder="e.g., 10 for 10% trail"
                    />
                  </div>
                  {entryPrice && (
                    <div>
                      <Label className="text-sm">Current Stop Price Preview</Label>
                      <div className="h-10 px-3 py-2 border rounded-md bg-muted text-sm">
                        {calculateCurrentStopPrice() || 'N/A'}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Moon Bag Configuration */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                {...form.register('moonBagEnabled')}
              />
              <Label className="text-base font-medium">Moon Bag (Hold Forever)</Label>
            </div>
            
            {watchedValues.moonBagEnabled && (
              <div className="ml-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm">Moon Bag Percentage (5-10%)</Label>
                    <Input
                      type="number"
                      step="0.1"
                      min="5"
                      max="10"
                      {...form.register('moonBagPercentage', { valueAsNumber: true })}
                      placeholder="e.g., 5"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Save Preset Dialog */}
      <Dialog open={showPresetDialog} onOpenChange={setShowPresetDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Save Exit Strategy Preset</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="presetName">Preset Name</Label>
              <Input
                id="presetName"
                value={presetName}
                onChange={(e) => setPresetName(e.target.value)}
                placeholder="e.g., Conservative, Aggressive, etc."
              />
            </div>
            <div>
              <Label htmlFor="presetDescription">Description (Optional)</Label>
              <Input
                id="presetDescription"
                value={presetDescription}
                onChange={(e) => setPresetDescription(e.target.value)}
                placeholder="Brief description of this strategy"
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowPresetDialog(false)}>
                Cancel
              </Button>
              <Button onClick={savePreset} disabled={isSavingPreset}>
                {isSavingPreset ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Save className="h-4 w-4 mr-2" />}
                Save Preset
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ExitStrategyForm;
