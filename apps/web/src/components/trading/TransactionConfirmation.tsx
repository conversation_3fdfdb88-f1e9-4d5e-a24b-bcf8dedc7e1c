'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert } from '@/components/ui/alert';
import { Label } from '@/components/ui/label';
import { MEVProtectionSettings, type MEVProtectionConfig } from './MEVProtectionSettings';

export interface TradeDetails {
  inputMint: string;
  outputMint: string;
  amountSol: number;
  expectedOutput: number;
  priceImpact: number;
  slippageBps: number;
  minimumReceived: number;
  route?: string[];
  marketData?: {
    inputSymbol: string;
    outputSymbol: string;
    inputPrice: number;
    outputPrice: number;
    ratio: number;
  };
}

export interface TransactionEstimate {
  baseFee: number;
  computeUnits: number;
  priorityFee: number;
  jitoTip: number;
  totalCost: number;
  successProbability: number;
  estimatedTime: string;
}

interface TransactionConfirmationProps {
  isOpen: boolean;
  tradeDetails: TradeDetails | null;
  onConfirm: (config: MEVProtectionConfig) => void;
  onCancel: () => void;
  isExecuting?: boolean;
  initialMEVLevel?: 'basic' | 'standard' | 'maximum';
  initialSpeedPreference?: 'economy' | 'standard' | 'fast' | 'turbo';
}

export function TransactionConfirmation({
  isOpen,
  tradeDetails,
  onConfirm,
  onCancel,
  isExecuting = false,
  initialMEVLevel = 'standard',
  initialSpeedPreference = 'standard'
}: TransactionConfirmationProps) {
  const [mevConfig, setMEVConfig] = useState<MEVProtectionConfig | null>(null);
  const [selectedLevel, setSelectedLevel] = useState<'basic' | 'standard' | 'maximum'>(initialMEVLevel);
  const [selectedSpeed, setSelectedSpeed] = useState<'economy' | 'standard' | 'fast' | 'turbo'>(initialSpeedPreference);
  const [estimate, setEstimate] = useState<TransactionEstimate | null>(null);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setValidationError(null);
    }
  }, [isOpen]);

  // Update estimate when MEV config changes
  useEffect(() => {
    if (mevConfig && tradeDetails) {
      updateEstimate();
    }
  }, [mevConfig, tradeDetails]);

  const updateEstimate = async () => {
    if (!mevConfig || !tradeDetails) return;

    try {
      // Calculate success probability
      const probabilityResponse = await fetch('/api/mev-protection/success-probability', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          priorityFeeLamports: mevConfig.priorityFeeLamports
        })
      });

      const probabilityData = await probabilityResponse.json();
      
      const newEstimate: TransactionEstimate = {
        baseFee: 5000, // Base transaction fee (~0.005 SOL)
        computeUnits: 200000, // Estimated compute units for Jupiter swap
        priorityFee: mevConfig.priorityFeeLamports,
        jitoTip: mevConfig.jitoTipLamports,
        totalCost: mevConfig.totalEstimatedCost,
        successProbability: probabilityData.success ? probabilityData.data.probability : 0.8,
        estimatedTime: getEstimatedTime(selectedSpeed, selectedLevel)
      };

      setEstimate(newEstimate);
    } catch (error) {
      console.warn('Failed to update estimate:', error);
    }
  };

  const getEstimatedTime = (speed: string, level: string): string => {
    const baseTime = {
      'economy': 30,
      'standard': 15,
      'fast': 8,
      'turbo': 5
    }[speed] || 15;

    const levelMultiplier = {
      'basic': 1,
      'standard': 0.8,
      'maximum': 0.6
    }[level] || 1;

    const estimatedSeconds = Math.round(baseTime * levelMultiplier);
    return `~${estimatedSeconds}s`;
  };

  const validateTransaction = async () => {
    if (!tradeDetails || !mevConfig) return false;

    setIsValidating(true);
    setValidationError(null);

    try {
      // Validate priority fee
      const feeValidation = await fetch('/api/mev-protection/validate-priority-fee', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          priorityFeeLamports: mevConfig.priorityFeeLamports,
          maxAllowedFee: 100000 // 0.1 SOL max
        })
      });

      const feeData = await feeValidation.json();
      
      if (!feeData.success || !feeData.data.isValid) {
        throw new Error(feeData.data?.error || 'Invalid priority fee');
      }

      // Warn for high fees
      if (feeData.data.warning) {
        setValidationError(`Warning: ${feeData.data.warning}`);
        return true; // Allow but warn
      }

      return true;
    } catch (error) {
      setValidationError(error instanceof Error ? error.message : 'Validation failed');
      return false;
    } finally {
      setIsValidating(false);
    }
  };

  const handleConfirm = async () => {
    if (!mevConfig) return;

    const isValid = await validateTransaction();
    if (isValid) {
      onConfirm(mevConfig);
    }
  };

  const formatSOL = (lamports: number) => {
    return (lamports / 1_000_000_000).toFixed(6);
  };

  const formatUSD = (sol: number) => {
    // Mock SOL price - in real app, would fetch from price service
    const solPrice = 100; // $100 per SOL
    return `$${(sol * solPrice).toFixed(2)}`;
  };

  const getPriceImpactColor = (impact: number) => {
    if (impact < 1) return 'text-green-600';
    if (impact < 3) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (!isOpen || !tradeDetails) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[95vh] overflow-y-auto">
        <div className="p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Confirm Transaction</h2>
            <Button variant="ghost" size="sm" onClick={onCancel} disabled={isExecuting}>
              ✕
            </Button>
          </div>

          {/* Trade Summary */}
          <div className="bg-gray-50 p-4 rounded-lg space-y-3">
            <h3 className="font-medium">Trade Summary</h3>
            
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">You Pay:</span>
                <div className="text-right">
                  <div className="font-medium">{tradeDetails.amountSol.toFixed(6)} SOL</div>
                  <div className="text-sm text-gray-500">
                    {formatUSD(tradeDetails.amountSol)}
                  </div>
                </div>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600">You Receive:</span>
                <div className="text-right">
                  <div className="font-medium">
                    {tradeDetails.expectedOutput.toLocaleString()} {tradeDetails.marketData?.outputSymbol || 'tokens'}
                  </div>
                  <div className="text-sm text-gray-500">
                    Minimum: {tradeDetails.minimumReceived.toLocaleString()}
                  </div>
                </div>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600">Price Impact:</span>
                <span className={`font-medium ${getPriceImpactColor(tradeDetails.priceImpact)}`}>
                  {tradeDetails.priceImpact.toFixed(2)}%
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600">Slippage Tolerance:</span>
                <span className="font-medium">{(tradeDetails.slippageBps / 100).toFixed(1)}%</span>
              </div>

              {tradeDetails.route && tradeDetails.route.length > 2 && (
                <div className="flex justify-between items-start">
                  <span className="text-gray-600">Route:</span>
                  <div className="text-right text-sm">
                    <Badge variant="secondary" className="text-xs">
                      {tradeDetails.route.length - 1} hop{tradeDetails.route.length > 2 ? 's' : ''}
                    </Badge>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* MEV Protection Settings */}
          <MEVProtectionSettings
            selectedLevel={selectedLevel}
            selectedSpeed={selectedSpeed}
            onLevelChange={setSelectedLevel}
            onSpeedChange={setSelectedSpeed}
            onConfigChange={setMEVConfig}
            disabled={isExecuting}
          />

          {/* Transaction Estimate */}
          {estimate && (
            <div className="bg-blue-50 p-4 rounded-lg space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Transaction Estimate</h3>
                <Badge className="bg-blue-100 text-blue-800">
                  {Math.round(estimate.successProbability * 100)}% success rate
                </Badge>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Base Transaction Fee:</span>
                  <span>{formatSOL(estimate.baseFee)} SOL</span>
                </div>
                <div className="flex justify-between">
                  <span>Priority Fee:</span>
                  <span>{formatSOL(estimate.priorityFee)} SOL</span>
                </div>
                {estimate.jitoTip > 0 && (
                  <div className="flex justify-between">
                    <span>MEV Protection (Jito Tip):</span>
                    <span>{formatSOL(estimate.jitoTip)} SOL</span>
                  </div>
                )}
                <div className="flex justify-between font-medium border-t pt-2">
                  <span>Total Transaction Cost:</span>
                  <div className="text-right">
                    <div>{formatSOL(estimate.totalCost)} SOL</div>
                    <div className="text-xs text-gray-600">{formatUSD(estimate.totalCost / 1_000_000_000)}</div>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span>Estimated Confirmation:</span>
                  <span>{estimate.estimatedTime}</span>
                </div>
              </div>
            </div>
          )}

          {/* Advanced Details */}
          <div className="space-y-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="text-gray-600 hover:text-gray-800"
            >
              {showAdvanced ? '▼' : '▶'} Advanced Details
            </Button>

            {showAdvanced && mevConfig && (
              <div className="bg-gray-50 p-3 rounded text-xs space-y-2">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="font-medium">Input Token</div>
                    <div className="font-mono text-gray-600 break-all">
                      {tradeDetails.inputMint.slice(0, 20)}...
                    </div>
                  </div>
                  <div>
                    <div className="font-medium">Output Token</div>
                    <div className="font-mono text-gray-600 break-all">
                      {tradeDetails.outputMint.slice(0, 20)}...
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="font-medium">Compute Unit Price</div>
                    <div>{mevConfig.computeUnitPriceMicroLamports} micro-lamports</div>
                  </div>
                  <div>
                    <div className="font-medium">Submission Method</div>
                    <div>{mevConfig.jitoTipLamports > 0 ? 'Jito Bundle' : 'Standard'}</div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Validation Error */}
          {validationError && (
            <Alert variant="destructive">
              <span className="text-red-600">⚠️</span>
              <div>
                <h4 className="font-semibold">Validation Warning</h4>
                <p className="text-sm mt-1">{validationError}</p>
              </div>
            </Alert>
          )}

          {/* High Price Impact Warning */}
          {tradeDetails.priceImpact > 3 && (
            <Alert variant="destructive">
              <span className="text-red-600">⚠️</span>
              <div>
                <h4 className="font-semibold">High Price Impact Warning</h4>
                <p className="text-sm mt-1">
                  This trade will significantly impact the token price ({tradeDetails.priceImpact.toFixed(2)}%). 
                  Consider reducing your trade size or using multiple smaller transactions.
                </p>
              </div>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              variant="outline"
              onClick={onCancel}
              disabled={isExecuting}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirm}
              disabled={!mevConfig || isExecuting || isValidating}
              className="flex-1"
            >
              {isValidating ? 'Validating...' : isExecuting ? 'Executing...' : 'Confirm Trade'}
            </Button>
          </div>

          {/* Execution Progress */}
          {isExecuting && (
            <div className="flex items-center justify-center py-2">
              <div className="animate-spin w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"></div>
              <span className="ml-2 text-sm text-gray-600">Executing transaction...</span>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}