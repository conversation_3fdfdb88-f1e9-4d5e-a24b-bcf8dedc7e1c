'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert } from '@/components/ui/alert';

export interface TransactionStep {
  id: string;
  name: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  description: string;
  timestamp?: Date;
  details?: string;
  duration?: number;
}

export interface TransactionProgressData {
  signature?: string;
  status: 'pending' | 'building' | 'signed' | 'submitted' | 'confirming' | 'confirmed' | 'finalized' | 'failed';
  confirmationStatus?: 'processed' | 'confirmed' | 'finalized' | null;
  confirmations?: number;
  slot?: number;
  blockTime?: number;
  error?: string;
  retryCount?: number;
  totalTime?: number;
  submissionMethod?: 'standard' | 'enhanced' | 'jito-bundle';
  steps: TransactionStep[];
}

interface TransactionProgressProps {
  isVisible: boolean;
  transactionData: TransactionProgressData | null;
  onClose: () => void;
  onRetry?: () => void;
  allowCancel?: boolean;
  onCancel?: () => void;
}

export function TransactionProgress({
  isVisible,
  transactionData,
  onClose,
  onRetry,
  allowCancel = false,
  onCancel
}: TransactionProgressProps) {
  const [elapsedTime, setElapsedTime] = useState(0);

  // Track elapsed time for active transactions
  useEffect(() => {
    if (!isVisible || !transactionData) return;
    
    const isActive = ['pending', 'building', 'signed', 'submitted', 'confirming'].includes(transactionData.status);
    if (!isActive) return;

    const startTime = Date.now();
    const interval = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => {
      clearInterval(interval);
      setElapsedTime(0);
    };
  }, [isVisible, transactionData?.status]);

  if (!isVisible || !transactionData) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
      case 'finalized':
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'failed':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'confirming':
      case 'submitted':
      case 'in_progress':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStepIcon = (step: TransactionStep) => {
    switch (step.status) {
      case 'completed':
        return '✅';
      case 'failed':
        return '❌';
      case 'in_progress':
        return '⏳';
      default:
        return '⭕';
    }
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getProgressPercentage = () => {
    const completedSteps = transactionData.steps.filter(step => step.status === 'completed').length;
    const totalSteps = transactionData.steps.length;
    return totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;
  };

  const isComplete = ['confirmed', 'finalized'].includes(transactionData.status);
  const isFailed = transactionData.status === 'failed';
  const isInProgress = ['pending', 'building', 'signed', 'submitted', 'confirming'].includes(transactionData.status);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-lg max-h-[90vh] overflow-y-auto">
        <div className="p-6 space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Transaction Progress</h2>
            <div className="flex items-center gap-2">
              {isInProgress && (
                <Badge variant="secondary" className="text-xs">
                  {formatDuration(elapsedTime)}
                </Badge>
              )}
              {!isInProgress && (
                <Button variant="ghost" size="sm" onClick={onClose}>
                  ✕
                </Button>
              )}
            </div>
          </div>

          {/* Overall Status */}
          <Alert className={getStatusColor(transactionData.status)}>
            <div className="flex items-center gap-2">
              <div className="font-semibold">
                Status: {transactionData.status.charAt(0).toUpperCase() + transactionData.status.slice(1)}
              </div>
              {transactionData.submissionMethod && (
                <Badge variant="outline" className="text-xs">
                  {transactionData.submissionMethod}
                </Badge>
              )}
            </div>
            {transactionData.signature && (
              <div className="mt-2 text-sm font-mono break-all">
                <span className="font-medium">Signature:</span> {transactionData.signature.slice(0, 20)}...
              </div>
            )}
          </Alert>

          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{Math.round(getProgressPercentage())}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${getProgressPercentage()}%` }}
              />
            </div>
          </div>

          {/* Transaction Steps */}
          <div className="space-y-2">
            <h3 className="font-medium text-sm">Steps</h3>
            <div className="space-y-2">
              {transactionData.steps.map((step, index) => (
                <div key={step.id} className="flex items-start gap-3 p-2 rounded border">
                  <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center text-sm">
                    {getStepIcon(step)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{step.name}</span>
                      {step.duration && (
                        <span className="text-xs text-gray-500">
                          {formatDuration(step.duration)}
                        </span>
                      )}
                    </div>
                    <p className="text-xs text-gray-600">{step.description}</p>
                    {step.details && (
                      <p className="text-xs text-gray-500 mt-1">{step.details}</p>
                    )}
                    {step.timestamp && (
                      <p className="text-xs text-gray-400 mt-1">
                        {step.timestamp.toLocaleTimeString()}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Transaction Details */}
          {(transactionData.confirmations !== undefined || transactionData.slot || transactionData.blockTime) && (
            <div className="bg-gray-50 p-3 rounded space-y-1">
              <h4 className="text-sm font-medium">Transaction Details</h4>
              <div className="space-y-1 text-xs">
                {transactionData.confirmationStatus && (
                  <div className="flex justify-between">
                    <span>Confirmation Status:</span>
                    <span>{transactionData.confirmationStatus}</span>
                  </div>
                )}
                {transactionData.confirmations !== undefined && (
                  <div className="flex justify-between">
                    <span>Confirmations:</span>
                    <span>{transactionData.confirmations}</span>
                  </div>
                )}
                {transactionData.slot && (
                  <div className="flex justify-between">
                    <span>Slot:</span>
                    <span>{transactionData.slot.toLocaleString()}</span>
                  </div>
                )}
                {transactionData.blockTime && (
                  <div className="flex justify-between">
                    <span>Block Time:</span>
                    <span>{new Date(transactionData.blockTime * 1000).toLocaleTimeString()}</span>
                  </div>
                )}
                {transactionData.totalTime && (
                  <div className="flex justify-between">
                    <span>Total Time:</span>
                    <span>{formatDuration(Math.floor(transactionData.totalTime / 1000))}</span>
                  </div>
                )}
                {transactionData.retryCount !== undefined && transactionData.retryCount > 0 && (
                  <div className="flex justify-between">
                    <span>Retries:</span>
                    <span>{transactionData.retryCount}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Error Display */}
          {transactionData.error && (
            <Alert variant="destructive">
              <span className="text-red-600">❌</span>
              <div>
                <h4 className="font-semibold">Transaction Failed</h4>
                <p className="text-sm mt-1">{transactionData.error}</p>
              </div>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4">
            {isComplete && (
              <Button onClick={onClose} className="flex-1">
                Close
              </Button>
            )}

            {isFailed && (
              <div className="flex gap-2 w-full">
                {onRetry && (
                  <Button onClick={onRetry} variant="outline" className="flex-1">
                    Retry Transaction
                  </Button>
                )}
                <Button onClick={onClose} variant="secondary" className="flex-1">
                  Close
                </Button>
              </div>
            )}

            {isInProgress && allowCancel && onCancel && (
              <div className="flex gap-2 w-full">
                <Button 
                  onClick={onCancel} 
                  variant="destructive" 
                  size="sm"
                  disabled={transactionData.status === 'submitted'}
                >
                  Cancel
                </Button>
                <div className="flex-1" />
              </div>
            )}

            {/* View on Explorer */}
            {transactionData.signature && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  window.open(
                    `https://solscan.io/tx/${transactionData.signature}`,
                    '_blank'
                  );
                }}
              >
                View on Explorer
              </Button>
            )}
          </div>

          {/* Loading Indicator for In-Progress Transactions */}
          {isInProgress && (
            <div className="flex items-center justify-center py-2">
              <div className="animate-spin w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"></div>
              <span className="ml-2 text-sm text-gray-600">
                {transactionData.status === 'confirming' ? 'Waiting for confirmation...' : 'Processing...'}
              </span>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}