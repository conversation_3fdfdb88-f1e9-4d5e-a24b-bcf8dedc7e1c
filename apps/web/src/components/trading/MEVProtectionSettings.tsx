'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert } from '@/components/ui/alert';

export interface MEVProtectionLevel {
  name: 'basic' | 'standard' | 'maximum';
  description: string;
  priorityFeeMultiplier: number;
  computeUnitMultiplier: number;
  jitoTipLamports: number;
  estimatedCostIncrease: number;
}

export interface NetworkCongestion {
  level: 'low' | 'medium' | 'high' | 'extreme';
  recentFees: {
    min: number;
    max: number;
    median: number;
    percentile75: number;
    percentile95: number;
  };
  blockHeight: number;
  timestamp: Date;
  sampleSize: number;
}

export interface MEVProtectionConfig {
  protectionLevel: 'basic' | 'standard' | 'maximum';
  speedPreference: 'economy' | 'standard' | 'fast' | 'turbo';
  priorityFeeLamports: number;
  computeUnitPriceMicroLamports: number;
  jitoTipLamports: number;
  totalEstimatedCost: number;
}

interface MEVProtectionSettingsProps {
  selectedLevel: 'basic' | 'standard' | 'maximum';
  selectedSpeed: 'economy' | 'standard' | 'fast' | 'turbo';
  onLevelChange: (level: 'basic' | 'standard' | 'maximum') => void;
  onSpeedChange: (speed: 'economy' | 'standard' | 'fast' | 'turbo') => void;
  onConfigChange: (config: MEVProtectionConfig) => void;
  disabled?: boolean;
}

export function MEVProtectionSettings({
  selectedLevel,
  selectedSpeed,
  onLevelChange,
  onSpeedChange,
  onConfigChange,
  disabled = false
}: MEVProtectionSettingsProps) {
  const [protectionLevels, setProtectionLevels] = useState<MEVProtectionLevel[]>([]);
  const [networkCongestion, setNetworkCongestion] = useState<NetworkCongestion | null>(null);
  const [currentConfig, setCurrentConfig] = useState<MEVProtectionConfig | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load protection levels on mount
  useEffect(() => {
    loadProtectionLevels();
  }, []);

  // Update config when selection changes
  useEffect(() => {
    if (protectionLevels.length > 0) {
      updateMEVConfig();
    }
  }, [selectedLevel, selectedSpeed, protectionLevels]);

  // Auto-refresh network congestion every 30 seconds
  useEffect(() => {
    loadNetworkCongestion();
    const interval = setInterval(loadNetworkCongestion, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadProtectionLevels = async () => {
    try {
      const response = await fetch('/api/mev-protection/levels');
      const data = await response.json();
      
      if (data.success) {
        setProtectionLevels(data.data);
      } else {
        throw new Error(data.error || 'Failed to load protection levels');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load protection levels');
    }
  };

  const loadNetworkCongestion = async () => {
    try {
      const response = await fetch('/api/mev-protection/network-congestion');
      const data = await response.json();
      
      if (data.success) {
        setNetworkCongestion(data.data);
      }
    } catch (err) {
      console.warn('Failed to load network congestion:', err);
    }
  };

  const updateMEVConfig = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/mev-protection/calculate-fee', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          protectionLevel: selectedLevel,
          speedPreference: selectedSpeed,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        const config: MEVProtectionConfig = {
          protectionLevel: selectedLevel,
          speedPreference: selectedSpeed,
          priorityFeeLamports: data.data.priorityFeeLamports,
          computeUnitPriceMicroLamports: data.data.computeUnitPriceMicroLamports,
          jitoTipLamports: data.data.jitoTipLamports,
          totalEstimatedCost: data.data.totalEstimatedCost,
        };
        
        setCurrentConfig(config);
        onConfigChange(config);
      } else {
        throw new Error(data.error || 'Failed to calculate MEV protection fee');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to calculate MEV protection');
    } finally {
      setIsLoading(false);
    }
  };

  const getCongestionColor = (level: string) => {
    switch (level) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'extreme': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatLamports = (lamports: number) => {
    return (lamports / 1_000_000_000).toFixed(6);
  };

  const selectedLevelConfig = protectionLevels.find(level => level.name === selectedLevel);

  return (
    <Card className="p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">MEV Protection Settings</h3>
        {networkCongestion && (
          <Badge className={getCongestionColor(networkCongestion.level)}>
            Network: {networkCongestion.level.toUpperCase()}
          </Badge>
        )}
      </div>

      {error && (
        <Alert variant="destructive">
          <span className="text-red-600">⚠️</span>
          <div>
            <h4 className="font-semibold">Configuration Error</h4>
            <p className="text-sm mt-1">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              className="mt-2"
              onClick={updateMEVConfig}
            >
              Retry
            </Button>
          </div>
        </Alert>
      )}

      {/* Protection Level Selection */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Protection Level</Label>
        <div className="grid grid-cols-1 gap-2">
          {protectionLevels.map((level) => (
            <button
              key={level.name}
              onClick={() => !disabled && onLevelChange(level.name)}
              disabled={disabled}
              className={`p-3 text-left border rounded-lg transition-colors ${
                selectedLevel === level.name
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
            >
              <div className="flex items-center justify-between mb-1">
                <span className="font-medium capitalize">{level.name}</span>
                <Badge variant="secondary">
                  +{level.estimatedCostIncrease}% cost
                </Badge>
              </div>
              <p className="text-xs text-gray-600">{level.description}</p>
              <div className="text-xs text-gray-500 mt-1">
                Jito tip: {formatLamports(level.jitoTipLamports)} SOL
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Speed Preference */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Transaction Speed</Label>
        <div className="grid grid-cols-2 gap-2">
          {(['economy', 'standard', 'fast', 'turbo'] as const).map((speed) => (
            <button
              key={speed}
              onClick={() => !disabled && onSpeedChange(speed)}
              disabled={disabled}
              className={`p-2 text-center border rounded transition-colors ${
                selectedSpeed === speed
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300'
              } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
            >
              <div className="text-sm font-medium capitalize">{speed}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Current Configuration Display */}
      {currentConfig && !isLoading && (
        <div className="bg-gray-50 p-3 rounded-lg space-y-2">
          <h4 className="text-sm font-medium">Current Configuration</h4>
          <div className="space-y-1 text-xs">
            <div className="flex justify-between">
              <span>Priority Fee:</span>
              <span>{formatLamports(currentConfig.priorityFeeLamports)} SOL</span>
            </div>
            <div className="flex justify-between">
              <span>Jito Tip:</span>
              <span>{formatLamports(currentConfig.jitoTipLamports)} SOL</span>
            </div>
            <div className="flex justify-between font-medium border-t pt-1">
              <span>Total Protection Cost:</span>
              <span>{formatLamports(currentConfig.totalEstimatedCost)} SOL</span>
            </div>
          </div>
        </div>
      )}

      {/* Network Information */}
      {networkCongestion && (
        <div className="bg-blue-50 p-3 rounded-lg">
          <h4 className="text-sm font-medium text-blue-800">Network Status</h4>
          <div className="text-xs text-blue-700 mt-1 space-y-1">
            <div>Congestion: {networkCongestion.level}</div>
            <div>Recent median fee: {formatLamports(networkCongestion.recentFees.median)} SOL</div>
            <div>Sample size: {networkCongestion.sampleSize} transactions</div>
          </div>
        </div>
      )}

      {isLoading && (
        <div className="text-center py-2">
          <div className="animate-spin inline-block w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
          <span className="ml-2 text-sm text-gray-600">Calculating protection costs...</span>
        </div>
      )}
    </Card>
  );
}