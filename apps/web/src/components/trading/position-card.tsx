import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { formatCurrency, formatPercentage, cn } from "@/lib/utils"

interface PositionCardProps {
  symbol: string
  currentPrice: number
  entryPrice: number
  quantity: number
  pnl: number
  pnlPercentage: number
  onClose?: () => void
}

export function PositionCard({
  symbol,
  currentPrice,
  entryPrice,
  quantity,
  pnl,
  pnlPercentage,
  onClose
}: PositionCardProps) {
  const isProfit = pnl > 0
  const pnlColor = isProfit ? 'text-success' : 'text-error'
  
  return (
    <Card variant="position" className="w-full max-w-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">{symbol}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">Current</p>
            <p className="font-medium">{formatCurrency(currentPrice)}</p>
          </div>
          <div>
            <p className="text-muted-foreground">Entry</p>
            <p className="font-medium">{formatCurrency(entryPrice)}</p>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">Quantity</p>
            <p className="font-medium">{quantity.toLocaleString()}</p>
          </div>
          <div>
            <p className="text-muted-foreground">P&L</p>
            <p className={cn("font-medium", pnlColor)}>
              {formatCurrency(pnl)} ({formatPercentage(pnlPercentage)})
            </p>
          </div>
        </div>
        
        {onClose && (
          <div className="pt-2">
            <Button 
              variant="sell" 
              size="sm" 
              className="w-full"
              onClick={onClose}
            >
              Close Position
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}