'use client';

import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { tokenBalanceService, type TokenBalance } from '@/services/tokenBalance';
import { RotateCcw } from 'lucide-react';

interface TokenBalanceDisplayProps {
  tokenMint: string;
  tokenSymbol?: string;
  className?: string;
  showRefreshButton?: boolean;
  onBalanceChange?: (balance: TokenBalance | null) => void;
}

export function TokenBalanceDisplay({
  tokenMint,
  tokenSymbol,
  className = '',
  showRefreshButton = true,
  onBalanceChange
}: TokenBalanceDisplayProps) {
  const [balance, setBalance] = useState<TokenBalance | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchBalance = async () => {
    if (!tokenMint) return;
    
    setLoading(true);
    setError(null);

    try {
      const tokenBalance = await tokenBalanceService.getTokenBalance(tokenMint);
      setBalance(tokenBalance);
      onBalanceChange?.(tokenBalance);
    } catch (err) {
      setError('Failed to fetch balance');
      console.error('Token balance fetch error:', err);
      setBalance(null);
      onBalanceChange?.(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBalance();
  }, [tokenMint]);

  const formatDisplayBalance = (): string => {
    if (!balance) return '0.00';
    return tokenBalanceService.formatTokenAmount(balance);
  };

  const getBalanceColor = (): string => {
    if (!balance) return 'text-gray-500';
    if (balance.uiAmount === 0) return 'text-gray-500';
    if (balance.uiAmount < 0.01) return 'text-yellow-600';
    if (balance.uiAmount >= 1000) return 'text-green-600 font-bold';
    return 'text-gray-900';
  };

  const displaySymbol = tokenSymbol || 
    (balance?.symbol) || 
    tokenBalanceService.getKnownTokenSymbol(tokenMint) || 
    tokenMint.slice(-4).toUpperCase();

  return (
    <div className={`flex items-center justify-between p-3 border rounded-lg ${className}`}>
      <div className="flex items-center gap-2">
        <span className="text-sm text-gray-600">Balance:</span>
        
        {loading && (
          <div className="h-4 w-16 bg-gray-200 rounded animate-pulse" />
        )}
        
        {error && (
          <Badge variant="destructive" className="text-xs">
            Error
          </Badge>
        )}
        
        {!loading && !error && (
          <div className="flex items-center gap-2">
            <span className={`font-mono text-sm ${getBalanceColor()}`}>
              {formatDisplayBalance()}
            </span>
            <span className="text-xs text-gray-500 font-medium">
              {displaySymbol}
            </span>
          </div>
        )}
      </div>

      {showRefreshButton && (
        <Button
          variant="ghost"
          size="sm"
          onClick={fetchBalance}
          disabled={loading}
          className="h-7 w-7 p-0"
          title="Refresh balance"
        >
          <RotateCcw className={`h-3 w-3 ${loading ? 'animate-spin' : ''}`} />
        </Button>
      )}
    </div>
  );
}

// Simplified version for inline display
export function InlineTokenBalance({
  tokenMint,
  tokenSymbol,
  className = '',
}: {
  tokenMint: string;
  tokenSymbol?: string;
  className?: string;
}) {
  const [balance, setBalance] = useState<TokenBalance | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBalance = async () => {
      setLoading(true);
      setError(null);
      try {
        const tokenBalance = await tokenBalanceService.getTokenBalance(tokenMint);
        setBalance(tokenBalance);
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : 'Unknown error';
        console.error('Failed to fetch token balance:', err);
        setError(errorMsg);
        setBalance(null);
      } finally {
        setLoading(false);
      }
    };

    if (tokenMint && tokenMint.trim()) {
      fetchBalance();
    } else {
      setLoading(false);
    }
  }, [tokenMint]);

  if (loading) {
    return <div className={`h-4 w-16 bg-gray-200 rounded animate-pulse ${className}`} />;
  }

  if (error) {
    return (
      <div className={`flex items-center gap-1 ${className}`}>
        <span className="font-mono text-sm text-red-500">Error</span>
        <span className="text-xs text-red-500">ERR</span>
      </div>
    );
  }

  const displayBalance = balance 
    ? tokenBalanceService.formatTokenAmount(balance)
    : '0.00';
  
  const displaySymbol = tokenSymbol || 
    (balance?.symbol) || 
    tokenBalanceService.getKnownTokenSymbol(tokenMint) || 
    'TOKEN';

  const balanceColor = !balance || balance.uiAmount === 0 
    ? 'text-gray-500' 
    : 'text-gray-400';

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <span className={`font-mono text-sm ${balanceColor}`}>
        {displayBalance}
      </span>
      <span className="text-xs text-gray-500">
        {displaySymbol}
      </span>
    </div>
  );
}