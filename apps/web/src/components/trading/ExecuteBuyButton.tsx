'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { TransactionConfirmation, type TradeDetails } from './TransactionConfirmation';
import { TransactionProgress, type TransactionProgressData, type TransactionStep } from './TransactionProgress';
import { type MEVProtectionConfig } from './MEVProtectionSettings';

export interface ExecuteBuyButtonProps {
  tradeDetails: TradeDetails | null;
  isConnected: boolean;
  isQuoteValid: boolean;
  onTradeComplete: (result: ExecuteTradeResult) => void;
  disabled?: boolean;
  className?: string;
  size?: 'sm' | 'default' | 'lg';
  walletBalance?: number;
}

export interface ExecuteTradeResult {
  success: boolean;
  signature?: string;
  error?: string;
  transactionDetails?: {
    fee: number;
    confirmationTime: number;
    retryCount: number;
    submissionMethod: string;
  };
}

export function ExecuteBuyButton({
  tradeDetails,
  isConnected,
  isQuoteValid,
  onTradeComplete,
  disabled = false,
  className = '',
  size = 'default',
  walletBalance
}: ExecuteBuyButtonProps) {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showProgress, setShowProgress] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);
  const [progressData, setProgressData] = useState<TransactionProgressData | null>(null);
  const [currentMEVConfig, setCurrentMEVConfig] = useState<MEVProtectionConfig | null>(null);

  // Check if trade is valid
  const canExecuteTrade = () => {
    if (!isConnected || !tradeDetails || !isQuoteValid || disabled) return false;
    
    // Check wallet balance
    if (walletBalance !== undefined) {
      const totalCost = tradeDetails.amountSol + (currentMEVConfig?.totalEstimatedCost || 10000) / 1_000_000_000;
      if (walletBalance < totalCost) return false;
    }

    return true;
  };

  const getButtonText = () => {
    if (!isConnected) return 'Connect Wallet';
    if (!tradeDetails) return 'Enter Amount';
    if (!isQuoteValid) return 'Get Quote';
    if (isExecuting) return 'Executing...';
    
    // Check balance
    if (walletBalance !== undefined && currentMEVConfig) {
      const totalCost = tradeDetails.amountSol + currentMEVConfig.totalEstimatedCost / 1_000_000_000;
      if (walletBalance < totalCost) return 'Insufficient Balance';
    }

    return `Buy ${tradeDetails.marketData?.outputSymbol || 'Token'}`;
  };

  const handleInitiateTrade = () => {
    if (!canExecuteTrade()) return;
    setShowConfirmation(true);
  };

  const handleConfirmTrade = async (mevConfig: MEVProtectionConfig) => {
    if (!tradeDetails || !mevConfig) return;

    setCurrentMEVConfig(mevConfig);
    setShowConfirmation(false);
    setIsExecuting(true);
    setShowProgress(true);

    // Initialize progress data
    const initialProgressData: TransactionProgressData = {
      status: 'pending',
      steps: [
        {
          id: 'build-transaction',
          name: 'Building Transaction',
          status: 'pending',
          description: 'Creating Jupiter swap transaction with MEV protection'
        },
        {
          id: 'sign-transaction',
          name: 'Sign Transaction',
          status: 'pending',
          description: 'Signing transaction with wallet'
        },
        {
          id: 'submit-transaction',
          name: 'Submit Transaction',
          status: 'pending',
          description: `Submitting via ${mevConfig.jitoTipLamports > 0 ? 'Jito bundle' : 'standard RPC'}`
        },
        {
          id: 'confirm-transaction',
          name: 'Confirm Transaction',
          status: 'pending',
          description: 'Waiting for network confirmation'
        },
        {
          id: 'finalize-transaction',
          name: 'Finalize Transaction',
          status: 'pending',
          description: 'Processing final confirmation'
        }
      ]
    };

    setProgressData(initialProgressData);

    try {
      // Execute the trade with MEV protection
      const result = await executeTradeWithMEVProtection(tradeDetails, mevConfig, (update) => {
        setProgressData(prev => prev ? { ...prev, ...update } : null);
      });

      // Update final progress data
      setProgressData(prev => prev ? {
        ...prev,
        status: result.success ? 'finalized' : 'failed',
        signature: result.signature,
        error: result.error,
        totalTime: result.transactionDetails?.confirmationTime
      } : null);

      // Call completion handler
      onTradeComplete(result);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      setProgressData(prev => prev ? {
        ...prev,
        status: 'failed',
        error: errorMessage,
        steps: prev.steps.map(step => 
          step.status === 'in_progress' ? { ...step, status: 'failed' } : step
        )
      } : null);

      onTradeComplete({
        success: false,
        error: errorMessage
      });
    } finally {
      setIsExecuting(false);
    }
  };

  // Mock implementation of MEV-protected trade execution
  const executeTradeWithMEVProtection = async (
    trade: TradeDetails, 
    mevConfig: MEVProtectionConfig,
    onProgress: (update: Partial<TransactionProgressData>) => void
  ): Promise<ExecuteTradeResult> => {
    const startTime = Date.now();

    // Step 1: Build transaction
    onProgress({
      status: 'building',
      steps: updateStepStatus('build-transaction', 'in_progress', 'Creating Jupiter swap with MEV parameters')
    });

    await delay(2000); // Simulate API call

    onProgress({
      steps: updateStepStatus('build-transaction', 'completed', 'Transaction built successfully')
    });

    // Step 2: Sign transaction
    onProgress({
      status: 'signed',
      steps: updateStepStatus('sign-transaction', 'in_progress', 'Requesting wallet signature')
    });

    await delay(1500);

    onProgress({
      steps: updateStepStatus('sign-transaction', 'completed', 'Transaction signed')
    });

    // Step 3: Submit transaction
    onProgress({
      status: 'submitted',
      steps: updateStepStatus('submit-transaction', 'in_progress', 
        mevConfig.jitoTipLamports > 0 ? 'Submitting via Jito bundle' : 'Submitting to RPC')
    });

    await delay(3000);

    const mockSignature = 'mock_signature_' + Math.random().toString(36).substring(2);
    
    onProgress({
      signature: mockSignature,
      submissionMethod: mevConfig.jitoTipLamports > 0 ? 'jito-bundle' : 'enhanced',
      steps: updateStepStatus('submit-transaction', 'completed', `Submitted with signature: ${mockSignature.slice(0, 20)}...`)
    });

    // Step 4: Confirm transaction
    onProgress({
      status: 'confirming',
      steps: updateStepStatus('confirm-transaction', 'in_progress', 'Waiting for network confirmation')
    });

    await delay(8000); // Simulate confirmation wait

    onProgress({
      confirmationStatus: 'confirmed',
      confirmations: 1,
      slot: Math.floor(Math.random() * 1000000) + 200000000,
      steps: updateStepStatus('confirm-transaction', 'completed', 'Transaction confirmed')
    });

    // Step 5: Finalize
    onProgress({
      status: 'confirmed',
      steps: updateStepStatus('finalize-transaction', 'in_progress', 'Processing final confirmation')
    });

    await delay(2000);

    const totalTime = Date.now() - startTime;

    onProgress({
      status: 'finalized',
      blockTime: Math.floor(Date.now() / 1000),
      totalTime,
      steps: updateStepStatus('finalize-transaction', 'completed', 'Trade completed successfully')
    });

    return {
      success: true,
      signature: mockSignature,
      transactionDetails: {
        fee: mevConfig.totalEstimatedCost,
        confirmationTime: totalTime,
        retryCount: 0,
        submissionMethod: mevConfig.jitoTipLamports > 0 ? 'jito-bundle' : 'enhanced'
      }
    };
  };

  const updateStepStatus = (stepId: string, status: TransactionStep['status'], description?: string): TransactionStep[] => {
    return progressData?.steps.map(step => {
      if (step.id === stepId) {
        return {
          ...step,
          status,
          description: description || step.description,
          timestamp: status === 'completed' || status === 'failed' ? new Date() : step.timestamp
        };
      }
      return step;
    }) || [];
  };

  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  const handleCancelConfirmation = () => {
    setShowConfirmation(false);
  };

  const handleCloseProgress = () => {
    setShowProgress(false);
    setProgressData(null);
  };

  const handleRetryTransaction = () => {
    if (currentMEVConfig && tradeDetails) {
      setShowProgress(false);
      setProgressData(null);
      handleConfirmTrade(currentMEVConfig);
    }
  };

  const getButtonVariant = () => {
    if (!canExecuteTrade()) return 'secondary';
    return 'default';
  };

  return (
    <>
      <Button
        size={size}
        variant={getButtonVariant()}
        onClick={handleInitiateTrade}
        disabled={!canExecuteTrade() || isExecuting}
        className={`relative ${className}`}
      >
        {getButtonText()}
        
        {tradeDetails && canExecuteTrade() && (
          <Badge 
            variant="secondary" 
            className="ml-2 text-xs bg-green-100 text-green-700"
          >
            MEV Protected
          </Badge>
        )}
      </Button>

      <TransactionConfirmation
        isOpen={showConfirmation}
        tradeDetails={tradeDetails}
        onConfirm={handleConfirmTrade}
        onCancel={handleCancelConfirmation}
        isExecuting={isExecuting}
      />

      <TransactionProgress
        isVisible={showProgress}
        transactionData={progressData}
        onClose={handleCloseProgress}
        onRetry={progressData?.status === 'failed' ? handleRetryTransaction : undefined}
        allowCancel={progressData?.status === 'pending' || progressData?.status === 'building'}
        onCancel={() => {
          setIsExecuting(false);
          setShowProgress(false);
          setProgressData(null);
        }}
      />
    </>
  );
}