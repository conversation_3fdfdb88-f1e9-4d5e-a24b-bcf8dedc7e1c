'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface TradingPreset {
  id: string;
  name: string;
  color: string;
  active: boolean;
  config?: {
    priorityFee: string;
    slippage: string;
    mevProtection: string;
  };
}

interface TradingPresetsProps {
  activePreset: string;
  onPresetChange: (presetId: string) => void;
  className?: string;
}

const TRADING_PRESETS: TradingPreset[] = [
  { 
    id: 'default', 
    name: 'DEFAULT', 
    color: 'bg-green-500', 
    active: true,
    config: {
      priorityFee: '0.01 SOL',
      slippage: '1.0%',
      mevProtection: 'Standard'
    }
  },
  { 
    id: 'vol', 
    name: 'VOL', 
    color: 'bg-gray-600', 
    active: false,
    config: {
      priorityFee: '0.02 SOL',
      slippage: '2.0%',
      mevProtection: 'High'
    }
  },
  { 
    id: 'dead', 
    name: 'DEAD', 
    color: 'bg-gray-600', 
    active: false,
    config: {
      priorityFee: '0.005 SOL',
      slippage: '0.5%',
      mevProtection: 'Standard'
    }
  },
  { 
    id: 'nun', 
    name: 'NUN', 
    color: 'bg-gray-600', 
    active: false,
    config: {
      priorityFee: '0.003 SOL',
      slippage: '0.3%',
      mevProtection: 'Low'
    }
  },
  { 
    id: 'p5', 
    name: 'P5', 
    color: 'bg-gray-600', 
    active: false,
    config: {
      priorityFee: '0.05 SOL',
      slippage: '5.0%',
      mevProtection: 'Maximum'
    }
  },
];

export function TradingPresets({ activePreset, onPresetChange, className = '' }: TradingPresetsProps) {
  const currentPreset = TRADING_PRESETS.find(p => p.id === activePreset);
  
  return (
    <Card className={`bg-gray-900 border-gray-800 ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg text-white">Trading Presets</CardTitle>
          <Badge className="bg-green-500 text-white">
            Active: {currentPreset?.name || activePreset.toUpperCase()}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-5 gap-2">
          {TRADING_PRESETS.map((preset) => (
            <Button
              key={preset.id}
              variant={activePreset === preset.id ? 'default' : 'outline'}
              className={`${
                activePreset === preset.id 
                  ? 'bg-green-500 hover:bg-green-600 text-white border-green-500' 
                  : 'bg-gray-800 hover:bg-gray-700 text-gray-300 border-gray-700'
              } font-medium`}
              onClick={() => onPresetChange(preset.id)}
              title={`${preset.name} Trading Configuration`}
            >
              {preset.name}
            </Button>
          ))}
        </div>

        {/* Current Preset Configuration */}
        {currentPreset?.config && (
          <div className="grid grid-cols-3 gap-4 mt-4 text-sm">
            <div>
              <span className="text-gray-400">Priority Fee</span>
              <div className="text-white font-medium">{currentPreset.config.priorityFee}</div>
            </div>
            <div>
              <span className="text-gray-400">Slippage Limit</span>
              <div className="text-white font-medium">{currentPreset.config.slippage}</div>
            </div>
            <div>
              <span className="text-gray-400">MEV Protection</span>
              <div className="text-white font-medium">{currentPreset.config.mevProtection}</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export { TRADING_PRESETS };
export type { TradingPreset };