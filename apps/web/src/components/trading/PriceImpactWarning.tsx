'use client';

import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface PriceImpactWarningProps {
  priceImpact: number; // percentage
  tradeAmount: string;
  tokenSymbol: string;
  onAcknowledge?: () => void;
  onCancel?: () => void;
  className?: string;
  showActions?: boolean;
}

export function PriceImpactWarning({
  priceImpact,
  tradeAmount,
  tokenSymbol,
  onAcknowledge,
  onCancel,
  className,
  showActions = true,
}: PriceImpactWarningProps) {
  const getImpactLevel = () => {
    if (priceImpact < 1) return 'low';
    if (priceImpact < 3) return 'medium';
    if (priceImpact < 5) return 'high';
    if (priceImpact < 10) return 'very-high';
    return 'extreme';
  };

  const getImpactData = () => {
    const level = getImpactLevel();
    
    switch (level) {
      case 'low':
        return {
          color: 'green',
          icon: '✅',
          title: 'Low Price Impact',
          description: 'This trade will have minimal impact on the token price.',
          recommendation: 'Good trade size for this token.',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          textColor: 'text-green-800',
        };
      
      case 'medium':
        return {
          color: 'yellow',
          icon: '⚠️',
          title: 'Moderate Price Impact',
          description: 'This trade will have a noticeable impact on the token price.',
          recommendation: 'Consider reducing trade size for better execution.',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-800',
        };
      
      case 'high':
        return {
          color: 'orange',
          icon: '🔶',
          title: 'High Price Impact',
          description: 'This trade will significantly impact the token price.',
          recommendation: 'Consider trading in smaller amounts or waiting for better liquidity.',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          textColor: 'text-orange-800',
        };
      
      case 'very-high':
        return {
          color: 'red',
          icon: '🚨',
          title: 'Very High Price Impact',
          description: 'This trade will severely impact the token price and may result in significant losses.',
          recommendation: 'Strongly consider reducing trade size by 50% or more.',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
        };
      
      case 'extreme':
        return {
          color: 'red',
          icon: '🛑',
          title: 'Extreme Price Impact - Trade Blocked',
          description: 'This trade would have an extreme impact on the token price and is not recommended.',
          recommendation: 'Please reduce your trade size to less than 5% price impact.',
          bgColor: 'bg-red-100',
          borderColor: 'border-red-300',
          textColor: 'text-red-900',
        };
      
      default:
        return {
          color: 'gray',
          icon: '❓',
          title: 'Unknown Impact',
          description: 'Unable to determine price impact.',
          recommendation: 'Please try again or contact support.',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          textColor: 'text-gray-800',
        };
    }
  };

  const impactData = getImpactData();
  const isExtreme = getImpactLevel() === 'extreme';

  const calculatePriceChange = () => {
    // Estimate the price change based on impact
    const estimatedChange = (parseFloat(tradeAmount) * priceImpact / 100).toFixed(6);
    return estimatedChange;
  };

  return (
    <Card className={`p-4 ${impactData.bgColor} ${impactData.borderColor} border ${className}`}>
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-start gap-3">
          <span className="text-2xl">{impactData.icon}</span>
          <div className="flex-1">
            <h3 className={`text-lg font-semibold ${impactData.textColor}`}>
              {impactData.title}
            </h3>
            <p className={`text-sm mt-1 ${impactData.textColor}`}>
              {impactData.description}
            </p>
          </div>
          <div className="text-right">
            <div className={`text-2xl font-bold ${impactData.textColor}`}>
              {priceImpact.toFixed(2)}%
            </div>
            <div className="text-xs text-gray-500">Price Impact</div>
          </div>
        </div>

        {/* Impact Details */}
        <div className={`bg-white/50 p-3 rounded-lg space-y-2 ${impactData.textColor}`}>
          <div className="flex items-center justify-between text-sm">
            <span>Trade Amount:</span>
            <span className="font-medium">{tradeAmount} {tokenSymbol}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span>Estimated Price Movement:</span>
            <span className="font-medium">±{calculatePriceChange()} {tokenSymbol}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span>Impact Level:</span>
            <span className="font-medium capitalize">{getImpactLevel().replace('-', ' ')}</span>
          </div>
        </div>

        {/* Recommendation */}
        <div className={`text-sm ${impactData.textColor}`}>
          <div className="font-medium mb-1">💡 Recommendation:</div>
          <p>{impactData.recommendation}</p>
        </div>

        {/* Educational Information */}
        <div className="bg-white/30 p-3 rounded-lg text-xs">
          <div className={`font-medium mb-2 ${impactData.textColor}`}>
            Why does price impact matter?
          </div>
          <ul className={`space-y-1 ${impactData.textColor} opacity-90`}>
            <li>• High impact means you'll get less favorable pricing</li>
            <li>• Your trade affects the token's market price</li>
            <li>• Other traders may front-run high-impact trades</li>
            <li>• Smaller trades often get better execution</li>
          </ul>
        </div>

        {/* Risk Scale */}
        <div className="space-y-2">
          <div className="text-xs text-gray-600">Impact Risk Scale:</div>
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 bg-green-400 rounded"></div>
              <span>0-1%</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 bg-yellow-400 rounded"></div>
              <span>1-3%</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 bg-orange-400 rounded"></div>
              <span>3-5%</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 bg-red-400 rounded"></div>
              <span>5-10%</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 bg-red-600 rounded"></div>
              <span>10%+</span>
            </div>
          </div>
          {/* Current position indicator */}
          <div className="relative h-2 bg-gray-200 rounded">
            <div 
              className={`absolute top-0 left-0 h-full bg-${impactData.color}-500 rounded`}
              style={{ width: `${Math.min(priceImpact * 10, 100)}%` }}
            />
            <div 
              className="absolute top-0 w-1 h-full bg-black rounded"
              style={{ left: `${Math.min(priceImpact * 10, 100)}%` }}
            />
          </div>
        </div>

        {/* Action Buttons */}
        {showActions && (
          <div className="flex gap-2 pt-2">
            {onCancel && (
              <Button
                variant="outline"
                size="sm"
                onClick={onCancel}
                className="flex-1"
              >
                Cancel Trade
              </Button>
            )}
            {onAcknowledge && !isExtreme && (
              <Button
                variant={priceImpact >= 5 ? 'destructive' : 'default'}
                size="sm"
                onClick={onAcknowledge}
                className="flex-1"
              >
                {priceImpact >= 5 ? 'Accept Risk & Continue' : 'Continue'}
              </Button>
            )}
            {isExtreme && (
              <div className="flex-1 text-center">
                <p className="text-xs text-red-700">
                  Trade blocked for your protection. Please reduce trade size.
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </Card>
  );
}