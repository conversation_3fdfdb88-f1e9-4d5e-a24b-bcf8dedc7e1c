'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';

interface SlippageConfigProps {
  value: number; // slippage in basis points (100 = 1%)
  onChange: (slippageBps: number) => void;
  className?: string;
  showAdvanced?: boolean;
}

const PRESET_SLIPPAGE = [50, 100, 300]; // 0.5%, 1%, 3%

export function SlippageConfig({
  value,
  onChange,
  className,
  showAdvanced = false,
}: SlippageConfigProps) {
  const [isCustom, setIsCustom] = useState(false);
  const [customValue, setCustomValue] = useState('');

  const formatSlippage = (bps: number) => {
    return (bps / 100).toFixed(2);
  };

  const parseSlippage = (percentage: string) => {
    const num = parseFloat(percentage);
    if (isNaN(num)) return 0;
    return Math.round(num * 100); // Convert to basis points
  };

  const getSlippageColor = (bps: number) => {
    if (bps <= 50) return 'text-green-600';      // 0.5% or less - good
    if (bps <= 100) return 'text-blue-600';      // 1% - standard
    if (bps <= 300) return 'text-yellow-600';    // 3% - moderate
    if (bps <= 500) return 'text-orange-600';    // 5% - high
    return 'text-red-600';                       // >5% - very high
  };

  const getSlippageWarning = (bps: number) => {
    if (bps <= 50) return 'Low slippage - may fail in volatile conditions';
    if (bps <= 100) return 'Standard slippage for most trades';
    if (bps <= 300) return 'Higher slippage - suitable for volatile tokens';
    if (bps <= 500) return 'High slippage - trade may be front-run';
    return 'Very high slippage - use with extreme caution';
  };

  const handlePresetClick = (slippageBps: number) => {
    setIsCustom(false);
    setCustomValue('');
    onChange(slippageBps);
  };

  const handleCustomSubmit = () => {
    if (!customValue) return;
    
    const bps = parseSlippage(customValue);
    if (bps > 0 && bps <= 5000) { // Max 50%
      onChange(bps);
    }
  };

  const handleCustomChange = (input: string) => {
    // Allow only numbers and decimal point
    const sanitized = input.replace(/[^0-9.]/g, '');
    setCustomValue(sanitized);
    
    // Auto-apply if it's a valid number
    const bps = parseSlippage(sanitized);
    if (bps > 0 && bps <= 5000) {
      onChange(bps);
    }
  };

  const isPresetSelected = (slippageBps: number) => {
    return value === slippageBps && !isCustom;
  };

  const isCustomSelected = !PRESET_SLIPPAGE.includes(value) || isCustom;

  return (
    <Card className={`p-4 ${className}`}>
      <div className="space-y-4">
        <div>
          <Label className="text-sm font-medium">Slippage Tolerance</Label>
          <p className="text-xs text-gray-500 mt-1">
            Maximum price movement you're willing to accept
          </p>
        </div>

        {/* Current Selection Display */}
        <div className="bg-gray-50 p-3 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Current Setting:</span>
            <div className="text-right">
              <span className={`font-bold text-lg ${getSlippageColor(value)}`}>
                {formatSlippage(value)}%
              </span>
              <div className="text-xs text-gray-500">
                {getSlippageWarning(value)}
              </div>
            </div>
          </div>
        </div>

        {/* Preset Buttons */}
        <div className="space-y-2">
          <Label className="text-xs text-gray-600">Quick Select:</Label>
          <div className="flex gap-2">
            {PRESET_SLIPPAGE.map((slippageBps) => (
              <Button
                key={slippageBps}
                variant={isPresetSelected(slippageBps) ? 'default' : 'outline'}
                size="sm"
                onClick={() => handlePresetClick(slippageBps)}
                className="flex-1"
              >
                {formatSlippage(slippageBps)}%
              </Button>
            ))}
          </div>
        </div>

        {/* Custom Input */}
        <div className="space-y-2">
          <Label className="text-xs text-gray-600">Custom:</Label>
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Input
                type="text"
                value={isCustomSelected ? formatSlippage(value) : customValue}
                onChange={(e) => {
                  setIsCustom(true);
                  handleCustomChange(e.target.value);
                }}
                placeholder="1.00"
                className="text-sm pr-8"
                onFocus={() => setIsCustom(true)}
              />
              <span className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-gray-500">
                %
              </span>
            </div>
            {isCustom && customValue && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleCustomSubmit}
              >
                Apply
              </Button>
            )}
          </div>
        </div>

        {/* Advanced Settings */}
        {showAdvanced && (
          <div className="border-t pt-4 space-y-3">
            <Label className="text-xs text-gray-600">Advanced:</Label>
            
            {/* Slippage Impact Calculator */}
            <div className="bg-blue-50 p-3 rounded-lg text-sm">
              <div className="font-medium text-blue-800 mb-2">Slippage Impact:</div>
              <div className="space-y-1 text-blue-700">
                <div>• For 1 SOL trade: ±{(value / 10000).toFixed(4)} SOL</div>
                <div>• For 0.1 SOL trade: ±{(value / 100000).toFixed(5)} SOL</div>
              </div>
            </div>

            {/* Risk Warning */}
            {value >= 300 && (
              <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-lg">
                <div className="flex items-start gap-2">
                  <span className="text-yellow-500">⚠️</span>
                  <div className="text-yellow-800 text-xs">
                    <strong>High Slippage Warning:</strong>
                    <p className="mt-1">
                      High slippage tolerance increases the risk of MEV attacks and front-running. 
                      Consider using smaller trade sizes.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Tips */}
            <div className="text-xs text-gray-500 space-y-1">
              <div>💡 <strong>Tips:</strong></div>
              <div>• Use 0.5-1% for established tokens</div>
              <div>• Use 2-3% for volatile/new tokens</div>
              <div>• Higher slippage = faster execution but higher costs</div>
            </div>
          </div>
        )}

        {/* Reset Button */}
        <div className="flex justify-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handlePresetClick(100)} // Reset to 1%
            className="text-xs"
          >
            Reset to Default (1%)
          </Button>
        </div>
      </div>
    </Card>
  );
}