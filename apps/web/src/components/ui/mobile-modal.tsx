'use client'

import { ReactNode, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Button } from './button'

interface MobileModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  children: ReactNode
  type?: 'default' | 'alert' | 'confirm'
  confirmText?: string
  cancelText?: string
  onConfirm?: () => void
}

export function MobileModal({
  isOpen,
  onClose,
  title,
  children,
  type = 'default',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm
}: MobileModalProps) {
  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }
    
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div 
      className={cn(
        "fixed inset-0 z-50",
        // Mobile: Full screen
        "lg:flex lg:items-center lg:justify-center lg:bg-black/50"
      )}
    >
      <div 
        className={cn(
          "bg-background text-foreground",
          // Mobile: Full screen
          "h-full w-full",
          // Desktop: Modal dialog
          "lg:h-auto lg:w-auto lg:max-w-md lg:rounded-lg lg:shadow-lg lg:border",
          // Alert styling
          type === 'alert' && "border-warning/20 bg-warning/5"
        )}
      >
        {/* Header */}
        {title && (
          <div className="border-b p-4 lg:p-6">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold lg:text-lg">{title}</h2>
              <button
                onClick={onClose}
                className="text-muted-foreground hover:text-foreground text-2xl lg:text-xl"
              >
                ×
              </button>
            </div>
          </div>
        )}
        
        {/* Content */}
        <div className="flex-1 p-4 lg:p-6">
          {children}
        </div>
        
        {/* Actions */}
        {type === 'confirm' && (
          <div className="border-t p-4 lg:p-6">
            <div className="flex flex-col gap-3 lg:flex-row lg:justify-end">
              <Button 
                variant="outline" 
                size="touch"
                className="w-full lg:w-auto"
                onClick={onClose}
              >
                {cancelText}
              </Button>
              <Button 
                variant="default"
                size="touch"
                className="w-full lg:w-auto"
                onClick={() => {
                  onConfirm?.()
                  onClose()
                }}
              >
                {confirmText}
              </Button>
            </div>
          </div>
        )}
        
        {/* Close button for non-confirm modals */}
        {type !== 'confirm' && (
          <div className="border-t p-4 lg:p-6">
            <Button 
              variant="outline"
              size="touch"
              className="w-full lg:w-auto"
              onClick={onClose}
            >
              Close
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}