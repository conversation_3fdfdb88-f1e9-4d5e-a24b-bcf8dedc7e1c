'use client'

import { useEffect } from 'react'
import { useSystemStore } from '@/stores/system-store'

interface StoreProviderProps {
  children: React.ReactNode
}

export function StoreProvider({ children }: StoreProviderProps) {
  const { setConnectionStatus, addNotification } = useSystemStore()
  
  useEffect(() => {
    // Initialize connection status on app load
    setConnectionStatus('api', 'connected')
    setConnectionStatus('websocket', 'connected')
    
    // Add welcome notification
    addNotification({
      type: 'info',
      title: 'Welcome to MemeTrader Pro',
      message: 'Trading system initialized successfully'
    })
    
    // Simulate heartbeat
    const heartbeatInterval = setInterval(() => {
      useSystemStore.getState().updateHeartbeat()
    }, 30000) // Every 30 seconds
    
    return () => clearInterval(heartbeatInterval)
  }, [setConnectionStatus, addNotification])
  
  return <>{children}</>
}