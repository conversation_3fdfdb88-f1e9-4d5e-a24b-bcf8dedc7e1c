'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Target, Settings, TrendingUp, TrendingDown, CheckCircle, ChevronDown } from 'lucide-react';

// Import types from service to ensure consistency
import { type ExitStrategyPreset, type TakeProfitTier } from '@/services/exitStrategy';

interface ExitStrategySelectorProps {
  onStrategySelect: (strategy: ExitStrategyPreset | null) => void;
  selectedStrategy: ExitStrategyPreset | null;
  isEnabled: boolean;
  onEnabledChange: (enabled: boolean) => void;
  className?: string;
}

// Default presets matching the main configuration page
const DEFAULT_PRESETS: ExitStrategyPreset[] = [
  {
    id: 'conservative',
    name: 'Conservative',
    description: 'Low risk, moderate returns',
    isDefault: false,
    configuration: {
      takeProfitTiers: [
        { percentage: 50, targetPercentage: 10, isActive: true },
        { percentage: 30, targetPercentage: 25, isActive: true },
        { percentage: 20, targetPercentage: 50, isActive: true }
      ],
      stopLossEnabled: true,
      stopLossPercentage: 5,
      trailingStopEnabled: false,
      moonBagEnabled: false
    }
  },
  {
    id: 'aggressive',
    name: 'Aggressive',
    description: 'Higher risk, higher returns',
    isDefault: false,
    configuration: {
      takeProfitTiers: [
        { percentage: 40, targetPercentage: 25, isActive: true },
        { percentage: 30, targetPercentage: 50, isActive: true },
        { percentage: 30, targetPercentage: 100, isActive: true }
      ],
      stopLossEnabled: true,
      stopLossPercentage: 10,
      trailingStopEnabled: true,
      trailingStopDistancePercentage: 15,
      moonBagEnabled: false
    }
  },
  {
    id: 'quick-flip',
    name: 'Quick Flip',
    description: 'Fast scalping strategy',
    isDefault: true,
    configuration: {
      takeProfitTiers: [
        { percentage: 100, targetPercentage: 5, isActive: true }
      ],
      stopLossEnabled: true,
      stopLossPercentage: 3,
      trailingStopEnabled: false,
      moonBagEnabled: false
    }
  },
  {
    id: 'hodl',
    name: 'HODL',
    description: 'Long-term holding strategy',
    isDefault: false,
    configuration: {
      takeProfitTiers: [
        { percentage: 25, targetPercentage: 50, isActive: true },
        { percentage: 25, targetPercentage: 100, isActive: true },
        { percentage: 25, targetPercentage: 200, isActive: true }
      ],
      stopLossEnabled: true,
      stopLossPercentage: 15,
      trailingStopEnabled: true,
      trailingStopDistancePercentage: 20,
      moonBagEnabled: true,
      moonBagPercentage: 25
    }
  },
  {
    id: 'scalping',
    name: 'Scalping',
    description: 'Ultra-fast trading strategy',
    isDefault: false,
    configuration: {
      takeProfitTiers: [
        { percentage: 100, targetPercentage: 3, isActive: true }
      ],
      stopLossEnabled: true,
      stopLossPercentage: 2,
      trailingStopEnabled: false,
      moonBagEnabled: false
    }
  }
];

export function ExitStrategySelector({
  onStrategySelect,
  selectedStrategy,
  isEnabled,
  onEnabledChange,
  className = ''
}: ExitStrategySelectorProps) {
  const [availablePresets, setAvailablePresets] = useState<ExitStrategyPreset[]>(DEFAULT_PRESETS);
  const [isLoading, setIsLoading] = useState(false);

  // Load presets from API
  useEffect(() => {
    const loadPresets = async () => {
      setIsLoading(true);
      try {
        // TODO: Replace with actual API call
        // const response = await fetch('/api/exit-strategies/presets');
        // const presets = await response.json();
        // setAvailablePresets(presets);
        
        // For now, use default presets and set default selection
        const defaultPreset = DEFAULT_PRESETS.find(p => p.isDefault) || DEFAULT_PRESETS[0];
        if (!selectedStrategy && isEnabled) {
          onStrategySelect(defaultPreset);
        }
      } catch (error) {
        console.error('Failed to load exit strategy presets:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadPresets();
  }, []);

  const handlePresetChange = (presetId: string) => {
    const preset = availablePresets.find(p => p.id === presetId);
    onStrategySelect(preset || null);
  };

  const handleEnabledToggle = (enabled: boolean) => {
    onEnabledChange(enabled);
    if (enabled && !selectedStrategy) {
      // Auto-select default preset when enabling
      const defaultPreset = availablePresets.find(p => p.isDefault) || availablePresets[0];
      onStrategySelect(defaultPreset);
    } else if (!enabled) {
      onStrategySelect(null);
    }
  };

  return (
    <Card className={`bg-gray-800 border-gray-700 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Target className="w-5 h-5 text-green-400" />
            Exit Strategy
          </CardTitle>
          <Checkbox
            checked={isEnabled}
            onCheckedChange={handleEnabledToggle}
            disabled={isLoading}
          />
        </div>
      </CardHeader>
      
      {isEnabled && (
        <CardContent className="space-y-4">
          {/* Preset Selection */}
          <div>
            <Label className="text-sm text-gray-300 mb-2 block">Strategy Preset</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="outline" 
                  className="w-full justify-between bg-gray-700 border-gray-600 text-white"
                  disabled={isLoading}
                >
                  {selectedStrategy ? selectedStrategy.name : "Choose exit strategy..."}
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full bg-gray-800 border-gray-600">
                {availablePresets.map((preset) => (
                  <DropdownMenuItem 
                    key={preset.id} 
                    onClick={() => handlePresetChange(preset.id)}
                    className="cursor-pointer hover:bg-gray-700"
                  >
                    <div className="flex items-center justify-between w-full">
                      <div>
                        <div className="font-medium text-white">{preset.name}</div>
                        <div className="text-xs text-gray-400">{preset.description}</div>
                      </div>
                      {preset.isDefault && (
                        <Badge variant="secondary" className="ml-2">Default</Badge>
                      )}
                    </div>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Strategy Summary */}
          {selectedStrategy && (
            <div className="space-y-3">
              <div className="p-3 bg-gray-700 rounded-lg">
                <h4 className="font-medium text-sm text-gray-200 mb-2 flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Strategy Summary
                </h4>
                
                <div className="space-y-2 text-xs">
                  {/* Take Profit Tiers */}
                  <div className="flex items-center gap-2">
                    <TrendingUp className="w-3 h-3 text-green-400" />
                    <span className="text-gray-300">
                      {selectedStrategy.configuration.takeProfitTiers.length} Take Profit Tier{selectedStrategy.configuration.takeProfitTiers.length !== 1 ? 's' : ''}
                    </span>
                  </div>
                  
                  {/* Stop Loss */}
                  {selectedStrategy.configuration.stopLossEnabled && (
                    <div className="flex items-center gap-2">
                      <TrendingDown className="w-3 h-3 text-red-400" />
                      <span className="text-gray-300">
                        Stop Loss: {selectedStrategy.configuration.stopLossPercentage}%
                      </span>
                    </div>
                  )}
                  
                  {/* Trailing Stop */}
                  {selectedStrategy.configuration.trailingStopEnabled && (
                    <div className="flex items-center gap-2">
                      <TrendingUp className="w-3 h-3 text-blue-400" />
                      <span className="text-gray-300">
                        Trailing Stop: {selectedStrategy.configuration.trailingStopDistancePercentage}%
                      </span>
                    </div>
                  )}
                  
                  {/* Moon Bag */}
                  {selectedStrategy.configuration.moonBagEnabled && (
                    <div className="flex items-center gap-2">
                      <span className="text-yellow-400">🌙</span>
                      <span className="text-gray-300">
                        Moon Bag: {selectedStrategy.configuration.moonBagPercentage}%
                      </span>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Quick Actions */}
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1 text-xs"
                  onClick={() => {
                    // TODO: Open detailed configuration
                    window.open('/exit-strategies', '_blank');
                  }}
                >
                  <Settings className="w-3 h-3 mr-1" />
                  Configure
                </Button>
              </div>
            </div>
          )}
          
          {/* Info Message */}
          <div className="p-2 bg-blue-900/20 border border-blue-700 rounded text-xs text-blue-300">
            <strong>Auto-Attach:</strong> This strategy will be automatically attached to your position after a successful trade.
          </div>
        </CardContent>
      )}
    </Card>
  );
}

export default ExitStrategySelector;
