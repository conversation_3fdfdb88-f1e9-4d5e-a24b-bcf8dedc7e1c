/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { WatchlistItem } from '../../components/watchlist/WatchlistItem';
import { WatchlistHeader } from '../../components/watchlist/WatchlistHeader';
import TradingPage from '../../app/trading/page';
import { useSelectedTokenStore } from '../../stores/selectedTokenStore';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn()
}));

// Mock hooks and services
jest.mock('../../hooks/useWallet', () => ({
  useWallet: () => ({
    wallet: { balance: 10.5, address: 'test-address' },
    isConnected: true,
    hasError: false,
    clearError: jest.fn(),
    reconnect: jest.fn(),
    hardReset: jest.fn()
  })
}));

jest.mock('../../hooks/usePrice', () => ({
  usePrice: () => ({
    formatSolAsUsd: (amount: number) => `$${(amount * 100).toFixed(2)}`
  })
}));

jest.mock('../../services/jupiterTrading', () => ({
  jupiterTradingService: {
    getQuote: jest.fn(),
    executeTrade: jest.fn()
  }
}));

jest.mock('../../services/tokenMetadata', () => ({
  tokenMetadataService: {
    getTokenMetadata: jest.fn().mockResolvedValue({
      symbol: 'TEST',
      name: 'Test Token',
      verified: true
    }),
    formatTokenAmount: jest.fn((amount) => amount)
  }
}));

jest.mock('../../services/tokenBalance', () => ({
  tokenBalanceService: {
    getTokenBalance: jest.fn().mockResolvedValue({ uiAmount: 5.0 })
  }
}));

// Mock zustand persist
jest.mock('zustand/middleware', () => ({
  persist: (config: any) => config
}));

describe('Watchlist to Trading Workflow Integration', () => {
  const mockPush = jest.fn();
  const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;

  beforeEach(() => {
    mockUseRouter.mockReturnValue({
      push: mockPush,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn()
    } as any);

    // Reset store
    useSelectedTokenStore.getState().clearSelection();
    
    jest.clearAllMocks();
  });

  describe('WatchlistItem to Trading Navigation', () => {
    const mockWatchlistItem = {
      id: '1',
      tokenAddress: 'So11111111111111111111111111111111111111112',
      tokenSymbol: 'SOL',
      tokenName: 'Solana',
      customName: 'My SOL',
      notes: 'Main holding',
      isPinned: true,
      priority: 1,
      verified: true,
      addedAt: new Date(),
      snapshot: {
        priceUsd: '100.50',
        priceChange1h: '2.5',
        priceChange24h: '-1.2',
        volume24h: '1000000'
      },
      metrics: {
        marketCap: '45000000000'
      }
    };

    it('should navigate to trading page with correct parameters', async () => {
      render(<WatchlistItem item={mockWatchlistItem} />);

      const sendToSwapButton = screen.getByTitle('Send to Trading');
      fireEvent.click(sendToSwapButton);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith(
          '/trading?mint=So11111111111111111111111111111111111111112&source=watchlist'
        );
      });

      // Check that store was updated
      const store = useSelectedTokenStore.getState();
      expect(store.selectedToken?.mint).toBe('So11111111111111111111111111111111111111112');
      expect(store.watchlistContext?.customName).toBe('My SOL');
      expect(store.navigationSource).toBe('watchlist');
    });

    it('should show loading state during navigation', async () => {
      render(<WatchlistItem item={mockWatchlistItem} />);

      const sendToSwapButton = screen.getByTitle('Send to Trading');
      fireEvent.click(sendToSwapButton);

      // Button should be disabled during loading
      expect(sendToSwapButton).toBeDisabled();
    });

    it('should handle navigation errors gracefully', async () => {
      mockPush.mockImplementationOnce(() => {
        throw new Error('Navigation failed');
      });

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      render(<WatchlistItem item={mockWatchlistItem} />);

      const sendToSwapButton = screen.getByTitle('Send to Trading');
      fireEvent.click(sendToSwapButton);

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith(
          'Failed to navigate to trading interface:',
          expect.any(Error)
        );
      });

      consoleSpy.mockRestore();
    });
  });

  describe('WatchlistHeader Bulk Trading', () => {
    const mockPinnedItems = [
      {
        id: '1',
        tokenAddress: 'So11111111111111111111111111111111111111112',
        tokenSymbol: 'SOL',
        tokenName: 'Solana',
        customName: 'My SOL',
        isPinned: true,
        verified: true
      },
      {
        id: '2',
        tokenAddress: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
        tokenSymbol: 'USDC',
        tokenName: 'USD Coin',
        isPinned: true,
        verified: true
      }
    ];

    it('should show bulk trading button when pinned items exist', () => {
      render(
        <WatchlistHeader
          pinnedCount={2}
          pinnedItems={mockPinnedItems}
        />
      );

      expect(screen.getByText('Trade Pinned')).toBeInTheDocument();
    });

    it('should not show bulk trading button when no pinned items', () => {
      render(
        <WatchlistHeader
          pinnedCount={0}
          pinnedItems={[]}
        />
      );

      expect(screen.queryByText('Trade Pinned')).not.toBeInTheDocument();
    });

    it('should navigate to trading with first pinned token', async () => {
      render(
        <WatchlistHeader
          pinnedCount={2}
          pinnedItems={mockPinnedItems}
        />
      );

      const bulkTradingButton = screen.getByText('Trade Pinned');
      fireEvent.click(bulkTradingButton);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith(
          '/trading?mint=So11111111111111111111111111111111111111112&source=watchlist'
        );
      });

      // Check store state
      const store = useSelectedTokenStore.getState();
      expect(store.selectedToken?.mint).toBe('So11111111111111111111111111111111111111112');
      expect(store.navigationSource).toBe('watchlist');
    });
  });

  describe('Trading Page URL Parameter Handling', () => {
    const mockUseSearchParams = jest.fn();

    beforeEach(() => {
      require('next/navigation').useSearchParams = mockUseSearchParams;
    });

    it('should parse URL parameters correctly', () => {
      const mockSearchParams = new URLSearchParams(
        'mint=So11111111111111111111111111111111111111112&source=watchlist&amount=1.5'
      );
      mockUseSearchParams.mockReturnValue(mockSearchParams);

      render(<TradingPage />);

      // Should show "From Watchlist" badge
      expect(screen.getByText('From Watchlist')).toBeInTheDocument();
      
      // Should show back button
      expect(screen.getByText('Back to Watchlist')).toBeInTheDocument();
    });

    it('should handle invalid URL parameters', () => {
      const mockSearchParams = new URLSearchParams(
        'mint=invalid_address&amount=-1&slippage=99999'
      );
      mockUseSearchParams.mockReturnValue(mockSearchParams);

      render(<TradingPage />);

      // Should show error messages
      expect(screen.getByText('URL Parameter Issues')).toBeInTheDocument();
    });

    it('should show watchlist context when available', () => {
      const mockSearchParams = new URLSearchParams(
        'mint=So11111111111111111111111111111111111111112&source=watchlist'
      );
      mockUseSearchParams.mockReturnValue(mockSearchParams);

      // Set up store with watchlist context
      useSelectedTokenStore.getState().setSelectedToken(
        {
          mint: 'So11111111111111111111111111111111111111112',
          symbol: 'SOL',
          name: 'Solana'
        },
        {
          customName: 'My SOL',
          notes: 'Test notes',
          isPinned: true
        }
      );

      render(<TradingPage />);

      // Should display watchlist context
      expect(screen.getByText('Custom: My SOL')).toBeInTheDocument();
      expect(screen.getByText('Notes: Test notes')).toBeInTheDocument();
      expect(screen.getByText('📌 Pinned')).toBeInTheDocument();
    });
  });

  describe('Back Navigation', () => {
    it('should navigate back to watchlist when back button is clicked', async () => {
      const mockSearchParams = new URLSearchParams('source=watchlist');
      require('next/navigation').useSearchParams.mockReturnValue(mockSearchParams);

      render(<TradingPage />);

      const backButton = screen.getByText('Back to Watchlist');
      fireEvent.click(backButton);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/watchlist');
      });
    });

    it('should handle keyboard navigation', async () => {
      const mockSearchParams = new URLSearchParams('source=watchlist');
      require('next/navigation').useSearchParams.mockReturnValue(mockSearchParams);

      render(<TradingPage />);

      // Simulate Escape key press
      fireEvent.keyDown(document, { key: 'Escape' });

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/watchlist');
      });
    });
  });

  describe('Cross-Component State Sharing', () => {
    it('should maintain state consistency across components', () => {
      const tokenData = {
        mint: 'So11111111111111111111111111111111111111112',
        symbol: 'SOL',
        name: 'Solana'
      };

      const watchlistContext = {
        customName: 'My SOL',
        isPinned: true
      };

      // Set state in store
      useSelectedTokenStore.getState().setSelectedToken(tokenData, watchlistContext);
      useSelectedTokenStore.getState().setNavigationSource('watchlist');

      // Check that state is accessible
      const store = useSelectedTokenStore.getState();
      expect(store.getDisplayName()).toBe('My SOL');
      expect(store.hasWatchlistContext()).toBe(true);
      expect(store.getFullContext()).toEqual({
        token: tokenData,
        watchlist: watchlistContext,
        source: 'watchlist'
      });
    });

    it('should handle store cleanup correctly', () => {
      // Set some state
      useSelectedTokenStore.getState().setSelectedToken(
        { mint: 'test', symbol: 'TEST' },
        { customName: 'Test' }
      );
      useSelectedTokenStore.getState().setNavigationSource('watchlist');

      // Clear state
      useSelectedTokenStore.getState().clearSelection();

      // Check that everything is cleared
      const store = useSelectedTokenStore.getState();
      expect(store.selectedToken).toBeNull();
      expect(store.watchlistContext).toBeNull();
      expect(store.navigationSource).toBeNull();
      expect(store.hasWatchlistContext()).toBe(false);
      expect(store.getDisplayName()).toBeNull();
    });
  });
});