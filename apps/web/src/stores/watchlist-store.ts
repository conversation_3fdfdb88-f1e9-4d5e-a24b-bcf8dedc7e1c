import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { WatchlistItemWithMetrics } from '../../../../packages/shared/src/types/watchlist'

// Polling intervals in milliseconds
export const POLLING_INTERVALS = {
  STANDARD: 60000, // 60 seconds for standard monitoring
  PINNED: 15000,   // 15 seconds for pinned items
  ALERT: 8000      // 8 seconds when approaching alert thresholds
} as const

export type PollingStatus = 'stopped' | 'active' | 'error'

export interface PollingState {
  status: PollingStatus
  currentInterval: number
  lastUpdateTimestamp: number | null
  errorCount: number
  isPolling: boolean
}

interface WatchlistState {
  // Watchlist data
  items: WatchlistItemWithMetrics[]
  selectedItem: WatchlistItemWithMetrics | null
  
  // Polling state
  polling: PollingState
  
  // Filtering and sorting
  sortBy: 'tokenSymbol' | 'priceUsd' | 'priceChange24h' | 'volume24h' | 'addedAt'
  sortOrder: 'asc' | 'desc'
  filterBy: 'all' | 'pinned' | 'alerts'
  searchQuery: string
  
  // Actions
  addItem: (item: WatchlistItemWithMetrics) => void
  removeItem: (id: string) => void
  updateItem: (id: string, updates: Partial<WatchlistItemWithMetrics>) => void
  selectItem: (item: WatchlistItemWithMetrics | null) => void
  togglePinned: (id: string) => void
  
  // Polling actions
  setPollingStatus: (status: PollingStatus) => void
  setPollingInterval: (interval: number) => void
  setLastUpdateTimestamp: (timestamp: number) => void
  incrementErrorCount: () => void
  resetErrorCount: () => void
  setIsPolling: (isPolling: boolean) => void
  
  // Market data updates
  updateMarketData: (data: WatchlistItemWithMetrics[]) => void
  
  // Filtering and sorting
  setSortBy: (sortBy: WatchlistState['sortBy']) => void
  setSortOrder: (sortOrder: 'asc' | 'desc') => void
  setFilterBy: (filterBy: WatchlistState['filterBy']) => void
  setSearchQuery: (query: string) => void
  
  // Computed getters
  getFilteredItems: () => WatchlistItemWithMetrics[]
  getSortedItems: (items: WatchlistItemWithMetrics[]) => WatchlistItemWithMetrics[]
  getDynamicPollingInterval: () => number
  getPinnedItems: () => WatchlistItemWithMetrics[]
  
  clearWatchlist: () => void
}

export const useWatchlistStore = create<WatchlistState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        items: [],
        selectedItem: null,
        
        // Polling initial state
        polling: {
          status: 'stopped',
          currentInterval: POLLING_INTERVALS.STANDARD,
          lastUpdateTimestamp: null,
          errorCount: 0,
          isPolling: false
        },
        
        sortBy: 'addedAt',
        sortOrder: 'desc',
        filterBy: 'all',
        searchQuery: '',
        
        // Actions
        addItem: (item) =>
          set((state) => ({
            items: [...state.items, item]
          })),
        
        removeItem: (id) =>
          set((state) => ({
            items: state.items.filter((item) => item.id !== id),
            selectedItem: state.selectedItem?.id === id ? null : state.selectedItem
          })),
        
        updateItem: (id, updates) =>
          set((state) => ({
            items: state.items.map((item) =>
              item.id === id 
                ? { ...item, ...updates, updatedAt: new Date() }
                : item
            ),
            selectedItem: state.selectedItem?.id === id
              ? { ...state.selectedItem, ...updates, updatedAt: new Date() }
              : state.selectedItem
          })),
        
        selectItem: (item) => set({ selectedItem: item }),
        
        togglePinned: (id) =>
          set((state) => ({
            items: state.items.map((item) =>
              item.id === id
                ? { ...item, isPinned: !item.isPinned, updatedAt: new Date() }
                : item
            )
          })),
        
        // Polling actions
        setPollingStatus: (status) =>
          set((state) => ({
            polling: { ...state.polling, status }
          })),
        
        setPollingInterval: (currentInterval) =>
          set((state) => ({
            polling: { ...state.polling, currentInterval }
          })),
        
        setLastUpdateTimestamp: (lastUpdateTimestamp) =>
          set((state) => ({
            polling: { ...state.polling, lastUpdateTimestamp }
          })),
        
        incrementErrorCount: () =>
          set((state) => ({
            polling: { ...state.polling, errorCount: state.polling.errorCount + 1 }
          })),
        
        resetErrorCount: () =>
          set((state) => ({
            polling: { ...state.polling, errorCount: 0 }
          })),
        
        setIsPolling: (isPolling) =>
          set((state) => ({
            polling: { ...state.polling, isPolling }
          })),
        
        // Market data updates
        updateMarketData: (data) =>
          set((state) => ({
            items: state.items.map((item) => {
              const updatedData = data.find((d) => d.id === item.id)
              return updatedData || item
            })
          })),
        
        // Filtering and sorting
        setSortBy: (sortBy) => set({ sortBy }),
        setSortOrder: (sortOrder) => set({ sortOrder }),
        setFilterBy: (filterBy) => set({ filterBy }),
        setSearchQuery: (searchQuery) => set({ searchQuery }),
        
        // Computed getters
        getFilteredItems: () => {
          const state = get()
          let filtered = state.items
          
          // Apply text search filter
          if (state.searchQuery) {
            const query = state.searchQuery.toLowerCase()
            filtered = filtered.filter(
              (item) =>
                item.tokenSymbol.toLowerCase().includes(query) ||
                item.tokenName.toLowerCase().includes(query) ||
                item.tokenAddress.toLowerCase().includes(query) ||
                (item.customName && item.customName.toLowerCase().includes(query))
            )
          }
          
          // Apply category filter
          switch (state.filterBy) {
            case 'pinned':
              filtered = filtered.filter((item) => item.isPinned)
              break
            case 'alerts':
              // For future: filter by items with alert thresholds
              filtered = filtered.filter((item) => item.isPinned) // Placeholder
              break
            // 'all' doesn't filter
          }
          
          return state.getSortedItems(filtered)
        },
        
        getSortedItems: (items) => {
          const state = get()
          
          return [...items].sort((a, b) => {
            // AC 15: Primary sort by pinned status (pinned items first)
            if (a.isPinned && !b.isPinned) return -1
            if (!a.isPinned && b.isPinned) return 1
            
            // Secondary sort by the selected column
            let comparison = 0
            
            switch (state.sortBy) {
              case 'tokenSymbol':
                comparison = a.tokenSymbol.localeCompare(b.tokenSymbol)
                break
              case 'priceUsd':
                const priceA = a.snapshot?.priceUsd?.toNumber() || 0
                const priceB = b.snapshot?.priceUsd?.toNumber() || 0
                comparison = priceA - priceB
                break
              case 'priceChange24h':
                const changeA = a.snapshot?.priceChange24h?.toNumber() || 0
                const changeB = b.snapshot?.priceChange24h?.toNumber() || 0
                comparison = changeA - changeB
                break
              case 'volume24h':
                const volumeA = a.snapshot?.volume24h?.toNumber() || 0
                const volumeB = b.snapshot?.volume24h?.toNumber() || 0
                comparison = volumeA - volumeB
                break
              case 'addedAt':
              default:
                // AC 15: Default sort by creation date (newest first)
                const dateA = typeof a.addedAt === 'string' ? new Date(a.addedAt) : a.addedAt
                const dateB = typeof b.addedAt === 'string' ? new Date(b.addedAt) : b.addedAt
                comparison = dateB.getTime() - dateA.getTime() // Newest first
                break
            }
            
            return state.sortOrder === 'asc' ? comparison : -comparison
          })
        },
        
        getDynamicPollingInterval: () => {
          const state = get()
          const pinnedItems = state.items.filter(item => item.isPinned)
          
          if (pinnedItems.length > 0) {
            return POLLING_INTERVALS.PINNED
          }
          
          return POLLING_INTERVALS.STANDARD
        },
        
        getPinnedItems: () => {
          const state = get()
          return state.items.filter(item => item.isPinned)
        },
        
        clearWatchlist: () =>
          set({
            items: [],
            selectedItem: null,
            searchQuery: '',
            filterBy: 'all'
          })
      }),
      {
        name: 'watchlist-store',
        partialize: (state) => ({
          // Persist all watchlist data except ephemeral UI state
          items: state.items,
          sortBy: state.sortBy,
          sortOrder: state.sortOrder,
          filterBy: state.filterBy
        })
      }
    ),
    {
      name: 'watchlist-store',
    }
  )
)