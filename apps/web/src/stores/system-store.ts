import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { walletService } from '@/services/wallet'

type NotificationType = 'info' | 'success' | 'warning' | 'error'

export interface Notification {
  id: string
  type: NotificationType
  title: string
  message: string
  timestamp: Date
  read: boolean
  persistent?: boolean // For critical alerts that shouldn't auto-dismiss
}

export interface ConnectionStatus {
  api: 'connected' | 'connecting' | 'disconnected' | 'error'
  wallet: 'connected' | 'connecting' | 'disconnected' | 'error'
  websocket: 'connected' | 'connecting' | 'disconnected' | 'error'
}

export interface WalletState {
  address: string | null
  balance: number | null // SOL balance
  isConnected: boolean
  network: string | null
  isMainnet: boolean
  lastUpdated: Date | null
  error: string | null
}

export interface SystemSettings {
  theme: 'light' | 'dark' | 'system'
  notifications: {
    enabled: boolean
    priceAlerts: boolean
    positionUpdates: boolean
    systemStatus: boolean
  }
  trading: {
    confirmations: boolean
    soundEffects: boolean
    autoRefresh: boolean
    refreshInterval: number // in seconds
  }
  display: {
    compactMode: boolean
    showPnLPercentage: boolean
    currencySymbol: 'USD' | 'SOL'
  }
}

interface SystemState {
  // App-wide state
  isLoading: boolean
  connectionStatus: ConnectionStatus
  lastHeartbeat?: Date
  
  // Wallet state
  wallet: WalletState
  
  // Notifications
  notifications: Notification[]
  unreadCount: number
  
  // User settings
  settings: SystemSettings
  
  // Error handling
  lastError?: {
    message: string
    timestamp: Date
    context?: string
  }
  
  // Actions
  setLoading: (loading: boolean) => void
  setConnectionStatus: (service: keyof ConnectionStatus, status: ConnectionStatus[keyof ConnectionStatus]) => void
  updateHeartbeat: () => void
  
  // Wallet actions
  updateWallet: (wallet: Partial<WalletState>) => void
  setWalletError: (error: string | null) => void
  refreshWalletBalance: () => Promise<void>
  connectWallet: () => Promise<void>
  resetWallet: () => void
  
  // Notification management
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void
  removeNotification: (id: string) => void
  markNotificationAsRead: (id: string) => void
  markAllNotificationsAsRead: () => void
  clearNotifications: () => void
  
  // Settings management
  updateSettings: (updates: Partial<SystemSettings>) => void
  resetSettings: () => void
  
  // Error handling
  setError: (error: { message: string; context?: string }) => void
  clearError: () => void
  
  // Computed getters
  getUnreadNotifications: () => Notification[]
  getNotificationsByType: (type: NotificationType) => Notification[]
}

const defaultSettings: SystemSettings = {
  theme: 'dark',
  notifications: {
    enabled: true,
    priceAlerts: true,
    positionUpdates: true,
    systemStatus: true
  },
  trading: {
    confirmations: true,
    soundEffects: false,
    autoRefresh: true,
    refreshInterval: 5
  },
  display: {
    compactMode: false,
    showPnLPercentage: true,
    currencySymbol: 'USD'
  }
}

export const useSystemStore = create<SystemState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        isLoading: false,
        connectionStatus: {
          api: 'disconnected',
          wallet: 'disconnected',
          websocket: 'disconnected'
        },
        lastHeartbeat: undefined,
        
        // Initial wallet state
        wallet: {
          address: null,
          balance: null,
          isConnected: false,
          network: null,
          isMainnet: false,
          lastUpdated: null,
          error: null,
        },
        
        notifications: [],
        unreadCount: 0,
        
        settings: defaultSettings,
        
        lastError: undefined,
        
        // Actions
        setLoading: (isLoading) => set({ isLoading }),
        
        setConnectionStatus: (service, status) =>
          set((state) => ({
            connectionStatus: {
              ...state.connectionStatus,
              [service]: status
            },
            lastHeartbeat: new Date()
          })),
          
        updateHeartbeat: () => set({ lastHeartbeat: new Date() }),
        
        // Wallet actions
        updateWallet: (walletUpdate) =>
          set((state) => ({
            wallet: {
              ...state.wallet,
              ...walletUpdate,
              lastUpdated: new Date(),
            }
          })),
        
        setWalletError: (error) =>
          set((state) => ({
            wallet: {
              ...state.wallet,
              error,
              lastUpdated: new Date(),
            }
          })),
        
        refreshWalletBalance: async () => {
          const { updateWallet, setWalletError, setConnectionStatus } = get();
          
          try {
            setConnectionStatus('wallet', 'connecting');
            const balance = await walletService.getWalletBalance();
            
            updateWallet({
              balance: balance.sol,
              error: null,
            });
            
            setConnectionStatus('wallet', 'connected');
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to refresh balance';
            setWalletError(errorMessage);
            setConnectionStatus('wallet', 'error');
          }
        },
        
        connectWallet: async () => {
          const { updateWallet, setWalletError, setConnectionStatus } = get();
          
          try {
            setConnectionStatus('wallet', 'connecting');
            const status = await walletService.getFullStatus();
            
            updateWallet({
              address: status.info.address,
              balance: status.balance?.sol || null,
              isConnected: status.connected,
              network: status.info.network,
              isMainnet: status.info.isMainnet,
              error: status.errors.length > 0 ? status.errors[0] : null,
            });
            
            setConnectionStatus('wallet', status.connected ? 'connected' : 'error');
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to connect wallet';
            setWalletError(errorMessage);
            setConnectionStatus('wallet', 'error');
          }
        },
        
        resetWallet: () =>
          set((state) => ({
            wallet: {
              address: null,
              balance: null,
              isConnected: false,
              network: null,
              isMainnet: false,
              lastUpdated: null,
              error: null,
            },
            connectionStatus: {
              ...state.connectionStatus,
              wallet: 'disconnected'
            }
          })),
        
        // Notification management
        addNotification: (notificationData) => {
          const notification: Notification = {
            ...notificationData,
            id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date(),
            read: false
          }
          
          set((state) => ({
            notifications: [notification, ...state.notifications],
            unreadCount: state.unreadCount + 1
          }))
          
          // Auto-remove non-persistent notifications after 5 seconds
          if (!notification.persistent) {
            setTimeout(() => {
              get().removeNotification(notification.id)
            }, 5000)
          }
        },
        
        removeNotification: (id) =>
          set((state) => {
            const notification = state.notifications.find(n => n.id === id)
            const wasUnread = notification && !notification.read
            
            return {
              notifications: state.notifications.filter((n) => n.id !== id),
              unreadCount: wasUnread ? Math.max(0, state.unreadCount - 1) : state.unreadCount
            }
          }),
        
        markNotificationAsRead: (id) =>
          set((state) => {
            const notification = state.notifications.find(n => n.id === id)
            const wasUnread = notification && !notification.read
            
            return {
              notifications: state.notifications.map((n) =>
                n.id === id ? { ...n, read: true } : n
              ),
              unreadCount: wasUnread ? Math.max(0, state.unreadCount - 1) : state.unreadCount
            }
          }),
        
        markAllNotificationsAsRead: () =>
          set((state) => ({
            notifications: state.notifications.map((n) => ({ ...n, read: true })),
            unreadCount: 0
          })),
        
        clearNotifications: () =>
          set({
            notifications: [],
            unreadCount: 0
          }),
        
        // Settings management
        updateSettings: (updates) =>
          set((state) => ({
            settings: {
              ...state.settings,
              ...updates
            }
          })),
        
        resetSettings: () => set({ settings: defaultSettings }),
        
        // Error handling
        setError: (error) =>
          set({
            lastError: {
              ...error,
              timestamp: new Date()
            }
          }),
        
        clearError: () => set({ lastError: undefined }),
        
        // Computed getters
        getUnreadNotifications: () => {
          const state = get()
          return state.notifications.filter((n) => !n.read)
        },
        
        getNotificationsByType: (type) => {
          const state = get()
          return state.notifications.filter((n) => n.type === type)
        }
      }),
      {
        name: 'system-store',
        partialize: (state) => ({
          // Persist user settings and non-ephemeral notifications
          settings: state.settings,
          notifications: state.notifications.filter(n => n.persistent)
        })
      }
    ),
    {
      name: 'system-store',
    }
  )
)