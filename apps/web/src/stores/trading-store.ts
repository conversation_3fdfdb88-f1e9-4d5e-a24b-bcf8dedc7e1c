import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { ExitStrategyPreset } from '@/services/exitStrategy'

// Import shared types following CLAUDE.md coding standards
// TODO: Import from @shared/types when package is properly configured
// import { Position } from '@shared/types'

// Temporary local types - MUST be replaced with shared types
export interface Position {
  id: string
  symbol: string
  currentPrice: number
  entryPrice: number
  quantity: number
  pnl: number
  pnlPercentage: number
  status: 'active' | 'closed' | 'pending'
  side: 'long' | 'short'
  createdAt: Date
  updatedAt: Date
}

// Exit Strategy Configuration Interface for form data
export interface ExitStrategyFormData {
  takeProfitTiers: {
    percentage: number;
    targetPercentage?: number;
    targetPrice?: number;
    isActive: boolean;
  }[];
  stopLossEnabled: boolean;
  stopLossPercentage?: number;
  stopLossPrice?: number;
  trailingStopEnabled: boolean;
  trailingStopDistancePercentage?: number;
  moonBagEnabled: boolean;
  moonBagPercentage?: number;
}

interface TradingState {
  // Current trading session data
  positions: Position[]
  selectedPosition: Position | null
  isTrading: boolean
  
  // Trading panel state
  orderType: 'market' | 'limit'
  orderSide: 'buy' | 'sell'
  orderAmount: number
  orderPrice?: number
  slippage: number
  
  // Exit Strategy state
  exitStrategyEnabled: boolean
  currentExitStrategy: ExitStrategyFormData | null
  selectedPreset: ExitStrategyPreset | null
  exitStrategyValid: boolean
  
  // Real-time data
  currentPrices: Record<string, number>
  priceUpdatedAt?: Date
  
  // Actions
  setPositions: (positions: Position[]) => void
  addPosition: (position: Position) => void
  updatePosition: (id: string, updates: Partial<Position>) => void
  removePosition: (id: string) => void
  selectPosition: (position: Position | null) => void
  
  setTradingState: (isTrading: boolean) => void
  setOrderType: (type: 'market' | 'limit') => void
  setOrderSide: (side: 'buy' | 'sell') => void
  setOrderAmount: (amount: number) => void
  setOrderPrice: (price: number | undefined) => void
  setSlippage: (slippage: number) => void
  
  // Exit Strategy actions
  setExitStrategyEnabled: (enabled: boolean) => void
  setCurrentExitStrategy: (strategy: ExitStrategyFormData | null) => void
  setSelectedPreset: (preset: ExitStrategyPreset | null) => void
  setExitStrategyValid: (valid: boolean) => void
  loadPreset: (preset: ExitStrategyPreset) => void
  clearExitStrategy: () => void
  
  updatePrices: (prices: Record<string, number>) => void
  clearTradingState: () => void
}

export const useTradingStore = create<TradingState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        positions: [],
        selectedPosition: null,
        isTrading: false,
        
        orderType: 'market',
        orderSide: 'buy',
        orderAmount: 0,
        orderPrice: undefined,
        slippage: 0.5, // 0.5% default slippage
        
        // Exit Strategy initial state
        exitStrategyEnabled: false,
        currentExitStrategy: null,
        selectedPreset: null,
        exitStrategyValid: false,
        
        currentPrices: {},
        priceUpdatedAt: undefined,
        
        // Actions
        setPositions: (positions) => set({ positions }),
        
        addPosition: (position) =>
          set((state) => ({
            positions: [...state.positions, position]
          })),
          
        updatePosition: (id, updates) =>
          set((state) => ({
            positions: state.positions.map((pos) =>
              pos.id === id ? { ...pos, ...updates, updatedAt: new Date() } : pos
            ),
            selectedPosition: state.selectedPosition?.id === id 
              ? { ...state.selectedPosition, ...updates, updatedAt: new Date() }
              : state.selectedPosition
          })),
          
        removePosition: (id) =>
          set((state) => ({
            positions: state.positions.filter((pos) => pos.id !== id),
            selectedPosition: state.selectedPosition?.id === id ? null : state.selectedPosition
          })),
          
        selectPosition: (position) => set({ selectedPosition: position }),
        
        setTradingState: (isTrading) => set({ isTrading }),
        setOrderType: (orderType) => set({ orderType }),
        setOrderSide: (orderSide) => set({ orderSide }),
        setOrderAmount: (orderAmount) => set({ orderAmount }),
        setOrderPrice: (orderPrice) => set({ orderPrice }),
        setSlippage: (slippage) => set({ slippage }),
        
        // Exit Strategy actions
        setExitStrategyEnabled: (enabled) => set({ exitStrategyEnabled: enabled }),
        
        setCurrentExitStrategy: (strategy) => 
          set((state) => ({
            currentExitStrategy: strategy,
            exitStrategyValid: strategy !== null
          })),
          
        setSelectedPreset: (preset) => set({ selectedPreset: preset }),
        
        setExitStrategyValid: (valid) => set({ exitStrategyValid: valid }),
        
        loadPreset: (preset) =>
          set((state) => {
            const config = preset.configuration;
            const strategy: ExitStrategyFormData = {
              takeProfitTiers: config.takeProfitTiers.map(tier => ({
                percentage: tier.percentage,
                targetPercentage: tier.targetPercentage,
                targetPrice: tier.targetPrice,
                isActive: tier.isActive,
              })),
              stopLossEnabled: config.stopLossEnabled,
              stopLossPercentage: config.stopLossPercentage,
              stopLossPrice: config.stopLossPrice,
              trailingStopEnabled: config.trailingStopEnabled,
              trailingStopDistancePercentage: config.trailingStopDistancePercentage,
              moonBagEnabled: config.moonBagEnabled,
              moonBagPercentage: config.moonBagPercentage,
            };
            
            return {
              selectedPreset: preset,
              currentExitStrategy: strategy,
              exitStrategyEnabled: true,
              exitStrategyValid: true
            };
          }),
          
        clearExitStrategy: () =>
          set({
            exitStrategyEnabled: false,
            currentExitStrategy: null,
            selectedPreset: null,
            exitStrategyValid: false
          }),
        
        updatePrices: (prices) =>
          set((state) => {
            // Update position PnL when prices change
            const updatedPositions = state.positions.map((position) => {
              const currentPrice = prices[position.symbol]
              if (currentPrice) {
                const priceDiff = currentPrice - position.entryPrice
                const pnl = priceDiff * position.quantity * (position.side === 'long' ? 1 : -1)
                const pnlPercentage = (priceDiff / position.entryPrice) * 100 * (position.side === 'long' ? 1 : -1)
                
                return {
                  ...position,
                  currentPrice,
                  pnl,
                  pnlPercentage,
                  updatedAt: new Date()
                }
              }
              return position
            })
            
            return {
              currentPrices: { ...state.currentPrices, ...prices },
              priceUpdatedAt: new Date(),
              positions: updatedPositions
            }
          }),
        
        clearTradingState: () =>
          set({
            positions: [],
            selectedPosition: null,
            isTrading: false,
            orderAmount: 0,
            orderPrice: undefined,
            currentPrices: {},
            priceUpdatedAt: undefined
          })
      }),
      {
        name: 'trading-store',
        partialize: (state) => ({
          // Only persist essential data, not ephemeral state
          positions: state.positions,
          orderType: state.orderType,
          slippage: state.slippage
        })
      }
    ),
    {
      name: 'trading-store',
    }
  )
)