import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'

// Import shared types following CLAUDE.md coding standards
// TODO: Import from @shared/types when package is properly configured
// import { Position } from '@shared/types'

// Temporary local types - MUST be replaced with shared types
export interface Position {
  id: string
  symbol: string
  currentPrice: number
  entryPrice: number
  quantity: number
  pnl: number
  pnlPercentage: number
  status: 'active' | 'closed' | 'pending'
  side: 'long' | 'short'
  createdAt: Date
  updatedAt: Date
}

interface TradingState {
  // Current trading session data
  positions: Position[]
  selectedPosition: Position | null
  isTrading: boolean
  
  // Trading panel state
  orderType: 'market' | 'limit'
  orderSide: 'buy' | 'sell'
  orderAmount: number
  orderPrice?: number
  slippage: number
  
  // Real-time data
  currentPrices: Record<string, number>
  priceUpdatedAt?: Date
  
  // Actions
  setPositions: (positions: Position[]) => void
  addPosition: (position: Position) => void
  updatePosition: (id: string, updates: Partial<Position>) => void
  removePosition: (id: string) => void
  selectPosition: (position: Position | null) => void
  
  setTradingState: (isTrading: boolean) => void
  setOrderType: (type: 'market' | 'limit') => void
  setOrderSide: (side: 'buy' | 'sell') => void
  setOrderAmount: (amount: number) => void
  setOrderPrice: (price: number | undefined) => void
  setSlippage: (slippage: number) => void
  
  updatePrices: (prices: Record<string, number>) => void
  clearTradingState: () => void
}

export const useTradingStore = create<TradingState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        positions: [],
        selectedPosition: null,
        isTrading: false,
        
        orderType: 'market',
        orderSide: 'buy',
        orderAmount: 0,
        orderPrice: undefined,
        slippage: 0.5, // 0.5% default slippage
        
        currentPrices: {},
        priceUpdatedAt: undefined,
        
        // Actions
        setPositions: (positions) => set({ positions }),
        
        addPosition: (position) =>
          set((state) => ({
            positions: [...state.positions, position]
          })),
          
        updatePosition: (id, updates) =>
          set((state) => ({
            positions: state.positions.map((pos) =>
              pos.id === id ? { ...pos, ...updates, updatedAt: new Date() } : pos
            ),
            selectedPosition: state.selectedPosition?.id === id 
              ? { ...state.selectedPosition, ...updates, updatedAt: new Date() }
              : state.selectedPosition
          })),
          
        removePosition: (id) =>
          set((state) => ({
            positions: state.positions.filter((pos) => pos.id !== id),
            selectedPosition: state.selectedPosition?.id === id ? null : state.selectedPosition
          })),
          
        selectPosition: (position) => set({ selectedPosition: position }),
        
        setTradingState: (isTrading) => set({ isTrading }),
        setOrderType: (orderType) => set({ orderType }),
        setOrderSide: (orderSide) => set({ orderSide }),
        setOrderAmount: (orderAmount) => set({ orderAmount }),
        setOrderPrice: (orderPrice) => set({ orderPrice }),
        setSlippage: (slippage) => set({ slippage }),
        
        updatePrices: (prices) =>
          set((state) => {
            // Update position PnL when prices change
            const updatedPositions = state.positions.map((position) => {
              const currentPrice = prices[position.symbol]
              if (currentPrice) {
                const priceDiff = currentPrice - position.entryPrice
                const pnl = priceDiff * position.quantity * (position.side === 'long' ? 1 : -1)
                const pnlPercentage = (priceDiff / position.entryPrice) * 100 * (position.side === 'long' ? 1 : -1)
                
                return {
                  ...position,
                  currentPrice,
                  pnl,
                  pnlPercentage,
                  updatedAt: new Date()
                }
              }
              return position
            })
            
            return {
              currentPrices: { ...state.currentPrices, ...prices },
              priceUpdatedAt: new Date(),
              positions: updatedPositions
            }
          }),
        
        clearTradingState: () =>
          set({
            positions: [],
            selectedPosition: null,
            isTrading: false,
            orderAmount: 0,
            orderPrice: undefined,
            currentPrices: {},
            priceUpdatedAt: undefined
          })
      }),
      {
        name: 'trading-store',
        partialize: (state) => ({
          // Only persist essential data, not ephemeral state
          positions: state.positions,
          orderType: state.orderType,
          slippage: state.slippage
        })
      }
    ),
    {
      name: 'trading-store',
    }
  )
)