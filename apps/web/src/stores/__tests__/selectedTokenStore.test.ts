import { renderHook, act } from '@testing-library/react';
import { useSelectedTokenStore } from '../selectedTokenStore';

// Mock zustand persist
jest.mock('zustand/middleware', () => ({
  persist: (config: any) => config
}));

describe('selectedTokenStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    act(() => {
      useSelectedTokenStore.getState().clearSelection();
    });
  });

  describe('setSelectedToken', () => {
    it('should set selected token with metadata', () => {
      const { result } = renderHook(() => useSelectedTokenStore());

      const tokenMetadata = {
        mint: 'So11111111111111111111111111111111111111112',
        symbol: 'SOL',
        name: '<PERSON><PERSON>',
        verified: true
      };

      act(() => {
        result.current.setSelectedToken(tokenMetadata);
      });

      expect(result.current.selectedToken).toEqual(tokenMetadata);
      expect(result.current.watchlistContext).toBeNull();
    });

    it('should set selected token with watchlist context', () => {
      const { result } = renderHook(() => useSelectedTokenStore());

      const tokenMetadata = {
        mint: 'So11111111111111111111111111111111111111112',
        symbol: 'SOL',
        name: 'Solana'
      };

      const watchlistContext = {
        customName: 'My SOL',
        notes: 'Primary holding',
        isPinned: true,
        priority: 1
      };

      act(() => {
        result.current.setSelectedToken(tokenMetadata, watchlistContext);
      });

      expect(result.current.selectedToken).toEqual(tokenMetadata);
      expect(result.current.watchlistContext).toEqual(watchlistContext);
    });
  });

  describe('setWatchlistContext', () => {
    it('should update watchlist context independently', () => {
      const { result } = renderHook(() => useSelectedTokenStore());

      const newContext = {
        customName: 'Updated Name',
        notes: 'Updated notes',
        isPinned: false
      };

      act(() => {
        result.current.setWatchlistContext(newContext);
      });

      expect(result.current.watchlistContext).toEqual(newContext);
    });
  });

  describe('setNavigationSource', () => {
    it('should set navigation source', () => {
      const { result } = renderHook(() => useSelectedTokenStore());

      act(() => {
        result.current.setNavigationSource('watchlist');
      });

      expect(result.current.navigationSource).toBe('watchlist');
    });
  });

  describe('setTradingContext', () => {
    it('should set trading context parameters', () => {
      const { result } = renderHook(() => useSelectedTokenStore());

      act(() => {
        result.current.setTradingContext(1.5, 100);
      });

      expect(result.current.suggestedAmount).toBe(1.5);
      expect(result.current.suggestedSlippage).toBe(100);
    });

    it('should handle partial trading context', () => {
      const { result } = renderHook(() => useSelectedTokenStore());

      act(() => {
        result.current.setTradingContext(2.0);
      });

      expect(result.current.suggestedAmount).toBe(2.0);
      expect(result.current.suggestedSlippage).toBeUndefined();
    });
  });

  describe('clearSelection', () => {
    it('should clear all state', () => {
      const { result } = renderHook(() => useSelectedTokenStore());

      // Set some state first
      act(() => {
        result.current.setSelectedToken({
          mint: 'test',
          symbol: 'TEST'
        });
        result.current.setNavigationSource('watchlist');
        result.current.setTradingContext(1.0, 50);
      });

      // Clear all state
      act(() => {
        result.current.clearSelection();
      });

      expect(result.current.selectedToken).toBeNull();
      expect(result.current.watchlistContext).toBeNull();
      expect(result.current.navigationSource).toBeNull();
      expect(result.current.suggestedAmount).toBeUndefined();
      expect(result.current.suggestedSlippage).toBeUndefined();
    });
  });

  describe('utility functions', () => {
    describe('hasWatchlistContext', () => {
      it('should return true when watchlist context exists', () => {
        const { result } = renderHook(() => useSelectedTokenStore());

        act(() => {
          result.current.setWatchlistContext({
            customName: 'Test Token'
          });
        });

        expect(result.current.hasWatchlistContext()).toBe(true);
      });

      it('should return false when no watchlist context', () => {
        const { result } = renderHook(() => useSelectedTokenStore());

        expect(result.current.hasWatchlistContext()).toBe(false);
      });
    });

    describe('getDisplayName', () => {
      it('should return custom name when available', () => {
        const { result } = renderHook(() => useSelectedTokenStore());

        act(() => {
          result.current.setSelectedToken({
            mint: 'test',
            symbol: 'TEST',
            name: 'Test Token'
          }, {
            customName: 'My Custom Name'
          });
        });

        expect(result.current.getDisplayName()).toBe('My Custom Name');
      });

      it('should return token name when no custom name', () => {
        const { result } = renderHook(() => useSelectedTokenStore());

        act(() => {
          result.current.setSelectedToken({
            mint: 'test',
            symbol: 'TEST',
            name: 'Test Token'
          });
        });

        expect(result.current.getDisplayName()).toBe('Test Token');
      });

      it('should return symbol when no name or custom name', () => {
        const { result } = renderHook(() => useSelectedTokenStore());

        act(() => {
          result.current.setSelectedToken({
            mint: 'test',
            symbol: 'TEST'
          });
        });

        expect(result.current.getDisplayName()).toBe('TEST');
      });

      it('should return "Unknown Token" when no token data', () => {
        const { result } = renderHook(() => useSelectedTokenStore());

        act(() => {
          result.current.setSelectedToken({
            mint: 'test'
          });
        });

        expect(result.current.getDisplayName()).toBe('Unknown Token');
      });

      it('should return null when no selected token', () => {
        const { result } = renderHook(() => useSelectedTokenStore());

        expect(result.current.getDisplayName()).toBeNull();
      });
    });

    describe('getFullContext', () => {
      it('should return complete context object', () => {
        const { result } = renderHook(() => useSelectedTokenStore());

        const token = { mint: 'test', symbol: 'TEST' };
        const watchlist = { customName: 'Custom' };
        const source = 'watchlist';

        act(() => {
          result.current.setSelectedToken(token, watchlist);
          result.current.setNavigationSource(source);
        });

        const context = result.current.getFullContext();

        expect(context.token).toEqual(token);
        expect(context.watchlist).toEqual(watchlist);
        expect(context.source).toBe(source);
      });

      it('should return null values when nothing is set', () => {
        const { result } = renderHook(() => useSelectedTokenStore());

        const context = result.current.getFullContext();

        expect(context.token).toBeNull();
        expect(context.watchlist).toBeNull();
        expect(context.source).toBeNull();
      });
    });
  });

  describe('state persistence', () => {
    it('should persist essential data only', () => {
      const { result } = renderHook(() => useSelectedTokenStore());

      act(() => {
        result.current.setSelectedToken({
          mint: 'test',
          symbol: 'TEST'
        }, {
          customName: 'Custom Name'
        });
        result.current.setNavigationSource('watchlist'); // Should not persist
        result.current.setTradingContext(1.0, 50); // Should not persist
      });

      // In a real scenario with persistence, we'd test that only selectedToken 
      // and watchlistContext are saved, not navigationSource or trading context
      expect(result.current.selectedToken).toBeTruthy();
      expect(result.current.watchlistContext).toBeTruthy();
      expect(result.current.navigationSource).toBe('watchlist');
      expect(result.current.suggestedAmount).toBe(1.0);
    });
  });
});