import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface TokenMetadata {
  mint: string;
  symbol?: string;
  name?: string;
  logo?: string;
  verified?: boolean;
  decimals?: number;
}

export interface WatchlistContext {
  customName?: string;
  notes?: string;
  isPinned?: boolean;
  priority?: number;
  addedAt?: Date;
}

export interface SelectedTokenState {
  // Core token information
  selectedToken: TokenMetadata | null;
  watchlistContext: WatchlistContext | null;
  navigationSource: string | null;
  
  // Trading context
  suggestedAmount?: number;
  suggestedSlippage?: number;
  
  // Actions
  setSelectedToken: (token: TokenMetadata | null, context?: WatchlistContext) => void;
  setWatchlistContext: (context: WatchlistContext | null) => void;
  setNavigationSource: (source: string | null) => void;
  setTradingContext: (amount?: number, slippage?: number) => void;
  clearSelection: () => void;
  
  // Utilities
  hasWatchlistContext: () => boolean;
  getDisplayName: () => string | null;
  getFullContext: () => {
    token: TokenMetadata | null;
    watchlist: WatchlistContext | null;
    source: string | null;
  };
}

export const useSelectedTokenStore = create<SelectedTokenState>()(
  persist(
    (set, get) => ({
      // Initial state
      selectedToken: null,
      watchlistContext: null,
      navigationSource: null,
      suggestedAmount: undefined,
      suggestedSlippage: undefined,

      // Actions
      setSelectedToken: (token, context) => {
        set({
          selectedToken: token,
          watchlistContext: context || null,
        });
      },

      setWatchlistContext: (context) => {
        set({ watchlistContext: context });
      },

      setNavigationSource: (source) => {
        set({ navigationSource: source });
      },

      setTradingContext: (amount, slippage) => {
        set({
          suggestedAmount: amount,
          suggestedSlippage: slippage,
        });
      },

      clearSelection: () => {
        set({
          selectedToken: null,
          watchlistContext: null,
          navigationSource: null,
          suggestedAmount: undefined,
          suggestedSlippage: undefined,
        });
      },

      // Utilities
      hasWatchlistContext: () => {
        const { watchlistContext } = get();
        return watchlistContext !== null;
      },

      getDisplayName: () => {
        const { selectedToken, watchlistContext } = get();
        if (!selectedToken) return null;
        
        // Prefer custom name from watchlist, then token name, then symbol
        return watchlistContext?.customName || 
               selectedToken.name || 
               selectedToken.symbol || 
               'Unknown Token';
      },

      getFullContext: () => {
        const { selectedToken, watchlistContext, navigationSource } = get();
        return {
          token: selectedToken,
          watchlist: watchlistContext,
          source: navigationSource,
        };
      },
    }),
    {
      name: 'selected-token-storage',
      partialize: (state) => ({
        // Only persist essential data, not temporary navigation state
        selectedToken: state.selectedToken,
        watchlistContext: state.watchlistContext,
      }),
    }
  )
);