import { WatchlistItemWithMetrics } from '../../../../../packages/shared/src/types/watchlist';

export interface PriceChange {
  currentPrice: number;
  previousPrice?: number;
  changeAmount?: number;
  changePercent?: number;
  interval: string;
}

export interface TokenPriceMetrics {
  tokenAddress: string;
  tokenSymbol: string;
  currentPrice: number;
  changes: {
    '5m'?: PriceChange;
    '15m'?: PriceChange;
    '30m'?: PriceChange;
    '1h'?: PriceChange;
    '4h'?: PriceChange;
    '24h'?: PriceChange;
  };
  volume24h?: number;
  marketCap?: number;
  lastUpdated: string;
}

export interface WatchlistItemWithPriceMetrics extends WatchlistItemWithMetrics {
  priceMetrics?: TokenPriceMetrics | null;
}

class PriceHistoryService {
  private baseUrl = '/api/watchlist';

  /**
   * Get watchlist items with price history metrics
   */
  async getItemsWithPriceHistory(): Promise<{
    items: WatchlistItemWithPriceMetrics[];
    total: number;
    timestamp: string;
  }> {
    try {
      // Fetch both endpoints in parallel
      const [historyResponse, regularResponse] = await Promise.all([
        fetch(`${this.baseUrl}/metrics/history`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }),
        fetch(`${this.baseUrl}/metrics`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })
      ]);

      if (!historyResponse.ok) {
        // Fallback to regular metrics if history endpoint not available
        if (historyResponse.status === 404) {
          console.log('Price history endpoint not available, falling back to regular metrics');
          return this.getItemsWithRegularMetrics();
        }
        throw new Error(`HTTP error! status: ${historyResponse.status}`);
      }

      const historyResult = await historyResponse.json();
      const regularResult = regularResponse.ok ? await regularResponse.json() : null;

      // Merge the data - use history as base and add snapshot data from regular
      if (regularResult && regularResult.items) {
        const mergedItems = historyResult.items.map((historyItem: any) => {
          const regularItem = regularResult.items.find((r: any) => r.id === historyItem.id);
          if (regularItem && regularItem.snapshot) {
            // Add snapshot data to get 24h change, volume, market cap
            return {
              ...historyItem,
              snapshot: regularItem.snapshot
            };
          }
          return historyItem;
        });
        
        return {
          items: mergedItems,
          total: historyResult.total,
          timestamp: historyResult.timestamp
        };
      }

      return historyResult;
    } catch (error) {
      console.error('Failed to fetch price history:', error);
      // Fallback to regular metrics
      return this.getItemsWithRegularMetrics();
    }
  }

  /**
   * Fallback to regular metrics from Jupiter
   */
  private async getItemsWithRegularMetrics(): Promise<{
    items: WatchlistItemWithPriceMetrics[];
    total: number;
    timestamp: string;
  }> {
    const response = await fetch(`${this.baseUrl}/metrics`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    // Convert regular metrics to price metrics format
    const itemsWithPriceMetrics = result.items.map((item: WatchlistItemWithMetrics) => ({
      ...item,
      priceMetrics: null // No historical data available
    }));

    return {
      items: itemsWithPriceMetrics,
      total: result.total,
      timestamp: result.timestamp
    };
  }
}

// Export singleton instance
export const priceHistoryService = new PriceHistoryService();