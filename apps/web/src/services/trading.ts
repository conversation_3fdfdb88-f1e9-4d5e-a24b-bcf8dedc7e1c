import { apiClient } from '@/lib/api-client'
import type { Position } from '@/stores/trading-store'

// API response types
interface PositionResponse {
  id: string
  symbol: string
  side: 'long' | 'short'
  size: number
  entry_price: number
  current_price: number
  pnl: number
  pnl_percentage: number
  status: 'active' | 'closed' | 'pending'
  created_at: string
  updated_at: string
}

interface CreatePositionRequest {
  symbol: string
  side: 'buy' | 'sell'
  amount: number
  order_type: 'market' | 'limit'
  price?: number
  slippage?: number
}

// Transform API response to frontend format
function transformPosition(apiPosition: PositionResponse): Position {
  return {
    id: apiPosition.id,
    symbol: apiPosition.symbol,
    currentPrice: apiPosition.current_price,
    entryPrice: apiPosition.entry_price,
    quantity: apiPosition.size,
    pnl: apiPosition.pnl,
    pnlPercentage: apiPosition.pnl_percentage,
    status: apiPosition.status,
    side: apiPosition.side,
    createdAt: new Date(apiPosition.created_at),
    updatedAt: new Date(apiPosition.updated_at)
  }
}

export class TradingService {
  static async getPositions(): Promise<Position[]> {
    const response = await apiClient.get<{ data: PositionResponse[] }>('/api/positions')
    return response.data.map(transformPosition)
  }
  
  static async getPosition(id: string): Promise<Position> {
    const response = await apiClient.get<{ data: PositionResponse }>(`/api/positions/${id}`)
    return transformPosition(response.data)
  }
  
  static async createPosition(request: CreatePositionRequest): Promise<Position> {
    const response = await apiClient.post<{ data: PositionResponse }>('/api/trades/create', request)
    return transformPosition(response.data)
  }
  
  static async closePosition(id: string): Promise<Position> {
    const response = await apiClient.post<{ data: PositionResponse }>(`/api/trades/${id}/close`)
    return transformPosition(response.data)
  }
  
  static async updatePosition(id: string, updates: Partial<CreatePositionRequest>): Promise<Position> {
    const response = await apiClient.put<{ data: PositionResponse }>(`/api/positions/${id}`, updates)
    return transformPosition(response.data)
  }
  
  // Price data methods
  static async getCurrentPrices(symbols: string[]): Promise<Record<string, number>> {
    const response = await apiClient.post<{ data: Record<string, number> }>('/api/prices/current', {
      symbols
    })
    return response.data
  }
  
  // Trading validation
  static async validateTrade(request: CreatePositionRequest): Promise<{
    valid: boolean
    errors?: string[]
    estimatedCost?: number
  }> {
    return apiClient.post('/api/trades/validate', request)
  }
}