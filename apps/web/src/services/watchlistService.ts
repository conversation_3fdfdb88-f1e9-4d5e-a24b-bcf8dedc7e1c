import { WatchlistItemWithMetrics, CreateWatchlistItemDto } from '../../../../../packages/shared/src/types/watchlist';

interface ApiResponse<T> {
  success?: boolean;
  error?: {
    code: string;
    message: string;
    details?: string[];
  };
  // For direct API responses that may not have success field
  id?: string;
  tokenAddress?: string;
}

class WatchlistService {
  private baseUrl = '/api/watchlist';

  /**
   * Add a new token to the watchlist
   */
  async addItem(data: CreateWatchlistItemDto): Promise<WatchlistItemWithMetrics> {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error?.message || `HTTP error! status: ${response.status}`);
      }

      // Return the created item
      return result;
    } catch (error) {
      console.error('Failed to add watchlist item:', error);
      throw error;
    }
  }

  /**
   * Get all watchlist items
   */
  async getItems(): Promise<WatchlistItemWithMetrics[]> {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return Array.isArray(result) ? result : [];
    } catch (error) {
      console.error('Failed to fetch watchlist items:', error);
      throw error;
    }
  }

  /**
   * Get watchlist items with market data/metrics
   */
  async getItemsWithMetrics(): Promise<{
    items: WatchlistItemWithMetrics[];
    total: number;
    timestamp: string;
    fromCache: boolean;
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/metrics`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Failed to fetch watchlist items with metrics:', error);
      throw error;
    }
  }

  /**
   * Update a watchlist item
   */
  async updateItem(id: string, data: Partial<CreateWatchlistItemDto>): Promise<WatchlistItemWithMetrics> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error?.message || `HTTP error! status: ${response.status}`);
      }

      return result;
    } catch (error) {
      console.error('Failed to update watchlist item:', error);
      throw error;
    }
  }

  /**
   * Delete a watchlist item
   */
  async deleteItem(id: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const result = await response.json();
        throw new Error(result.error?.message || `HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to delete watchlist item:', error);
      throw error;
    }
  }

  /**
   * Toggle pin status of a watchlist item
   */
  async togglePin(id: string): Promise<WatchlistItemWithMetrics> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}/pin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error?.message || `HTTP error! status: ${response.status}`);
      }

      return result;
    } catch (error) {
      console.error('Failed to toggle pin status:', error);
      throw error;
    }
  }

  /**
   * Get only pinned items
   */
  async getPinnedItems(): Promise<WatchlistItemWithMetrics[]> {
    try {
      const response = await fetch(`${this.baseUrl}/filter/pinned`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return Array.isArray(result) ? result : [];
    } catch (error) {
      console.error('Failed to fetch pinned items:', error);
      throw error;
    }
  }

  /**
   * Bulk import items
   */
  async bulkImport(items: CreateWatchlistItemDto[]): Promise<{
    imported: WatchlistItemWithMetrics[];
    errors: Array<{ item: CreateWatchlistItemDto; error: string }>;
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/bulk`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ items }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error?.message || `HTTP error! status: ${response.status}`);
      }

      return result;
    } catch (error) {
      console.error('Failed to bulk import items:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const watchlistService = new WatchlistService();