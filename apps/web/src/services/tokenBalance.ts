interface TokenBalance {
  mint: string;
  amount: string;
  decimals: number;
  uiAmount: number;
  symbol?: string;
  name?: string;
}

interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
}

class TokenBalanceService {
  private baseUrl = '/api/wallet';

  /**
   * Get all SPL token balances for the connected wallet
   */
  async getTokenBalances(): Promise<TokenBalance[]> {
    const response = await fetch(`${this.baseUrl}/token-balances`);
    const data: ApiResponse<TokenBalance[]> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to get token balances');
    }

    return data.data;
  }

  /**
   * Get balance for a specific token mint
   */
  async getTokenBalance(mint: string): Promise<TokenBalance | null> {
    const response = await fetch(`${this.baseUrl}/token-balance/${mint}`);
    const data: ApiResponse<TokenBalance | null> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to get token balance');
    }

    return data.data;
  }

  /**
   * Format token amount for display
   */
  formatTokenAmount(balance: TokenBalance, displayDecimals: number = 4): string {
    if (balance.uiAmount === 0) return '0';
    
    // For very small amounts, show more decimals
    if (balance.uiAmount < 0.0001) {
      return balance.uiAmount.toFixed(8);
    }
    
    // For normal amounts, show specified decimals
    if (balance.uiAmount < 1) {
      return balance.uiAmount.toFixed(Math.min(displayDecimals + 2, 6));
    }
    
    return balance.uiAmount.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: displayDecimals,
    });
  }

  /**
   * Get token balance with retry logic
   */
  async getTokenBalanceWithRetry(mint: string, maxRetries: number = 2): Promise<TokenBalance | null> {
    let lastError: Error | null = null;

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await this.getTokenBalance(mint);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (i < maxRetries) {
          // Wait before retry (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
        }
      }
    }

    console.warn(`Failed to get token balance after ${maxRetries + 1} attempts:`, lastError?.message);
    return null;
  }

  /**
   * Check if wallet has sufficient token balance for a transaction
   */
  async hasEnoughTokenBalance(mint: string, requiredAmount: number): Promise<{
    hasEnough: boolean;
    currentBalance: number;
    shortfall: number;
  }> {
    try {
      const balance = await this.getTokenBalance(mint);
      
      if (!balance) {
        return {
          hasEnough: false,
          currentBalance: 0,
          shortfall: requiredAmount,
        };
      }

      return {
        hasEnough: balance.uiAmount >= requiredAmount,
        currentBalance: balance.uiAmount,
        shortfall: Math.max(0, requiredAmount - balance.uiAmount),
      };
    } catch (error) {
      console.error('Error checking token balance:', error);
      return {
        hasEnough: false,
        currentBalance: 0,
        shortfall: requiredAmount,
      };
    }
  }

  /**
   * Get popular/well-known token symbols
   */
  getKnownTokenSymbol(mint: string): string | undefined {
    const knownTokens: Record<string, string> = {
      'So11111111111111111111111111111111111111112': 'SOL',
      'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USDC',
      'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 'USDT',
      'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263': 'BONK',
      'JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN': 'JUP',
      'rndrizKT3MK1iimdxRdWabcF7Zg7AR5T4nud4EkHBof': 'RND',
      'FYXnJhgLmhcELqsKyAfdKZExFfA7gaskQa5TuEZE9RZT': 'TEST',
      // Add more known tokens as needed
    };

    return knownTokens[mint];
  }

  /**
   * Filter balances to show only significant amounts
   */
  filterSignificantBalances(balances: TokenBalance[], minValue: number = 0.01): TokenBalance[] {
    return balances.filter(balance => balance.uiAmount >= minValue);
  }

  /**
   * Sort balances by USD value (simplified - by amount for now)
   */
  sortBalancesByValue(balances: TokenBalance[]): TokenBalance[] {
    return [...balances].sort((a, b) => b.uiAmount - a.uiAmount);
  }
}

// Export singleton instance
export const tokenBalanceService = new TokenBalanceService();
export type { TokenBalance };