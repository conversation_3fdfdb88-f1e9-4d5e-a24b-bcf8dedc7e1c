// Exit Strategy Service
// Handles API calls for exit strategy management

export interface ExitStrategyPreset {
  id: string;
  name: string;
  description: string;
  isDefault: boolean;
  configuration: ExitStrategyConfiguration;
  createdAt?: string;
  updatedAt?: string;
}

export interface ExitStrategyConfiguration {
  takeProfitTiers: TakeProfitTier[];
  stopLossEnabled: boolean;
  stopLossPercentage?: number;
  stopLossPrice?: number;
  trailingStopEnabled: boolean;
  trailingStopDistancePercentage?: number;
  moonBagEnabled: boolean;
  moonBagPercentage?: number;
}

export interface TakeProfitTier {
  id?: string;
  percentage: number;
  targetPrice?: number;
  targetPercentage?: number;
  isActive: boolean;
}

export interface ExitStrategy {
  id: string;
  positionId: string;
  presetId?: string;
  status: 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'CANCELLED';
  configuration: ExitStrategyConfiguration;
  createdAt: string;
  updatedAt: string;
  activatedAt?: string;
  completedAt?: string;
}

export interface CreateExitStrategyRequest {
  positionId: string;
  presetId?: string;
  configuration: ExitStrategyConfiguration;
}

export interface AttachStrategyToPositionRequest {
  positionId: string;
  presetId: string;
}

class ExitStrategyService {
  private baseUrl = '/api/exit-strategies';

  // Preset Management
  async getPresets(): Promise<ExitStrategyPreset[]> {
    try {
      const response = await fetch(`${this.baseUrl}/presets`);
      if (!response.ok) {
        throw new Error(`Failed to fetch presets: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching exit strategy presets:', error);
      throw error;
    }
  }

  async getPreset(presetId: string): Promise<ExitStrategyPreset> {
    try {
      const response = await fetch(`${this.baseUrl}/presets/${presetId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch preset: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching exit strategy preset:', error);
      throw error;
    }
  }

  async getDefaultPreset(): Promise<ExitStrategyPreset> {
    try {
      const response = await fetch(`${this.baseUrl}/presets/default`);
      if (!response.ok) {
        throw new Error(`Failed to fetch default preset: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching default exit strategy preset:', error);
      throw error;
    }
  }

  async createPreset(preset: Omit<ExitStrategyPreset, 'id' | 'createdAt' | 'updatedAt'>): Promise<ExitStrategyPreset> {
    try {
      const response = await fetch(`${this.baseUrl}/presets`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(preset),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to create preset: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error creating exit strategy preset:', error);
      throw error;
    }
  }

  async updatePreset(presetId: string, preset: Partial<ExitStrategyPreset>): Promise<ExitStrategyPreset> {
    try {
      const response = await fetch(`${this.baseUrl}/presets/${presetId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(preset),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update preset: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error updating exit strategy preset:', error);
      throw error;
    }
  }

  async deletePreset(presetId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/presets/${presetId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to delete preset: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error deleting exit strategy preset:', error);
      throw error;
    }
  }

  async setDefaultPreset(presetId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/presets/default/${presetId}`, {
        method: 'PUT',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to set default preset: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error setting default exit strategy preset:', error);
      throw error;
    }
  }

  // Strategy Management
  async createStrategy(request: CreateExitStrategyRequest): Promise<string> {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to create exit strategy: ${response.statusText}`);
      }
      
      const result = await response.json();
      return result.strategyId;
    } catch (error) {
      console.error('Error creating exit strategy:', error);
      throw error;
    }
  }

  async getStrategy(strategyId: string): Promise<ExitStrategy> {
    try {
      const response = await fetch(`${this.baseUrl}/${strategyId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch exit strategy: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching exit strategy:', error);
      throw error;
    }
  }

  async getStrategiesByPosition(positionId: string): Promise<ExitStrategy[]> {
    try {
      const response = await fetch(`${this.baseUrl}/position/${positionId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch strategies for position: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching strategies for position:', error);
      throw error;
    }
  }

  async updateStrategy(strategyId: string, configuration: ExitStrategyConfiguration): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/${strategyId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ configuration }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update exit strategy: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error updating exit strategy:', error);
      throw error;
    }
  }

  async deleteStrategy(strategyId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/${strategyId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to delete exit strategy: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error deleting exit strategy:', error);
      throw error;
    }
  }

  // Position Integration
  async attachStrategyToPosition(request: AttachStrategyToPositionRequest): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/attach/${request.positionId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ presetId: request.presetId }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to attach strategy to position: ${response.statusText}`);
      }
      
      const result = await response.json();
      return result.strategyId;
    } catch (error) {
      console.error('Error attaching strategy to position:', error);
      throw error;
    }
  }

  // Validation
  async validateConfiguration(configuration: ExitStrategyConfiguration): Promise<{ isValid: boolean; errors: string[] }> {
    try {
      const response = await fetch(`${this.baseUrl}/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ configuration }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to validate configuration: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error validating exit strategy configuration:', error);
      throw error;
    }
  }

  // Health Check
  async getHealthStatus(): Promise<{ status: string; timestamp: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/health`);
      if (!response.ok) {
        throw new Error(`Failed to get health status: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error getting exit strategy service health:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const exitStrategyService = new ExitStrategyService();
export default exitStrategyService;
