interface TokenInfo {
  address: string;
  name: string;
  symbol: string;
  decimals: number;
}

interface Route {
  dexLabel: string;
  percentage: number;
}

interface Quote {
  inputMint: string;
  outputMint: string;
  inAmount: string;
  outAmount: string;
  priceImpactPct: string;
  slippageBps: number;
  routes: Route[];
  pricePerToken: string;
  estimatedGas: string;
  inputToken?: TokenInfo;
  outputToken?: TokenInfo;
}

interface MEVProtectionConfig {
  level: 'basic' | 'standard' | 'maximum';
  priorityFeeMultiplier?: number;
  computeUnitMultiplier?: number;
  jitoTipLamports?: number;
  estimatedCostIncrease?: number;
}

interface TradeRequest {
  inputMint: string;
  outputMint: string;
  amountSol: number; // Amount of input token (not necessarily SOL, despite the name)
  slippageBps?: number;
  maxPriceImpact?: number;
  simulate?: boolean;
  mevProtection?: MEVProtectionConfig;
  speedPreference?: 'economy' | 'standard' | 'fast' | 'turbo';
}

interface TradeResult {
  success: boolean;
  signature?: string;
  error?: string;
  quote?: Quote;
  positionId?: string; // Position ID for exit strategy attachment
  transaction?: {
    signature: string;
    confirmationStatus?: 'processed' | 'confirmed' | 'finalized';
    slot?: number;
  };
  priceMonitoring?: {
    entryPrice: string;
    monitoringStarted: boolean;
  };
  mevProtection?: {
    level: string;
    priorityFeeLamports: number;
    jitoTipLamports: number;
    totalMevCost: number;
    estimatedSuccessProbability: number;
  };
  fees?: {
    networkFee: number;
    priorityFee: number;
    mevProtectionFee: number;
    totalFee: number;
  };
}

interface Position {
  id: string;
  tokenAddress: string;
  entryPrice: string;
  quantity: string;
  currentPrice: string;
  pnlPercent: string;
  pnlAmount: string;
  createdAt: string;
  lastUpdated: string;
}

interface TokenValidation {
  isValid: boolean;
  error?: string;
}

interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
}

class JupiterTradingService {
  private baseUrl = '/api/trading';

  /**
   * Get a quote for a token swap
   */
  async getQuote(request: {
    inputMint: string;
    outputMint: string;
    amountSol: number;
    slippageBps?: number;
  }): Promise<Quote> {
    const response = await fetch(`${this.baseUrl}/quote`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    const data: ApiResponse<Quote> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to get quote');
    }

    return data.data;
  }

  /**
   * Execute a complete buy trade with MEV protection
   */
  async executeTrade(request: TradeRequest, skipValidation: boolean = false): Promise<TradeResult> {
    // Optional validation - skip to maintain backward compatibility
    if (!skipValidation) {
      const validation = this.validateTradeRequest(request);
      if (!validation.isValid) {
        console.warn('Trade validation warning:', validation.error);
        // Don't throw error, just log warning to maintain compatibility
      }
    }

    // Set default MEV protection if not specified
    const tradeRequest = {
      ...request,
      mevProtection: request.mevProtection || { level: 'standard' as const },
      speedPreference: request.speedPreference || 'standard' as const
    };

    const response = await fetch(`${this.baseUrl}/buy`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(tradeRequest),
    });

    const data: ApiResponse<TradeResult> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to execute trade');
    }

    return data.data;
  }

  /**
   * Get all trading positions
   */
  async getPositions(): Promise<Position[]> {
    const response = await fetch(`${this.baseUrl}/positions`);
    const data: ApiResponse<Position[]> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to get positions');
    }

    return data.data;
  }

  /**
   * Get a specific position
   */
  async getPosition(id: string): Promise<Position> {
    const response = await fetch(`${this.baseUrl}/positions/${id}`);
    const data: ApiResponse<Position> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to get position');
    }

    return data.data;
  }

  /**
   * Get trading statistics
   */
  async getTradingStats(): Promise<{
    totalTrades: number;
    successfulTrades: number;
    failedTrades: number;
    totalVolumeSol: string;
    averageTradeSize: string;
    successRate: string;
  }> {
    const response = await fetch(`${this.baseUrl}/stats`);
    const data: ApiResponse<any> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to get trading stats');
    }

    return data.data;
  }

  /**
   * Start price monitoring
   */
  async startPriceMonitoring(intervalMs?: number): Promise<void> {
    const response = await fetch(`${this.baseUrl}/monitor/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ intervalMs }),
    });

    const data: ApiResponse<any> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to start price monitoring');
    }
  }

  /**
   * Stop price monitoring
   */
  async stopPriceMonitoring(): Promise<void> {
    const response = await fetch(`${this.baseUrl}/monitor/stop`, {
      method: 'POST',
    });

    const data: ApiResponse<any> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to stop price monitoring');
    }
  }

  /**
   * Get supported tokens
   */
  async getSupportedTokens(): Promise<TokenInfo[]> {
    const response = await fetch(`${this.baseUrl}/tokens/supported`);
    const data: ApiResponse<TokenInfo[]> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to get supported tokens');
    }

    return data.data;
  }

  /**
   * Validate a token contract address
   */
  async validateTokenAddress(address: string): Promise<TokenValidation> {
    const response = await fetch(`${this.baseUrl}/tokens/validate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ address }),
    });

    const data: ApiResponse<TokenValidation> = await response.json();
    
    if (!response.ok) {
      return { isValid: false, error: 'Network error' };
    }

    return data.success ? data.data : { isValid: false, error: data.error };
  }

  /**
   * Calculate estimated output amount
   */
  calculateEstimatedOutput(
    inputAmount: number,
    pricePerToken: string,
    decimals: number = 9
  ): string {
    const price = parseFloat(pricePerToken);
    const output = inputAmount / price;
    return (output * Math.pow(10, decimals)).toFixed(0);
  }

  /**
   * Format token amount for display
   */
  formatTokenAmount(amount: string, decimals: number = 9, displayDecimals: number = 6): string {
    const num = parseFloat(amount) / Math.pow(10, decimals);
    return num.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: displayDecimals,
    });
  }

  /**
   * Calculate USD value (simplified)
   */
  calculateUSDValue(solAmount: number, solPriceUSD: number = 100): string {
    return (solAmount * solPriceUSD).toLocaleString('en-US', {
      style: 'currency',
      currency: 'USD',
    });
  }

  /**
   * Validate trade parameters
   */
  validateTradeRequest(request: TradeRequest): { isValid: boolean; error?: string } {
    // Basic Solana address validation (more flexible)
    const isValidSolanaAddress = (address: string): boolean => {
      if (!address || typeof address !== 'string') return false;
      // Solana addresses are base58 encoded and typically 32-44 characters
      return address.length >= 32 && address.length <= 44 && /^[1-9A-HJ-NP-Za-km-z]+$/.test(address);
    };

    if (!isValidSolanaAddress(request.inputMint)) {
      return { isValid: false, error: 'Invalid input token address' };
    }

    if (!isValidSolanaAddress(request.outputMint)) {
      return { isValid: false, error: 'Invalid output token address' };
    }

    if (request.inputMint === request.outputMint) {
      return { isValid: false, error: 'Input and output tokens cannot be the same' };
    }

    if (request.amountSol <= 0) {
      return { isValid: false, error: 'Trade amount must be positive' };
    }

    // Apply different limits based on token type
    if (request.inputMint === 'So11111111111111111111111111111111111111112') {
      // SOL limit - reasonable limit for SOL trades
      if (request.amountSol > 10) {
        return { isValid: false, error: 'SOL trade amount exceeds maximum limit of 10 SOL' };
      }
    } else {
      // For other tokens, use a much higher limit or no limit for most cases
      // Most tokens are worth much less than SOL, so allow larger quantities
      if (request.amountSol > 1000000) { // 1 million tokens max (very generous)
        return { isValid: false, error: 'Token trade amount exceeds maximum limit of 1,000,000 tokens' };
      }
    }

    if (request.slippageBps && (request.slippageBps < 1 || request.slippageBps > 5000)) {
      return { isValid: false, error: 'Slippage must be between 0.01% and 50%' };
    }

    if (request.maxPriceImpact && request.maxPriceImpact > 10) {
      return { isValid: false, error: 'Maximum price impact cannot exceed 10%' };
    }

    return { isValid: true };
  }

  /**
   * Get MEV protection recommendations
   */
  async getMEVProtectionRecommendations(
    speedPreference: 'economy' | 'standard' | 'fast' | 'turbo' = 'standard'
  ): Promise<{
    levels: MEVProtectionConfig[];
    networkCongestion: {
      level: string;
      recentFees: {
        median: number;
        percentile75: number;
        percentile95: number;
      };
    };
  }> {
    const response = await fetch('/api/mev-protection/recommendations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ speedPreference }),
    });

    const data: ApiResponse<any> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to get MEV protection recommendations');
    }

    return data.data;
  }

  /**
   * Monitor transaction status with real-time updates
   */
  async monitorTransaction(
    signature: string,
    onUpdate: (status: {
      signature: string;
      status: 'processed' | 'confirmed' | 'finalized' | 'failed';
      confirmations: number;
      slot?: number;
      error?: string;
    }) => void,
    timeoutMs: number = 60000
  ): Promise<void> {
    const startTime = Date.now();
    const pollInterval = 2000; // 2 seconds

    const poll = async () => {
      try {
        const response = await fetch(`/api/transactions/status/${signature}`);
        const data: ApiResponse<any> = await response.json();
        
        if (data.success) {
          onUpdate(data.data);
          
          // Stop polling if confirmed/finalized/failed
          if (['confirmed', 'finalized', 'failed'].includes(data.data.status)) {
            return;
          }
        }
        
        // Continue polling if not timeout
        if (Date.now() - startTime < timeoutMs) {
          setTimeout(poll, pollInterval);
        } else {
          onUpdate({
            signature,
            status: 'failed',
            confirmations: 0,
            error: 'Transaction monitoring timed out'
          });
        }
      } catch (error) {
        onUpdate({
          signature,
          status: 'failed',
          confirmations: 0,
          error: error instanceof Error ? error.message : 'Unknown monitoring error'
        });
      }
    };

    await poll();
  }

  /**
   * Execute trade with real-time transaction monitoring
   */
  async executeTradeWithMonitoring(
    request: TradeRequest,
    onTransactionUpdate: (status: any) => void
  ): Promise<TradeResult> {
    const result = await this.executeTrade(request);
    
    if (result.success && result.signature) {
      // Start monitoring the transaction
      this.monitorTransaction(result.signature, onTransactionUpdate).catch(error => {
        console.error('Transaction monitoring failed:', error);
      });
    }
    
    return result;
  }

  /**
   * Get quote with retry logic
   */
  async getQuoteWithRetry(
    request: {
      inputMint: string;
      outputMint: string;
      amountSol: number;
      slippageBps?: number;
    },
    maxRetries: number = 2
  ): Promise<Quote> {
    let lastError: Error | null = null;

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await this.getQuote(request);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (i < maxRetries) {
          // Wait before retry (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
        }
      }
    }

    throw lastError || new Error('Failed to get quote after retries');
  }
}

// Export singleton instance
export const jupiterTradingService = new JupiterTradingService();
export type { 
  Quote, 
  TradeRequest, 
  TradeResult, 
  Position, 
  TokenInfo, 
  TokenValidation, 
  MEVProtectionConfig 
};