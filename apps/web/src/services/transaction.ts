interface TransactionParams {
  recipient: string;
  amountSol: number;
  memo?: string;
}

interface TransactionValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  estimatedFee: number;
  estimatedTotal: number;
}

interface TransactionLimits {
  maxPerTransaction: number;
  maxDaily: number;
  maxCount: number;
  requireConfirmation: boolean;
}

interface TransactionResult {
  signature: string;
  success: boolean;
  confirmationStatus: 'processed' | 'confirmed' | 'finalized';
  slot?: number;
  fee?: number;
  error?: string;
}

interface TransactionHistory {
  signature: string;
  type: 'transfer' | 'swap';
  recipient?: string;
  amount: number;
  fee: number;
  timestamp: string;
  status: 'success' | 'failed';
}

interface DailyStats {
  transactionsToday: number;
  amountToday: number;
  limits: TransactionLimits;
  remainingAmount: number;
  remainingCount: number;
}

interface TransactionStatus {
  signature: string;
  confirmations: number;
  status: 'processed' | 'confirmed' | 'finalized' | 'failed';
  slot?: number;
  blockTime?: number;
}

interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
  details?: string[];
}

class TransactionService {
  private baseUrl = '/api/transactions';

  /**
   * Get current transaction limits and daily stats
   */
  async getLimitsAndStats(): Promise<{ limits: TransactionLimits; dailyStats: DailyStats }> {
    const response = await fetch(`${this.baseUrl}/limits`);
    const data: ApiResponse<{ limits: TransactionLimits; dailyStats: DailyStats }> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to get transaction limits');
    }

    return data.data;
  }

  /**
   * Validate transaction parameters
   */
  async validateTransaction(params: TransactionParams): Promise<TransactionValidation> {
    const response = await fetch(`${this.baseUrl}/validate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });

    const data: ApiResponse<TransactionValidation> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to validate transaction');
    }

    return data.data;
  }

  /**
   * Simulate a transaction without actually sending it
   */
  async simulateTransaction(params: TransactionParams): Promise<TransactionResult> {
    const response = await fetch(`${this.baseUrl}/simulate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ...params, simulate: true }),
    });

    const data: ApiResponse<TransactionResult> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to simulate transaction');
    }

    return data.data;
  }

  /**
   * Sign and send a transaction
   */
  async sendTransaction(params: TransactionParams): Promise<TransactionResult> {
    const response = await fetch(`${this.baseUrl}/send`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ...params, simulate: false }),
    });

    const data: ApiResponse<TransactionResult> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to send transaction');
    }

    return data.data;
  }

  /**
   * Test wallet connection and signing capability
   */
  async testWalletConnection(): Promise<{
    success: boolean;
    address: string;
    balance: number;
    canSign: boolean;
    error?: string;
  }> {
    const response = await fetch(`${this.baseUrl}/test`);
    const data: ApiResponse<{
      success: boolean;
      address: string;
      balance: number;
      canSign: boolean;
      error?: string;
    }> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to test wallet connection');
    }

    return data.data;
  }

  /**
   * Get transaction status by signature
   */
  async getTransactionStatus(signature: string): Promise<TransactionStatus> {
    const response = await fetch(`${this.baseUrl}/status/${signature}`);
    const data: ApiResponse<TransactionStatus> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to get transaction status');
    }

    return data.data;
  }

  /**
   * Get transaction history with daily stats
   */
  async getTransactionHistory(): Promise<{
    transactions: TransactionHistory[];
    dailyStats: DailyStats;
  }> {
    const response = await fetch(`${this.baseUrl}/history`);
    const data: ApiResponse<{
      transactions: TransactionHistory[];
      dailyStats: DailyStats;
    }> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to get transaction history');
    }

    return data.data;
  }

  /**
   * Clear transaction history (for privacy)
   */
  async clearTransactionHistory(): Promise<void> {
    const response = await fetch(`${this.baseUrl}/history/clear`, {
      method: 'POST',
    });

    const data: ApiResponse<{ message: string }> = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to clear transaction history');
    }
  }

  /**
   * Execute a complete transaction with validation and confirmation
   */
  async executeTransaction(
    params: TransactionParams,
    options: {
      simulate?: boolean;
      skipValidation?: boolean;
      requireConfirmation?: boolean;
    } = {}
  ): Promise<TransactionResult> {
    const { simulate = false, skipValidation = false, requireConfirmation = true } = options;

    // Step 1: Validate transaction if not skipped
    if (!skipValidation) {
      const validation = await this.validateTransaction(params);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      // Show warnings to user (this would be handled in UI)
      if (validation.warnings.length > 0) {
        console.warn('Transaction warnings:', validation.warnings);
      }
    }

    // Step 2: Simulate if requested
    if (simulate) {
      return this.simulateTransaction(params);
    }

    // Step 3: Send transaction
    return this.sendTransaction(params);
  }

  /**
   * Monitor transaction until confirmed
   */
  async monitorTransaction(
    signature: string,
    timeoutMs: number = 60000,
    pollIntervalMs: number = 2000
  ): Promise<TransactionStatus> {
    const startTime = Date.now();

    while (Date.now() - startTime < timeoutMs) {
      try {
        const status = await this.getTransactionStatus(signature);
        
        // Return if confirmed or finalized
        if (status.status === 'confirmed' || status.status === 'finalized' || status.status === 'failed') {
          return status;
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, pollIntervalMs));
      } catch (error) {
        console.warn('Error monitoring transaction:', error);
        await new Promise(resolve => setTimeout(resolve, pollIntervalMs));
      }
    }

    throw new Error('Transaction monitoring timed out');
  }

  /**
   * Format SOL amount for display
   */
  formatSolAmount(amount: number, decimals: number = 6): string {
    return amount.toFixed(decimals);
  }

  /**
   * Format transaction signature for display
   */
  formatSignature(signature: string, chars: number = 8): string {
    if (!signature || signature.length < chars * 2) return signature;
    return `${signature.slice(0, chars)}...${signature.slice(-chars)}`;
  }

  /**
   * Validate Solana address format
   */
  isValidSolanaAddress(address: string): boolean {
    try {
      // Basic validation: Solana addresses are 32-44 characters, base58 encoded
      return address.length >= 32 && address.length <= 44 && /^[1-9A-HJ-NP-Za-km-z]+$/.test(address);
    } catch {
      return false;
    }
  }

  /**
   * Calculate estimated total cost including fees
   */
  calculateTotalCost(amount: number, estimatedFee: number = 0.000005): number {
    return amount + estimatedFee;
  }

  /**
   * Check if amount is within transaction limits
   */
  isValidTransactionAmount(amount: number, limits: TransactionLimits): {
    isValid: boolean;
    reason?: string;
  } {
    if (amount <= 0) {
      return { isValid: false, reason: 'Amount must be positive' };
    }

    if (amount > limits.maxPerTransaction) {
      return { 
        isValid: false, 
        reason: `Amount exceeds maximum per transaction (${limits.maxPerTransaction} SOL)` 
      };
    }

    return { isValid: true };
  }

  /**
   * Get transaction status color for UI
   */
  getStatusColor(status: string): string {
    switch (status) {
      case 'confirmed':
      case 'finalized':
      case 'success':
        return 'green';
      case 'processed':
        return 'yellow';
      case 'failed':
        return 'red';
      default:
        return 'gray';
    }
  }

  /**
   * Get status display text
   */
  getStatusText(status: string): string {
    switch (status) {
      case 'confirmed':
        return 'Confirmed';
      case 'finalized':
        return 'Finalized';
      case 'processed':
        return 'Processing';
      case 'failed':
        return 'Failed';
      case 'success':
        return 'Success';
      default:
        return 'Unknown';
    }
  }
}

// Export singleton instance
export const transactionService = new TransactionService();
export type { 
  TransactionParams, 
  TransactionValidation, 
  TransactionLimits, 
  TransactionResult, 
  TransactionHistory, 
  TransactionStatus,
  DailyStats 
};