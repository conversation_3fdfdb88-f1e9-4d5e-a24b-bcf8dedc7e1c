interface TokenPrice {
  symbol: string;
  price: number;
  lastUpdated: Date;
}

interface PriceCache {
  [symbol: string]: {
    price: number;
    lastUpdated: Date;
  };
}

class PriceService {
  private cache: PriceCache = {};
  private readonly CACHE_DURATION = 30000; // 30 seconds

  /**
   * Get current SOL price in USD
   */
  async getSolPrice(): Promise<number> {
    return this.getTokenPrice('SOL');
  }

  /**
   * Get token price from CoinGecko API
   */
  async getTokenPrice(symbol: string): Promise<number> {
    const cached = this.cache[symbol];
    const now = new Date();

    // Return cached price if still valid
    if (cached && (now.getTime() - cached.lastUpdated.getTime()) < this.CACHE_DURATION) {
      return cached.price;
    }

    try {
      // Use CoinGecko API for real-time prices
      const coinId = symbol === 'SOL' ? 'solana' : symbol.toLowerCase();
      const response = await fetch(
        `https://api.coingecko.com/api/v3/simple/price?ids=${coinId}&vs_currencies=usd`,
        {
          headers: {
            'Accept': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`CoinGecko API error: ${response.status}`);
      }

      const data = await response.json();
      const price = data[coinId]?.usd;

      if (typeof price !== 'number') {
        throw new Error(`Price not found for ${symbol}`);
      }

      // Cache the price
      this.cache[symbol] = {
        price,
        lastUpdated: now,
      };

      return price;
    } catch (error) {
      console.warn(`Failed to fetch ${symbol} price:`, error);
      
      // Return cached price if available, otherwise fallback
      if (cached) {
        return cached.price;
      }
      
      // Fallback prices (approximate)
      const fallbackPrices: { [key: string]: number } = {
        SOL: 200, // Approximate SOL price fallback
      };
      
      return fallbackPrices[symbol] || 0;
    }
  }

  /**
   * Convert SOL amount to USD
   */
  async solToUsd(solAmount: number): Promise<number> {
    const solPrice = await this.getSolPrice();
    return solAmount * solPrice;
  }

  /**
   * Format USD amount for display
   */
  formatUsd(amount: number, decimals: number = 2): string {
    if (amount < 0.01) {
      return '$0.00';
    }
    return `$${amount.toFixed(decimals)}`;
  }

  /**
   * Clear price cache
   */
  clearCache(): void {
    this.cache = {};
  }

  /**
   * Get all cached prices
   */
  getCachedPrices(): PriceCache {
    return { ...this.cache };
  }
}

// Export singleton instance
export const priceService = new PriceService();
export type { TokenPrice };