interface TokenMetadata {
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  logoURI?: string;
  verified?: boolean;
}

interface TokenCache {
  [address: string]: {
    metadata: TokenMetadata;
    lastUpdated: Date;
  };
}

class TokenMetadataService {
  private cache: TokenCache = {};
  private readonly CACHE_DURATION = 300000; // 5 minutes

  // Known token metadata for popular tokens
  private knownTokens: { [address: string]: TokenMetadata } = {
    'So11111111111111111111111111111111111111112': {
      address: 'So11111111111111111111111111111111111111112',
      name: '<PERSON><PERSON>',
      symbol: 'SOL',
      decimals: 9,
      logoURI: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png',
      verified: true,
    },
    'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': {
      address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
      name: 'USD Coin',
      symbol: 'USDC',
      decimals: 6,
      logoURI: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v/logo.png',
      verified: true,
    },
    'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': {
      address: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
      name: 'Tether USD',
      symbol: 'USDT',
      decimals: 6,
      logoURI: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB/logo.png',
      verified: true,
    },
    // Popular Solana tokens
    'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263': {
      address: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
      name: 'Bonk',
      symbol: 'BONK',
      decimals: 5,
      logoURI: 'https://arweave.net/hQiPZOsRZXGXBJd_82PhVdlM_hACsT_q6wqwf5cSY7I',
      verified: true,
    },
    'EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm': {
      address: 'EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm',
      name: 'dogwifhat',
      symbol: 'WIF',
      decimals: 6,
      logoURI: 'https://bafkreifryvyui4gshimmxl26uec3ol3kummjnuljb34vt7gl7cgml3hnrq.ipfs.nftstorage.link/',
      verified: true,
    },
    'JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN': {
      address: 'JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN',
      name: 'Jupiter',
      symbol: 'JUP',
      decimals: 6,
      logoURI: 'https://static.jup.ag/jup/icon.png',
      verified: true,
    },
    // Test token for debugging
    'FYXnJhgLmhcELqsKyAfdKZExFfA7gaskQa5TuEZE9RZT': {
      address: 'FYXnJhgLmhcELqsKyAfdKZExFfA7gaskQa5TuEZE9RZT',
      name: 'Test Token',
      symbol: 'TEST',
      decimals: 9,
      verified: false,
    },
  };

  /**
   * Get token metadata with caching
   */
  async getTokenMetadata(address: string): Promise<TokenMetadata> {
    // Check cache first
    const cached = this.cache[address];
    const now = new Date();
    
    if (cached && (now.getTime() - cached.lastUpdated.getTime()) < this.CACHE_DURATION) {
      return cached.metadata;
    }

    // Check if it's a known token
    if (this.knownTokens[address]) {
      const metadata = this.knownTokens[address];
      this.cache[address] = {
        metadata,
        lastUpdated: now,
      };
      return metadata;
    }

    // Try to fetch from Jupiter token list or other sources
    try {
      const metadata = await this.fetchTokenMetadata(address);
      this.cache[address] = {
        metadata,
        lastUpdated: now,
      };
      return metadata;
    } catch (error) {
      // Only log for development, reduce console noise in production
      if (process.env.NODE_ENV === 'development') {
        console.debug(`Token metadata not found for ${address.slice(0, 8)}...`);
      }
      
      // Return basic metadata as fallback
      const fallbackMetadata: TokenMetadata = {
        address,
        name: 'Unknown Token',
        symbol: 'UNK',
        decimals: 9, // Most Solana tokens use 9 decimals
        verified: false,
      };

      this.cache[address] = {
        metadata: fallbackMetadata,
        lastUpdated: now,
      };
      
      return fallbackMetadata;
    }
  }

  /**
   * Try to fetch token metadata from various sources
   */
  private async fetchTokenMetadata(address: string): Promise<TokenMetadata> {
    // Try Jupiter token list first (this would be the actual implementation)
    // For now, we'll simulate with the basic metadata
    
    // In a real implementation, you would:
    // 1. Try Jupiter token list API
    // 2. Try Solana token registry
    // 3. Try on-chain metadata (Metaplex)
    // 4. Try other token aggregators

    throw new Error('Token metadata not found');
  }

  /**
   * Format token amount based on decimals
   */
  formatTokenAmount(amount: string, metadata: TokenMetadata, displayDecimals: number = 2): string {
    const num = parseFloat(amount) / Math.pow(10, metadata.decimals);
    
    if (num === 0) return '0';
    if (num < 0.01) return `<0.${'0'.repeat(displayDecimals - 1)}1`;
    
    return num.toLocaleString(undefined, {
      minimumFractionDigits: 0,
      maximumFractionDigits: displayDecimals,
    });
  }

  /**
   * Get token display info for UI
   */
  getTokenDisplayInfo(metadata: TokenMetadata): {
    displayName: string;
    displaySymbol: string;
    isVerified: boolean;
    logoURI?: string;
  } {
    return {
      displayName: metadata.name,
      displaySymbol: metadata.symbol,
      isVerified: metadata.verified || false,
      logoURI: metadata.logoURI,
    };
  }

  /**
   * Clear metadata cache
   */
  clearCache(): void {
    this.cache = {};
  }

  /**
   * Add custom token metadata
   */
  addCustomToken(metadata: TokenMetadata): void {
    this.knownTokens[metadata.address] = metadata;
  }

  /**
   * Check if token is likely a high-decimal token (>9 decimals)
   */
  isHighDecimalToken(metadata: TokenMetadata): boolean {
    return metadata.decimals > 9;
  }
}

// Export singleton instance
export const tokenMetadataService = new TokenMetadataService();
export type { TokenMetadata };