import { apiClient } from '@/lib/api-client'
import type { WatchlistToken } from '@/stores/watchlist-store'

interface WatchlistTokenResponse {
  id: string
  symbol: string
  name: string
  address: string
  current_price?: number
  price_change_24h?: number
  volume_24h?: number
  market_cap?: number
  is_tracking: boolean
  alert_thresholds?: {
    price_above?: number
    price_below?: number
    volume_above?: number
  }
  created_at: string
  updated_at: string
}

interface TokenSearchResult {
  symbol: string
  name: string
  address: string
  logo_uri?: string
  market_cap?: number
  verified?: boolean
}

// Transform API response to frontend format
function transformWatchlistToken(apiToken: WatchlistTokenResponse): WatchlistToken {
  return {
    id: apiToken.id,
    symbol: apiToken.symbol,
    name: apiToken.name,
    address: apiToken.address,
    currentPrice: apiToken.current_price,
    priceChange24h: apiToken.price_change_24h,
    volume24h: apiToken.volume_24h,
    marketCap: apiToken.market_cap,
    isTracking: apiToken.is_tracking,
    alertThresholds: apiToken.alert_thresholds ? {
      priceAbove: apiToken.alert_thresholds.price_above,
      priceBelow: apiToken.alert_thresholds.price_below,
      volumeAbove: apiToken.alert_thresholds.volume_above
    } : undefined,
    addedAt: new Date(apiToken.created_at),
    updatedAt: new Date(apiToken.updated_at)
  }
}

export class WatchlistService {
  static async getWatchlist(): Promise<WatchlistToken[]> {
    const response = await apiClient.get<{ data: WatchlistTokenResponse[] }>('/api/watchlist')
    return response.data.map(transformWatchlistToken)
  }
  
  static async addToWatchlist(token: {
    symbol: string
    name: string
    address: string
    isTracking?: boolean
  }): Promise<WatchlistToken> {
    const response = await apiClient.post<{ data: WatchlistTokenResponse }>('/api/watchlist', {
      symbol: token.symbol,
      name: token.name,
      address: token.address,
      is_tracking: token.isTracking || false
    })
    return transformWatchlistToken(response.data)
  }
  
  static async removeFromWatchlist(id: string): Promise<void> {
    await apiClient.delete(`/api/watchlist/${id}`)
  }
  
  static async updateWatchlistToken(
    id: string, 
    updates: {
      isTracking?: boolean
      alertThresholds?: {
        priceAbove?: number
        priceBelow?: number
        volumeAbove?: number
      }
    }
  ): Promise<WatchlistToken> {
    const response = await apiClient.put<{ data: WatchlistTokenResponse }>(`/api/watchlist/${id}`, {
      is_tracking: updates.isTracking,
      alert_thresholds: updates.alertThresholds ? {
        price_above: updates.alertThresholds.priceAbove,
        price_below: updates.alertThresholds.priceBelow,
        volume_above: updates.alertThresholds.volumeAbove
      } : undefined
    })
    return transformWatchlistToken(response.data)
  }
  
  // Token search and discovery
  static async searchTokens(query: string, limit = 10): Promise<TokenSearchResult[]> {
    const response = await apiClient.get<{ data: TokenSearchResult[] }>(
      `/api/tokens/search?q=${encodeURIComponent(query)}&limit=${limit}`
    )
    return response.data
  }
  
  static async getTokenInfo(addressOrSymbol: string): Promise<TokenSearchResult> {
    const response = await apiClient.get<{ data: TokenSearchResult }>(
      `/api/tokens/${encodeURIComponent(addressOrSymbol)}`
    )
    return response.data
  }
  
  // Bulk price updates for watchlist
  static async updateWatchlistPrices(tokenIds: string[]): Promise<Record<string, {
    price: number
    priceChange24h: number
    volume24h: number
    marketCap: number
  }>> {
    const response = await apiClient.post<{ 
      data: Record<string, {
        price: number
        price_change_24h: number
        volume_24h: number
        market_cap: number
      }> 
    }>('/api/watchlist/prices', { token_ids: tokenIds })
    
    // Transform keys to camelCase
    const transformed: Record<string, any> = {}
    Object.entries(response.data).forEach(([id, data]) => {
      transformed[id] = {
        price: data.price,
        priceChange24h: data.price_change_24h,
        volume24h: data.volume_24h,
        marketCap: data.market_cap
      }
    })
    
    return transformed
  }
}