interface WalletInfo {
  publicKey: string;
  address: string;
  isValid: boolean;
  network: string;
  rpcUrl: string;
  isMainnet: boolean;
}

interface WalletBalance {
  sol: number;
  lamports: number;
  lastUpdated: string;
}

interface WalletValidation {
  isValid: boolean;
  address?: string;
  error?: string;
}

interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
}

class WalletService {
  public baseUrl = '/api/wallet';

  /**
   * Get wallet information including address and network
   */
  async getWalletInfo(): Promise<WalletInfo> {
    const response = await fetch(`${this.baseUrl}/info`);
    const data: ApiResponse<WalletInfo> = await response.json();

    if (!data.success) {
      throw new Error(data.error || 'Failed to get wallet info');
    }

    return data.data;
  }

  /**
   * Get wallet balance with retry logic
   */
  async getWalletBalance(retries: number = 2): Promise<WalletBalance> {
    let lastError: Error | null = null;

    for (let i = 0; i <= retries; i++) {
      try {
        const response = await fetch(`${this.baseUrl}/balance`);
        const data: ApiResponse<WalletBalance> = await response.json();

        if (data.success) {
          return data.data;
        } else {
          throw new Error(data.error || 'Failed to get wallet balance');
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        // Wait before retry (exponential backoff)
        if (i < retries) {
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
        }
      }
    }

    throw lastError || new Error('Failed to get wallet balance after retries');
  }

  /**
   * Validate wallet connection and permissions
   */
  async validateWallet(): Promise<WalletValidation> {
    const response = await fetch(`${this.baseUrl}/validate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}),
    });

    const data: ApiResponse<WalletValidation> = await response.json();
    return data.data;
  }

  /**
   * Check wallet connection status
   */
  async checkConnection(): Promise<{ connected: boolean; error?: string }> {
    try {
      const info = await this.getWalletInfo();
      return { connected: info.isValid };
    } catch (error) {
      return {
        connected: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get wallet status with all information
   */
  async getFullStatus(): Promise<{
    info: WalletInfo;
    balance?: WalletBalance;
    validation: WalletValidation;
    connected: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];
    let info: WalletInfo;
    let balance: WalletBalance | undefined;
    let validation: WalletValidation;

    try {
      info = await this.getWalletInfo();
    } catch (error) {
      const errorMsg = `Info error: ${error instanceof Error ? error.message : 'Unknown'}`;
      errors.push(errorMsg);
      // Create default info if failed
      info = {
        publicKey: '',
        address: '',
        isValid: false,
        network: 'unknown',
        rpcUrl: '',
        isMainnet: false,
      };
    }

    try {
      balance = await this.getWalletBalance(1); // Single retry for full status
    } catch (error) {
      const errorMsg = `Balance error: ${error instanceof Error ? error.message : 'Unknown'}`;
      errors.push(errorMsg);
    }

    try {
      validation = await this.validateWallet();
    } catch (error) {
      const errorMsg = `Validation error: ${error instanceof Error ? error.message : 'Unknown'}`;
      errors.push(errorMsg);
      validation = { isValid: false, error: 'Validation failed' };
    }

    return {
      info,
      balance,
      validation,
      connected: info.isValid && validation.isValid,
      errors,
    };
  }

  /**
   * Poll wallet status with callback
   */
  startStatusPolling(
    callback: (status: WalletInfo) => void,
    intervalMs: number = 30000
  ): () => void {
    const poll = async () => {
      try {
        const status = await this.getWalletInfo();
        callback(status);
      } catch (error) {
        // Silently handle polling errors
      }
    };

    // Initial poll
    poll();

    // Set up interval
    const interval = setInterval(poll, intervalMs);

    // Return cleanup function
    return () => clearInterval(interval);
  }

  /**
   * Format address for display
   */
  formatAddress(address: string, chars: number = 4): string {
    if (!address || address.length < chars * 2) return address;
    return `${address.slice(0, chars)}...${address.slice(-chars)}`;
  }

  /**
   * Format SOL amount for display
   */
  formatSolAmount(sol: number, decimals: number = 6): string {
    return sol.toFixed(decimals);
  }

  /**
   * Convert SOL to lamports
   */
  solToLamports(sol: number): number {
    return Math.floor(sol * 1_000_000_000);
  }

  /**
   * Convert lamports to SOL
   */
  lamportsToSol(lamports: number): number {
    return lamports / 1_000_000_000;
  }

  /**
   * Check if amount is within transaction limits
   */
  isValidTransactionAmount(sol: number, maxAmount: number = 0.05): boolean {
    return sol > 0 && sol <= maxAmount;
  }
}

// Export singleton instance
export const walletService = new WalletService();
export type { WalletInfo, WalletBalance, WalletValidation };