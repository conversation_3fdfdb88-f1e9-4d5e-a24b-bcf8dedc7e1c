interface TokenSearchResult {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  logoURI?: string;
  verified: boolean;
}

interface TokenMetadata {
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  logoURI?: string;
  verified?: boolean;
  tags?: string[];
}

interface TokenValidationResult {
  isValid: boolean;
  address?: string;
  symbol?: string;
  name?: string;
  decimals?: number;
  verified?: boolean;
  error?: string;
}

interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
}

interface SearchResponse {
  data: TokenSearchResult[];
  meta: {
    query: string;
    count: number;
    limit: number;
  };
}

class TokenService {
  private baseUrl = '/api/tokens';

  /**
   * Search for tokens by symbol or name
   */
  async searchTokens(query: string, limit: number = 10): Promise<TokenSearchResult[]> {
    if (!query || query.length < 2) {
      return [];
    }

    try {
      const params = new URLSearchParams({
        q: query,
        limit: limit.toString()
      });

      const response = await fetch(`${this.baseUrl}/search?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<SearchResponse['data']> & { meta?: SearchResponse['meta'] } = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Search failed');
      }

      return result.data || [];
    } catch (error) {
      console.error('Failed to search tokens:', error);
      return [];
    }
  }

  /**
   * Get detailed metadata for a specific token
   */
  async getTokenMetadata(address: string): Promise<TokenMetadata | null> {
    if (!address) {
      return null;
    }

    try {
      const response = await fetch(`${this.baseUrl}/metadata/${address}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 400) {
          // Invalid address format
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<TokenMetadata> = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to get token metadata');
      }

      return result.data;
    } catch (error) {
      console.error('Failed to get token metadata:', error);
      return null;
    }
  }

  /**
   * Validate a token address and get basic info
   */
  async validateToken(address: string): Promise<TokenValidationResult> {
    if (!address) {
      return { isValid: false, error: 'Address is required' };
    }

    try {
      const response = await fetch(`${this.baseUrl}/validate/${address}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<TokenValidationResult> = await response.json();

      if (!result.success) {
        return { isValid: false, error: result.error || 'Validation failed' };
      }

      return result.data;
    } catch (error) {
      console.error('Failed to validate token:', error);
      return { isValid: false, error: 'Failed to validate token address' };
    }
  }

  /**
   * Check if a token address is a valid Solana address format
   */
  isValidSolanaAddressFormat(address: string): boolean {
    // Basic format validation
    if (!address || address.length < 32 || address.length > 44) {
      return false;
    }
    
    // Check for valid base58 characters
    const base58Regex = /^[1-9A-HJ-NP-Za-km-z]+$/;
    return base58Regex.test(address);
  }

  /**
   * Format token amount with proper decimals
   */
  formatTokenAmount(amount: string | number, decimals: number, displayDecimals: number = 6): string {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    
    if (isNaN(num)) return '0';
    if (num === 0) return '0';
    
    const actualAmount = num / Math.pow(10, decimals);
    
    if (actualAmount < Math.pow(10, -displayDecimals)) {
      return `<${Math.pow(10, -displayDecimals)}`;
    }
    
    return actualAmount.toLocaleString(undefined, {
      minimumFractionDigits: 0,
      maximumFractionDigits: displayDecimals,
    });
  }

  /**
   * Get a shorter display version of a token address
   */
  formatTokenAddress(address: string, length: number = 4): string {
    if (!address || address.length <= length * 2 + 3) {
      return address;
    }
    
    return `${address.slice(0, length)}...${address.slice(-length)}`;
  }

  /**
   * Check if a token is likely a known/verified token
   */
  isKnownToken(address: string): boolean {
    const knownTokens = [
      'So11111111111111111111111111111111111111112', // SOL
      'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
      'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB', // USDT
      'JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN', // JUP
    ];
    
    return knownTokens.includes(address);
  }

  /**
   * Debounced search function for real-time search UIs
   */
  private searchTimeouts: Map<string, NodeJS.Timeout> = new Map();

  async debouncedSearch(
    query: string, 
    callback: (results: TokenSearchResult[]) => void,
    debounceMs: number = 300
  ): Promise<void> {
    // Clear existing timeout for this query
    const existingTimeout = this.searchTimeouts.get(query);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Set new timeout
    const timeout = setTimeout(async () => {
      const results = await this.searchTokens(query);
      callback(results);
      this.searchTimeouts.delete(query);
    }, debounceMs);

    this.searchTimeouts.set(query, timeout);
  }

  /**
   * Clear all debounced search timeouts
   */
  clearSearchTimeouts(): void {
    this.searchTimeouts.forEach(timeout => clearTimeout(timeout));
    this.searchTimeouts.clear();
  }
}

// Export singleton instance
export const tokenService = new TokenService();
export type { TokenSearchResult, TokenMetadata, TokenValidationResult };