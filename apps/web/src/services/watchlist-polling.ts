import { WatchlistItemWithMetrics } from '../../../packages/shared/src/types/watchlist'

type PriceData = Record<string, {
  price: number
  priceChange24h: number
  volume24h: number
  marketCap: number
}>

export interface DataFreshnessInfo {
  lastUpdateTimestamp: number
  cacheHitRate: number
  averageAge: number
  staleEntries: number
}

export interface PollingServiceOptions {
  onDataUpdate: (data: WatchlistItemWithMetrics[]) => void
  onError: (error: Error) => void
  onStatusChange: (status: 'stopped' | 'active' | 'error') => void
  onMetricsUpdate: (metrics: {
    averageResponseTime: number
    totalRequests: number
    successfulRequests: number
    failedRequests: number
  }) => void
}

export interface RequestMetrics {
  startTime: number
  endTime: number
  success: boolean
  error?: string
}

export class WatchlistPollingService {
  private intervalId: NodeJS.Timeout | null = null
  private isRunning = false
  private isPaused = false
  private currentInterval = 60000
  private watchlistItems: WatchlistItemWithMetrics[] = []
  private tokenAddresses: string[] = []

  // Request management
  private pendingRequest: Promise<void> | null = null
  private requestCache = new Map<string, { data: any; timestamp: number }>()
  private readonly cacheTimeout = 30000 // 30 seconds cache TTL

  // Metrics tracking
  private requestHistory: RequestMetrics[] = []
  private readonly maxHistorySize = 100
  private lastUpdateTimestamp = 0
  private cacheHits = 0
  private cacheMisses = 0

  // Error handling and backoff
  private consecutiveErrors = 0
  private readonly maxConsecutiveErrors = 5
  private backoffMultiplier = 1
  private readonly maxBackoffMultiplier = 8
  private lastErrorTime = 0

  constructor(private options: PollingServiceOptions) {}

  start(items: WatchlistItemWithMetrics[], interval: number) {
    this.watchlistItems = [...items]
    this.tokenAddresses = items.map(item => item.tokenAddress)
    this.currentInterval = interval
    this.isRunning = true
    this.isPaused = false
    this.consecutiveErrors = 0
    this.backoffMultiplier = 1

    // Notify status change
    this.options.onStatusChange('active')

    // Initial fetch
    this.fetchNow(this.tokenAddresses).catch(() => {
      // Error handling is done in fetchTokenMetrics
    })

    // Set up polling interval
    this.scheduleNextPoll()
  }

  stop() {
    this.isRunning = false
    this.isPaused = false

    if (this.intervalId) {
      clearTimeout(this.intervalId)
      this.intervalId = null
    }

    // Cancel pending request if any
    this.pendingRequest = null

    // Notify status change
    this.options.onStatusChange('stopped')
  }

  pause() {
    this.isPaused = true

    if (this.intervalId) {
      clearTimeout(this.intervalId)
      this.intervalId = null
    }
  }

  resume() {
    if (!this.isRunning) return

    this.isPaused = false
    this.scheduleNextPoll()
  }

  updateInterval(newInterval: number) {
    this.currentInterval = newInterval

    // Restart polling with new interval if currently active
    if (this.isRunning && !this.isPaused) {
      if (this.intervalId) {
        clearTimeout(this.intervalId)
      }
      this.scheduleNextPoll()
    }
  }

  async fetchNow(tokenAddresses?: string[]): Promise<void> {
    // Use current token addresses if none provided
    const addresses = tokenAddresses || this.tokenAddresses
    
    // Prevent duplicate simultaneous requests
    if (this.pendingRequest) {
      return this.pendingRequest
    }

    this.pendingRequest = this.fetchTokenMetrics(addresses)
    try {
      await this.pendingRequest
    } finally {
      this.pendingRequest = null
    }
  }

  updateItems(items: WatchlistItemWithMetrics[]) {
    this.watchlistItems = [...items]
    this.tokenAddresses = items.map(item => item.tokenAddress)
    
    // If polling is active, restart with new items
    if (this.isRunning && !this.isPaused) {
      // Don't restart completely, just update the addresses for next poll
      // The current poll will complete with old addresses, next one will use new ones
    }
  }

  private mergePriceDataWithItems(priceData: PriceData): WatchlistItemWithMetrics[] {
    return this.watchlistItems.map(item => {
      const price = priceData[item.tokenAddress]
      if (price) {
        return {
          ...item,
          snapshot: {
            tokenAddress: item.tokenAddress,
            priceUsd: price.price.toString() as any,
            priceChange24h: price.priceChange24h.toString() as any,
            volume24h: price.volume24h.toString() as any,
            fdv: price.marketCap.toString() as any,
            lastUpdated: new Date(),
            source: 'cache'
          }
        }
      }
      return item
    })
  }

  private scheduleNextPoll() {
    if (!this.isRunning || this.isPaused) return

    // Apply exponential backoff if there have been consecutive errors
    const effectiveInterval = this.currentInterval * this.backoffMultiplier

    this.intervalId = setTimeout(() => {
      if (this.isRunning && !this.isPaused) {
        this.fetchTokenMetrics(this.tokenAddresses).catch(() => {
          // Error handling is done in fetchTokenMetrics
        })
        this.scheduleNextPoll()
      }
    }, effectiveInterval)
  }

  private async fetchTokenMetrics(tokenAddresses: string[]): Promise<void> {
    if (tokenAddresses.length === 0) return

    const startTime = Date.now()
    let requestMetrics: RequestMetrics = {
      startTime,
      endTime: 0,
      success: false,
      error: undefined
    }

    try {
      // Check cache first for recent data
      const cachedData = this.getCachedData(tokenAddresses)
      if (cachedData && Object.keys(cachedData).length === tokenAddresses.length) {
        // All tokens found in cache
        this.cacheHits++
        requestMetrics.endTime = Date.now()
        requestMetrics.success = true
        this.updateMetrics(requestMetrics)
        
        // Convert cached price data back to watchlist items format
        const updatedItems = this.mergePriceDataWithItems(cachedData)
        this.options.onDataUpdate(updatedItems)
        this.onRequestSuccess()
        return
      }

      // Cache miss - fetch fresh data from API
      this.cacheMisses++
      
      // Fetch both endpoints in parallel to get complete data
      const [historyResponse, regularResponse] = await Promise.all([
        fetch('/api/watchlist/metrics/history', {
          method: 'GET',
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        }),
        fetch('/api/watchlist/metrics', {
          method: 'GET',
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        })
      ])

      if (!historyResponse.ok) {
        throw new Error(`History API request failed: ${historyResponse.status} ${historyResponse.statusText}`)
      }

      const historyResult = await historyResponse.json()
      const regularResult = regularResponse.ok ? await regularResponse.json() : null

      if (!historyResult.items || !Array.isArray(historyResult.items)) {
        throw new Error('Invalid API response format')
      }

      // Merge the data - use history as base and add snapshot data from regular
      let updatedItems: WatchlistItemWithMetrics[] = historyResult.items
      
      if (regularResult && regularResult.items) {
        updatedItems = historyResult.items.map((historyItem: any) => {
          const regularItem = regularResult.items.find((r: any) => r.id === historyItem.id)
          if (regularItem && regularItem.snapshot) {
            // Add snapshot data to get 24h change, volume, market cap
            return {
              ...historyItem,
              snapshot: regularItem.snapshot
            }
          }
          return historyItem
        })
      }

      // Also create price data for caching
      const priceData: PriceData = {}
      updatedItems.forEach((item) => {
        if (item.snapshot && tokenAddresses.includes(item.tokenAddress)) {
          priceData[item.tokenAddress] = {
            price: Number(item.snapshot.priceUsd?.toString() || 0),
            priceChange24h: Number(item.snapshot.priceChange24h?.toString() || 0),
            volume24h: Number(item.snapshot.volume24h?.toString() || 0),
            marketCap: Number(item.snapshot.fdv?.toString() || 0) // Use fdv as marketCap fallback
          }
        }
      })

      // Update cache with fresh data
      this.updateCache(priceData)
      this.lastUpdateTimestamp = Date.now()

      requestMetrics.endTime = Date.now()
      requestMetrics.success = true
      this.updateMetrics(requestMetrics)

      // Notify listeners with correctly formatted data
      this.options.onDataUpdate(updatedItems)
      this.onRequestSuccess()

    } catch (error) {
      requestMetrics.endTime = Date.now()
      requestMetrics.success = false
      requestMetrics.error = error instanceof Error ? error.message : 'Unknown error'

      this.updateMetrics(requestMetrics)
      this.onRequestError(error instanceof Error ? error : new Error('Unknown error'))
    }
  }

  private getCachedData(tokenAddresses: string[]): PriceData | null {
    const now = Date.now()
    const cachedData: PriceData = {}

    for (const address of tokenAddresses) {
      const cached = this.requestCache.get(address)
      if (cached && (now - cached.timestamp) < this.cacheTimeout) {
        cachedData[address] = cached.data
      }
    }

    // Return cached data only if all tokens are found and cache is not empty
    return Object.keys(cachedData).length === tokenAddresses.length && tokenAddresses.length > 0 ? cachedData : null
  }

  private updateCache(priceData: PriceData) {
    const timestamp = Date.now()

    Object.entries(priceData).forEach(([address, data]) => {
      this.requestCache.set(address, { data, timestamp })
    })

    // Clean up old cache entries
    this.cleanupCache()
  }

  private cleanupCache() {
    const now = Date.now()
    const keysToDelete: string[] = []

    this.requestCache.forEach((cached, key) => {
      if ((now - cached.timestamp) > this.cacheTimeout) {
        keysToDelete.push(key)
      }
    })

    keysToDelete.forEach(key => this.requestCache.delete(key))
  }

  private updateMetrics(requestMetrics: RequestMetrics) {
    // Add to history
    this.requestHistory.push(requestMetrics)

    // Keep only recent history
    if (this.requestHistory.length > this.maxHistorySize) {
      this.requestHistory = this.requestHistory.slice(-this.maxHistorySize)
    }

    // Calculate metrics
    const totalRequests = this.requestHistory.length
    const successfulRequests = this.requestHistory.filter(r => r.success).length
    const failedRequests = totalRequests - successfulRequests

    const responseTimes = this.requestHistory
      .filter(r => r.success)
      .map(r => r.endTime - r.startTime)

    const averageResponseTime = responseTimes.length > 0
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
      : 0

    // Notify metrics update
    this.options.onMetricsUpdate({
      averageResponseTime,
      totalRequests,
      successfulRequests,
      failedRequests
    })
  }

  private onRequestSuccess() {
    this.consecutiveErrors = 0
    this.backoffMultiplier = 1
  }

  private onRequestError(error: Error) {
    this.consecutiveErrors++
    this.lastErrorTime = Date.now()

    // Implement exponential backoff
    if (this.consecutiveErrors >= 3) {
      this.backoffMultiplier = Math.min(
        this.maxBackoffMultiplier,
        Math.pow(2, this.consecutiveErrors - 2)
      )
    }

    // Stop polling if too many consecutive errors
    if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
      this.options.onStatusChange('error')
      this.stop()
      this.options.onError(new Error(
        `Polling stopped due to ${this.maxConsecutiveErrors} consecutive errors. Last error: ${error.message}`
      ))
      return
    }

    this.options.onError(error)
  }

  // Public getters for debugging/monitoring
  getStatus() {
    return {
      isRunning: this.isRunning,
      isPaused: this.isPaused,
      currentInterval: this.currentInterval,
      consecutiveErrors: this.consecutiveErrors,
      backoffMultiplier: this.backoffMultiplier,
      tokenCount: this.tokenAddresses.length,
      cacheSize: this.requestCache.size,
      lastErrorTime: this.lastErrorTime
    }
  }

  clearCache() {
    this.requestCache.clear()
    this.cacheHits = 0
    this.cacheMisses = 0
  }

  forceCacheBust() {
    this.clearCache()
    // Force immediate refresh if polling is active
    if (this.isRunning && !this.isPaused) {
      this.fetchNow().catch(() => {
        // Error handling is done in fetchTokenMetrics
      })
    }
  }

  getDataFreshness(): DataFreshnessInfo {
    const now = Date.now()
    const totalRequests = this.cacheHits + this.cacheMisses
    const cacheHitRate = totalRequests > 0 ? this.cacheHits / totalRequests : 0

    // Calculate average age of cached entries
    let totalAge = 0
    let staleEntries = 0
    
    this.requestCache.forEach(cached => {
      const age = now - cached.timestamp
      totalAge += age
      if (age > this.cacheTimeout) {
        staleEntries++
      }
    })

    const averageAge = this.requestCache.size > 0 ? totalAge / this.requestCache.size : 0

    return {
      lastUpdateTimestamp: this.lastUpdateTimestamp,
      cacheHitRate,
      averageAge,
      staleEntries
    }
  }
}
