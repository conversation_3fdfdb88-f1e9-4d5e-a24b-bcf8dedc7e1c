type PriceData = Record<string, {
  price: number
  priceChange24h: number
  volume24h: number
  marketCap: number
}>

export interface PollingServiceOptions {
  onDataUpdate: (data: PriceData) => void
  onError: (error: Error) => void
  onMetricsUpdate: (metrics: {
    averageResponseTime: number
    totalRequests: number
    successfulRequests: number
    failedRequests: number
  }) => void
}

export interface RequestMetrics {
  startTime: number
  endTime: number
  success: boolean
  error?: string
}

export class WatchlistPollingService {
  private intervalId: NodeJS.Timeout | null = null
  private isRunning = false
  private isPaused = false
  private currentInterval = 60000
  private tokenAddresses: string[] = []

  // Request management
  private pendingRequest: Promise<void> | null = null
  private requestCache = new Map<string, { data: any; timestamp: number }>()
  private readonly cacheTimeout = 30000 // 30 seconds cache TTL

  // Metrics tracking
  private requestHistory: RequestMetrics[] = []
  private readonly maxHistorySize = 100

  // Error handling and backoff
  private consecutiveErrors = 0
  private readonly maxConsecutiveErrors = 5
  private backoffMultiplier = 1
  private readonly maxBackoffMultiplier = 8
  private lastErrorTime = 0

  constructor(private options: PollingServiceOptions) {}

  start(tokenAddresses: string[], interval: number) {
    this.tokenAddresses = [...tokenAddresses]
    this.currentInterval = interval
    this.isRunning = true
    this.isPaused = false
    this.consecutiveErrors = 0
    this.backoffMultiplier = 1

    // Initial fetch
    this.fetchNow(tokenAddresses).catch(() => {
      // Error handling is done in fetchTokenMetrics
    })

    // Set up polling interval
    this.scheduleNextPoll()
  }

  stop() {
    this.isRunning = false
    this.isPaused = false

    if (this.intervalId) {
      clearTimeout(this.intervalId)
      this.intervalId = null
    }

    // Cancel pending request if any
    this.pendingRequest = null
  }

  pause() {
    this.isPaused = true

    if (this.intervalId) {
      clearTimeout(this.intervalId)
      this.intervalId = null
    }
  }

  resume() {
    if (!this.isRunning) return

    this.isPaused = false
    this.scheduleNextPoll()
  }

  updateInterval(newInterval: number) {
    this.currentInterval = newInterval

    // Restart polling with new interval if currently active
    if (this.isRunning && !this.isPaused) {
      if (this.intervalId) {
        clearTimeout(this.intervalId)
      }
      this.scheduleNextPoll()
    }
  }

  async fetchNow(tokenAddresses: string[]): Promise<void> {
    // Prevent duplicate simultaneous requests
    if (this.pendingRequest) {
      return this.pendingRequest
    }

    this.pendingRequest = this.fetchTokenMetrics(tokenAddresses)
    try {
      await this.pendingRequest
    } finally {
      this.pendingRequest = null
    }
  }

  private scheduleNextPoll() {
    if (!this.isRunning || this.isPaused) return

    // Apply exponential backoff if there have been consecutive errors
    const effectiveInterval = this.currentInterval * this.backoffMultiplier

    this.intervalId = setTimeout(() => {
      if (this.isRunning && !this.isPaused) {
        this.fetchTokenMetrics(this.tokenAddresses).catch(() => {
          // Error handling is done in fetchTokenMetrics
        })
        this.scheduleNextPoll()
      }
    }, effectiveInterval)
  }

  private async fetchTokenMetrics(tokenAddresses: string[]): Promise<void> {
    if (tokenAddresses.length === 0) return

    const startTime = Date.now()
    let requestMetrics: RequestMetrics = {
      startTime,
      endTime: 0,
      success: false,
      error: undefined
    }

    try {
      // Check cache first for recent data
      const cachedData = this.getCachedData(tokenAddresses)
      if (cachedData && Object.keys(cachedData).length === tokenAddresses.length) {
        // All tokens found in cache
        requestMetrics.endTime = Date.now()
        requestMetrics.success = true
        this.updateMetrics(requestMetrics)
        this.options.onDataUpdate(cachedData)
        this.onRequestSuccess()
        return
      }

      // Fetch fresh data from API
      const response = await fetch('/api/watchlist/metrics', {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()

      if (!result.data || !Array.isArray(result.data)) {
        throw new Error('Invalid API response format')
      }

      // Transform API response to expected format
      const priceData: PriceData = {}

      if (Array.isArray(result.data)) {
        result.data.forEach((item: any) => {
          if (item.metrics && tokenAddresses.includes(item.tokenAddress)) {
            const metrics = item.metrics
            priceData[item.tokenAddress] = {
              price: Number(metrics.priceUsd?.toString() || 0),
              priceChange24h: Number(metrics.priceChange24h?.toString() || 0),
              volume24h: Number(metrics.volume24h?.toString() || 0),
              marketCap: Number(metrics.marketCap?.toString() || 0)
            }
          }
        })
      } else {
        console.warn('API response data is not an array:', result.data)
      }

      // Update cache with fresh data
      this.updateCache(priceData)

      requestMetrics.endTime = Date.now()
      requestMetrics.success = true
      this.updateMetrics(requestMetrics)

      // Notify listeners
      this.options.onDataUpdate(priceData)
      this.onRequestSuccess()

    } catch (error) {
      requestMetrics.endTime = Date.now()
      requestMetrics.success = false
      requestMetrics.error = error instanceof Error ? error.message : 'Unknown error'

      this.updateMetrics(requestMetrics)
      this.onRequestError(error instanceof Error ? error : new Error('Unknown error'))
    }
  }

  private getCachedData(tokenAddresses: string[]): PriceData | null {
    const now = Date.now()
    const cachedData: PriceData = {}

    for (const address of tokenAddresses) {
      const cached = this.requestCache.get(address)
      if (cached && (now - cached.timestamp) < this.cacheTimeout) {
        cachedData[address] = cached.data
      }
    }

    // Return cached data only if all tokens are found and cache is not empty
    return Object.keys(cachedData).length === tokenAddresses.length && tokenAddresses.length > 0 ? cachedData : null
  }

  private updateCache(priceData: PriceData) {
    const timestamp = Date.now()

    Object.entries(priceData).forEach(([address, data]) => {
      this.requestCache.set(address, { data, timestamp })
    })

    // Clean up old cache entries
    this.cleanupCache()
  }

  private cleanupCache() {
    const now = Date.now()
    const keysToDelete: string[] = []

    this.requestCache.forEach((cached, key) => {
      if ((now - cached.timestamp) > this.cacheTimeout) {
        keysToDelete.push(key)
      }
    })

    keysToDelete.forEach(key => this.requestCache.delete(key))
  }

  private updateMetrics(requestMetrics: RequestMetrics) {
    // Add to history
    this.requestHistory.push(requestMetrics)

    // Keep only recent history
    if (this.requestHistory.length > this.maxHistorySize) {
      this.requestHistory = this.requestHistory.slice(-this.maxHistorySize)
    }

    // Calculate metrics
    const totalRequests = this.requestHistory.length
    const successfulRequests = this.requestHistory.filter(r => r.success).length
    const failedRequests = totalRequests - successfulRequests

    const responseTimes = this.requestHistory
      .filter(r => r.success)
      .map(r => r.endTime - r.startTime)

    const averageResponseTime = responseTimes.length > 0
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
      : 0

    // Notify metrics update
    this.options.onMetricsUpdate({
      averageResponseTime,
      totalRequests,
      successfulRequests,
      failedRequests
    })
  }

  private onRequestSuccess() {
    this.consecutiveErrors = 0
    this.backoffMultiplier = 1
  }

  private onRequestError(error: Error) {
    this.consecutiveErrors++
    this.lastErrorTime = Date.now()

    // Implement exponential backoff
    if (this.consecutiveErrors >= 3) {
      this.backoffMultiplier = Math.min(
        this.maxBackoffMultiplier,
        Math.pow(2, this.consecutiveErrors - 2)
      )
    }

    // Stop polling if too many consecutive errors
    if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
      this.stop()
      this.options.onError(new Error(
        `Polling stopped due to ${this.maxConsecutiveErrors} consecutive errors. Last error: ${error.message}`
      ))
      return
    }

    this.options.onError(error)
  }

  // Public getters for debugging/monitoring
  getStatus() {
    return {
      isRunning: this.isRunning,
      isPaused: this.isPaused,
      currentInterval: this.currentInterval,
      consecutiveErrors: this.consecutiveErrors,
      backoffMultiplier: this.backoffMultiplier,
      tokenCount: this.tokenAddresses.length,
      cacheSize: this.requestCache.size,
      lastErrorTime: this.lastErrorTime
    }
  }

  clearCache() {
    this.requestCache.clear()
  }
}
