'use client';

import { useState, useEffect, useCallback } from 'react';
import { priceService } from '@/services/price';

export function usePrice() {
  const [solPrice, setSolPrice] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Fetch SOL price
  const fetchSolPrice = useCallback(async () => {
    try {
      setIsLoading(true);
      const price = await priceService.getSolPrice();
      setSolPrice(price);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to fetch SOL price:', error);
      // Keep existing price if fetch fails
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchSolPrice();
  }, [fetchSolPrice]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(fetchSolPrice, 30000);
    return () => clearInterval(interval);
  }, [fetchSolPrice]);

  // Convert SOL amount to USD
  const solToUsd = useCallback((solAmount: number): number => {
    if (!solPrice) return 0;
    return solAmount * solPrice;
  }, [solPrice]);

  // Format USD amount for display
  const formatUsd = useCallback((amount: number, decimals: number = 2): string => {
    return priceService.formatUsd(amount, decimals);
  }, []);

  // Convert and format SOL to USD in one step
  const formatSolAsUsd = useCallback((solAmount: number, decimals: number = 2): string => {
    const usdAmount = solToUsd(solAmount);
    return formatUsd(usdAmount, decimals);
  }, [solToUsd, formatUsd]);

  return {
    solPrice,
    isLoading,
    lastUpdated,
    solToUsd,
    formatUsd,
    formatSolAsUsd,
    refresh: fetchSolPrice,
  };
}