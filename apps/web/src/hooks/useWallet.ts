'use client';

import { useCallback, useEffect } from 'react';
import { useSystemStore } from '@/stores/system-store';
import { walletService } from '@/services/wallet';

export function useWallet() {
  const wallet = useSystemStore((state) => state.wallet);
  const connectionStatus = useSystemStore((state) => state.connectionStatus.wallet);
  const updateWallet = useSystemStore((state) => state.updateWallet);
  const setWalletError = useSystemStore((state) => state.setWalletError);
  const refreshWalletBalance = useSystemStore((state) => state.refreshWalletBalance);
  const connectWallet = useSystemStore((state) => state.connectWallet);
  const resetWallet = useSystemStore((state) => state.resetWallet);

  // Derived state
  const isLoading = connectionStatus === 'connecting';
  const hasError = wallet.error !== null;
  const isConnected = wallet.isConnected && connectionStatus === 'connected';

  // Initialize wallet on first load
  useEffect(() => {
    let mounted = true;
    
    const initializeWallet = async () => {
      if (!mounted) return;
      
      try {
        await connectWallet();
      } catch (error) {
        // Error is already handled in connectWallet
      }
    };

    // Only try to connect if wallet is not connected and not currently connecting
    if (!wallet.isConnected && connectionStatus !== 'connecting') {
      initializeWallet();
    }

    return () => {
      mounted = false;
    };
  }, [wallet.isConnected, connectionStatus]); // Simplified dependencies

  // Reconnect wallet
  const reconnect = useCallback(async () => {
    try {
      await connectWallet();
    } catch (error) {
      // Error is already handled in connectWallet
    }
  }, [connectWallet]);
  
  // Hard reset wallet and reconnect
  const hardReset = useCallback(async () => {
    // Clear all localStorage that might contain cached wallet data
    if (typeof window !== 'undefined') {
      Object.keys(localStorage).forEach(key => {
        if (key.includes('wallet') || key.includes('system-store')) {
          localStorage.removeItem(key);
        }
      });
    }
    
    resetWallet();
    await new Promise(resolve => setTimeout(resolve, 200)); // Small delay
    try {
      await connectWallet();
    } catch (error) {
      // Error is already handled in connectWallet
    }
  }, [resetWallet, connectWallet]);

  // Refresh balance only
  const refreshBalance = useCallback(async () => {
    try {
      await refreshWalletBalance();
    } catch (error) {
      // Error is already handled in refreshWalletBalance
    }
  }, [refreshWalletBalance]);

  // Format address for display
  const formatAddress = useCallback((chars: number = 4) => {
    if (!wallet.address) return '';
    return walletService.formatAddress(wallet.address, chars);
  }, [wallet.address]);

  // Format balance for display
  const formatBalance = useCallback((decimals: number = 6) => {
    if (wallet.balance === null) return '';
    return walletService.formatSolAmount(wallet.balance, decimals);
  }, [wallet.balance]);

  // Clear wallet error
  const clearError = useCallback(() => {
    setWalletError(null);
  }, [setWalletError]);

  // Check if transaction amount is valid
  const isValidTransactionAmount = useCallback((sol: number) => {
    return walletService.isValidTransactionAmount(sol);
  }, []);

  // Get wallet status summary
  const getStatusSummary = useCallback(() => {
    if (isLoading) return 'Connecting...';
    if (hasError) return wallet.error || 'Error';
    if (!isConnected) return 'Disconnected';
    if (wallet.balance === null) return 'Connected (Balance Unknown)';
    if (wallet.balance < 0.01) return 'Connected (Low Balance)';
    return 'Connected';
  }, [isLoading, hasError, isConnected, wallet.error, wallet.balance]);

  // Get status color for UI
  const getStatusColor = useCallback(() => {
    if (isLoading) return 'text-yellow-600';
    if (hasError) return 'text-red-600';
    if (!isConnected) return 'text-gray-600';
    if (wallet.balance !== null && wallet.balance < 0.01) return 'text-orange-600';
    return 'text-green-600';
  }, [isLoading, hasError, isConnected, wallet.balance]);

  // Auto-refresh balance periodically
  const startAutoRefresh = useCallback((intervalMs: number = 30000) => {
    const interval = setInterval(async () => {
      if (isConnected && !isLoading) {
        try {
          await refreshWalletBalance();
        } catch (error) {
          // Silently handle auto-refresh errors
        }
      }
    }, intervalMs);

    return () => clearInterval(interval);
  }, [isConnected, isLoading, refreshWalletBalance]);

  return {
    // Wallet state
    wallet,
    isLoading,
    hasError,
    isConnected,
    connectionStatus,

    // Actions
    connect: reconnect,
    reconnect,
    hardReset,
    refreshBalance,
    clearError,

    // Utilities
    formatAddress,
    formatBalance,
    isValidTransactionAmount,
    getStatusSummary,
    getStatusColor,
    startAutoRefresh,

    // Raw wallet service access for advanced usage
    walletService,
  };
}