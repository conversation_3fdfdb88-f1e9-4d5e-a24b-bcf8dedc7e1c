import { useTradingStore } from '@/stores/trading-store'
import { useWatchlistStore } from '@/stores/watchlist-store'
import { useSystemStore } from '@/stores/system-store'

// Re-export store hooks for easy importing
export { useTradingStore, useWatchlistStore, useSystemStore }

// Custom hooks for common store operations
export const usePositions = () => {
  const positions = useTradingStore((state) => state.positions)
  const addPosition = useTradingStore((state) => state.addPosition)
  const updatePosition = useTradingStore((state) => state.updatePosition)
  const removePosition = useTradingStore((state) => state.removePosition)
  const selectedPosition = useTradingStore((state) => state.selectedPosition)
  const selectPosition = useTradingStore((state) => state.selectPosition)
  
  return {
    positions,
    selectedPosition,
    addPosition,
    updatePosition,
    removePosition,
    selectPosition
  }
}

export const useWatchlist = () => {
  const tokens = useWatchlistStore((state) => state.getFilteredTokens())
  const addToken = useWatchlistStore((state) => state.addToken)
  const removeToken = useWatchlistStore((state) => state.removeToken)
  const updateToken = useWatchlistStore((state) => state.updateToken)
  const selectedToken = useWatchlistStore((state) => state.selectedToken)
  const selectToken = useWatchlistStore((state) => state.selectToken)
  const toggleTracking = useWatchlistStore((state) => state.toggleTracking)
  
  return {
    tokens,
    selectedToken,
    addToken,
    removeToken,
    updateToken,
    selectToken,
    toggleTracking
  }
}

export const useNotifications = () => {
  const notifications = useSystemStore((state) => state.notifications)
  const unreadCount = useSystemStore((state) => state.unreadCount)
  const addNotification = useSystemStore((state) => state.addNotification)
  const removeNotification = useSystemStore((state) => state.removeNotification)
  const markAsRead = useSystemStore((state) => state.markNotificationAsRead)
  const markAllAsRead = useSystemStore((state) => state.markAllNotificationsAsRead)
  const clearAll = useSystemStore((state) => state.clearNotifications)
  
  const getUnreadNotifications = useSystemStore((state) => state.getUnreadNotifications)
  const getNotificationsByType = useSystemStore((state) => state.getNotificationsByType)
  
  return {
    notifications,
    unreadCount,
    addNotification,
    removeNotification,
    markAsRead,
    markAllAsRead,
    clearAll,
    getUnreadNotifications,
    getNotificationsByType
  }
}

export const useConnectionStatus = () => {
  const connectionStatus = useSystemStore((state) => state.connectionStatus)
  const setConnectionStatus = useSystemStore((state) => state.setConnectionStatus)
  const lastHeartbeat = useSystemStore((state) => state.lastHeartbeat)
  const updateHeartbeat = useSystemStore((state) => state.updateHeartbeat)
  
  return {
    connectionStatus,
    setConnectionStatus,
    lastHeartbeat,
    updateHeartbeat,
    isConnected: connectionStatus.api === 'connected' && connectionStatus.wallet === 'connected'
  }
}

export const useAppSettings = () => {
  const settings = useSystemStore((state) => state.settings)
  const updateSettings = useSystemStore((state) => state.updateSettings)
  const resetSettings = useSystemStore((state) => state.resetSettings)
  
  return {
    settings,
    updateSettings,
    resetSettings
  }
}