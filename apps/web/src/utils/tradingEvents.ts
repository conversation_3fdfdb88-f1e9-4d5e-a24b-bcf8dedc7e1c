import { useEffect } from 'react';

/**
 * Event system for cross-component communication between trading and watchlist
 */

export type TradingEventType = 
  | 'trade_initiated'
  | 'trade_completed'
  | 'trade_failed'
  | 'quote_received'
  | 'position_opened'
  | 'position_closed'
  | 'watchlist_token_selected'
  | 'navigation_from_watchlist'
  | 'performance_updated';

export interface TradingEvent {
  type: TradingEventType;
  timestamp: Date;
  sessionId: string;
  data: any;
}

export interface TradingEventHandlers {
  [key: string]: (event: TradingEvent) => void;
}

class TradingEventEmitter {
  private handlers: TradingEventHandlers = {};
  private sessionId: string;

  constructor() {
    this.sessionId = this.generateSessionId();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Subscribe to trading events
   */
  on(eventType: TradingEventType, handler: (event: TradingEvent) => void): () => void {
    const key = `${eventType}_${Math.random().toString(36).substr(2, 9)}`;
    this.handlers[key] = handler;

    // Return unsubscribe function
    return () => {
      delete this.handlers[key];
    };
  }

  /**
   * Emit trading event
   */
  emit(eventType: TradingEventType, data: any): void {
    const event: TradingEvent = {
      type: eventType,
      timestamp: new Date(),
      sessionId: this.sessionId,
      data
    };

    // Call all handlers for this event type
    Object.keys(this.handlers).forEach(key => {
      if (key.startsWith(eventType)) {
        try {
          this.handlers[key](event);
        } catch (error) {
          console.error(`Error in trading event handler for ${eventType}:`, error);
        }
      }
    });

    // Log for debugging in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[TradingEvent] ${eventType}:`, data);
    }
  }

  /**
   * Remove all event handlers
   */
  removeAllListeners(): void {
    this.handlers = {};
  }

  /**
   * Get current session ID
   */
  getSessionId(): string {
    return this.sessionId;
  }

  /**
   * Start new session (useful for tracking user journeys)
   */
  startNewSession(): string {
    this.sessionId = this.generateSessionId();
    return this.sessionId;
  }
}

// Singleton instance
export const tradingEventEmitter = new TradingEventEmitter();

/**
 * Hook for React components to listen to trading events
 */
export function useTradingEvents(
  eventType: TradingEventType,
  handler: (event: TradingEvent) => void,
  dependencies: any[] = []
): void {
  useEffect(() => {
    if (typeof window === 'undefined') return; // SSR safety
    
    const unsubscribe = tradingEventEmitter.on(eventType, handler);
    return unsubscribe;
  }, dependencies);
}

/**
 * Convenience functions for common events
 */
export const TradingEvents = {
  // Trading lifecycle events
  tradeInitiated: (tokenAddress: string, amount: number, source?: string) => {
    tradingEventEmitter.emit('trade_initiated', {
      tokenAddress,
      amount,
      source,
      timestamp: new Date()
    });
  },

  tradeCompleted: (tokenAddress: string, success: boolean, signature?: string) => {
    tradingEventEmitter.emit('trade_completed', {
      tokenAddress,
      success,
      signature,
      timestamp: new Date()
    });
  },

  tradeFailed: (tokenAddress: string, error: string) => {
    tradingEventEmitter.emit('trade_failed', {
      tokenAddress,
      error,
      timestamp: new Date()
    });
  },

  // Navigation events
  navigationFromWatchlist: (tokenAddress: string, context: any) => {
    tradingEventEmitter.emit('navigation_from_watchlist', {
      tokenAddress,
      context,
      timestamp: new Date()
    });
  },

  // Watchlist events
  tokenSelected: (tokenAddress: string, source: string) => {
    tradingEventEmitter.emit('watchlist_token_selected', {
      tokenAddress,
      source,
      timestamp: new Date()
    });
  },

  // Performance tracking
  performanceUpdated: (tokenAddress: string, metrics: any) => {
    tradingEventEmitter.emit('performance_updated', {
      tokenAddress,
      metrics,
      timestamp: new Date()
    });
  }
};

/**
 * Analytics tracker for user journey
 */
export class UserJourneyTracker {
  private journeyStart: Date;
  private steps: Array<{ step: string; timestamp: Date; duration?: number }> = [];
  private lastStepTime: Date;

  constructor() {
    this.journeyStart = new Date();
    this.lastStepTime = this.journeyStart;
    this.trackStep('journey_start');
  }

  trackStep(stepName: string, metadata?: any): void {
    const now = new Date();
    const duration = now.getTime() - this.lastStepTime.getTime();

    this.steps.push({
      step: stepName,
      timestamp: now,
      duration
    });

    this.lastStepTime = now;

    // Emit event for real-time tracking
    tradingEventEmitter.emit('trade_initiated', {
      journeyStep: stepName,
      stepDuration: duration,
      totalJourneyTime: now.getTime() - this.journeyStart.getTime(),
      metadata
    });
  }

  getJourneyMetrics() {
    const totalTime = this.lastStepTime.getTime() - this.journeyStart.getTime();
    return {
      sessionId: tradingEventEmitter.getSessionId(),
      startTime: this.journeyStart,
      endTime: this.lastStepTime,
      totalDuration: totalTime,
      steps: this.steps,
      stepCount: this.steps.length
    };
  }

  reset(): void {
    this.journeyStart = new Date();
    this.lastStepTime = this.journeyStart;
    this.steps = [];
    tradingEventEmitter.startNewSession();
  }
}

// Global journey tracker instance
export const userJourneyTracker = new UserJourneyTracker();