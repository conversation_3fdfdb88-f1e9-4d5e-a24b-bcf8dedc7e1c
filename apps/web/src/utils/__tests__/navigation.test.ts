/**
 * @jest-environment jsdom
 */

import {
  saveNavigationContext,
  restoreNavigationContext,
  clearNavigationContext,
  buildNavigationUrl,
  navigateWithContext
} from '../navigation';

describe('navigation utilities', () => {
  // Mock sessionStorage
  const mockSessionStorage = {
    store: {} as Record<string, string>,
    getItem: jest.fn((key: string) => mockSessionStorage.store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      mockSessionStorage.store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete mockSessionStorage.store[key];
    }),
    clear: jest.fn(() => {
      mockSessionStorage.store = {};
    })
  };

  beforeAll(() => {
    Object.defineProperty(window, 'sessionStorage', {
      value: mockSessionStorage,
      writable: true
    });

    Object.defineProperty(window, 'location', {
      value: {
        origin: 'https://localhost:3000',
        pathname: '/watchlist',
        search: '?filter=pinned'
      },
      writable: true
    });
  });

  beforeEach(() => {
    mockSessionStorage.clear();
    jest.clearAllMocks();
  });

  describe('saveNavigationContext', () => {
    it('should save navigation context to sessionStorage', () => {
      const context = {
        scrollPosition: 100,
        filters: { pinned: true },
        sortBy: 'name',
        sortOrder: 'asc' as const
      };

      saveNavigationContext('/watchlist', context);

      expect(mockSessionStorage.setItem).toHaveBeenCalledWith(
        'bmad-navigation-context',
        expect.stringContaining('/watchlist')
      );

      const savedData = JSON.parse(mockSessionStorage.store['bmad-navigation-context']);
      expect(savedData.path).toBe('/watchlist');
      expect(savedData.context.scrollPosition).toBe(100);
      expect(savedData.context.filters).toEqual({ pinned: true });
      expect(savedData.context.timestamp).toBeDefined();
    });

    it('should handle sessionStorage errors gracefully', () => {
      mockSessionStorage.setItem.mockImplementationOnce(() => {
        throw new Error('Storage full');
      });

      // Should not throw
      expect(() => {
        saveNavigationContext('/test', { test: true });
      }).not.toThrow();
    });
  });

  describe('restoreNavigationContext', () => {
    it('should restore valid navigation context', () => {
      const context = {
        scrollPosition: 200,
        filters: { search: 'test' },
        timestamp: Date.now()
      };

      mockSessionStorage.store['bmad-navigation-context'] = JSON.stringify({
        path: '/watchlist',
        context
      });

      const restored = restoreNavigationContext('/watchlist');

      expect(restored).toBeTruthy();
      expect(restored?.scrollPosition).toBe(200);
      expect(restored?.filters).toEqual({ search: 'test' });
    });

    it('should return null for wrong path', () => {
      mockSessionStorage.store['bmad-navigation-context'] = JSON.stringify({
        path: '/trading',
        context: { test: true, timestamp: Date.now() }
      });

      const restored = restoreNavigationContext('/watchlist');
      expect(restored).toBeNull();
    });

    it('should return null for expired context', () => {
      const oldTimestamp = Date.now() - (35 * 60 * 1000); // 35 minutes ago
      mockSessionStorage.store['bmad-navigation-context'] = JSON.stringify({
        path: '/watchlist',
        context: { test: true, timestamp: oldTimestamp }
      });

      const restored = restoreNavigationContext('/watchlist');
      expect(restored).toBeNull();
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('bmad-navigation-context');
    });

    it('should handle missing or invalid storage data', () => {
      // No data
      expect(restoreNavigationContext('/test')).toBeNull();

      // Invalid JSON
      mockSessionStorage.store['bmad-navigation-context'] = 'invalid json';
      expect(restoreNavigationContext('/test')).toBeNull();
    });

    it('should handle sessionStorage errors gracefully', () => {
      mockSessionStorage.getItem.mockImplementationOnce(() => {
        throw new Error('Storage error');
      });

      expect(restoreNavigationContext('/test')).toBeNull();
    });
  });

  describe('clearNavigationContext', () => {
    it('should clear navigation context from sessionStorage', () => {
      mockSessionStorage.store['bmad-navigation-context'] = 'test data';

      clearNavigationContext();

      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('bmad-navigation-context');
    });

    it('should handle sessionStorage errors gracefully', () => {
      mockSessionStorage.removeItem.mockImplementationOnce(() => {
        throw new Error('Storage error');
      });

      expect(() => clearNavigationContext()).not.toThrow();
    });
  });

  describe('buildNavigationUrl', () => {
    it('should build URL with new parameters', () => {
      const url = buildNavigationUrl('/trading', {
        mint: 'So11111111111111111111111111111111111111112',
        source: 'watchlist'
      });

      expect(url).toBe('/trading?mint=So11111111111111111111111111111111111111112&source=watchlist');
    });

    it('should preserve existing parameters when requested', () => {
      const url = buildNavigationUrl('/trading', {
        mint: 'So11111111111111111111111111111111111111112'
      }, true);

      expect(url).toContain('filter=pinned'); // From mocked location.search
      expect(url).toContain('mint=So11111111111111111111111111111111111111112');
    });

    it('should override existing parameters with new ones', () => {
      const url = buildNavigationUrl('/trading', {
        filter: 'all', // Override existing 'pinned'
        mint: 'So11111111111111111111111111111111111111112'
      }, true);

      expect(url).toContain('filter=all');
      expect(url).not.toContain('filter=pinned');
      expect(url).toContain('mint=So11111111111111111111111111111111111111112');
    });

    it('should handle boolean and number parameters', () => {
      const url = buildNavigationUrl('/test', {
        enabled: true,
        count: 42,
        text: 'hello'
      });

      expect(url).toContain('enabled=true');
      expect(url).toContain('count=42');
      expect(url).toContain('text=hello');
    });

    it('should skip undefined and null parameters', () => {
      const url = buildNavigationUrl('/test', {
        defined: 'value',
        undefined: undefined,
        null: null
      });

      expect(url).toContain('defined=value');
      expect(url).not.toContain('undefined=');
      expect(url).not.toContain('null=');
    });
  });

  describe('navigateWithContext', () => {
    it('should save context and return target path', () => {
      const context = { scrollPosition: 100 };
      const result = navigateWithContext('/trading', context, '/watchlist');

      expect(result).toBe('/trading');
      expect(mockSessionStorage.setItem).toHaveBeenCalled();
    });

    it('should return target path without saving when no current path', () => {
      const result = navigateWithContext('/trading', { test: true });

      expect(result).toBe('/trading');
      expect(mockSessionStorage.setItem).not.toHaveBeenCalled();
    });
  });
});