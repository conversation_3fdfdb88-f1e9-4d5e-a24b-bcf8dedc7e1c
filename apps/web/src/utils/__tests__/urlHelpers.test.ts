import { parseSwapParams, isValidSolanaAddress, buildSwapUrl } from '../urlHelpers';

describe('urlHelpers', () => {
  describe('parseSwapParams', () => {
    it('should parse valid URL parameters correctly', () => {
      const searchParams = new URLSearchParams('mint=So11111111111111111111111111111111111111112&amount=1.5&slippage=100&source=watchlist');
      const result = parseSwapParams(searchParams);
      
      expect(result.mint).toBe('So11111111111111111111111111111111111111112');
      expect(result.amount).toBe(1.5);
      expect(result.slippage).toBe(100);
      expect(result.source).toBe('watchlist');
      expect(result.errors).toHaveLength(0);
    });

    it('should handle missing parameters gracefully', () => {
      const searchParams = new URLSearchParams();
      const result = parseSwapParams(searchParams);
      
      expect(result.mint).toBeNull();
      expect(result.amount).toBeNull();
      expect(result.slippage).toBeNull();
      expect(result.source).toBeNull();
      expect(result.errors).toHaveLength(0);
    });

    it('should validate token address format', () => {
      const searchParams = new URLSearchParams('mint=invalid_address');
      const result = parseSwapParams(searchParams);
      
      expect(result.mint).toBeNull();
      expect(result.errors).toContain('Invalid token address format');
    });

    it('should validate amount parameter', () => {
      const searchParams = new URLSearchParams('amount=-1');
      const result = parseSwapParams(searchParams);
      
      expect(result.amount).toBeNull();
      expect(result.errors).toContain('Invalid amount - must be a positive number');
    });

    it('should validate slippage parameter', () => {
      const searchParams = new URLSearchParams('slippage=99999');
      const result = parseSwapParams(searchParams);
      
      expect(result.slippage).toBeNull();
      expect(result.errors).toContain('Invalid slippage - must be between 0 and 10000 basis points');
    });

    it('should validate source parameter', () => {
      const searchParams = new URLSearchParams('source=invalid_source');
      const result = parseSwapParams(searchParams);
      
      expect(result.source).toBeNull();
      expect(result.errors).toContain('Invalid source - must be one of: watchlist, external, direct');
    });

    it('should handle multiple errors', () => {
      const searchParams = new URLSearchParams('mint=invalid&amount=-1&slippage=99999&source=invalid');
      const result = parseSwapParams(searchParams);
      
      expect(result.errors).toHaveLength(4);
    });
  });

  describe('isValidSolanaAddress', () => {
    it('should validate correct Solana addresses', () => {
      const validAddresses = [
        'So11111111111111111111111111111111111111112', // SOL
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
        'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB', // USDT
      ];

      validAddresses.forEach(address => {
        expect(isValidSolanaAddress(address)).toBe(true);
      });
    });

    it('should reject invalid addresses', () => {
      const invalidAddresses = [
        '',
        'invalid',
        '123',
        'too_short',
        'this_is_way_too_long_for_a_solana_address_and_should_fail',
        'InvalidChars@#$%',
      ];

      invalidAddresses.forEach(address => {
        expect(isValidSolanaAddress(address)).toBe(false);
      });
    });

    it('should handle null and undefined inputs', () => {
      expect(isValidSolanaAddress(null as any)).toBe(false);
      expect(isValidSolanaAddress(undefined as any)).toBe(false);
    });
  });

  describe('buildSwapUrl', () => {
    // Mock window.location.origin for tests
    beforeAll(() => {
      Object.defineProperty(window, 'location', {
        value: {
          origin: 'https://localhost:3000'
        },
        writable: true
      });
    });
    
    // Reset mocks after tests
    afterAll(() => {
      // Restore original window.location if needed
      delete (window as any).location;
    });

    it('should build URL with all parameters', () => {
      const params = {
        mint: 'So11111111111111111111111111111111111111112',
        amount: '1.5',
        slippage: '100',
        source: 'watchlist'
      };

      const url = buildSwapUrl(params);
      
      expect(url).toContain('/trading');
      expect(url).toContain('mint=So11111111111111111111111111111111111111112');
      expect(url).toContain('amount=1.5');
      expect(url).toContain('slippage=100');
      expect(url).toContain('source=watchlist');
    });

    it('should build URL with minimal parameters', () => {
      const params = {
        mint: 'So11111111111111111111111111111111111111112'
      };

      const url = buildSwapUrl(params);
      
      expect(url).toContain('/trading');
      expect(url).toContain('mint=So11111111111111111111111111111111111111112');
      expect(url).not.toContain('amount=');
      expect(url).not.toContain('slippage=');
      expect(url).not.toContain('source=');
    });

    it('should handle empty parameters', () => {
      const url = buildSwapUrl({});
      
      expect(url).toBe('https://localhost:3000/trading');
    });
  });
});