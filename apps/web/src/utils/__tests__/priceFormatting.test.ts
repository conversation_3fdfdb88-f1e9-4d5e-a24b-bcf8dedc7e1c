import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  formatPrice, 
  formatPercentage, 
  formatVolume, 
  formatMarketCap, 
  formatPriceChangeWithTrend,
  truncateAddress,
  formatRelativeTime
} from '../priceFormatting';

describe('priceFormatting', () => {
  describe('formatPrice', () => {
    it('formats micro-caps with 6 decimal places', () => {
      expect(formatPrice(0.000001)).toBe('$0.000001');
      expect(formatPrice(0.009999)).toBe('$0.009999');
    });

    it('formats small caps with 4 decimal places', () => {
      expect(formatPrice(0.01)).toBe('$0.0100');
      expect(formatPrice(1.2345)).toBe('$1.2345');
      expect(formatPrice(99.9999)).toBe('$99.9999');
    });

    it('formats large caps with 3 decimal places', () => {
      expect(formatPrice(100.1)).toBe('$100.100');
      expect(formatPrice(1000.56789)).toBe('$1000.568');
    });

    it('handles edge cases', () => {
      expect(formatPrice(0)).toBe('$0.000000');
      expect(formatPrice(NaN)).toBe('$0.000000');
    });
  });

  describe('formatPercentage', () => {
    it('formats positive percentages with + sign', () => {
      expect(formatPercentage(5.5)).toBe('+5.50%');
      expect(formatPercentage(0.01)).toBe('+0.01%');
    });

    it('formats negative percentages', () => {
      expect(formatPercentage(-3.25)).toBe('-3.25%');
    });

    it('formats zero percentage', () => {
      expect(formatPercentage(0)).toBe('+0.00%');
    });

    it('handles edge cases', () => {
      expect(formatPercentage(NaN)).toBe('0.00%');
    });
  });

  describe('formatVolume', () => {
    it('formats billions with B suffix', () => {
      expect(formatVolume(1500000000)).toBe('$1.50B');
      expect(formatVolume(1000000000)).toBe('$1.00B');
    });

    it('formats millions with M suffix', () => {
      expect(formatVolume(1500000)).toBe('$1.50M');
      expect(formatVolume(1000000)).toBe('$1.00M');
    });

    it('formats thousands with K suffix', () => {
      expect(formatVolume(1500)).toBe('$1.50K');
      expect(formatVolume(1000)).toBe('$1.00K');
    });

    it('formats small numbers without suffix', () => {
      expect(formatVolume(999)).toBe('$999');
      expect(formatVolume(100)).toBe('$100');
    });

    it('handles zero and edge cases', () => {
      expect(formatVolume(0)).toBe('$0');
      expect(formatVolume(NaN)).toBe('$0');
    });
  });

  describe('formatMarketCap', () => {
    it('formats trillions with T suffix', () => {
      expect(formatMarketCap(1500000000000)).toBe('$1.50T');
    });

    it('formats billions with B suffix', () => {
      expect(formatMarketCap(1500000000)).toBe('$1.50B');
    });

    it('handles negative numbers', () => {
      expect(formatMarketCap(-1000000000)).toBe('$-1.00B');
    });
  });

  describe('formatPriceChangeWithTrend', () => {
    it('identifies positive trends', () => {
      const result = formatPriceChangeWithTrend(5.5);
      expect(result.formatted).toBe('+5.50%');
      expect(result.isPositive).toBe(true);
      expect(result.isNegative).toBe(false);
      expect(result.isNeutral).toBe(false);
    });

    it('identifies negative trends', () => {
      const result = formatPriceChangeWithTrend(-3.2);
      expect(result.formatted).toBe('-3.20%');
      expect(result.isPositive).toBe(false);
      expect(result.isNegative).toBe(true);
      expect(result.isNeutral).toBe(false);
    });

    it('identifies neutral trends', () => {
      const result = formatPriceChangeWithTrend(0);
      expect(result.formatted).toBe('+0.00%');
      expect(result.isPositive).toBe(false);
      expect(result.isNegative).toBe(false);
      expect(result.isNeutral).toBe(true);
    });
  });

  describe('truncateAddress', () => {
    it('truncates long addresses', () => {
      const address = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';
      expect(truncateAddress(address)).toBe('EPjFWd...Dt1v');
    });

    it('returns short addresses unchanged', () => {
      const address = 'short';
      expect(truncateAddress(address)).toBe('short');
    });

    it('handles custom start and end lengths', () => {
      const address = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';
      expect(truncateAddress(address, 8, 6)).toBe('EPjFWdd5...yTDt1v');
    });
  });

  describe('formatRelativeTime', () => {
    const now = new Date('2024-01-01T12:00:00Z');
    
    beforeEach(() => {
      vi.useFakeTimers();
      vi.setSystemTime(now);
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('formats recent time as "Just now"', () => {
      const date = new Date('2024-01-01T11:59:30Z'); // 30 seconds ago
      expect(formatRelativeTime(date)).toBe('Just now');
    });

    it('formats minutes ago', () => {
      const date = new Date('2024-01-01T11:55:00Z'); // 5 minutes ago
      expect(formatRelativeTime(date)).toBe('5 minutes ago');
    });

    it('formats hours ago', () => {
      const date = new Date('2024-01-01T10:00:00Z'); // 2 hours ago
      expect(formatRelativeTime(date)).toBe('2 hours ago');
    });

    it('formats days ago', () => {
      const date = new Date('2023-12-30T12:00:00Z'); // 2 days ago
      expect(formatRelativeTime(date)).toBe('2 days ago');
    });

    it('handles singular forms', () => {
      const oneMinuteAgo = new Date('2024-01-01T11:59:00Z');
      const oneHourAgo = new Date('2024-01-01T11:00:00Z');
      const oneDayAgo = new Date('2023-12-31T12:00:00Z');

      expect(formatRelativeTime(oneMinuteAgo)).toBe('1 minute ago');
      expect(formatRelativeTime(oneHourAgo)).toBe('1 hour ago');
      expect(formatRelativeTime(oneDayAgo)).toBe('1 day ago');
    });
  });
});