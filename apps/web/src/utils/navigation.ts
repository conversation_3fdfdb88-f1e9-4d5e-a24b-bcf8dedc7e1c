export interface NavigationContext {
  scrollPosition?: number;
  filters?: Record<string, any>;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  selectedItems?: string[];
  searchQuery?: string;
  activeTab?: string;
  timestamp?: number;
}

export interface NavigationState {
  path: string;
  context: NavigationContext;
}

const NAVIGATION_STORAGE_KEY = 'bmad-navigation-context';
const MAX_CONTEXT_AGE = 30 * 60 * 1000; // 30 minutes

/**
 * Save current page context for later restoration
 */
export function saveNavigationContext(path: string, context: NavigationContext): void {
  try {
    const state: NavigationState = {
      path,
      context: {
        ...context,
        timestamp: Date.now()
      }
    };
    
    sessionStorage.setItem(NAVIGATION_STORAGE_KEY, JSON.stringify(state));
  } catch (error) {
    console.warn('Failed to save navigation context:', error);
  }
}

/**
 * Restore navigation context for a specific path
 */
export function restoreNavigationContext(path: string): NavigationContext | null {
  try {
    const stored = sessionStorage.getItem(NAVIGATION_STORAGE_KEY);
    if (!stored) return null;
    
    const state: NavigationState = JSON.parse(stored);
    
    // Check if context is for the right path and not too old
    if (state.path !== path) return null;
    
    const age = Date.now() - (state.context.timestamp || 0);
    if (age > MAX_CONTEXT_AGE) {
      clearNavigationContext();
      return null;
    }
    
    return state.context;
  } catch (error) {
    console.warn('Failed to restore navigation context:', error);
    return null;
  }
}

/**
 * Clear stored navigation context
 */
export function clearNavigationContext(): void {
  try {
    sessionStorage.removeItem(NAVIGATION_STORAGE_KEY);
  } catch (error) {
    console.warn('Failed to clear navigation context:', error);
  }
}

/**
 * Hook to preserve scroll position on navigation
 */
export function preserveScrollPosition(elementId?: string): {
  saveScrollPosition: () => void;
  restoreScrollPosition: () => void;
} {
  const saveScrollPosition = () => {
    const element = elementId ? document.getElementById(elementId) : window;
    const scrollTop = elementId 
      ? (document.getElementById(elementId)?.scrollTop || 0)
      : window.scrollY;
    
    const currentPath = window.location.pathname;
    const existingContext = restoreNavigationContext(currentPath) || {};
    
    saveNavigationContext(currentPath, {
      ...existingContext,
      scrollPosition: scrollTop
    });
  };

  const restoreScrollPosition = () => {
    const currentPath = window.location.pathname;
    const context = restoreNavigationContext(currentPath);
    
    if (context?.scrollPosition !== undefined) {
      setTimeout(() => {
        if (elementId) {
          const element = document.getElementById(elementId);
          if (element) {
            element.scrollTop = context.scrollPosition!;
          }
        } else {
          window.scrollTo(0, context.scrollPosition!);
        }
      }, 100);
    }
  };

  return { saveScrollPosition, restoreScrollPosition };
}

/**
 * Generate URL with preserved context parameters
 */
export function buildNavigationUrl(
  basePath: string,
  params: Record<string, string | number | boolean> = {},
  preserveExisting: boolean = false
): string {
  const url = new URL(basePath, window.location.origin);
  
  // Preserve existing parameters if requested
  if (preserveExisting) {
    const currentParams = new URLSearchParams(window.location.search);
    currentParams.forEach((value, key) => {
      if (!params.hasOwnProperty(key)) {
        url.searchParams.set(key, value);
      }
    });
  }
  
  // Add new parameters
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      url.searchParams.set(key, value.toString());
    }
  });
  
  return url.pathname + url.search;
}

/**
 * Navigate with context preservation
 */
export function navigateWithContext(
  targetPath: string,
  context: NavigationContext = {},
  currentPath?: string
): string {
  // Save current context if path is provided
  if (currentPath) {
    saveNavigationContext(currentPath, context);
  }
  
  return targetPath;
}

/**
 * Keyboard navigation handler
 */
export function setupKeyboardNavigation(handlers: {
  onEscape?: () => void;
  onBack?: () => void;
  onForward?: () => void;
}): () => void {
  const handleKeyDown = (event: KeyboardEvent) => {
    // Ignore if user is typing in an input
    if (event.target instanceof HTMLInputElement || 
        event.target instanceof HTMLTextAreaElement) {
      return;
    }
    
    switch (event.key) {
      case 'Escape':
        if (handlers.onEscape) {
          event.preventDefault();
          handlers.onEscape();
        }
        break;
      case 'ArrowLeft':
        if (event.altKey && handlers.onBack) {
          event.preventDefault();
          handlers.onBack();
        }
        break;
      case 'ArrowRight':
        if (event.altKey && handlers.onForward) {
          event.preventDefault();
          handlers.onForward();
        }
        break;
    }
  };
  
  document.addEventListener('keydown', handleKeyDown);
  
  // Return cleanup function
  return () => {
    document.removeEventListener('keydown', handleKeyDown);
  };
}