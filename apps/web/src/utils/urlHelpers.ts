import { PublicKey } from '@solana/web3.js';
import { validateTokenAddress } from '@packages/shared/src/utils/tokenValidation';

export interface SwapParams {
  mint?: string;
  amount?: string;
  slippage?: string;
  source?: string;
}

export interface ParsedSwapParams {
  mint: string | null;
  amount: number | null;
  slippage: number | null;
  source: string | null;
  errors: string[];
}

export function parseSwapParams(searchParams: URLSearchParams): ParsedSwapParams {
  const result: ParsedSwapParams = {
    mint: null,
    amount: null,
    slippage: null,
    source: null,
    errors: []
  };

  // Parse mint parameter
  const mintParam = searchParams.get('mint');
  if (mintParam) {
    if (isValidSolanaAddress(mintParam)) {
      result.mint = mintParam;
    } else {
      result.errors.push('Invalid token address format');
    }
  }

  // Parse amount parameter
  const amountParam = searchParams.get('amount');
  if (amountParam) {
    const amount = parseFloat(amountParam);
    if (isNaN(amount) || amount <= 0) {
      result.errors.push('Invalid amount - must be a positive number');
    } else {
      result.amount = amount;
    }
  }

  // Parse slippage parameter (in basis points)
  const slippageParam = searchParams.get('slippage');
  if (slippageParam) {
    const slippage = parseInt(slippageParam, 10);
    if (isNaN(slippage) || slippage < 0 || slippage > 10000) {
      result.errors.push('Invalid slippage - must be between 0 and 10000 basis points');
    } else {
      result.slippage = slippage;
    }
  }

  // Parse source parameter
  const sourceParam = searchParams.get('source');
  if (sourceParam) {
    const validSources = ['watchlist', 'external', 'direct'];
    if (validSources.includes(sourceParam)) {
      result.source = sourceParam;
    } else {
      result.errors.push(`Invalid source - must be one of: ${validSources.join(', ')}`);
    }
  }

  return result;
}

export function isValidSolanaAddress(address: string): boolean {
  // Delegate to shared validation utility for consistency
  return validateTokenAddress(address).isValid;
}

export function buildSwapUrl(params: SwapParams): string {
  const url = new URL('/trading', window.location.origin);
  
  if (params.mint) {
    url.searchParams.set('mint', params.mint);
  }
  
  if (params.amount) {
    url.searchParams.set('amount', params.amount);
  }
  
  if (params.slippage) {
    url.searchParams.set('slippage', params.slippage);
  }
  
  if (params.source) {
    url.searchParams.set('source', params.source);
  }
  
  return url.toString();
}