{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "CommonJS", "moduleResolution": "node", "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "declaration": true, "declarationMap": true, "sourceMap": true, "baseUrl": ".", "outDir": "dist", "rootDir": ".", "paths": {"@shared/*": ["../../packages/shared/src/*"], "@config/*": ["../../packages/config/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests", "prisma"]}