{"name": "@solana-trading-app/api", "version": "1.0.0", "private": true, "scripts": {"build": "tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "lint": "eslint src/**/*.ts", "typecheck": "tsc --noEmit", "test": "vitest", "test:unit": "vitest run --dir src", "test:integration": "vitest run tests/integration", "test:watch": "vitest --watch", "test:coverage": "vitest run --coverage", "clean": "rm -rf dist node_modules", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "db:generate": "prisma generate"}, "dependencies": {"@bull-board/api": "^5.10.2", "@bull-board/express": "^5.10.2", "@prisma/client": "^5.8.1", "@solana/web3.js": "^1.87.6", "axios": "^1.11.0", "bullmq": "^5.1.9", "compression": "^1.7.4", "cors": "^2.8.5", "decimal.js": "^10.4.3", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^7.1.0", "isomorphic-dompurify": "^2.9.0", "node-cron": "^3.0.3", "pg": "^8.16.3", "pino": "^8.17.2", "pino-http": "^9.0.0", "pino-pretty": "^10.3.1", "prisma": "^5.8.1", "redis": "^4.6.12", "zod": "^3.22.4"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-session": "^1.17.10", "@types/node": "^20.11.0", "@types/node-cron": "^3.0.11", "@types/supertest": "^6.0.2", "supertest": "^6.3.4", "tsx": "^4.7.0", "typescript": "^5.3.3", "vitest": "^1.2.2"}}