import { PrismaClient, PositionStatus, ExitStrategyType, TransactionType, TransactionStatus, PriceSource } from '@prisma/client';
import { Decimal } from 'decimal.js';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Clean existing data
  await prisma.exitStrategy.deleteMany();
  await prisma.transaction.deleteMany();
  await prisma.position.deleteMany();
  await prisma.watchlistItem.deleteMany();
  await prisma.priceSnapshot.deleteMany();
  await prisma.jobQueueState.deleteMany();
  await prisma.pollingConfig.deleteMany();

  console.log('🧹 Cleaned existing data');

  // Create sample positions
  const position1 = await prisma.position.create({
    data: {
      tokenAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
      tokenSymbol: 'BONK',
      tokenName: 'Bonk',
      entryPrice: new Decimal('0.00002340'),
      currentPrice: new Decimal('0.00002856'),
      quantity: new Decimal('42735042.73'),
      entryAmountSol: new Decimal('1.0'),
      currentValueSol: new Decimal('1.22'),
      pnlSol: new Decimal('0.22'),
      pnlPercentage: new Decimal('22.00'),
      status: PositionStatus.ACTIVE,
      transactionSignature: '5VfyKRmfRfyZmBUfcY7xXMtpqwJ3sLqP8V4v5hCtZzPb6Z9Nw2xBkW7qpJ2Qf6XPm1Y4sK3vR',
      slippage: new Decimal('1.00'),
      jupiterQuoteId: 'quote_bonk_001'
    }
  });

  const position2 = await prisma.position.create({
    data: {
      tokenAddress: 'WENWENvqqNya429ubCdR81ZmD69brwQaaBYY6p3LCpk',
      tokenSymbol: 'WEN',
      tokenName: 'Wen',
      entryPrice: new Decimal('0.00012500'),
      currentPrice: new Decimal('0.00009800'),
      quantity: new Decimal('8000000.0'),
      entryAmountSol: new Decimal('1.0'),
      currentValueSol: new Decimal('0.784'),
      pnlSol: new Decimal('-0.216'),
      pnlPercentage: new Decimal('-21.60'),
      status: PositionStatus.ACTIVE,
      transactionSignature: '3XfgKRmfRfyZmBUfcY7xXMtpqwJ3sLqP8V4v5hCtZzPb6Z9Nw2xBkW7qpJ2Qf6XPm1Y4sK2vR',
      slippage: new Decimal('1.50'),
      jupiterQuoteId: 'quote_wen_001'
    }
  });

  console.log('📊 Created sample positions');

  // Create exit strategies
  await prisma.exitStrategy.create({
    data: {
      positionId: position1.id,
      type: ExitStrategyType.TAKE_PROFIT,
      takeProfitTiers: [
        {
          id: 'tier_1',
          percentage: new Decimal('50.0'),
          priceTarget: new Decimal('0.00003500'),
          isExecuted: false
        },
        {
          id: 'tier_2', 
          percentage: new Decimal('50.0'),
          priceTarget: new Decimal('0.00005000'),
          isExecuted: false
        }
      ],
      stopLossConfig: {
        priceTarget: new Decimal('0.00001800'),
        isTrailing: false,
        isExecuted: false
      }
    }
  });

  await prisma.exitStrategy.create({
    data: {
      positionId: position2.id,
      type: ExitStrategyType.STOP_LOSS,
      stopLossConfig: {
        priceTarget: new Decimal('0.00008000'),
        isTrailing: true,
        trailingDistance: new Decimal('0.00002000'),
        isExecuted: false
      }
    }
  });

  console.log('🎯 Created exit strategies');

  // Create sample transactions
  await prisma.transaction.create({
    data: {
      positionId: position1.id,
      signature: '5VfyKRmfRfyZmBUfcY7xXMtpqwJ3sLqP8V4v5hCtZzPb6Z9Nw2xBkW7qpJ2Qf6XPm1Y4sK3vR',
      type: TransactionType.BUY,
      tokenAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
      amountSol: new Decimal('1.0'),
      amountToken: new Decimal('42735042.73'),
      price: new Decimal('0.00002340'),
      fees: new Decimal('0.005'),
      slippage: new Decimal('1.00'),
      status: TransactionStatus.CONFIRMED,
      jupiterQuoteId: 'quote_bonk_001',
      confirmedAt: new Date()
    }
  });

  await prisma.transaction.create({
    data: {
      positionId: position2.id,
      signature: '3XfgKRmfRfyZmBUfcY7xXMtpqwJ3sLqP8V4v5hCtZzPb6Z9Nw2xBkW7qpJ2Qf6XPm1Y4sK2vR',
      type: TransactionType.BUY,
      tokenAddress: 'WENWENvqqNya429ubCdR81ZmD69brwQaaBYY6p3LCpk',
      amountSol: new Decimal('1.0'),
      amountToken: new Decimal('8000000.0'),
      price: new Decimal('0.00012500'),
      fees: new Decimal('0.005'),
      slippage: new Decimal('1.50'),
      status: TransactionStatus.CONFIRMED,
      jupiterQuoteId: 'quote_wen_001',
      confirmedAt: new Date()
    }
  });

  console.log('💳 Created sample transactions');

  // Create watchlist items
  await prisma.watchlistItem.create({
    data: {
      tokenAddress: 'So11111111111111111111111111111111111111112',
      tokenSymbol: 'SOL',
      tokenName: 'Solana',
      customName: 'Solana Native',
      notes: 'Native SOL token for reference',
      currentPrice: new Decimal('98.50'),
      priceChange24h: new Decimal('2.30'),
      priceChangePercentage24h: new Decimal('2.39'),
      marketCap: new Decimal('46500000000'),
      volume24h: new Decimal('1250000000')
    }
  });

  await prisma.watchlistItem.create({
    data: {
      tokenAddress: 'mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So',
      tokenSymbol: 'mSOL',
      tokenName: 'Marinade staked SOL',
      customName: 'Marinade SOL',
      notes: 'Liquid staking token',
      currentPrice: new Decimal('108.20'),
      priceChange24h: new Decimal('1.80'),
      priceChangePercentage24h: new Decimal('1.69'),
      marketCap: new Decimal('1200000000'),
      volume24h: new Decimal('25000000')
    }
  });

  console.log('👀 Created watchlist items');

  // Create price snapshots for TimescaleDB testing
  const now = new Date();
  const priceSnapshots = [];
  
  for (let i = 0; i < 10; i++) {
    const timestamp = new Date(now.getTime() - i * 5 * 60 * 1000); // 5 minute intervals
    priceSnapshots.push({
      tokenAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
      price: new Decimal((0.00002340 + (Math.random() - 0.5) * 0.000005).toFixed(8)),
      volume24h: new Decimal('1500000'),
      marketCap: new Decimal('2300000000'),
      priceChange24h: new Decimal(((Math.random() - 0.5) * 0.000002).toFixed(8)),
      timestamp: timestamp,
      source: PriceSource.COINMARKETCAP
    });
  }

  await prisma.priceSnapshot.createMany({
    data: priceSnapshots
  });

  console.log('📈 Created price snapshots for TimescaleDB');

  // Create polling configuration
  await prisma.pollingConfig.createMany({
    data: [
      {
        configKey: 'price_monitor_active_positions',
        intervalMs: 5000,
        metadata: { description: 'Price monitoring for active positions' }
      },
      {
        configKey: 'price_monitor_watchlist',
        intervalMs: 30000,
        metadata: { description: 'Price monitoring for watchlist items' }
      },
      {
        configKey: 'exit_strategy_execution',
        intervalMs: 2000,
        metadata: { description: 'Exit strategy execution checks' }
      }
    ]
  });

  console.log('⚙️ Created polling configuration');
  console.log('✅ Database seeded successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });