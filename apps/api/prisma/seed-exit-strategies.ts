import { PrismaClient } from '@prisma/client';
import { ExitStrategyConfiguration } from '../src/types/exit-strategy.js';

const prisma = new PrismaClient();

const defaultPresets: Array<{
  name: string;
  description: string;
  isDefault: boolean;
  configuration: ExitStrategyConfiguration;
}> = [
  {
    name: 'Conservative Exit Strategy',
    description: 'A safe exit strategy with moderate profit targets and strict stop losses',
    isDefault: true,
    configuration: {
      takeProfitTiers: [
        {
          index: 1,
          priceTarget: 1.5, // 50% profit
          percentage: 50,
          positionPercentage: 30,
          isTriggered: false
        },
        {
          index: 2,
          priceTarget: 2.0, // 100% profit
          percentage: 100,
          positionPercentage: 40,
          isTriggered: false
        },
        {
          index: 3,
          priceTarget: 3.0, // 200% profit
          percentage: 200,
          positionPercentage: 20,
          isTriggered: false
        }
      ],
      stopLoss: {
        enabled: true,
        percentage: 15 // 15% stop loss
      },
      trailingStop: {
        enabled: false
      },
      moonBag: {
        enabled: true,
        percentage: 10 // Keep 10% as moon bag
      }
    }
  },
  {
    name: 'Aggressive Exit Strategy',
    description: 'High-risk, high-reward strategy with larger profit targets',
    isDefault: false,
    configuration: {
      takeProfitTiers: [
        {
          index: 1,
          priceTarget: 2.0, // 100% profit
          percentage: 100,
          positionPercentage: 25,
          isTriggered: false
        },
        {
          index: 2,
          priceTarget: 5.0, // 400% profit
          percentage: 400,
          positionPercentage: 35,
          isTriggered: false
        },
        {
          index: 3,
          priceTarget: 10.0, // 900% profit
          percentage: 900,
          positionPercentage: 25,
          isTriggered: false
        }
      ],
      stopLoss: {
        enabled: true,
        percentage: 25 // 25% stop loss (higher risk tolerance)
      },
      trailingStop: {
        enabled: true,
        distancePercentage: 20 // 20% trailing distance
      },
      moonBag: {
        enabled: true,
        percentage: 15 // Keep 15% as moon bag
      }
    }
  },
  {
    name: 'Quick Flip Strategy',
    description: 'Fast exit strategy for quick profits on volatile moves',
    isDefault: false,
    configuration: {
      takeProfitTiers: [
        {
          index: 1,
          priceTarget: 1.2, // 20% profit
          percentage: 20,
          positionPercentage: 50,
          isTriggered: false
        },
        {
          index: 2,
          priceTarget: 1.5, // 50% profit
          percentage: 50,
          positionPercentage: 40,
          isTriggered: false
        }
      ],
      stopLoss: {
        enabled: true,
        percentage: 10 // Tight 10% stop loss
      },
      trailingStop: {
        enabled: false
      },
      moonBag: {
        enabled: true,
        percentage: 10 // Small moon bag
      }
    }
  },
  {
    name: 'HODL Strategy',
    description: 'Long-term holding strategy with trailing stops and minimal selling',
    isDefault: false,
    configuration: {
      takeProfitTiers: [
        {
          index: 1,
          priceTarget: 10.0, // 900% profit before first sell
          percentage: 900,
          positionPercentage: 20,
          isTriggered: false
        }
      ],
      stopLoss: {
        enabled: true,
        percentage: 30 // Wide stop loss for volatility
      },
      trailingStop: {
        enabled: true,
        distancePercentage: 25 // Wide trailing stop
      },
      moonBag: {
        enabled: true,
        percentage: 80 // Keep most as moon bag
      }
    }
  },
  {
    name: 'Scalping Strategy',
    description: 'High-frequency strategy for small, quick profits',
    isDefault: false,
    configuration: {
      takeProfitTiers: [
        {
          index: 1,
          priceTarget: 1.05, // 5% profit
          percentage: 5,
          positionPercentage: 60,
          isTriggered: false
        },
        {
          index: 2,
          priceTarget: 1.1, // 10% profit
          percentage: 10,
          positionPercentage: 35,
          isTriggered: false
        }
      ],
      stopLoss: {
        enabled: true,
        percentage: 3 // Very tight stop loss
      },
      trailingStop: {
        enabled: false
      },
      moonBag: {
        enabled: true,
        percentage: 5 // Minimal moon bag
      }
    }
  }
];

async function seedExitStrategyPresets() {
  console.log('🌱 Seeding exit strategy presets...');

  try {
    // Clear existing presets
    console.log('📝 Clearing existing exit strategy presets...');
    await prisma.exitStrategyPreset.deleteMany();

    // Insert new presets
    for (const preset of defaultPresets) {
      console.log(`📝 Creating preset: ${preset.name}`);

      await prisma.exitStrategyPreset.create({
        data: {
          name: preset.name,
          description: preset.description,
          isDefault: preset.isDefault,
          configuration: JSON.parse(JSON.stringify(preset.configuration))
        }
      });
    }

    console.log('✅ Exit strategy presets seeded successfully!');

    // Display summary
    const presetCount = await prisma.exitStrategyPreset.count();
    const defaultPreset = await prisma.exitStrategyPreset.findFirst({
      where: { isDefault: true }
    });

    console.log(`📊 Summary:`);
    console.log(`   - Total presets: ${presetCount}`);
    console.log(`   - Default preset: ${defaultPreset?.name || 'None'}`);

  } catch (error) {
    console.error('❌ Error seeding exit strategy presets:', error);
    throw error;
  }
}

async function main() {
  await seedExitStrategyPresets();
}

if (require.main === module) {
  main()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { seedExitStrategyPresets };
