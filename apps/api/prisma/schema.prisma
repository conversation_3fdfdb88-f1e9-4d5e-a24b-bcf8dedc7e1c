// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Position {
  id                    String   @id @default(cuid())
  tokenAddress          String   @map("token_address")
  tokenSymbol           String   @map("token_symbol")
  tokenName             String   @map("token_name")
  entryPrice            Decimal  @map("entry_price") @db.Decimal(20, 8)
  currentPrice          Decimal  @map("current_price") @db.Decimal(20, 8)
  quantity              Decimal  @db.Decimal(30, 8)
  entryAmountSol        Decimal  @map("entry_amount_sol") @db.Decimal(20, 8)
  currentValueSol       Decimal  @map("current_value_sol") @db.Decimal(20, 8)
  pnlSol                Decimal  @map("pnl_sol") @db.Decimal(20, 8)
  pnlPercentage         Decimal  @map("pnl_percentage") @db.Decimal(10, 4)
  status                PositionStatus @default(ACTIVE)
  entryTimestamp        DateTime @default(now()) @map("entry_timestamp")
  lastUpdateTimestamp   DateTime @updatedAt @map("last_update_timestamp")
  exitTimestamp         DateTime? @map("exit_timestamp")
  transactionSignature  String   @map("transaction_signature") @db.VarChar(88)
  slippage              Decimal  @db.Decimal(6, 2)
  jupiterQuoteId        String?  @map("jupiter_quote_id")
  
  // Relations
  exitStrategies        ExitStrategy[]
  transactions          Transaction[]
  
  @@map("positions")
  @@index([tokenAddress])
  @@index([status])
  @@index([entryTimestamp])
}

model ExitStrategy {
  id                String              @id @default(cuid())
  positionId        String              @map("position_id")
  type              ExitStrategyType
  isActive          Boolean             @default(true) @map("is_active")
  takeProfitTiers   Json?               @map("take_profit_tiers")
  stopLossConfig    Json?               @map("stop_loss_config")
  timeBasedExit     DateTime?           @map("time_based_exit")
  createdAt         DateTime            @default(now()) @map("created_at")
  updatedAt         DateTime            @updatedAt @map("updated_at")
  
  // Relations
  position          Position            @relation(fields: [positionId], references: [id], onDelete: Cascade)
  
  @@map("exit_strategies")
  @@index([positionId])
  @@index([type])
  @@index([isActive])
}

model Transaction {
  id                  String              @id @default(cuid())
  positionId          String?             @map("position_id")
  signature           String              @unique @db.VarChar(88)
  type                TransactionType
  tokenAddress        String              @map("token_address")
  amountSol           Decimal             @map("amount_sol") @db.Decimal(20, 8)
  amountToken         Decimal             @map("amount_token") @db.Decimal(30, 8)
  price               Decimal             @db.Decimal(20, 8)
  fees                Decimal             @db.Decimal(20, 8)
  slippage            Decimal             @db.Decimal(6, 2)
  status              TransactionStatus   @default(PENDING)
  jupiterQuoteId      String?             @map("jupiter_quote_id")
  createdAt           DateTime            @default(now()) @map("created_at")
  confirmedAt         DateTime?           @map("confirmed_at")
  
  // Relations
  position            Position?           @relation(fields: [positionId], references: [id])
  
  @@map("transactions")
  @@index([signature])
  @@index([positionId])
  @@index([type])
  @@index([status])
  @@index([createdAt])
}

model WatchlistItem {
  id                      String    @id @default(cuid())
  tokenAddress            String    @unique @map("token_address")
  tokenSymbol             String    @map("token_symbol")
  tokenName               String    @map("token_name")
  customName              String?   @map("custom_name")
  notes                   String?   @db.Text
  isPinned                Boolean   @default(false) @map("is_pinned")
  addedAt                 DateTime  @default(now()) @map("added_at")
  updatedAt               DateTime  @updatedAt @map("updated_at")
  isActive                Boolean   @default(true) @map("is_active")
  
  @@map("watchlist_items")
  @@index([tokenAddress])
  @@index([isActive])
  @@index([isPinned, addedAt])
  @@index([addedAt])
}

model PriceSnapshot {
  tokenAddress      String              @map("token_address")
  timestamp         DateTime            @default(now())
  price             Decimal             @db.Decimal(20, 8)
  volume24h         Decimal?            @map("volume_24h") @db.Decimal(20, 2)
  marketCap         Decimal?            @map("market_cap") @db.Decimal(20, 2)
  priceChange24h    Decimal             @map("price_change_24h") @db.Decimal(20, 8)
  source            PriceSource
  
  @@id([tokenAddress, timestamp])
  @@map("price_snapshots")
  @@index([timestamp])
  @@index([source])
}

model JobQueueState {
  id                String              @id @default(cuid())
  jobId             String              @unique @map("job_id")
  name              String
  data              Json
  opts              Json?
  progress          Int                 @default(0)
  delay             Int                 @default(0)
  timestamp         DateTime            @default(now())
  processedOn       DateTime?           @map("processed_on")
  finishedOn        DateTime?           @map("finished_on")
  failedReason      String?             @map("failed_reason") @db.Text
  
  @@map("job_queue_state")
  @@index([jobId])
  @@index([name])
  @@index([timestamp])
}

model PollingConfig {
  id                String              @id @default(cuid())
  configKey         String              @unique @map("config_key")
  intervalMs        Int                 @map("interval_ms")
  isActive          Boolean             @default(true) @map("is_active")
  lastUpdate        DateTime            @updatedAt @map("last_update")
  metadata          Json?
  
  @@map("polling_config")
  @@index([configKey])
  @@index([isActive])
}

// Enums
enum PositionStatus {
  ACTIVE
  CLOSED
  PENDING_EXIT
  ERROR
}

enum ExitStrategyType {
  TAKE_PROFIT
  STOP_LOSS
  TRAILING_STOP
  TIME_BASED
}

enum TransactionType {
  BUY
  SELL
  PARTIAL_SELL
}

enum TransactionStatus {
  PENDING
  CONFIRMED
  FAILED
}

enum PriceSource {
  COINMARKETCAP
  JUPITER
  HELIUS
}