-- Create<PERSON>num
CREATE TYPE "PositionStatus" AS ENUM ('ACTIVE', 'CLOSED', 'PENDING_EXIT', 'ERROR');

-- CreateEnum
CREATE TYPE "ExitStrategyType" AS ENUM ('TAKE_PROFIT', 'STOP_LOSS', 'TRAILING_STOP', 'TIME_BASED');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "TransactionType" AS ENUM ('BUY', 'SELL', 'PARTIAL_SELL');

-- CreateEnum
CREATE TYPE "TransactionStatus" AS ENUM ('PENDING', 'CONFIRMED', 'FAILED');

-- CreateEnum
CREATE TYPE "PriceSource" AS ENUM ('COINMARKETCAP', 'JUPITER', 'HELIUS');

-- CreateTable
CREATE TABLE "positions" (
    "id" TEXT NOT NULL,
    "token_address" TEXT NOT NULL,
    "token_symbol" TEXT NOT NULL,
    "token_name" TEXT NOT NULL,
    "entry_price" DECIMAL(20,8) NOT NULL,
    "current_price" DECIMAL(20,8) NOT NULL,
    "quantity" DECIMAL(30,8) NOT NULL,
    "entry_amount_sol" DECIMAL(20,8) NOT NULL,
    "current_value_sol" DECIMAL(20,8) NOT NULL,
    "pnl_sol" DECIMAL(20,8) NOT NULL,
    "pnl_percentage" DECIMAL(10,4) NOT NULL,
    "status" "PositionStatus" NOT NULL DEFAULT 'ACTIVE',
    "entry_timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_update_timestamp" TIMESTAMP(3) NOT NULL,
    "exit_timestamp" TIMESTAMP(3),
    "transaction_signature" VARCHAR(88) NOT NULL,
    "slippage" DECIMAL(6,2) NOT NULL,
    "jupiter_quote_id" TEXT,

    CONSTRAINT "positions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "exit_strategies" (
    "id" TEXT NOT NULL,
    "position_id" TEXT NOT NULL,
    "type" "ExitStrategyType" NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "take_profit_tiers" JSONB,
    "stop_loss_config" JSONB,
    "time_based_exit" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "exit_strategies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "transactions" (
    "id" TEXT NOT NULL,
    "position_id" TEXT,
    "signature" VARCHAR(88) NOT NULL,
    "type" "TransactionType" NOT NULL,
    "token_address" TEXT NOT NULL,
    "amount_sol" DECIMAL(20,8) NOT NULL,
    "amount_token" DECIMAL(30,8) NOT NULL,
    "price" DECIMAL(20,8) NOT NULL,
    "fees" DECIMAL(20,8) NOT NULL,
    "slippage" DECIMAL(6,2) NOT NULL,
    "status" "TransactionStatus" NOT NULL DEFAULT 'PENDING',
    "jupiter_quote_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "confirmed_at" TIMESTAMP(3),

    CONSTRAINT "transactions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "watchlist_items" (
    "id" TEXT NOT NULL,
    "token_address" TEXT NOT NULL,
    "token_symbol" TEXT NOT NULL,
    "token_name" TEXT NOT NULL,
    "custom_name" TEXT,
    "notes" TEXT,
    "is_pinned" BOOLEAN NOT NULL DEFAULT false,
    "added_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "watchlist_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "price_snapshots" (
    "id" TEXT NOT NULL,
    "token_address" TEXT NOT NULL,
    "price" DECIMAL(20,8) NOT NULL,
    "volume_24h" DECIMAL(20,2),
    "market_cap" DECIMAL(20,2),
    "price_change_24h" DECIMAL(20,8) NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "source" "PriceSource" NOT NULL,

    CONSTRAINT "price_snapshots_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "job_queue_state" (
    "id" TEXT NOT NULL,
    "job_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "data" JSONB NOT NULL,
    "opts" JSONB,
    "progress" INTEGER NOT NULL DEFAULT 0,
    "delay" INTEGER NOT NULL DEFAULT 0,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "processed_on" TIMESTAMP(3),
    "finished_on" TIMESTAMP(3),
    "failed_reason" TEXT,

    CONSTRAINT "job_queue_state_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "polling_config" (
    "id" TEXT NOT NULL,
    "config_key" TEXT NOT NULL,
    "interval_ms" INTEGER NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "last_update" TIMESTAMP(3) NOT NULL,
    "metadata" JSONB,

    CONSTRAINT "polling_config_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "positions_token_address_idx" ON "positions"("token_address");

-- CreateIndex
CREATE INDEX "positions_status_idx" ON "positions"("status");

-- CreateIndex
CREATE INDEX "positions_entry_timestamp_idx" ON "positions"("entry_timestamp");

-- CreateIndex
CREATE INDEX "exit_strategies_position_id_idx" ON "exit_strategies"("position_id");

-- CreateIndex
CREATE INDEX "exit_strategies_type_idx" ON "exit_strategies"("type");

-- CreateIndex
CREATE INDEX "exit_strategies_is_active_idx" ON "exit_strategies"("is_active");

-- CreateIndex
CREATE UNIQUE INDEX "transactions_signature_key" ON "transactions"("signature");

-- CreateIndex
CREATE INDEX "transactions_signature_idx" ON "transactions"("signature");

-- CreateIndex
CREATE INDEX "transactions_position_id_idx" ON "transactions"("position_id");

-- CreateIndex
CREATE INDEX "transactions_type_idx" ON "transactions"("type");

-- CreateIndex
CREATE INDEX "transactions_status_idx" ON "transactions"("status");

-- CreateIndex
CREATE INDEX "transactions_created_at_idx" ON "transactions"("created_at");

-- CreateIndex
CREATE UNIQUE INDEX "watchlist_items_token_address_key" ON "watchlist_items"("token_address");

-- CreateIndex
CREATE INDEX "watchlist_items_token_address_idx" ON "watchlist_items"("token_address");

-- CreateIndex
CREATE INDEX "watchlist_items_is_active_idx" ON "watchlist_items"("is_active");

-- CreateIndex
CREATE INDEX "watchlist_items_is_pinned_added_at_idx" ON "watchlist_items"("is_pinned", "added_at");

-- CreateIndex
CREATE INDEX "watchlist_items_added_at_idx" ON "watchlist_items"("added_at");

-- CreateIndex
CREATE INDEX "price_snapshots_token_address_timestamp_idx" ON "price_snapshots"("token_address", "timestamp");

-- CreateIndex
CREATE INDEX "price_snapshots_timestamp_idx" ON "price_snapshots"("timestamp");

-- CreateIndex
CREATE INDEX "price_snapshots_source_idx" ON "price_snapshots"("source");

-- CreateIndex
CREATE UNIQUE INDEX "job_queue_state_job_id_key" ON "job_queue_state"("job_id");

-- CreateIndex
CREATE INDEX "job_queue_state_job_id_idx" ON "job_queue_state"("job_id");

-- CreateIndex
CREATE INDEX "job_queue_state_name_idx" ON "job_queue_state"("name");

-- CreateIndex
CREATE INDEX "job_queue_state_timestamp_idx" ON "job_queue_state"("timestamp");

-- CreateIndex
CREATE UNIQUE INDEX "polling_config_config_key_key" ON "polling_config"("config_key");

-- CreateIndex
CREATE INDEX "polling_config_config_key_idx" ON "polling_config"("config_key");

-- CreateIndex
CREATE INDEX "polling_config_is_active_idx" ON "polling_config"("is_active");

-- AddForeignKey
ALTER TABLE "exit_strategies" ADD CONSTRAINT "exit_strategies_position_id_fkey" FOREIGN KEY ("position_id") REFERENCES "positions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "transactions" ADD CONSTRAINT "transactions_position_id_fkey" FOREIGN KEY ("position_id") REFERENCES "positions"("id") ON DELETE SET NULL ON UPDATE CASCADE;

