const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkToken() {
  const tokenAddress = 'GVa62H8JdrHQNTh1ryDirwssiyv7Uh3vYLgEab7Abonk';
  
  console.log(`Checking for token: ${tokenAddress}`);
  
  // Check all items (active and inactive)
  const allItems = await prisma.watchlistItem.findMany({
    where: {
      tokenAddress: tokenAddress
    }
  });
  
  console.log(`Found ${allItems.length} items with this address (active + inactive):`);
  allItems.forEach(item => {
    console.log(`  - ID: ${item.id}, Symbol: ${item.tokenSymbol}, Active: ${item.isActive}`);
  });
  
  // Check only active items
  const activeItems = await prisma.watchlistItem.findMany({
    where: {
      tokenAddress: tokenAddress,
      isActive: true
    }
  });
  
  console.log(`Found ${activeItems.length} ACTIVE items with this address`);
  
  // Show all tokens in watchlist
  const allTokens = await prisma.watchlistItem.findMany({
    select: {
      tokenAddress: true,
      tokenSymbol: true,
      isActive: true
    }
  });
  
  console.log('\nAll tokens in database:');
  allTokens.forEach(token => {
    console.log(`  ${token.tokenSymbol}: ${token.tokenAddress} (Active: ${token.isActive})`);
  });
  
  await prisma.$disconnect();
}

checkToken().catch(console.error);