import { PrismaClient } from '@prisma/client';
import { PriceHistoryService } from '../services/PriceHistoryService';
import { MarketDataService } from '../services/MarketDataService';
import { WatchlistService } from '../services/WatchlistService';
import { apiLogger } from '../lib/logger';

const prisma = new PrismaClient();
const watchlistService = new WatchlistService(prisma);
const marketDataService = new MarketDataService();
const priceHistoryService = new PriceHistoryService(
  prisma,
  marketDataService,
  watchlistService
);

async function startRecording() {
  try {
    apiLogger.info('Starting price history recording...');
    
    // Start recording with 1-minute intervals
    await priceHistoryService.startRecording(60000); // 60 seconds
    
    apiLogger.info('Price history recording started successfully');
    
    // Keep the process running
    process.on('SIGINT', () => {
      apiLogger.info('Stopping price history recording...');
      priceHistoryService.stopRecording();
      prisma.$disconnect();
      process.exit(0);
    });
    
  } catch (error) {
    apiLogger.error({ error }, 'Failed to start price recording');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  startRecording();
}

export { startRecording };