import { PrismaClient, ExitStrategy, ExitStrategyPreset, ExitTrigger, Position } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import {
  ExitStrategyConfiguration,
  TakeProfitTier,
  StopLossConfig,
  TrailingStopConfig,
  MoonBagConfig,
  ValidationResult,
  ExitStrategyValidation,
  ValidationError
} from '../types/exit-strategy.js';

export class ExitStrategyModel implements ExitStrategyValidation {
  constructor(private prisma: PrismaClient) {}

  // Configuration validation methods
  validateConfiguration(config: ExitStrategyConfiguration): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate take profit tiers
    const tierValidation = this.validateTakeProfitTiers(config.takeProfitTiers);
    errors.push(...tierValidation.errors);
    warnings.push(...tierValidation.warnings);

    // Validate stop loss
    const stopLossValidation = this.validateStopLoss(config.stopLoss, 0); // Entry price will be validated in service
    errors.push(...stopLossValidation.errors);
    warnings.push(...stopLossValidation.warnings);

    // Validate trailing stop
    const trailingStopValidation = this.validateTrailingStop(config.trailingStop);
    errors.push(...trailingStopValidation.errors);
    warnings.push(...trailingStopValidation.warnings);

    // Validate moon bag
    const moonBagValidation = this.validateMoonBag(config.moonBag);
    errors.push(...moonBagValidation.errors);
    warnings.push(...moonBagValidation.warnings);

    // Validate total percentages don't exceed 100%
    const totalTierPercentage = config.takeProfitTiers.reduce(
      (sum, tier) => sum + tier.positionPercentage,
      0
    );
    const moonBagPercentage = config.moonBag.enabled ? config.moonBag.percentage : 0;
    const totalPercentage = totalTierPercentage + moonBagPercentage;

    if (totalPercentage > 100) {
      errors.push(
        `Total position percentage (${totalPercentage}%) exceeds 100%. Take profit tiers: ${totalTierPercentage}%, Moon bag: ${moonBagPercentage}%`
      );
    }

    if (totalPercentage < 100 && !config.trailingStop.enabled && !config.stopLoss.enabled) {
      warnings.push(
        `Only ${totalPercentage}% of position is covered by exit strategies. Consider enabling stop loss or trailing stop for remaining ${100 - totalPercentage}%`
      );
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  validateTakeProfitTiers(tiers: TakeProfitTier[]): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (tiers.length === 0) {
      warnings.push('No take profit tiers configured');
      return { isValid: true, errors, warnings };
    }

    if (tiers.length > 5) {
      errors.push('Maximum 5 take profit tiers allowed');
    }

    // Sort tiers by price target to validate order
    const sortedTiers = [...tiers].sort((a, b) => a.priceTarget - b.priceTarget);

    for (let i = 0; i < sortedTiers.length; i++) {
      const tier = sortedTiers[i];

      // Validate individual tier
      if (tier.priceTarget <= 0) {
        errors.push(`Tier ${tier.index}: Price target must be positive`);
      }

      if (tier.percentage <= 0 || tier.percentage > 100) {
        errors.push(`Tier ${tier.index}: Percentage must be between 0 and 100`);
      }

      if (tier.positionPercentage <= 0 || tier.positionPercentage > 100) {
        errors.push(`Tier ${tier.index}: Position percentage must be between 0 and 100`);
      }

      // Validate order (each tier should have higher price target than previous)
      if (i > 0 && tier.priceTarget <= sortedTiers[i - 1].priceTarget) {
        errors.push(`Tier ${tier.index}: Price target must be higher than previous tier`);
      }
    }

    // Check for duplicate tier indices
    const indices = tiers.map(t => t.index);
    const uniqueIndices = new Set(indices);
    if (indices.length !== uniqueIndices.size) {
      errors.push('Duplicate tier indices found');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  validateStopLoss(stopLoss: StopLossConfig, entryPrice: number): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!stopLoss.enabled) {
      return { isValid: true, errors, warnings };
    }

    if (!stopLoss.percentage && !stopLoss.price) {
      errors.push('Stop loss enabled but no percentage or price specified');
    }

    if (stopLoss.percentage && (stopLoss.percentage <= 0 || stopLoss.percentage >= 100)) {
      errors.push('Stop loss percentage must be between 0 and 100');
    }

    if (stopLoss.price && entryPrice > 0 && stopLoss.price >= entryPrice) {
      errors.push('Stop loss price must be lower than entry price');
    }

    if (stopLoss.percentage && stopLoss.percentage > 50) {
      warnings.push(`Stop loss percentage of ${stopLoss.percentage}% is quite high`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  validateTrailingStop(trailingStop: TrailingStopConfig): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!trailingStop.enabled) {
      return { isValid: true, errors, warnings };
    }

    if (!trailingStop.distancePercentage && !trailingStop.distanceAmount) {
      errors.push('Trailing stop enabled but no distance percentage or amount specified');
    }

    if (trailingStop.distancePercentage && (trailingStop.distancePercentage <= 0 || trailingStop.distancePercentage >= 100)) {
      errors.push('Trailing stop distance percentage must be between 0 and 100');
    }

    if (trailingStop.distanceAmount && trailingStop.distanceAmount <= 0) {
      errors.push('Trailing stop distance amount must be positive');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  validateMoonBag(moonBag: MoonBagConfig): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!moonBag.enabled) {
      return { isValid: true, errors, warnings };
    }

    if (moonBag.percentage <= 0 || moonBag.percentage > 50) {
      errors.push('Moon bag percentage must be between 0 and 50');
    }

    if (moonBag.percentage > 20) {
      warnings.push(`Moon bag percentage of ${moonBag.percentage}% is quite high`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // Database operations
  async createExitStrategy(
    positionId: string,
    configuration: ExitStrategyConfiguration,
    presetId?: string
  ): Promise<string> {
    // Validate configuration
    const validation = this.validateConfiguration(configuration);
    if (!validation.isValid) {
      throw new ValidationError(
        'Invalid exit strategy configuration',
        validation.errors
      );
    }

    // Get position to validate entry price
    const position = await this.prisma.position.findUnique({
      where: { id: positionId }
    });

    if (!position) {
      throw new Error(`Position ${positionId} not found`);
    }

    // Validate stop loss against entry price
    if (configuration.stopLoss.enabled) {
      const entryPrice = position.entryPrice.toNumber();
      const stopLossValidation = this.validateStopLoss(configuration.stopLoss, entryPrice);
      if (!stopLossValidation.isValid) {
        throw new ValidationError(
          'Invalid stop loss configuration',
          stopLossValidation.errors
        );
      }
    }

    return await this.prisma.$transaction(async (tx) => {
      // Create exit strategy
      const strategy = await tx.exitStrategy.create({
        data: {
          positionId,
          presetId,
          takeProfitTiers: JSON.parse(JSON.stringify(configuration.takeProfitTiers)),
          stopLossEnabled: configuration.stopLoss.enabled,
          stopLossPercentage: configuration.stopLoss.percentage ? new Decimal(configuration.stopLoss.percentage) : null,
          stopLossPrice: configuration.stopLoss.price ? new Decimal(configuration.stopLoss.price) : null,
          trailingStopEnabled: configuration.trailingStop.enabled,
          trailingStopDistancePercentage: configuration.trailingStop.distancePercentage ? new Decimal(configuration.trailingStop.distancePercentage) : null,
          trailingStopDistanceAmount: configuration.trailingStop.distanceAmount ? new Decimal(configuration.trailingStop.distanceAmount) : null,
          trailingStopHighestPrice: configuration.trailingStop.highestPrice ? new Decimal(configuration.trailingStop.highestPrice) : null,
          trailingStopCurrentStopPrice: configuration.trailingStop.currentStopPrice ? new Decimal(configuration.trailingStop.currentStopPrice) : null,
          moonBagEnabled: configuration.moonBag.enabled,
          moonBagPercentage: new Decimal(configuration.moonBag.percentage),
          activatedAt: new Date()
        }
      });

      // Create triggers for take profit tiers
      for (const tier of configuration.takeProfitTiers) {
        await tx.exitTrigger.create({
          data: {
            strategyId: strategy.id,
            type: 'TAKE_PROFIT',
            tierIndex: tier.index,
            triggerPrice: new Decimal(tier.priceTarget),
            triggerPercentage: new Decimal(tier.percentage),
            positionPercentage: new Decimal(tier.positionPercentage),
            tokenAmount: new Decimal(position.quantity.toNumber() * tier.positionPercentage / 100)
          }
        });
      }

      // Create stop loss trigger if enabled
      if (configuration.stopLoss.enabled) {
        const stopPrice = configuration.stopLoss.price ||
          (position.entryPrice.toNumber() * (100 - configuration.stopLoss.percentage!) / 100);

        await tx.exitTrigger.create({
          data: {
            strategyId: strategy.id,
            type: 'STOP_LOSS',
            triggerPrice: new Decimal(stopPrice),
            triggerPercentage: configuration.stopLoss.percentage ? new Decimal(configuration.stopLoss.percentage) : null,
            positionPercentage: new Decimal(100), // Stop loss covers remaining position
            tokenAmount: position.quantity
          }
        });
      }

      // Create trailing stop trigger if enabled
      if (configuration.trailingStop.enabled) {
        const currentPrice = position.currentPrice.toNumber();
        const distance = configuration.trailingStop.distancePercentage ?
          currentPrice * configuration.trailingStop.distancePercentage / 100 :
          configuration.trailingStop.distanceAmount!;

        await tx.exitTrigger.create({
          data: {
            strategyId: strategy.id,
            type: 'TRAILING_STOP',
            triggerPrice: new Decimal(currentPrice - distance),
            positionPercentage: new Decimal(100), // Trailing stop covers remaining position
            tokenAmount: position.quantity
          }
        });
      }

      return strategy.id;
    });
  }

  async getExitStrategy(strategyId: string): Promise<(ExitStrategy & { triggers: ExitTrigger[] }) | null> {
    return await this.prisma.exitStrategy.findUnique({
      where: { id: strategyId },
      include: { triggers: true }
    });
  }

  async getExitStrategyByPosition(positionId: string): Promise<(ExitStrategy & { triggers: ExitTrigger[] }) | null> {
    return await this.prisma.exitStrategy.findUnique({
      where: { positionId },
      include: { triggers: true }
    });
  }

  async updateExitStrategy(
    strategyId: string,
    configuration: Partial<ExitStrategyConfiguration>
  ): Promise<void> {
    const strategy = await this.getExitStrategy(strategyId);
    if (!strategy) {
      throw new Error(`Exit strategy ${strategyId} not found`);
    }

    // Merge with existing configuration
    const currentConfig = this.strategyToConfiguration(strategy);
    const newConfig = { ...currentConfig, ...configuration };

    // Validate merged configuration
    const validation = this.validateConfiguration(newConfig);
    if (!validation.isValid) {
      throw new ValidationError(
        'Invalid exit strategy configuration',
        validation.errors
      );
    }

    await this.prisma.$transaction(async (tx) => {
      // Update strategy
      await tx.exitStrategy.update({
        where: { id: strategyId },
        data: {
          takeProfitTiers: JSON.parse(JSON.stringify(newConfig.takeProfitTiers)),
          stopLossEnabled: newConfig.stopLoss.enabled,
          stopLossPercentage: newConfig.stopLoss.percentage ? new Decimal(newConfig.stopLoss.percentage) : null,
          stopLossPrice: newConfig.stopLoss.price ? new Decimal(newConfig.stopLoss.price) : null,
          trailingStopEnabled: newConfig.trailingStop.enabled,
          trailingStopDistancePercentage: newConfig.trailingStop.distancePercentage ? new Decimal(newConfig.trailingStop.distancePercentage) : null,
          trailingStopDistanceAmount: newConfig.trailingStop.distanceAmount ? new Decimal(newConfig.trailingStop.distanceAmount) : null,
          moonBagEnabled: newConfig.moonBag.enabled,
          moonBagPercentage: new Decimal(newConfig.moonBag.percentage)
        }
      });

      // Delete existing triggers
      await tx.exitTrigger.deleteMany({
        where: {
          strategyId,
          status: 'PENDING' // Only delete pending triggers
        }
      });

      // Recreate triggers based on new configuration
      // This would need position data for accurate calculations
      // Implementation would be similar to createExitStrategy
    });
  }

  async deleteExitStrategy(strategyId: string): Promise<void> {
    await this.prisma.exitStrategy.delete({
      where: { id: strategyId }
    });
  }

  // Helper method to convert database strategy to configuration
  private strategyToConfiguration(strategy: ExitStrategy & { triggers: ExitTrigger[] }): ExitStrategyConfiguration {
    return {
      takeProfitTiers: Array.isArray(strategy.takeProfitTiers)
        ? strategy.takeProfitTiers as unknown as TakeProfitTier[]
        : [],
      stopLoss: {
        enabled: strategy.stopLossEnabled,
        percentage: strategy.stopLossPercentage?.toNumber(),
        price: strategy.stopLossPrice?.toNumber()
      },
      trailingStop: {
        enabled: strategy.trailingStopEnabled,
        distancePercentage: strategy.trailingStopDistancePercentage?.toNumber(),
        distanceAmount: strategy.trailingStopDistanceAmount?.toNumber(),
        highestPrice: strategy.trailingStopHighestPrice?.toNumber(),
        currentStopPrice: strategy.trailingStopCurrentStopPrice?.toNumber()
      },
      moonBag: {
        enabled: strategy.moonBagEnabled,
        percentage: strategy.moonBagPercentage?.toNumber() ?? 5.0
      }
    };
  }
}

export class ExitStrategyPresetModel {
  constructor(private prisma: PrismaClient) {}

  async createPreset(
    name: string,
    configuration: ExitStrategyConfiguration,
    description?: string,
    isDefault = false
  ): Promise<string> {
    // Validate configuration
    const validator = new ExitStrategyModel(this.prisma);
    const validation = validator.validateConfiguration(configuration);
    if (!validation.isValid) {
      throw new ValidationError(
        'Invalid preset configuration',
        validation.errors
      );
    }

    // If setting as default, unset other defaults
    if (isDefault) {
      await this.prisma.exitStrategyPreset.updateMany({
        where: { isDefault: true },
        data: { isDefault: false }
      });
    }

    const preset = await this.prisma.exitStrategyPreset.create({
      data: {
        name,
        description,
        isDefault,
        configuration: JSON.parse(JSON.stringify(configuration))
      }
    });

    return preset.id;
  }

  async getPreset(presetId: string): Promise<ExitStrategyPreset | null> {
    return await this.prisma.exitStrategyPreset.findUnique({
      where: { id: presetId }
    });
  }

  async getAllPresets(): Promise<ExitStrategyPreset[]> {
    return await this.prisma.exitStrategyPreset.findMany({
      orderBy: [
        { isDefault: 'desc' },
        { name: 'asc' }
      ]
    });
  }

  async getDefaultPreset(): Promise<ExitStrategyPreset | null> {
    return await this.prisma.exitStrategyPreset.findFirst({
      where: { isDefault: true }
    });
  }

  async updatePreset(
    presetId: string,
    updates: {
      name?: string;
      description?: string;
      configuration?: ExitStrategyConfiguration;
      isDefault?: boolean;
    }
  ): Promise<void> {
    // Validate configuration if provided
    if (updates.configuration) {
      const validator = new ExitStrategyModel(this.prisma);
      const validation = validator.validateConfiguration(updates.configuration);
      if (!validation.isValid) {
        throw new ValidationError(
          'Invalid preset configuration',
          validation.errors
        );
      }
    }

    // If setting as default, unset other defaults
    if (updates.isDefault) {
      await this.prisma.exitStrategyPreset.updateMany({
        where: {
          isDefault: true,
          id: { not: presetId }
        },
        data: { isDefault: false }
      });
    }

    await this.prisma.exitStrategyPreset.update({
      where: { id: presetId },
      data: {
        ...updates,
        configuration: updates.configuration ? JSON.parse(JSON.stringify(updates.configuration)) : undefined
      }
    });
  }

  async deletePreset(presetId: string): Promise<void> {
    await this.prisma.exitStrategyPreset.delete({
      where: { id: presetId }
    });
  }
}
