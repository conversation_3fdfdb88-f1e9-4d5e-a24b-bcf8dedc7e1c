import { Request, Response, NextFunction } from 'express';
import { ZodError } from 'zod';
import { apiLogger } from '../lib/logger';
import { ApiError } from '@shared/types';

// Custom API Error class
export class CustomApiError extends Error {
  public statusCode: number;
  public code: string;
  public details?: any;

  constructor(statusCode: number, code: string, message: string, details?: any) {
    super(message);
    this.name = 'CustomApiError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
  }
}

// Error handler middleware
export function errorHandler(
  err: any,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  apiLogger.error({
    error: {
      name: err.name,
      message: err.message,
      stack: err.stack,
    },
    method: req.method,
    url: req.originalUrl,
    userAgent: req.get('User-Agent'),
  }, 'API Error occurred');

  // Handle Zod validation errors
  if (err instanceof ZodError) {
    const apiError: ApiError = {
      code: 'VALIDATION_ERROR',
      message: 'Invalid request data',
      details: err.errors.map(e => ({
        path: e.path.join('.'),
        message: e.message,
        code: e.code,
      })),
    };

    res.status(400).json({
      success: false,
      error: apiError.code,
      message: apiError.message,
      details: apiError.details,
      timestamp: new Date().toISOString(),
    });
    return;
  }

  // Handle custom API errors
  if (err instanceof CustomApiError) {
    const apiError: ApiError = {
      code: err.code,
      message: err.message,
      details: err.details,
    };

    res.status(err.statusCode).json({
      success: false,
      error: apiError.code,
      message: apiError.message,
      details: apiError.details,
      timestamp: new Date().toISOString(),
    });
    return;
  }

  // Handle Prisma errors
  if (err.code === 'P2002') {
    res.status(409).json({
      success: false,
      error: 'CONFLICT',
      message: 'Resource already exists',
      timestamp: new Date().toISOString(),
    });
    return;
  }

  if (err.code === 'P2025') {
    res.status(404).json({
      success: false,
      error: 'NOT_FOUND',
      message: 'Resource not found',
      timestamp: new Date().toISOString(),
    });
    return;
  }

  // Handle Redis connection errors
  if (err.code === 'ECONNREFUSED' || err.code === 'ENOTFOUND') {
    res.status(503).json({
      success: false,
      error: 'SERVICE_UNAVAILABLE',
      message: 'External service temporarily unavailable',
      timestamp: new Date().toISOString(),
    });
    return;
  }

  // Handle rate limiting errors
  if (err.statusCode === 429 || err.message?.includes('Too many requests')) {
    res.status(429).json({
      success: false,
      error: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests, please try again later',
      timestamp: new Date().toISOString(),
    });
    return;
  }

  // Default server error
  res.status(500).json({
    success: false,
    error: 'INTERNAL_SERVER_ERROR',
    message: process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : err.message || 'An unexpected error occurred',
    timestamp: new Date().toISOString(),
    ...(process.env.NODE_ENV !== 'production' && { stack: err.stack }),
  });
}

// Async error wrapper
export function asyncHandler<T extends Request = Request, U extends Response = Response>(
  fn: (req: T, res: U, next: NextFunction) => Promise<any>
) {
  return (req: T, res: U, next: NextFunction): void => {
    fn(req, res, next).catch(next);
  };
}