/**
 * Data Retention Policy Middleware
 * Implements automated data cleanup and retention policies
 * for GDPR compliance and database optimization
 */

import { Request, Response, NextFunction } from 'express';
import { apiLogger } from '../lib/logger';
import { PrismaClient } from '@prisma/client';
import cron from 'node-cron';

export interface RetentionPolicy {
  table: string;
  retentionPeriodDays: number;
  dateField: string;
  conditions?: Record<string, any>;
  archiveBeforeDelete?: boolean;
}

export interface DataRetentionConfig {
  policies: RetentionPolicy[];
  batchSize: number;
  maxDeletionsPerRun: number;
  dryRun: boolean;
  enabled: boolean;
}

export class DataRetentionManager {
  private prisma: PrismaClient;
  private config: DataRetentionConfig;
  private isRunning = false;

  constructor(prisma: PrismaClient, config: DataRetentionConfig) {
    this.prisma = prisma;
    this.config = config;
  }

  /**
   * Default retention policies for the application
   */
  static getDefaultPolicies(): RetentionPolicy[] {
    return [
      {
        table: 'trade_logs',
        retentionPeriodDays: 90, // 3 months
        dateField: 'created_at',
        conditions: { status: 'completed' },
        archiveBeforeDelete: true,
      },
      {
        table: 'api_logs',
        retentionPeriodDays: 30, // 1 month
        dateField: 'timestamp',
        archiveBeforeDelete: false,
      },
      {
        table: 'user_sessions',
        retentionPeriodDays: 7, // 1 week for expired sessions
        dateField: 'expires_at',
        conditions: { is_active: false },
        archiveBeforeDelete: false,
      },
      {
        table: 'price_history',
        retentionPeriodDays: 365, // 1 year
        dateField: 'timestamp',
        archiveBeforeDelete: true,
      },
      {
        table: 'error_logs',
        retentionPeriodDays: 60, // 2 months
        dateField: 'created_at',
        archiveBeforeDelete: false,
      },
      {
        table: 'audit_logs',
        retentionPeriodDays: 2555, // 7 years for compliance
        dateField: 'created_at',
        archiveBeforeDelete: true,
      },
    ];
  }

  /**
   * Execute retention policies
   */
  async executeRetentionPolicies(): Promise<void> {
    if (this.isRunning) {
      apiLogger.warn('Data retention cleanup already running, skipping');
      return;
    }

    if (!this.config.enabled) {
      apiLogger.info('Data retention cleanup is disabled');
      return;
    }

    this.isRunning = true;
    const startTime = Date.now();

    try {
      apiLogger.info(
        { 
          policiesCount: this.config.policies.length,
          dryRun: this.config.dryRun 
        },
        'Starting data retention cleanup'
      );

      const results: Array<{ policy: string; deletedCount: number; archivedCount: number }> = [];

      for (const policy of this.config.policies) {
        try {
          const result = await this.executePolicy(policy);
          results.push({
            policy: policy.table,
            deletedCount: result.deleted,
            archivedCount: result.archived,
          });
        } catch (error) {
          apiLogger.error(
            { error, policy: policy.table },
            'Failed to execute retention policy'
          );
        }
      }

      const totalDeleted = results.reduce((sum, r) => sum + r.deletedCount, 0);
      const totalArchived = results.reduce((sum, r) => sum + r.archivedCount, 0);

      apiLogger.info(
        {
          results,
          totalDeleted,
          totalArchived,
          duration: Date.now() - startTime,
          dryRun: this.config.dryRun,
        },
        'Data retention cleanup completed'
      );
    } catch (error) {
      apiLogger.error({ error }, 'Data retention cleanup failed');
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Execute a single retention policy
   */
  private async executePolicy(policy: RetentionPolicy): Promise<{ deleted: number; archived: number }> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - policy.retentionPeriodDays);

    apiLogger.info(
      {
        table: policy.table,
        cutoffDate,
        retentionDays: policy.retentionPeriodDays,
      },
      'Executing retention policy'
    );

    // Build the where clause
    const whereClause: any = {
      [policy.dateField]: {
        lt: cutoffDate,
      },
    };

    if (policy.conditions) {
      Object.assign(whereClause, policy.conditions);
    }

    let deletedCount = 0;
    let archivedCount = 0;

    try {
      // First, get count of records to be affected
      const totalCount = await (this.prisma as any)[policy.table].count({
        where: whereClause,
      });

      if (totalCount === 0) {
        apiLogger.info({ table: policy.table }, 'No records found for retention cleanup');
        return { deleted: 0, archived: 0 };
      }

      if (totalCount > this.config.maxDeletionsPerRun) {
        apiLogger.warn(
          {
            table: policy.table,
            totalCount,
            maxDeletions: this.config.maxDeletionsPerRun,
          },
          'Too many records for single run, will process in batches'
        );
      }

      // Process in batches
      let processedCount = 0;
      while (processedCount < Math.min(totalCount, this.config.maxDeletionsPerRun)) {
        const batchSize = Math.min(
          this.config.batchSize,
          this.config.maxDeletionsPerRun - processedCount,
          totalCount - processedCount
        );

        // Get batch of records
        const records = await (this.prisma as any)[policy.table].findMany({
          where: whereClause,
          take: batchSize,
          orderBy: { [policy.dateField]: 'asc' },
        });

        if (records.length === 0) break;

        // Archive if required
        if (policy.archiveBeforeDelete) {
          const archiveResult = await this.archiveRecords(policy.table, records);
          archivedCount += archiveResult;
        }

        // Delete records (or simulate in dry run)
        if (this.config.dryRun) {
          apiLogger.info(
            { table: policy.table, count: records.length },
            'DRY RUN: Would delete records'
          );
          deletedCount += records.length;
        } else {
          const deleteResult = await (this.prisma as any)[policy.table].deleteMany({
            where: {
              id: { in: records.map((r: any) => r.id) },
            },
          });
          deletedCount += deleteResult.count;
        }

        processedCount += records.length;

        // Small delay between batches to reduce database load
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      apiLogger.info(
        {
          table: policy.table,
          deletedCount,
          archivedCount,
          totalCount,
        },
        'Retention policy completed'
      );

      return { deleted: deletedCount, archived: archivedCount };
    } catch (error) {
      apiLogger.error(
        { error, table: policy.table },
        'Failed to execute retention policy'
      );
      throw error;
    }
  }

  /**
   * Archive records before deletion
   */
  private async archiveRecords(tableName: string, records: any[]): Promise<number> {
    if (records.length === 0) return 0;

    try {
      const archiveTableName = `${tableName}_archive`;
      
      if (this.config.dryRun) {
        apiLogger.info(
          { table: tableName, count: records.length },
          'DRY RUN: Would archive records'
        );
        return records.length;
      }

      // Create archive table if it doesn't exist (simplified approach)
      // In production, archive tables should be pre-created with proper schema
      await this.ensureArchiveTableExists(tableName, archiveTableName);

      // Insert records into archive table
      const archiveRecords = records.map(record => ({
        ...record,
        archived_at: new Date(),
        original_id: record.id,
      }));

      await (this.prisma as any)[`${tableName}_archive`].createMany({
        data: archiveRecords,
        skipDuplicates: true,
      });

      apiLogger.info(
        { table: tableName, archivedCount: records.length },
        'Records archived successfully'
      );

      return records.length;
    } catch (error) {
      apiLogger.error(
        { error, table: tableName, recordCount: records.length },
        'Failed to archive records'
      );
      throw error;
    }
  }

  /**
   * Ensure archive table exists (placeholder implementation)
   */
  private async ensureArchiveTableExists(originalTable: string, archiveTable: string): Promise<void> {
    // This is a placeholder - in production you would use proper schema migrations
    apiLogger.info(
      { originalTable, archiveTable },
      'Archive table existence check (implement proper schema migration)'
    );
  }

  /**
   * Schedule retention cleanup jobs
   */
  scheduleRetentionJobs(): void {
    if (!this.config.enabled) {
      apiLogger.info('Data retention scheduling is disabled');
      return;
    }

    // Run daily at 2 AM
    cron.schedule('0 2 * * *', async () => {
      try {
        await this.executeRetentionPolicies();
      } catch (error) {
        apiLogger.error({ error }, 'Scheduled retention cleanup failed');
      }
    }, {
      scheduled: true,
      timezone: 'UTC',
    });

    // Run weekly deep cleanup on Sundays at 3 AM
    cron.schedule('0 3 * * 0', async () => {
      try {
        await this.performDeepCleanup();
      } catch (error) {
        apiLogger.error({ error }, 'Scheduled deep cleanup failed');
      }
    }, {
      scheduled: true,
      timezone: 'UTC',
    });

    apiLogger.info('Data retention jobs scheduled');
  }

  /**
   * Perform deep cleanup operations
   */
  private async performDeepCleanup(): Promise<void> {
    apiLogger.info('Starting deep cleanup operations');

    try {
      // Vacuum analyze for better performance (PostgreSQL specific)
      if (process.env.DATABASE_TYPE === 'postgresql') {
        await this.vacuumAnalyze();
      }

      // Clean up orphaned records
      await this.cleanupOrphanedRecords();

      // Update statistics
      await this.updateRetentionStatistics();

      apiLogger.info('Deep cleanup operations completed');
    } catch (error) {
      apiLogger.error({ error }, 'Deep cleanup operations failed');
    }
  }

  /**
   * Vacuum analyze database tables (PostgreSQL specific)
   */
  private async vacuumAnalyze(): Promise<void> {
    try {
      const tables = this.config.policies.map(p => p.table);
      
      for (const table of tables) {
        if (!this.config.dryRun) {
          await this.prisma.$executeRawUnsafe(`VACUUM ANALYZE ${table}`);
        }
        apiLogger.info({ table }, 'Table vacuum analyze completed');
      }
    } catch (error) {
      apiLogger.error({ error }, 'Vacuum analyze failed');
    }
  }

  /**
   * Clean up orphaned records
   */
  private async cleanupOrphanedRecords(): Promise<void> {
    apiLogger.info('Cleaning up orphaned records (implement based on your schema)');
    // Implement based on your specific database schema and relationships
  }

  /**
   * Update retention statistics
   */
  private async updateRetentionStatistics(): Promise<void> {
    try {
      const stats: Record<string, any> = {};
      
      for (const policy of this.config.policies) {
        const count = await (this.prisma as any)[policy.table].count();
        stats[policy.table] = count;
      }

      apiLogger.info({ stats }, 'Database table statistics updated');
    } catch (error) {
      apiLogger.error({ error }, 'Failed to update retention statistics');
    }
  }

  /**
   * Get retention status for monitoring
   */
  async getRetentionStatus(): Promise<{
    isRunning: boolean;
    lastRun?: Date;
    policies: RetentionPolicy[];
    tableStats: Record<string, number>;
  }> {
    const tableStats: Record<string, number> = {};
    
    try {
      for (const policy of this.config.policies) {
        tableStats[policy.table] = await (this.prisma as any)[policy.table].count();
      }
    } catch (error) {
      apiLogger.error({ error }, 'Failed to get table statistics');
    }

    return {
      isRunning: this.isRunning,
      policies: this.config.policies,
      tableStats,
    };
  }
}

// Middleware to attach retention manager to request
export function attachRetentionManager(retentionManager: DataRetentionManager) {
  return (req: Request, res: Response, next: NextFunction): void => {
    req.retentionManager = retentionManager;
    next();
  };
}

// Default configuration
export const defaultRetentionConfig: DataRetentionConfig = {
  policies: DataRetentionManager.getDefaultPolicies(),
  batchSize: 1000,
  maxDeletionsPerRun: 10000,
  dryRun: process.env.NODE_ENV !== 'production',
  enabled: process.env.ENABLE_DATA_RETENTION === 'true',
};

declare global {
  namespace Express {
    interface Request {
      retentionManager?: DataRetentionManager;
    }
  }
}