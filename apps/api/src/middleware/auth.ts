import { Request, Response, NextFunction } from 'express';
import { config, securityConfig } from '../lib/config';
import { apiLogger } from '../lib/logger';
import { CustomApiError } from './errorHandler';

// Session interface is defined in ../types/session.d.ts

// Authentication middleware for routes (not session config)
// Session configuration is handled in server.ts

// Authentication middleware for single-user setup
export function authMiddleware(req: Request, res: Response, next: NextFunction): void {
  // Check if session is authenticated
  if (!req.session.authenticated) {
    // For development and test, auto-authenticate if no session exists
    if (config.NODE_ENV === 'development' || config.NODE_ENV === 'test') {
      req.session.authenticated = true;
      req.session.loginTime = new Date();
      req.session.lastActivity = Date.now();
      
      apiLogger.info({
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }, 'Auto-authenticated in development mode');
      
      next();
      return;
    }

    apiLogger.warn({
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
    }, 'Unauthenticated request blocked');

    res.status(401).json({
      success: false,
      error: 'UNAUTHORIZED',
      message: 'Authentication required',
      timestamp: new Date().toISOString(),
    });
    return;
  }

  // Update last activity
  req.session.lastActivity = Date.now();
  
  // Check for session timeout (4 hours of inactivity)
  const sessionTimeout = 4 * 60 * 60 * 1000; // 4 hours
  if (req.session.lastActivity && Date.now() - req.session.lastActivity > sessionTimeout) {
    req.session.destroy((err) => {
      if (err) {
        apiLogger.error({ error: err }, 'Failed to destroy expired session');
      }
    });

    res.status(401).json({
      success: false,
      error: 'SESSION_EXPIRED',
      message: 'Session has expired',
      timestamp: new Date().toISOString(),
    });
    return;
  }

  next();
}

// Optional authentication (for public endpoints that benefit from user context)
export function optionalAuth(req: Request, res: Response, next: NextFunction): void {
  if (req.session.authenticated) {
    req.session.lastActivity = Date.now();
  }
  next();
}

// Login endpoint for manual authentication
export function login(req: Request, res: Response): void {
  // In a real application, you would validate credentials here
  // For single-user setup, we'll use a simple approach
  
  const { password } = req.body;
  
  // Simple password check (replace with proper authentication)
  const validPassword = securityConfig.adminPassword;
  
  if (password !== validPassword) {
    apiLogger.warn({
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }, 'Failed login attempt');

    throw new CustomApiError(401, 'INVALID_CREDENTIALS', 'Invalid password');
  }

  req.session.authenticated = true;
  req.session.loginTime = new Date();
  req.session.lastActivity = Date.now();

  apiLogger.info({
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  }, 'User successfully logged in');

  res.json({
    success: true,
    message: 'Successfully authenticated',
    timestamp: new Date().toISOString(),
  });
}

// Logout endpoint
export function logout(req: Request, res: Response): void {
  req.session.destroy((err) => {
    if (err) {
      apiLogger.error({ error: err }, 'Failed to destroy session during logout');
      throw new CustomApiError(500, 'LOGOUT_FAILED', 'Failed to logout');
    }

    apiLogger.info({
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }, 'User successfully logged out');

    res.json({
      success: true,
      message: 'Successfully logged out',
      timestamp: new Date().toISOString(),
    });
  });
}

// Session status endpoint
export function getSessionStatus(req: Request, res: Response): void {
  res.json({
    success: true,
    data: {
      authenticated: !!req.session.authenticated,
      loginTime: req.session.loginTime || null,
      lastActivity: req.session.lastActivity || null,
    },
    timestamp: new Date().toISOString(),
  });
}