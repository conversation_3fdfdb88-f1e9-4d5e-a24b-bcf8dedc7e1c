import { Request, Response, NextFunction } from 'express';
import { ZodSchema, z, ZodError } from 'zod';
import { apiLogger } from '../lib/logger';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import DOMPurify from 'isomorphic-dompurify';

// Enhanced validation middleware factory with error handling
export function validateSchema(schema: {
  body?: ZodSchema;
  query?: ZodSchema;
  params?: ZodSchema;
  headers?: ZodSchema;
}) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const validationResults: { [key: string]: any } = {};

      // Validate request body
      if (schema.body) {
        validationResults.body = schema.body.parse(req.body);
        req.body = validationResults.body;
      }

      // Validate query parameters
      if (schema.query) {
        validationResults.query = schema.query.parse(req.query);
        req.query = validationResults.query;
      }

      // Validate route parameters
      if (schema.params) {
        validationResults.params = schema.params.parse(req.params);
        req.params = validationResults.params;
      }

      // Validate headers
      if (schema.headers) {
        validationResults.headers = schema.headers.parse(req.headers);
      }

      // Store validation results for debugging
      req.validationResults = validationResults;

      next();
    } catch (error) {
      if (error instanceof ZodError) {
        // Format Zod validation errors for better user experience
        const formattedError = {
          type: 'validation_error',
          message: 'Request validation failed',
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code,
          })),
        };

        apiLogger.warn({
          error: formattedError,
          path: req.path,
          method: req.method,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
        }, 'Request validation failed');

        res.status(400).json(formattedError);
        return;
      }

      apiLogger.error({
        error,
        path: req.path,
        method: req.method,
      }, 'Unexpected validation error');
      
      next(error);
    }
  };
}

// Enhanced common validation schemas
export const commonSchemas = {
  // Pagination schema with improved validation
  pagination: z.object({
    page: z.string().optional().transform((val) => val ? parseInt(val, 10) : 1),
    limit: z.string().optional().transform((val) => val ? parseInt(val, 10) : 20),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
  }).refine((data) => data.page > 0, {
    message: 'Page must be greater than 0',
    path: ['page'],
  }).refine((data) => data.limit > 0 && data.limit <= 100, {
    message: 'Limit must be between 1 and 100',
    path: ['limit'],
  }),

  // ID parameter schema with multiple format support
  id: z.object({
    id: z.string().refine(
      (val) => z.string().uuid().safeParse(val).success || /^[a-zA-Z0-9_-]{21}$/.test(val),
      'Invalid ID format (must be UUID or nanoid)'
    ),
  }),

  // Enhanced token address schema (Solana address format)
  tokenAddress: z.object({
    tokenAddress: z.string().regex(
      /^[1-9A-HJ-NP-Za-km-z]{32,44}$/,
      'Invalid Solana token address format'
    ),
  }),

  // Trading pair validation
  tradingPair: z.object({
    baseToken: z.string().regex(/^[1-9A-HJ-NP-Za-km-z]{32,44}$/, 'Invalid base token address'),
    quoteToken: z.string().regex(/^[1-9A-HJ-NP-Za-km-z]{32,44}$/, 'Invalid quote token address'),
  }),

  // Amount validation with precision handling
  amount: z.object({
    amount: z.string().refine(
      (val) => {
        const num = parseFloat(val);
        return !isNaN(num) && num > 0 && num < 1e12;
      },
      'Amount must be a positive number less than 1 trillion'
    ),
  }),

  // API key validation for external services
  apiKey: z.string().min(32, 'API key must be at least 32 characters').max(256, 'API key too long'),

  // Common headers validation
  authHeaders: z.object({
    authorization: z.string().regex(/^Bearer .+/, 'Invalid authorization header format'),
    'content-type': z.string().optional(),
    'x-api-version': z.string().optional(),
  }),
};

// Enhanced request sanitization middleware with XSS protection
export function sanitizeRequest(req: Request, res: Response, next: NextFunction): void {
  // Enhanced string sanitization with XSS protection
  const sanitizeString = (str: string): string => {
    // Remove null bytes and control characters
    let sanitized = str.replace(/[\0-\x1F\x7F]/g, '');
    
    // Use DOMPurify for XSS protection
    sanitized = DOMPurify.sanitize(sanitized, { ALLOWED_TAGS: [] });
    
    // Additional sanitization for common attack vectors
    sanitized = sanitized
      .replace(/javascript:/gi, '')
      .replace(/data:/gi, '')
      .replace(/vbscript:/gi, '')
      .replace(/onload=/gi, '')
      .replace(/onerror=/gi, '');
    
    return sanitized.trim();
  };

  // Recursively sanitize object properties with size limits
  const sanitizeObject = (obj: any, depth = 0): any => {
    // Prevent deep nesting attacks
    if (depth > 10) {
      apiLogger.warn({ path: req.path, depth }, 'Object nesting too deep, truncating');
      return '[TRUNCATED: Too deep]';
    }

    if (typeof obj === 'string') {
      // Limit string length to prevent memory attacks
      if (obj.length > 10000) {
        apiLogger.warn({ path: req.path, length: obj.length }, 'String too long, truncating');
        obj = obj.substring(0, 10000);
      }
      return sanitizeString(obj);
    }
    
    if (Array.isArray(obj)) {
      // Limit array size
      if (obj.length > 1000) {
        apiLogger.warn({ path: req.path, length: obj.length }, 'Array too large, truncating');
        obj = obj.slice(0, 1000);
      }
      return obj.map(item => sanitizeObject(item, depth + 1));
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized: any = {};
      let keyCount = 0;
      
      for (const [key, value] of Object.entries(obj)) {
        // Limit number of object keys
        if (keyCount >= 100) {
          apiLogger.warn({ path: req.path, keyCount }, 'Too many object keys, truncating');
          break;
        }
        
        const sanitizedKey = sanitizeString(key);
        if (sanitizedKey && sanitizedKey.length > 0) {
          sanitized[sanitizedKey] = sanitizeObject(value, depth + 1);
          keyCount++;
        }
      }
      return sanitized;
    }
    
    return obj;
  };

  try {
    const originalBody = req.body ? JSON.stringify(req.body) : null;
    const originalQuery = req.query ? JSON.stringify(req.query) : null;

    // Sanitize request body
    if (req.body) {
      req.body = sanitizeObject(req.body);
    }

    // Sanitize query parameters
    if (req.query) {
      req.query = sanitizeObject(req.query);
    }

    // Log if significant sanitization occurred
    const sanitizedBody = req.body ? JSON.stringify(req.body) : null;
    const sanitizedQuery = req.query ? JSON.stringify(req.query) : null;
    
    if ((originalBody && sanitizedBody && originalBody !== sanitizedBody) ||
        (originalQuery && sanitizedQuery && originalQuery !== sanitizedQuery)) {
      apiLogger.info({
        path: req.path,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      }, 'Request sanitization applied');
    }
    
    next();
  } catch (error) {
    apiLogger.error({
      error,
      path: req.path,
      method: req.method,
      ip: req.ip,
    }, 'Request sanitization failed');
    
    next(error);
  }
}

// Rate limiting configurations
export const rateLimitConfigs = {
  // General API rate limiting
  general: rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000, // Limit each IP to 1000 requests per windowMs
    message: {
      error: 'Too many requests from this IP, please try again later.',
      retryAfter: '15 minutes'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // Skip rate limiting for health checks and internal services
      return req.path === '/health' || req.get('X-Internal-Service') === 'true';
    },
  }),

  // Strict rate limiting for trading endpoints
  trading: rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 60, // 60 requests per minute
    message: {
      error: 'Trading API rate limit exceeded. Please slow down.',
      retryAfter: '1 minute'
    },
    standardHeaders: true,
    legacyHeaders: false,
  }),

  // Auth rate limiting
  auth: rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // 10 attempts per 15 minutes
    message: {
      error: 'Too many authentication attempts, please try again later.',
      retryAfter: '15 minutes'
    },
    standardHeaders: true,
    legacyHeaders: false,
  }),
};

// Enhanced security headers middleware
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", 'data:', 'https:'],
      connectSrc: ["'self'", 'https://api.mainnet-beta.solana.com'],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
  crossOriginResourcePolicy: { policy: 'cross-origin' },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },
});

// Request size limiting middleware
export function limitRequestSize(maxSize: number = 1024 * 1024) { // 1MB default
  return (req: Request, res: Response, next: NextFunction): void => {
    const contentLength = parseInt(req.get('Content-Length') || '0', 10);
    
    if (contentLength > maxSize) {
      apiLogger.warn({
        contentLength,
        maxSize,
        path: req.path,
        ip: req.ip,
      }, 'Request size limit exceeded');
      
      res.status(413).json({
        error: 'Request entity too large',
        maxSize: `${maxSize} bytes`,
        receivedSize: `${contentLength} bytes`,
      });
      return;
    }
    
    next();
  };
}