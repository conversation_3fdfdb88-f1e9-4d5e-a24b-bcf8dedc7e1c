import rateLimit from 'express-rate-limit';
import { config } from '../lib/config';
import { apiLogger } from '../lib/logger';

// General API rate limiting
export const apiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Limit each IP to 1000 requests per windowMs
  message: {
    success: false,
    error: 'RATE_LIMIT_EXCEEDED',
    message: 'Too many requests from this IP, please try again later.',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    apiLogger.warn({
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
    }, 'Rate limit exceeded for general API');
    
    res.status(429).json({
      success: false,
      error: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests from this IP, please try again later.',
      timestamp: new Date().toISOString(),
    });
  },
});

// Stricter rate limiting for trading endpoints
export const tradingRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 60, // Limit each IP to 60 trading requests per minute
  message: {
    success: false,
    error: 'TRADING_RATE_LIMIT_EXCEEDED',
    message: 'Too many trading requests, please wait before trying again.',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    apiLogger.warn({
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
      body: req.body,
    }, 'Trading rate limit exceeded');
    
    res.status(429).json({
      success: false,
      error: 'TRADING_RATE_LIMIT_EXCEEDED',
      message: 'Too many trading requests, please wait before trying again.',
      timestamp: new Date().toISOString(),
    });
  },
});

// Strict rate limiting for authentication endpoints
export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 auth requests per 15 minutes
  message: {
    success: false,
    error: 'AUTH_RATE_LIMIT_EXCEEDED',
    message: 'Too many authentication attempts, please try again later.',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    apiLogger.warn({
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
    }, 'Auth rate limit exceeded');
    
    res.status(429).json({
      success: false,
      error: 'AUTH_RATE_LIMIT_EXCEEDED',
      message: 'Too many authentication attempts, please try again later.',
      timestamp: new Date().toISOString(),
    });
  },
});