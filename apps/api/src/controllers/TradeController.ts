import { Request, Response } from 'express';
import { z } from 'zod';
import { apiLogger } from '../lib/logger';
import { CustomApiError, asyncHandler } from '../middleware/errorHandler';
import { addPriceMonitorJob, addNotificationJob } from '../jobs/queues';

// Request schemas
const QuoteRequestSchema = z.object({
  inputMint: z.string().length(44, 'Invalid input mint address'),
  outputMint: z.string().length(44, 'Invalid output mint address'),
  amount: z.number().positive('Amount must be positive'),
  slippageBps: z.number().min(1).max(1000).optional().default(100),
});

const BuyRequestSchema = z.object({
  tokenAddress: z.string().length(44, 'Invalid token address'),
  amountSol: z.number().positive('Amount must be positive'),
  slippageBps: z.number().min(1).max(1000).optional().default(100),
  quoteId: z.string().optional(),
});

export class TradeController {
  /**
   * Get Jupiter quote for token purchase
   * POST /api/trades/quote
   */
  static getQuote = asyncHandler(async (req: Request, res: Response) => {
    const { inputMint, outputMint, amount, slippageBps } = QuoteRequestSchema.parse(req.body);

    apiLogger.info({
      inputMint,
      outputMint,
      amount,
      slippageBps,
    }, 'Quote request received');

    try {
      // Mock quote response - will be replaced with actual Jupiter API call
      const mockQuote = {
        inputMint,
        outputMint,
        inAmount: amount.toString(),
        outAmount: (amount * 42735042.73).toString(), // Mock conversion rate
        otherAmountThreshold: (amount * 42735042.73 * 0.99).toString(), // 1% slippage
        swapMode: 'ExactIn' as const,
        slippageBps,
        priceImpactPct: '0.1',
        routePlan: [
          {
            swapInfo: {
              ammKey: 'mock-amm-key',
              label: 'Mock DEX',
              inputMint,
              outputMint,
              inAmount: amount.toString(),
              outAmount: (amount * 42735042.73).toString(),
              feeAmount: (amount * 0.003).toString(),
              feeMint: inputMint,
            },
            percent: 100,
          },
        ],
        quoteId: `quote_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
      };

      // Add price monitoring job for the target token
      await addPriceMonitorJob({
        tokenAddress: outputMint,
        source: 'jupiter',
        timestamp: Date.now(),
      });

      res.json({
        success: true,
        data: mockQuote,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      apiLogger.error({
        error,
        inputMint,
        outputMint,
        amount,
      }, 'Failed to get quote');
      
      throw new CustomApiError(500, 'QUOTE_FAILED', 'Failed to get trading quote');
    }
  });

  /**
   * Execute buy transaction
   * POST /api/trades/buy
   */
  static executeBuy = asyncHandler(async (req: Request, res: Response) => {
    const { tokenAddress, amountSol, slippageBps, quoteId } = BuyRequestSchema.parse(req.body);

    apiLogger.info({
      tokenAddress,
      amountSol,
      slippageBps,
      quoteId,
    }, 'Buy execution request received');

    try {
      // Mock buy execution - will be replaced with actual Jupiter integration
      const mockExecution = {
        signature: generateMockSignature(),
        amountSol,
        amountToken: amountSol * 42735042.73, // Mock conversion
        price: amountSol / (amountSol * 42735042.73),
        slippageActual: slippageBps * 0.8, // Usually less than requested
        fees: amountSol * 0.005, // 0.5% fees
        status: 'confirmed' as const,
        timestamp: new Date().toISOString(),
      };

      // Send notification about successful trade
      await addNotificationJob({
        type: 'trade_execution',
        recipient: 'telegram',
        title: 'Buy Order Executed',
        message: `Successfully bought ${mockExecution.amountToken.toFixed(2)} tokens for ${amountSol} SOL`,
        data: {
          tokenAddress,
          amountSol,
          amountToken: mockExecution.amountToken,
          signature: mockExecution.signature,
        },
      });

      res.json({
        success: true,
        data: mockExecution,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      apiLogger.error({
        error,
        tokenAddress,
        amountSol,
      }, 'Failed to execute buy order');
      
      // Send error notification
      await addNotificationJob({
        type: 'error',
        recipient: 'telegram',
        title: 'Buy Order Failed',
        message: `Failed to execute buy order for ${amountSol} SOL`,
        data: {
          tokenAddress,
          amountSol,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });

      throw new CustomApiError(500, 'BUY_FAILED', 'Failed to execute buy order');
    }
  });
}

// Generate mock Solana transaction signature
function generateMockSignature(): string {
  const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz123456789';
  let result = '';
  for (let i = 0; i < 88; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}