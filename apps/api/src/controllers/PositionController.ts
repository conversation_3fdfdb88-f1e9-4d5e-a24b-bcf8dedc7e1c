import { Request, Response } from 'express';
import { z } from 'zod';
import { api<PERSON>ogger } from '../lib/logger';
import { prisma } from '../lib/database';
import { CustomApiError, asyncHandler } from '../middleware/errorHandler';
import { addExitExecutionJob, addNotificationJob } from '../jobs/queues';
import { Decimal } from 'decimal.js';

// Request schemas
const GetPositionsQuerySchema = z.object({
  status: z.enum(['ACTIVE', 'CLOSED', 'PENDING_EXIT', 'ERROR']).optional(),
  page: z.string().optional().transform(val => val ? parseInt(val, 10) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val, 10) : 20),
});

const UpdatePositionSchema = z.object({
  exitStrategy: z.object({
    takeProfitTiers: z.array(z.object({
      percentage: z.number().min(1).max(100),
      priceTarget: z.number().positive(),
    })).optional(),
    stopLoss: z.object({
      priceTarget: z.number().positive(),
      isTrailing: z.boolean().optional().default(false),
      trailingDistance: z.number().positive().optional(),
    }).optional(),
    timeBasedExit: z.string().datetime().optional(),
  }).optional(),
});

const ClosePositionSchema = z.object({
  percentage: z.number().min(1).max(100).optional().default(100),
  reason: z.string().optional(),
});

export class PositionController {
  /**
   * Get positions with optional filtering
   * GET /api/positions
   */
  static getPositions = asyncHandler(async (req: Request, res: Response) => {
    const { status, page, limit } = GetPositionsQuerySchema.parse(req.query);

    apiLogger.info({
      status,
      page,
      limit,
    }, 'Get positions request received');

    try {
      const skip = (page - 1) * limit;

      const [positions, total] = await Promise.all([
        prisma.position.findMany({
          where: status ? { status } : undefined,
          include: {
            exitStrategies: {
              where: { isActive: true },
            },
            transactions: {
              orderBy: { createdAt: 'desc' },
              take: 5, // Latest 5 transactions per position
            },
          },
          orderBy: { entryTimestamp: 'desc' },
          skip,
          take: limit,
        }),
        prisma.position.count({
          where: status ? { status } : undefined,
        }),
      ]);

      // Format positions for response
      const formattedPositions = positions.map(position => ({
        id: position.id,
        tokenAddress: position.tokenAddress,
        tokenSymbol: position.tokenSymbol,
        tokenName: position.tokenName,
        entryPrice: position.entryPrice.toString(),
        currentPrice: position.currentPrice.toString(),
        quantity: position.quantity.toString(),
        entryAmountSol: position.entryAmountSol.toString(),
        currentValueSol: position.currentValueSol.toString(),
        pnlSol: position.pnlSol.toString(),
        pnlPercentage: position.pnlPercentage.toString(),
        status: position.status,
        entryTimestamp: position.entryTimestamp.toISOString(),
        lastUpdateTimestamp: position.lastUpdateTimestamp.toISOString(),
        exitTimestamp: position.exitTimestamp?.toISOString(),
        exitStrategies: position.exitStrategies,
        recentTransactions: position.transactions,
      }));

      res.json({
        success: true,
        data: {
          positions: formattedPositions,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit),
          },
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      apiLogger.error({
        error,
        status,
        page,
        limit,
      }, 'Failed to get positions');
      
      throw new CustomApiError(500, 'POSITIONS_FETCH_FAILED', 'Failed to fetch positions');
    }
  });

  /**
   * Get single position by ID
   * GET /api/positions/:id
   */
  static getPosition = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    apiLogger.info({ positionId: id }, 'Get position request received');

    try {
      const position = await prisma.position.findUnique({
        where: { id },
        include: {
          exitStrategies: true,
          transactions: {
            orderBy: { createdAt: 'desc' },
          },
        },
      });

      if (!position) {
        throw new CustomApiError(404, 'POSITION_NOT_FOUND', 'Position not found');
      }

      const formattedPosition = {
        id: position.id,
        tokenAddress: position.tokenAddress,
        tokenSymbol: position.tokenSymbol,
        tokenName: position.tokenName,
        entryPrice: position.entryPrice.toString(),
        currentPrice: position.currentPrice.toString(),
        quantity: position.quantity.toString(),
        entryAmountSol: position.entryAmountSol.toString(),
        currentValueSol: position.currentValueSol.toString(),
        pnlSol: position.pnlSol.toString(),
        pnlPercentage: position.pnlPercentage.toString(),
        status: position.status,
        entryTimestamp: position.entryTimestamp.toISOString(),
        lastUpdateTimestamp: position.lastUpdateTimestamp.toISOString(),
        exitTimestamp: position.exitTimestamp?.toISOString(),
        transactionSignature: position.transactionSignature,
        slippage: position.slippage.toString(),
        jupiterQuoteId: position.jupiterQuoteId,
        exitStrategies: position.exitStrategies,
        transactions: position.transactions,
      };

      res.json({
        success: true,
        data: formattedPosition,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      if (error instanceof CustomApiError) {
        throw error;
      }

      apiLogger.error({
        error,
        positionId: id,
      }, 'Failed to get position');
      
      throw new CustomApiError(500, 'POSITION_FETCH_FAILED', 'Failed to fetch position');
    }
  });

  /**
   * Update position and exit strategy
   * PATCH /api/positions/:id
   */
  static updatePosition = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { exitStrategy } = UpdatePositionSchema.parse(req.body);

    apiLogger.info({
      positionId: id,
      exitStrategy,
    }, 'Update position request received');

    try {
      // Verify position exists and is active
      const position = await prisma.position.findFirst({
        where: {
          id,
          status: 'ACTIVE',
        },
      });

      if (!position) {
        throw new CustomApiError(404, 'POSITION_NOT_FOUND', 'Active position not found');
      }

      if (exitStrategy) {
        // Create or update exit strategy
        const strategyData: any = {
          positionId: id,
          type: 'TAKE_PROFIT',
          isActive: true,
        };

        if (exitStrategy.takeProfitTiers) {
          strategyData.takeProfitTiers = exitStrategy.takeProfitTiers.map((tier, index) => ({
            id: `tier_${index + 1}`,
            percentage: tier.percentage,
            priceTarget: tier.priceTarget,
            isExecuted: false,
          }));
        }

        if (exitStrategy.stopLoss) {
          strategyData.stopLossConfig = {
            priceTarget: exitStrategy.stopLoss.priceTarget,
            isTrailing: exitStrategy.stopLoss.isTrailing,
            trailingDistance: exitStrategy.stopLoss.trailingDistance,
            isExecuted: false,
          };
        }

        if (exitStrategy.timeBasedExit) {
          strategyData.timeBasedExit = new Date(exitStrategy.timeBasedExit);
        }

        // Deactivate existing strategies
        await prisma.exitStrategy.updateMany({
          where: { positionId: id },
          data: { isActive: false },
        });

        // Create new strategy
        await prisma.exitStrategy.create({
          data: strategyData,
        });

        apiLogger.info({
          positionId: id,
          strategyData,
        }, 'Exit strategy updated for position');
      }

      // Get updated position
      const updatedPosition = await prisma.position.findUnique({
        where: { id },
        include: {
          exitStrategies: {
            where: { isActive: true },
          },
        },
      });

      res.json({
        success: true,
        data: updatedPosition,
        message: 'Position updated successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      if (error instanceof CustomApiError) {
        throw error;
      }

      apiLogger.error({
        error,
        positionId: id,
        exitStrategy,
      }, 'Failed to update position');
      
      throw new CustomApiError(500, 'POSITION_UPDATE_FAILED', 'Failed to update position');
    }
  });

  /**
   * Close position immediately
   * DELETE /api/positions/:id
   */
  static closePosition = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { percentage, reason } = ClosePositionSchema.parse(req.body);

    apiLogger.info({
      positionId: id,
      percentage,
      reason,
    }, 'Close position request received');

    try {
      // Verify position exists and is active
      const position = await prisma.position.findFirst({
        where: {
          id,
          status: 'ACTIVE',
        },
        include: {
          exitStrategies: {
            where: { isActive: true },
            take: 1,
          },
        },
      });

      if (!position) {
        throw new CustomApiError(404, 'POSITION_NOT_FOUND', 'Active position not found');
      }

      // Update position status to pending exit
      await prisma.position.update({
        where: { id },
        data: { status: 'PENDING_EXIT' },
      });

      // Add exit execution job
      await addExitExecutionJob({
        positionId: id,
        strategyId: position.exitStrategies[0]?.id || 'manual_close',
        triggerType: 'manual',
        percentage,
      });

      // Send notification
      await addNotificationJob({
        type: 'trade_execution',
        recipient: 'telegram',
        title: 'Position Close Requested',
        message: `Closing ${percentage}% of position in ${position.tokenSymbol}`,
        data: {
          positionId: id,
          tokenSymbol: position.tokenSymbol,
          percentage,
          reason,
        },
      });

      res.json({
        success: true,
        message: `Position close request submitted for ${percentage}% of holdings`,
        data: {
          positionId: id,
          percentage,
          status: 'PENDING_EXIT',
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      if (error instanceof CustomApiError) {
        throw error;
      }

      apiLogger.error({
        error,
        positionId: id,
        percentage,
      }, 'Failed to close position');
      
      throw new CustomApiError(500, 'POSITION_CLOSE_FAILED', 'Failed to close position');
    }
  });
}