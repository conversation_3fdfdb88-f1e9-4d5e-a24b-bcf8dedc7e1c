import { redis, getCache, setCache, incrementCounter } from './redis';
import { apiLogger } from './logger';

/**
 * Configuration for a rate limiter
 */
export interface RateLimitConfig {
  name: string;
  requestsPerSecond: number;
  requestsPerMinute?: number;
  requestsPerHour?: number;
  requestsPerDay?: number;
  burstCapacity?: number; // Maximum tokens in bucket
  refillRate?: number; // Tokens refilled per second
}

/**
 * Rate limit status information
 */
export interface RateLimitStatus {
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  retryAfter?: number; // Seconds to wait before next request
}

/**
 * Token bucket state
 */
interface TokenBucketState {
  tokens: number;
  lastRefill: number; // Timestamp in milliseconds
}

/**
 * Queued request information
 */
interface QueuedRequest {
  id: string;
  timestamp: number;
  resolve: (status: RateLimitStatus) => void;
  reject: (error: Error) => void;
}

/**
 * Token bucket algorithm implementation for rate limiting
 * Supports Redis-backed persistence and request queuing
 */
export class RateLimitManager {
  private readonly config: Required<RateLimitConfig>;
  private readonly logger = apiLogger.child({ service: 'RateLimitManager' });
  private readonly requestQueues = new Map<string, QueuedRequest[]>();
  private readonly processingQueues = new Set<string>();

  constructor(config: RateLimitConfig) {
    // Set defaults for optional config
    this.config = {
      ...config,
      requestsPerMinute: config.requestsPerMinute,
      requestsPerHour: config.requestsPerHour,
      requestsPerDay: config.requestsPerDay,
      burstCapacity: config.burstCapacity ?? config.requestsPerSecond * 10,
      refillRate: config.refillRate ?? config.requestsPerSecond
    };

    this.logger.info({ config: this.config }, 'RateLimitManager initialized');
  }

  /**
   * Check if a request is allowed and consume a token if so
   * @param identifier - Unique identifier for the client/provider
   * @param cost - Number of tokens to consume (default: 1)
   * @returns Promise resolving to rate limit status
   */
  async checkRateLimit(
    identifier: string, 
    cost: number = 1
  ): Promise<RateLimitStatus> {
    const bucketKey = this.getBucketKey(identifier);
    
    try {
      // Get current bucket state
      const bucketState = await this.getBucketState(bucketKey);
      
      // Refill tokens based on time elapsed
      const now = Date.now();
      const timeSinceRefill = (now - bucketState.lastRefill) / 1000; // Convert to seconds
      const tokensToAdd = Math.floor(timeSinceRefill * this.config.refillRate);
      
      let availableTokens = Math.min(
        bucketState.tokens + tokensToAdd,
        this.config.burstCapacity
      );

      // Check if request can be served
      if (availableTokens >= cost) {
        // Consume tokens
        availableTokens -= cost;
        
        // Update bucket state
        const newState: TokenBucketState = {
          tokens: availableTokens,
          lastRefill: now
        };
        
        await this.saveBucketState(bucketKey, newState);
        
        // Also check time-based limits
        const timeBasedStatus = await this.checkTimeBasedLimits(identifier);
        
        const status: RateLimitStatus = {
          allowed: timeBasedStatus.allowed,
          remaining: Math.min(availableTokens, timeBasedStatus.remaining),
          resetTime: this.getNextResetTime()
        };

        if (timeBasedStatus.allowed) {
          // Increment time-based counters
          await this.incrementTimeCounters(identifier);
        } else {
          // Refund tokens if time-based limit exceeded
          newState.tokens = Math.min(newState.tokens + cost, this.config.burstCapacity);
          await this.saveBucketState(bucketKey, newState);
          status.retryAfter = Math.ceil(timeBasedStatus.retryAfter || 60);
        }

        this.logger.debug({
          identifier,
          cost,
          status
        }, 'Rate limit check completed');

        return status;

      } else {
        // Not enough tokens available
        const retryAfter = Math.ceil((cost - availableTokens) / this.config.refillRate);
        
        this.logger.debug({
          identifier,
          cost,
          availableTokens,
          retryAfter
        }, 'Rate limit exceeded - insufficient tokens');

        return {
          allowed: false,
          remaining: availableTokens,
          resetTime: this.getNextResetTime(),
          retryAfter
        };
      }

    } catch (error) {
      this.logger.error({ error, identifier }, 'Rate limit check failed');
      
      // Fail open - allow request if rate limiter is down
      return {
        allowed: true,
        remaining: this.config.burstCapacity,
        resetTime: this.getNextResetTime()
      };
    }
  }

  /**
   * Queue a request to be processed when rate limit allows
   * @param identifier - Unique identifier for the client/provider
   * @param cost - Number of tokens to consume
   * @param timeoutMs - Maximum time to wait in queue (default: 30000ms)
   * @returns Promise that resolves when rate limit allows the request
   */
  async queueRequest(
    identifier: string,
    cost: number = 1,
    timeoutMs: number = 30000
  ): Promise<RateLimitStatus> {
    return new Promise((resolve, reject) => {
      const requestId = `${identifier}-${Date.now()}-${Math.random()}`;
      const queuedRequest: QueuedRequest = {
        id: requestId,
        timestamp: Date.now(),
        resolve,
        reject
      };

      // Add to queue
      if (!this.requestQueues.has(identifier)) {
        this.requestQueues.set(identifier, []);
      }
      this.requestQueues.get(identifier)!.push(queuedRequest);

      // Set timeout
      setTimeout(() => {
        this.removeFromQueue(identifier, requestId);
        reject(new Error(`Request queued for ${timeoutMs}ms without processing`));
      }, timeoutMs);

      // Start processing queue if not already processing
      if (!this.processingQueues.has(identifier)) {
        this.processQueue(identifier, cost);
      }

      this.logger.debug({
        identifier,
        queueSize: this.requestQueues.get(identifier)!.length
      }, 'Request queued');
    });
  }

  /**
   * Get current rate limit status without consuming tokens
   * @param identifier - Unique identifier for the client/provider
   * @returns Current rate limit status
   */
  async getStatus(identifier: string): Promise<RateLimitStatus> {
    const bucketKey = this.getBucketKey(identifier);
    
    try {
      const bucketState = await this.getBucketState(bucketKey);
      const now = Date.now();
      const timeSinceRefill = (now - bucketState.lastRefill) / 1000;
      const tokensToAdd = Math.floor(timeSinceRefill * this.config.refillRate);
      const availableTokens = Math.min(
        bucketState.tokens + tokensToAdd,
        this.config.burstCapacity
      );

      const timeBasedStatus = await this.checkTimeBasedLimits(identifier);

      return {
        allowed: availableTokens > 0 && timeBasedStatus.allowed,
        remaining: Math.min(availableTokens, timeBasedStatus.remaining),
        resetTime: this.getNextResetTime(),
        retryAfter: timeBasedStatus.retryAfter
      };

    } catch (error) {
      this.logger.error({ error, identifier }, 'Failed to get rate limit status');
      return {
        allowed: true,
        remaining: this.config.burstCapacity,
        resetTime: this.getNextResetTime()
      };
    }
  }

  /**
   * Reset rate limits for an identifier
   * @param identifier - Identifier to reset
   */
  async reset(identifier: string): Promise<void> {
    try {
      const bucketKey = this.getBucketKey(identifier);
      const newState: TokenBucketState = {
        tokens: this.config.burstCapacity,
        lastRefill: Date.now()
      };
      
      await this.saveBucketState(bucketKey, newState);
      await this.resetTimeCounters(identifier);
      
      this.logger.info({ identifier }, 'Rate limits reset');
    } catch (error) {
      this.logger.error({ error, identifier }, 'Failed to reset rate limits');
      throw error;
    }
  }

  /**
   * Get the rate limiter configuration
   */
  getConfig(): Required<RateLimitConfig> {
    return { ...this.config };
  }

  /**
   * Process queued requests for an identifier
   */
  private async processQueue(identifier: string, cost: number): Promise<void> {
    if (this.processingQueues.has(identifier)) {
      return; // Already processing
    }

    this.processingQueues.add(identifier);

    try {
      while (true) {
        const queue = this.requestQueues.get(identifier);
        if (!queue || queue.length === 0) {
          break;
        }

        const request = queue[0];
        
        // Check if request has timed out
        if (Date.now() - request.timestamp > 30000) {
          queue.shift();
          request.reject(new Error('Request timeout'));
          continue;
        }

        // Try to process the request
        const status = await this.checkRateLimit(identifier, cost);
        
        if (status.allowed) {
          // Request can be processed
          queue.shift();
          request.resolve(status);
        } else {
          // Still rate limited, wait and retry
          const waitTime = Math.min((status.retryAfter || 1) * 1000, 5000);
          await this.sleep(waitTime);
        }
      }
    } catch (error) {
      this.logger.error({ error, identifier }, 'Error processing request queue');
    } finally {
      this.processingQueues.delete(identifier);
    }
  }

  /**
   * Remove a request from the queue
   */
  private removeFromQueue(identifier: string, requestId: string): void {
    const queue = this.requestQueues.get(identifier);
    if (queue) {
      const index = queue.findIndex(req => req.id === requestId);
      if (index >= 0) {
        queue.splice(index, 1);
      }
    }
  }

  /**
   * Get token bucket state from Redis
   */
  private async getBucketState(bucketKey: string): Promise<TokenBucketState> {
    const cached = await getCache<TokenBucketState>(bucketKey);
    
    if (cached) {
      return cached;
    }

    // Initialize new bucket
    const initialState: TokenBucketState = {
      tokens: this.config.burstCapacity,
      lastRefill: Date.now()
    };

    await this.saveBucketState(bucketKey, initialState);
    return initialState;
  }

  /**
   * Save token bucket state to Redis
   */
  private async saveBucketState(
    bucketKey: string, 
    state: TokenBucketState
  ): Promise<void> {
    // Use a TTL longer than the maximum refill time
    const ttl = Math.ceil(this.config.burstCapacity / this.config.refillRate) + 3600; // +1 hour buffer
    await setCache(bucketKey, state, ttl);
  }

  /**
   * Check time-based rate limits (per minute, hour, day)
   */
  private async checkTimeBasedLimits(identifier: string): Promise<{
    allowed: boolean;
    remaining: number;
    retryAfter?: number;
  }> {
    const now = Date.now();
    const checks = [];

    // Check per-minute limit
    if (this.config.requestsPerMinute) {
      const minuteKey = this.getTimeCounterKey(identifier, 'minute');
      const minuteCount = await getCache<number>(minuteKey) || 0;
      checks.push({
        limit: this.config.requestsPerMinute,
        current: minuteCount,
        window: 60,
        type: 'minute'
      });
    }

    // Check per-hour limit
    if (this.config.requestsPerHour) {
      const hourKey = this.getTimeCounterKey(identifier, 'hour');
      const hourCount = await getCache<number>(hourKey) || 0;
      checks.push({
        limit: this.config.requestsPerHour,
        current: hourCount,
        window: 3600,
        type: 'hour'
      });
    }

    // Check per-day limit
    if (this.config.requestsPerDay) {
      const dayKey = this.getTimeCounterKey(identifier, 'day');
      const dayCount = await getCache<number>(dayKey) || 0;
      checks.push({
        limit: this.config.requestsPerDay,
        current: dayCount,
        window: 86400,
        type: 'day'
      });
    }

    // Find the most restrictive limit that's exceeded
    for (const check of checks) {
      if (check.current >= check.limit) {
        return {
          allowed: false,
          remaining: 0,
          retryAfter: check.window
        };
      }
    }

    // Find minimum remaining requests across all time windows
    const remaining = checks.length > 0 
      ? Math.min(...checks.map(check => check.limit - check.current))
      : this.config.burstCapacity;

    return {
      allowed: true,
      remaining
    };
  }

  /**
   * Increment time-based counters
   */
  private async incrementTimeCounters(identifier: string): Promise<void> {
    const promises = [];

    if (this.config.requestsPerMinute) {
      const minuteKey = this.getTimeCounterKey(identifier, 'minute');
      promises.push(incrementCounter(minuteKey, 60));
    }

    if (this.config.requestsPerHour) {
      const hourKey = this.getTimeCounterKey(identifier, 'hour');
      promises.push(incrementCounter(hourKey, 3600));
    }

    if (this.config.requestsPerDay) {
      const dayKey = this.getTimeCounterKey(identifier, 'day');
      promises.push(incrementCounter(dayKey, 86400));
    }

    await Promise.all(promises);
  }

  /**
   * Reset time-based counters
   */
  private async resetTimeCounters(identifier: string): Promise<void> {
    const keys = [
      this.getTimeCounterKey(identifier, 'minute'),
      this.getTimeCounterKey(identifier, 'hour'),
      this.getTimeCounterKey(identifier, 'day')
    ];

    await Promise.all(
      keys.map(key => redis.del(key).catch(err => 
        this.logger.warn({ error: err, key }, 'Failed to delete counter key')
      ))
    );
  }

  /**
   * Utility methods for key generation
   */
  private getBucketKey(identifier: string): string {
    return `rate-limit:bucket:${this.config.name}:${identifier}`;
  }

  private getTimeCounterKey(
    identifier: string, 
    window: 'minute' | 'hour' | 'day'
  ): string {
    const now = new Date();
    let timeKey: string;

    switch (window) {
      case 'minute':
        timeKey = `${now.getFullYear()}-${now.getMonth()}-${now.getDate()}-${now.getHours()}-${now.getMinutes()}`;
        break;
      case 'hour':
        timeKey = `${now.getFullYear()}-${now.getMonth()}-${now.getDate()}-${now.getHours()}`;
        break;
      case 'day':
        timeKey = `${now.getFullYear()}-${now.getMonth()}-${now.getDate()}`;
        break;
    }

    return `rate-limit:counter:${this.config.name}:${identifier}:${window}:${timeKey}`;
  }

  private getNextResetTime(): Date {
    const now = new Date();
    // Next minute boundary
    return new Date(now.getFullYear(), now.getMonth(), now.getDate(), 
                   now.getHours(), now.getMinutes() + 1, 0, 0);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Registry for managing multiple rate limiters
 */
export class RateLimitRegistry {
  private limiters = new Map<string, RateLimitManager>();
  private readonly logger = apiLogger.child({ service: 'RateLimitRegistry' });

  /**
   * Register a rate limiter
   */
  register(config: RateLimitConfig): RateLimitManager {
    const limiter = new RateLimitManager(config);
    this.limiters.set(config.name, limiter);
    this.logger.info({ name: config.name }, 'Rate limiter registered');
    return limiter;
  }

  /**
   * Get a rate limiter by name
   */
  get(name: string): RateLimitManager | undefined {
    return this.limiters.get(name);
  }

  /**
   * Get all registered limiters
   */
  getAll(): Map<string, RateLimitManager> {
    return new Map(this.limiters);
  }

  /**
   * Remove a rate limiter
   */
  unregister(name: string): boolean {
    const removed = this.limiters.delete(name);
    if (removed) {
      this.logger.info({ name }, 'Rate limiter unregistered');
    }
    return removed;
  }
}

// Export singleton registry
export const rateLimitRegistry = new RateLimitRegistry();