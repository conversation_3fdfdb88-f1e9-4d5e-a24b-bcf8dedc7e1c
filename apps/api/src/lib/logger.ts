import pino from 'pino';
import { config, isDevelopment } from './config';

// Create logger configuration
const loggerConfig: pino.LoggerOptions = {
  level: config.LOG_LEVEL,
  ...(isDevelopment && {
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'yyyy-mm-dd HH:MM:ss.l',
        ignore: 'pid,hostname'
      }
    }
  })
};

// Create base logger
export const logger = pino(loggerConfig);

// Create child loggers for different components
export const dbLogger = logger.child({ component: 'database' });
export const redisLogger = logger.child({ component: 'redis' });
export const apiLogger = logger.child({ component: 'api' });
export const jobLogger = logger.child({ component: 'jobs' });
export const tradingLogger = logger.child({ component: 'trading' });

// Helper function to create component-specific loggers
export function createLogger(component: string) {
  return logger.child({ component });
}

// Error logging helper
export function logError(error: unknown, context?: Record<string, any>) {
  const errorObj = error instanceof Error 
    ? { 
        name: error.name,
        message: error.message,
        stack: error.stack
      }
    : error;

  logger.error({ 
    error: errorObj,
    ...context 
  }, 'Error occurred');
}

// Request logging middleware helper
export function createRequestLogger() {
  return pino({
    ...loggerConfig,
    serializers: {
      req: pino.stdSerializers.req,
      res: pino.stdSerializers.res,
    }
  });
}