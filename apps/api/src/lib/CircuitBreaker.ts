import { apiLogger } from './logger';

/**
 * Configuration for circuit breaker
 */
export interface CircuitBreakerConfig {
  name: string;
  failureThreshold: number; // Number of failures before opening circuit
  resetTimeoutMs: number; // Time to wait before trying again
  monitoringWindowMs: number; // Time window for failure tracking
  successThreshold: number; // Number of successes needed to close circuit
}

/**
 * Circuit breaker states
 */
export enum CircuitBreakerState {
  CLOSED = 'closed',     // Normal operation
  OPEN = 'open',         // Failing fast
  HALF_OPEN = 'half-open' // Testing if service recovered
}

/**
 * Circuit breaker statistics
 */
export interface CircuitBreakerStats {
  state: CircuitBreakerState;
  failures: number;
  successes: number;
  lastFailureTime?: Date;
  lastSuccessTime?: Date;
  totalRequests: number;
  rejectedRequests: number;
}

/**
 * Circuit breaker implementation for external service protection
 */
export class CircuitBreaker<T = any, R = any> {
  private state: CircuitBreakerState = CircuitBreakerState.CLOSED;
  private failures: number = 0;
  private successes: number = 0;
  private lastFailureTime?: Date;
  private lastSuccessTime?: Date;
  private totalRequests: number = 0;
  private rejectedRequests: number = 0;
  private resetTimer?: NodeJS.Timeout;
  
  private readonly logger = apiLogger.child({ 
    service: 'CircuitBreaker',
    name: this.config.name 
  });

  constructor(
    private readonly config: CircuitBreakerConfig,
    private readonly action: (input: T) => Promise<R>
  ) {
    this.logger.info({ config }, 'Circuit breaker initialized');
  }

  /**
   * Execute the protected action
   */
  async execute(input: T): Promise<R> {
    this.totalRequests++;

    if (this.state === CircuitBreakerState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.state = CircuitBreakerState.HALF_OPEN;
        this.logger.info('Circuit breaker transitioning to HALF_OPEN');
      } else {
        this.rejectedRequests++;
        this.logger.debug('Request rejected - circuit breaker OPEN');
        throw new CircuitBreakerError(
          `Circuit breaker is OPEN for ${this.config.name}`,
          'CIRCUIT_BREAKER_OPEN',
          this.config.name
        );
      }
    }

    try {
      const result = await this.action(input);
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure(error);
      throw error;
    }
  }

  /**
   * Get current statistics
   */
  getStats(): CircuitBreakerStats {
    return {
      state: this.state,
      failures: this.failures,
      successes: this.successes,
      lastFailureTime: this.lastFailureTime,
      lastSuccessTime: this.lastSuccessTime,
      totalRequests: this.totalRequests,
      rejectedRequests: this.rejectedRequests
    };
  }

  /**
   * Reset the circuit breaker to closed state
   */
  reset(): void {
    this.state = CircuitBreakerState.CLOSED;
    this.failures = 0;
    this.successes = 0;
    this.lastFailureTime = undefined;
    this.lastSuccessTime = undefined;
    
    if (this.resetTimer) {
      clearTimeout(this.resetTimer);
      this.resetTimer = undefined;
    }

    this.logger.info('Circuit breaker manually reset to CLOSED');
  }

  /**
   * Force circuit breaker to open state
   */
  forceOpen(): void {
    this.state = CircuitBreakerState.OPEN;
    this.logger.warn('Circuit breaker forced to OPEN state');
  }

  /**
   * Handle successful execution
   */
  private onSuccess(): void {
    this.successes++;
    this.lastSuccessTime = new Date();

    if (this.state === CircuitBreakerState.HALF_OPEN) {
      if (this.successes >= this.config.successThreshold) {
        this.state = CircuitBreakerState.CLOSED;
        this.failures = 0;
        this.successes = 0;
        this.logger.info('Circuit breaker closed after successful recovery');
      }
    } else if (this.state === CircuitBreakerState.CLOSED) {
      // Reset failure count after successful operation
      this.failures = Math.max(0, this.failures - 1);
    }
  }

  /**
   * Handle failed execution
   */
  private onFailure(error: any): void {
    this.failures++;
    this.lastFailureTime = new Date();

    this.logger.warn({
      error: error.message,
      failures: this.failures,
      threshold: this.config.failureThreshold
    }, 'Circuit breaker recorded failure');

    if (this.state === CircuitBreakerState.HALF_OPEN) {
      // Go back to open if failure during recovery
      this.state = CircuitBreakerState.OPEN;
      this.successes = 0;
      this.scheduleReset();
      this.logger.warn('Circuit breaker opened after failed recovery attempt');
    } else if (this.state === CircuitBreakerState.CLOSED) {
      // Check if we should open the circuit
      if (this.failures >= this.config.failureThreshold) {
        this.state = CircuitBreakerState.OPEN;
        this.scheduleReset();
        this.logger.warn('Circuit breaker opened due to failure threshold exceeded');
      }
    }
  }

  /**
   * Check if we should attempt to reset from open state
   */
  private shouldAttemptReset(): boolean {
    if (!this.lastFailureTime) return true;
    
    const timeSinceLastFailure = Date.now() - this.lastFailureTime.getTime();
    return timeSinceLastFailure >= this.config.resetTimeoutMs;
  }

  /**
   * Schedule automatic reset attempt
   */
  private scheduleReset(): void {
    if (this.resetTimer) {
      clearTimeout(this.resetTimer);
    }

    this.resetTimer = setTimeout(() => {
      if (this.state === CircuitBreakerState.OPEN) {
        this.logger.info('Scheduled reset timeout reached');
        // The next request will transition to HALF_OPEN
      }
    }, this.config.resetTimeoutMs);
  }
}

/**
 * Custom error for circuit breaker failures
 */
export class CircuitBreakerError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly circuitName: string
  ) {
    super(message);
    this.name = 'CircuitBreakerError';
  }
}

/**
 * Registry for managing multiple circuit breakers
 */
export class CircuitBreakerRegistry {
  private breakers = new Map<string, CircuitBreaker>();
  private readonly logger = apiLogger.child({ service: 'CircuitBreakerRegistry' });

  /**
   * Create and register a circuit breaker
   */
  create<T, R>(
    config: CircuitBreakerConfig,
    action: (input: T) => Promise<R>
  ): CircuitBreaker<T, R> {
    const breaker = new CircuitBreaker(config, action);
    this.breakers.set(config.name, breaker as any);
    this.logger.info({ name: config.name }, 'Circuit breaker registered');
    return breaker;
  }

  /**
   * Get a circuit breaker by name
   */
  get(name: string): CircuitBreaker | undefined {
    return this.breakers.get(name);
  }

  /**
   * Get statistics for all circuit breakers
   */
  getAllStats(): Record<string, CircuitBreakerStats> {
    const stats: Record<string, CircuitBreakerStats> = {};
    
    for (const [name, breaker] of this.breakers.entries()) {
      stats[name] = breaker.getStats();
    }

    return stats;
  }

  /**
   * Reset all circuit breakers
   */
  resetAll(): void {
    for (const breaker of this.breakers.values()) {
      breaker.reset();
    }
    this.logger.info('All circuit breakers reset');
  }

  /**
   * Remove a circuit breaker
   */
  remove(name: string): boolean {
    const removed = this.breakers.delete(name);
    if (removed) {
      this.logger.info({ name }, 'Circuit breaker removed');
    }
    return removed;
  }
}

// Export singleton registry
export const circuitBreakerRegistry = new CircuitBreakerRegistry();