import { config, apiConfig, securityConfig } from './config';
import { apiLogger } from './logger';
import { connectRedis, checkRedisConnection } from './redis';
import { initializeDatabase } from './database';

interface ValidationResult {
  success: boolean;
  message: string;
  severity: 'error' | 'warning' | 'info';
}

export class EnvironmentValidator {
  private results: ValidationResult[] = [];

  /**
   * Validate all environment requirements and dependencies
   */
  async validate(): Promise<{ valid: boolean; results: ValidationResult[] }> {
    this.results = [];
    
    apiLogger.info('Starting environment validation...');

    // Core environment validation
    this.validateNodeEnvironment();
    this.validateServerConfiguration();
    this.validateDatabaseConfiguration();
    this.validateRedisConfiguration();
    this.validateAPIKeyConfiguration();
    this.validateSecurityConfiguration();
    
    // External dependency validation (temporarily disabled for startup)
    // await this.validateDatabaseConnection();
    // await this.validateRedisConnection();
    
    // Log results
    this.logValidationResults();
    
    const hasErrors = this.results.some(r => r.severity === 'error');
    
    return {
      valid: !hasErrors,
      results: this.results
    };
  }

  private validateNodeEnvironment(): void {
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion < 18) {
      this.addResult('error', `Node.js version ${nodeVersion} is not supported. Minimum version: 18.x`);
    } else {
      this.addResult('info', `Node.js version: ${nodeVersion}`);
    }
    
    this.addResult('info', `Environment: ${config.NODE_ENV}`);
    this.addResult('info', `Process ID: ${process.pid}`);
  }

  private validateServerConfiguration(): void {
    if (!config.PORT) {
      this.addResult('error', 'PORT environment variable is required');
    } else if (config.PORT < 1024 && process.getuid && process.getuid() !== 0) {
      this.addResult('warning', `Port ${config.PORT} requires root privileges on Unix systems`);
    } else {
      this.addResult('info', `Server will run on ${config.HOST}:${config.PORT}`);
    }
    
    if (!config.HOST) {
      this.addResult('warning', 'HOST not specified, using default: localhost');
    }
  }

  private validateDatabaseConfiguration(): void {
    if (!config.DATABASE_URL) {
      this.addResult('error', 'DATABASE_URL environment variable is required');
    } else {
      // Parse database URL for basic validation
      try {
        const url = new URL(config.DATABASE_URL);
        if (!['postgresql', 'postgres'].includes(url.protocol.replace(':', ''))) {
          this.addResult('warning', 'Database URL should use PostgreSQL protocol');
        } else {
          this.addResult('info', `Database configured: ${url.protocol}//${url.host}/${url.pathname.slice(1)}`);
        }
      } catch (error) {
        this.addResult('error', 'DATABASE_URL format is invalid');
      }
    }
  }

  private validateRedisConfiguration(): void {
    if (config.REDIS_URL) {
      try {
        const url = new URL(config.REDIS_URL);
        this.addResult('info', `Redis configured via URL: ${url.protocol}//${url.host}`);
      } catch (error) {
        this.addResult('error', 'REDIS_URL format is invalid');
      }
    } else {
      this.addResult('info', `Redis configured: ${config.REDIS_HOST}:${config.REDIS_PORT}`);
    }
  }

  private validateAPIKeyConfiguration(): void {
    if (!apiConfig.helius.apiKey) {
      this.addResult('warning', 'HELIUS_API_KEY not set - will use default Solana RPC endpoints');
    } else {
      this.addResult('info', 'Helius RPC configured');
    }
    
  }

  private validateSecurityConfiguration(): void {
    // Validate session secret
    if (securityConfig.sessionSecret.length < 32) {
      this.addResult('error', 'SESSION_SECRET must be at least 32 characters long');
    } else {
      this.addResult('info', 'Session secret meets security requirements');
    }
    
    // Validate admin password
    if (securityConfig.adminPassword.length < 8) {
      this.addResult('error', 'ADMIN_PASSWORD must be at least 8 characters long');
    } else {
      this.addResult('info', 'Admin password meets security requirements');
    }
    
    if (config.NODE_ENV === 'production') {
      // Additional production security checks
      if (securityConfig.sessionSecret.includes('dev-secret') || securityConfig.sessionSecret === 'dev-secret-key-change-in-production') {
        this.addResult('error', 'SESSION_SECRET must be changed from default value in production');
      }
      
      if (securityConfig.adminPassword === 'admin123' || securityConfig.adminPassword === 'password') {
        this.addResult('error', 'ADMIN_PASSWORD must be changed from default value in production');
      }
      
      this.addResult('info', 'Production security validation completed');
    } else {
      this.addResult('warning', 'Running in development mode - some security checks relaxed');
    }
  }

  private async validateDatabaseConnection(): Promise<void> {
    try {
      await initializeDatabase();
      this.addResult('info', 'Database connection successful');
    } catch (error) {
      this.addResult('error', `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async validateRedisConnection(): Promise<void> {
    try {
      await connectRedis();
      const isHealthy = await checkRedisConnection();
      
      if (isHealthy) {
        this.addResult('info', 'Redis connection successful');
      } else {
        this.addResult('error', 'Redis connection unhealthy');
      }
    } catch (error) {
      this.addResult('error', `Redis connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private addResult(severity: 'error' | 'warning' | 'info', message: string): void {
    this.results.push({ success: severity !== 'error', message, severity });
  }

  private logValidationResults(): void {
    const errors = this.results.filter(r => r.severity === 'error');
    const warnings = this.results.filter(r => r.severity === 'warning');
    const info = this.results.filter(r => r.severity === 'info');

    if (errors.length > 0) {
      apiLogger.error(`Environment validation failed with ${errors.length} errors:`);
      errors.forEach(error => apiLogger.error(`❌ ${error.message}`));
    }

    if (warnings.length > 0) {
      apiLogger.warn(`Environment validation completed with ${warnings.length} warnings:`);
      warnings.forEach(warning => apiLogger.warn(`⚠️  ${warning.message}`));
    }

    info.forEach(result => apiLogger.info(`✅ ${result.message}`));

    if (errors.length === 0) {
      apiLogger.info(`✅ Environment validation completed successfully (${this.results.length} checks)`);
    }
  }
}

export const validateEnvironment = async (): Promise<{ valid: boolean; results: ValidationResult[] }> => {
  const validator = new EnvironmentValidator();
  return await validator.validate();
};