import { createClient } from 'redis';
import { getRedisConfig } from './config';
import { apiLogger } from './logger';

// Create Redis client with environment configuration
const redisConfig = getRedisConfig();
export const redis = createClient({
  socket: {
    host: redisConfig.host,
    port: redisConfig.port
  },
  ...(redisConfig.password && { password: redisConfig.password })
});

// Redis connection event handlers
redis.on('connect', () => {
  apiLogger.info('Redis client connected');
});

redis.on('ready', () => {
  apiLogger.info('Redis client ready');
});

redis.on('error', (error) => {
  apiLogger.error({ error }, 'Redis client error');
});

redis.on('end', () => {
  apiLogger.info('Redis client disconnected');
});

// Connect to Redis
export async function connectRedis(): Promise<void> {
  try {
    await redis.connect();
    apiLogger.info('Redis connected successfully');
  } catch (error) {
    apiLogger.error({ error }, 'Failed to connect to Redis');
    throw error;
  }
}

// Redis health check
export async function checkRedisConnection(): Promise<boolean> {
  try {
    // Ensure Redis is connected before ping
    if (!redis.isReady) {
      try {
        await redis.connect();
      } catch (connectError) {
        apiLogger.error({ error: connectError }, 'Redis connection failed during health check');
        return false;
      }
    }
    
    const response = await redis.ping();
    const isHealthy = response === 'PONG';
    if (isHealthy) {
      apiLogger.info('Redis health check successful');
    } else {
      apiLogger.warn('Redis health check failed - unexpected response');
    }
    return isHealthy;
  } catch (error) {
    apiLogger.error({ error }, 'Redis health check failed');
    return false;
  }
}

// Cache helpers with TTL
export async function setCache(
  key: string, 
  value: any, 
  ttlSeconds: number = 300
): Promise<void> {
  try {
    const serializedValue = JSON.stringify(value);
    await redis.setEx(key, ttlSeconds, serializedValue);
  } catch (error) {
    apiLogger.error({ error, key }, 'Failed to set cache');
    throw error;
  }
}

export async function getCache<T = any>(key: string): Promise<T | null> {
  try {
    const value = await redis.get(key);
    if (!value) return null;
    return JSON.parse(value) as T;
  } catch (error) {
    apiLogger.error({ error, key }, 'Failed to get cache');
    return null;
  }
}

export async function deleteCache(key: string): Promise<void> {
  try {
    await redis.del(key);
  } catch (error) {
    apiLogger.error({ error, key }, 'Failed to delete cache');
    throw error;
  }
}

export async function deleteCachePattern(pattern: string): Promise<void> {
  try {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(keys);
    }
  } catch (error) {
    apiLogger.error({ error, pattern }, 'Failed to delete cache pattern');
    throw error;
  }
}

// Increment counter with TTL
export async function incrementCounter(
  key: string, 
  ttlSeconds: number = 3600
): Promise<number> {
  try {
    const count = await redis.incr(key);
    if (count === 1) {
      await redis.expire(key, ttlSeconds);
    }
    return count;
  } catch (error) {
    apiLogger.error({ error, key }, 'Failed to increment counter');
    throw error;
  }
}

// List operations for job queues
export async function pushToList(key: string, value: any): Promise<void> {
  try {
    const serializedValue = JSON.stringify(value);
    await redis.lPush(key, serializedValue);
  } catch (error) {
    apiLogger.error({ error, key }, 'Failed to push to list');
    throw error;
  }
}

export async function popFromList<T = any>(key: string): Promise<T | null> {
  try {
    const value = await redis.rPop(key);
    if (!value) return null;
    return JSON.parse(value) as T;
  } catch (error) {
    apiLogger.error({ error, key }, 'Failed to pop from list');
    return null;
  }
}

// Graceful shutdown
export async function disconnectRedis(): Promise<void> {
  try {
    await redis.quit();
    apiLogger.info('Redis disconnected');
  } catch (error) {
    apiLogger.error({ error }, 'Error disconnecting from Redis');
  }
}