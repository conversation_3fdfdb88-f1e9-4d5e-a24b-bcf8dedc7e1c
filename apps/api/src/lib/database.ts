import { PrismaClient } from '@prisma/client';
import { config, isDevelopment } from './config';
import pino from 'pino';

const logger = pino({ name: 'database' });

declare global {
  // eslint-disable-next-line no-var
  var __prisma: PrismaClient | undefined;
}

// Create Prisma client with logging configuration
function createPrismaClient(): PrismaClient {
  const logLevels: Array<'query' | 'info' | 'warn' | 'error'> = isDevelopment 
    ? ['query', 'info', 'warn', 'error']
    : ['warn', 'error'];

  return new PrismaClient({
    log: logLevels,
    errorFormat: 'colorless',
  });
}

// Singleton pattern for Prisma client
export const prisma = globalThis.__prisma ?? createPrismaClient();

if (isDevelopment) {
  globalThis.__prisma = prisma;
}

// Database connection health check
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    // Test basic connectivity and execute a simple query
    await prisma.$queryRaw`SELECT 1 as test`;
    
    // Test TimescaleDB extension availability (skip for now)
    // await prisma.$queryRaw`SELECT extname FROM pg_extension WHERE extname = 'timescaledb'`;
    
    logger.info('Database connection and TimescaleDB extension verified');
    return true;
  } catch (error) {
    logger.error({ 
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, 'Database connection or TimescaleDB extension check failed');
    return false;
  }
}

// TimescaleDB specific functions
export async function enableTimescaleExtension(): Promise<void> {
  try {
    await prisma.$executeRaw`CREATE EXTENSION IF NOT EXISTS timescaledb;`;
    logger.info('TimescaleDB extension enabled');
  } catch (error) {
    logger.error({ error }, 'Failed to enable TimescaleDB extension');
    throw error;
  }
}

export async function createHypertables(): Promise<void> {
  try {
    // Create hypertable for price_snapshots
    await prisma.$executeRaw`
      SELECT create_hypertable(
        'price_snapshots',
        'timestamp',
        if_not_exists => TRUE,
        chunk_time_interval => INTERVAL '1 hour'
      );
    `;

    // Create index for better query performance
    await prisma.$executeRaw`
      CREATE INDEX IF NOT EXISTS price_snapshots_token_address_time_idx 
      ON price_snapshots (token_address, timestamp DESC);
    `;

    logger.info('TimescaleDB hypertables created successfully');
  } catch (error) {
    logger.error({ error }, 'Failed to create TimescaleDB hypertables');
    throw error;
  }
}

// Database initialization function
export async function initializeDatabase(): Promise<void> {
  try {
    logger.info('Initializing database...');
    
    // Check connection
    const isConnected = await checkDatabaseConnection();
    if (!isConnected) {
      throw new Error('Cannot connect to database');
    }

    // Enable TimescaleDB extension
    await enableTimescaleExtension();

    // Create hypertables
    await createHypertables();

    logger.info('Database initialized successfully');
  } catch (error) {
    logger.error({ error }, 'Database initialization failed');
    throw error;
  }
}

// Graceful shutdown
export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect();
    logger.info('Database disconnected');
  } catch (error) {
    logger.error({ error }, 'Error disconnecting from database');
  }
}

// Transaction helper
export async function withTransaction<T>(
  fn: (prisma: PrismaClient) => Promise<T>
): Promise<T> {
  return prisma.$transaction(fn);
}