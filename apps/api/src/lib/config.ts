import { z } from 'zod';
import dotenv from 'dotenv';
import path from 'path';

// Load local .env file first, then root .env file
dotenv.config(); // Load local .env file
dotenv.config({ path: path.resolve(__dirname, '../../../../.env') }); // Load root .env file (lower priority)

// Configuration schema with validation
const configSchema = z.object({
  // Server configuration
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().default('3001').transform(Number),
  HOST: z.string().default('localhost'),

  // Database configuration
  DATABASE_URL: z.string(),
  
  // Redis configuration
  REDIS_URL: z.string().optional(),
  REDIS_HOST: z.string().default('localhost'),
  REDIS_PORT: z.string().default('6379').transform(Number),
  REDIS_PASSWORD: z.string().optional(),

  // Security configuration
  SESSION_SECRET: z.string().min(32, 'Session secret must be at least 32 characters'),
  ADMIN_PASSWORD: z.string().min(8, 'Admin password must be at least 8 characters'),

  // External API keys
  HELIUS_API_KEY: z.string().optional(),
  JUPITER_API_KEY: z.string().optional(),
  JUPITER_API_URL: z.string().optional(),
  JUPITER_QUOTE_API_URL: z.string().default('https://lite-api.jup.ag/swap/v1'),
  JUPITER_SWAP_API_URL: z.string().default('https://lite-api.jup.ag/swap/v1'),

  // Wallet configuration
  WALLET_SECRET_KEY: z.string().optional(),
  ENCRYPTED_PRIVATE_KEY: z.string().optional(),
  WALLET_PASSWORD: z.string().optional(),
  TRADING_WALLET_ADDRESS: z.string().optional(),
  SOLANA_NETWORK: z.enum(['mainnet-beta', 'devnet', 'testnet']).default('mainnet-beta'),
  WALLET_MAX_TRANSACTION_AMOUNT: z.string().default('0.05').transform(Number), // Conservative default for mainnet
  
  // Trading configuration
  DEFAULT_SLIPPAGE_BPS: z.string().default('100').transform(Number),
  MAX_POSITION_SIZE_SOL: z.string().default('1000').transform(Number),
  
  // Job queue configuration
  JOB_QUEUE_CONCURRENCY: z.string().default('5').transform(Number),
  PRICE_MONITOR_INTERVAL_MS: z.string().default('5000').transform(Number),
  
  // Logging
  LOG_LEVEL: z.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']).default('info'),
});

// Parse and validate configuration
function parseConfig() {
  try {
    const config = configSchema.parse(process.env);
    return config;
  } catch (error) {
    console.error('❌ Invalid environment configuration:');
    if (error instanceof z.ZodError) {
      error.issues.forEach((issue) => {
        console.error(`  - ${issue.path.join('.')}: ${issue.message}`);
      });
    }
    process.exit(1);
  }
}

export const config = parseConfig();

// Helper function to check if running in production
export const isProduction = config.NODE_ENV === 'production';
export const isDevelopment = config.NODE_ENV === 'development';
export const isTest = config.NODE_ENV === 'test';

// Database URL builder for different environments
export function getDatabaseUrl(): string {
  return config.DATABASE_URL;
}

// Redis connection configuration
export function getRedisConfig() {
  if (config.REDIS_URL) {
    return { url: config.REDIS_URL };
  }
  
  // Only include password if it's actually provided
  const redisConfig: any = {
    host: config.REDIS_HOST,
    port: config.REDIS_PORT,
  };
  
  if (config.REDIS_PASSWORD) {
    redisConfig.password = config.REDIS_PASSWORD;
  }
  
  return redisConfig;
}

// Security configuration
export const securityConfig = {
  sessionSecret: config.SESSION_SECRET,
  adminPassword: config.ADMIN_PASSWORD,
};

// Helper function to get RPC URL with fallback
function getRpcUrl(): string {
  if (config.HELIUS_API_KEY) {
    return config.SOLANA_NETWORK === 'mainnet-beta' 
      ? `https://mainnet.helius-rpc.com/?api-key=${config.HELIUS_API_KEY}`
      : `https://${config.SOLANA_NETWORK}.helius-rpc.com/?api-key=${config.HELIUS_API_KEY}`;
  }
  
  // Fallback to public RPC endpoints when API key is not available
  switch (config.SOLANA_NETWORK) {
    case 'mainnet-beta':
      return 'https://api.mainnet-beta.solana.com';
    case 'devnet':
      return 'https://api.devnet.solana.com';
    case 'testnet':
      return 'https://api.testnet.solana.com';
    default:
      return 'https://api.mainnet-beta.solana.com';
  }
}

// Wallet configuration
export const walletConfig = {
  privateKey: config.ENCRYPTED_PRIVATE_KEY, // Backward compatibility
  encryptedPrivateKey: config.ENCRYPTED_PRIVATE_KEY,
  walletPassword: config.WALLET_PASSWORD,
  tradingWalletAddress: config.TRADING_WALLET_ADDRESS,
  network: config.SOLANA_NETWORK,
  maxTransactionAmount: config.WALLET_MAX_TRANSACTION_AMOUNT,
  rpcUrl: getRpcUrl(),
};

// External API configuration
export const apiConfig = {
  helius: {
    baseUrl: 'https://mainnet.helius-rpc.com',
    apiKey: config.HELIUS_API_KEY,
  },
  jupiter: {
    baseUrl: config.JUPITER_QUOTE_API_URL || config.JUPITER_API_URL || 'https://lite-api.jup.ag/swap/v1',
    apiKey: config.JUPITER_API_KEY, // Optional API key for higher rate limits
    isV1Api: (config.JUPITER_QUOTE_API_URL || config.JUPITER_API_URL || '').includes('/swap/v1'), // Auto-detect V1 API
    useLiteApi: (config.JUPITER_QUOTE_API_URL || config.JUPITER_API_URL || '').includes('lite-api.jup.ag'), // Lite vs Pro API
  },
};