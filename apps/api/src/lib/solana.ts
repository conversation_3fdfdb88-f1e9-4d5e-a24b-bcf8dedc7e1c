import { Connection, Keypair, clusterApiUrl } from '@solana/web3.js';
import fs from 'fs';
import path from 'path';
import { walletConfig } from './config';
import { apiLogger } from './logger';

// Wallet instance - singleton pattern
let walletInstance: SolanaWallet | null = null;

interface SolanaWallet {
  keypair: Keypair;
  publicKey: string;
  getKeypair: () => Keypair;
  getPublicKey: () => PublicKey;
}

/**
 * Get Solana RPC connection
 */
export function getConnection(): Connection {
  // Use Helius RPC if API key is available, otherwise use public endpoint
  const rpcUrl = process.env.HELIUS_API_KEY 
    ? `https://mainnet.helius-rpc.com/?api-key=${process.env.HELIUS_API_KEY}`
    : clusterApiUrl(walletConfig.network);
    
  return new Connection(rpcUrl, {
    commitment: 'confirmed',
    confirmTransactionInitialTimeout: 60000,
  });
}

/**
 * Get wallet instance (creates or loads existing)
 */
export function getWallet(): SolanaWallet {
  if (walletInstance) {
    return walletInstance;
  }

  // Try to load from environment variable first
  if (walletConfig.privateKey) {
    try {
      const secretKey = Uint8Array.from(JSON.parse(walletConfig.privateKey));
      const keypair = Keypair.fromSecretKey(secretKey);
      
      walletInstance = {
        keypair,
        publicKey: keypair.publicKey.toBase58(),
        getKeypair: () => keypair,
        getPublicKey: () => keypair.publicKey,
      };

      apiLogger.info('Wallet loaded from environment variable', {
        address: walletInstance.publicKey,
        isProduction: process.env.NODE_ENV === 'production',
      });

      return walletInstance;
    } catch (error) {
      apiLogger.error('Failed to load wallet from environment variable', { error });
    }
  }

  // Fall back to development wallet file
  const devWalletPath = path.join(process.cwd(), '.dev-wallet.json');
  
  try {
    if (fs.existsSync(devWalletPath)) {
      const walletData = JSON.parse(fs.readFileSync(devWalletPath, 'utf8'));
      const keypair = Keypair.fromSecretKey(Uint8Array.from(walletData.secretKey));
      
      walletInstance = {
        keypair,
        publicKey: keypair.publicKey.toBase58(),
        getKeypair: () => keypair,
        getPublicKey: () => keypair.publicKey,
      };

      apiLogger.info('Wallet loaded from development file', {
        address: walletInstance.publicKey,
        file: devWalletPath,
      });

      return walletInstance;
    }
  } catch (error) {
    apiLogger.error('Failed to load wallet from file', { error });
  }

  // Create new development wallet if none exists
  const newKeypair = Keypair.generate();
  
  try {
    fs.writeFileSync(devWalletPath, JSON.stringify({
      secretKey: Array.from(newKeypair.secretKey),
      publicKey: newKeypair.publicKey.toBase58(),
      createdAt: new Date().toISOString(),
    }, null, 2));

    walletInstance = {
      keypair: newKeypair,
      publicKey: newKeypair.publicKey.toBase58(),
      getKeypair: () => newKeypair,
      getPublicKey: () => newKeypair.publicKey,
    };

    apiLogger.info('New development wallet generated and saved', {
      address: walletInstance.publicKey,
      file: devWalletPath,
    });

    return walletInstance;
  } catch (error) {
    apiLogger.error('Failed to create development wallet', { error });
    throw new Error('Could not initialize wallet');
  }
}

/**
 * Initialize wallet on startup
 */
export function initializeWallet(): void {
  try {
    const wallet = getWallet();
    apiLogger.info('Wallet initialized', {
      address: wallet.publicKey,
      isProduction: process.env.NODE_ENV === 'production',
    });
  } catch (error) {
    apiLogger.error('Failed to initialize wallet', { error });
    throw error;
  }
}

/**
 * Validate wallet connection
 */
export async function validateWalletConnection(): Promise<{
  isValid: boolean;
  address?: string;
  balance?: number;
  error?: string;
}> {
  try {
    const wallet = getWallet();
    const connection = getConnection();
    
    const balance = await connection.getBalance(wallet.getPublicKey());
    
    return {
      isValid: true,
      address: wallet.publicKey,
      balance: balance / 1_000_000_000, // Convert lamports to SOL
    };
  } catch (error) {
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Get network information
 */
export function getNetworkInfo() {
  const network = walletConfig.network;
  const isMainnet = network === 'mainnet-beta';
  
  return {
    network,
    isMainnet,
    rpcUrl: process.env.HELIUS_API_KEY 
      ? `https://mainnet.helius-rpc.com/?api-key=${process.env.HELIUS_API_KEY}`
      : clusterApiUrl(network),
    maxTransactionAmount: walletConfig.maxTransactionAmount,
  };
}