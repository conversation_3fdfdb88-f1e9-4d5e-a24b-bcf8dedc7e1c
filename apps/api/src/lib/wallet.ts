/**
 * Secure Wallet Management Utilities
 * Handles private key loading, validation, and signing for local development
 * 
 * SECURITY WARNING: This is for development/testing only.
 * Production implementations should use proper key management solutions.
 */

import { Keypair, PublicKey, Transaction, VersionedTransaction, SystemProgram, LAMPORTS_PER_SOL } from '@solana/web3.js';
import { apiLogger } from './logger';
import { config, isProduction } from './config';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';

export interface WalletInfo {
  publicKey: string;
  address: string;
  isValid: boolean;
  balance?: number;
}

export interface SigningResult {
  signature: string;
  transaction: Transaction | VersionedTransaction;
  success: boolean;
  error?: string;
}

export class SecureWallet {
  private keypair: Keypair | null = null;
  private isInitialized = false;

  constructor() {
    // Initialize wallet on creation
    this.initialize();
  }

  /**
   * Initialize wallet from environment configuration
   */
  private initialize(): void {
    try {
      // Check for direct secret key first (preferred)
      if (config.WALLET_SECRET_KEY && config.WALLET_SECRET_KEY !== 'your_base58_private_key_here') {
        this.loadFromSecretKey(config.WALLET_SECRET_KEY);
      } else if (config.ENCRYPTED_PRIVATE_KEY && config.ENCRYPTED_PRIVATE_KEY !== 'your_encrypted_private_key_here') {
        this.loadFromEncryptedKey(config.ENCRYPTED_PRIVATE_KEY, config.WALLET_PASSWORD || '');
      } else if (!isProduction) {
        // Generate or load development wallet
        this.loadOrGenerateDevWallet();
      } else {
        throw new Error('No valid wallet configuration found in production');
      }

      // Validate that we have the expected wallet address
      const expectedAddress = config.TRADING_WALLET_ADDRESS;
      const actualAddress = this.getAddress();
      
      if (expectedAddress && expectedAddress !== actualAddress) {
        apiLogger.warn({
          expected: expectedAddress,
          actual: actualAddress,
        }, 'Wallet address mismatch - continuing with loaded wallet');
      }

      this.isInitialized = true;
      apiLogger.info({
        address: this.getAddress(),
        expectedAddress: expectedAddress,
        isProduction,
      }, 'Wallet initialized');
    } catch (error) {
      apiLogger.error({ error }, 'Failed to initialize wallet');
      throw error;
    }
  }

  /**
   * Load wallet from encrypted private key
   */
  private loadFromEncryptedKey(encryptedKey: string, password: string): void {
    try {
      // For development, treat the "encrypted" key as a base58 private key
      // In production, this would implement proper encryption/decryption
      if (!password && !isProduction) {
        apiLogger.warn('No wallet password provided, treating encrypted key as base58 private key');
        this.loadFromSecretKey(encryptedKey);
        return;
      }
      
      // For now, assume the encrypted key is actually a base58 private key
      // TODO: Implement proper encryption/decryption in production
      this.loadFromSecretKey(encryptedKey);
      
      apiLogger.info('Wallet loaded from encrypted private key');
    } catch (error) {
      apiLogger.error({ error }, 'Failed to load wallet from encrypted private key');
      throw new Error('Invalid encrypted private key or password');
    }
  }

  /**
   * Load wallet from base58 secret key
   */
  private loadFromSecretKey(secretKey: string): void {
    try {
      // Decode base58 secret key
      const decoded = this.decodeBase58(secretKey);
      if (decoded.length !== 64) {
        throw new Error('Invalid secret key length');
      }

      this.keypair = Keypair.fromSecretKey(new Uint8Array(decoded));
      
      // Verify against public key if provided
      if (config.WALLET_PUBLIC_KEY && config.WALLET_PUBLIC_KEY !== 'your_wallet_public_key_here') {
        const expectedPublicKey = config.WALLET_PUBLIC_KEY;
        const actualPublicKey = this.keypair.publicKey.toBase58();
        
        if (expectedPublicKey !== actualPublicKey) {
          apiLogger.warn({
            expected: expectedPublicKey,
            actual: actualPublicKey,
          }, 'Public key mismatch - using derived public key');
        }
      }

      apiLogger.info('Wallet loaded from secret key');
    } catch (error) {
      apiLogger.error({ error }, 'Failed to load wallet from secret key');
      throw new Error('Invalid wallet secret key');
    }
  }

  /**
   * Load or generate development wallet
   */
  private loadOrGenerateDevWallet(): void {
    const devWalletPath = path.join(process.cwd(), '.dev-wallet.json');
    
    try {
      if (fs.existsSync(devWalletPath)) {
        // Load existing development wallet
        const walletData = JSON.parse(fs.readFileSync(devWalletPath, 'utf8'));
        this.keypair = Keypair.fromSecretKey(new Uint8Array(walletData.secretKey));
        apiLogger.info('Development wallet loaded from file');
      } else {
        // Generate new development wallet
        this.keypair = Keypair.generate();
        
        // Save to file for persistence
        const walletData = {
          publicKey: this.keypair.publicKey.toBase58(),
          secretKey: Array.from(this.keypair.secretKey),
          created: new Date().toISOString(),
        };
        
        fs.writeFileSync(devWalletPath, JSON.stringify(walletData, null, 2));
        fs.chmodSync(devWalletPath, 0o600); // Restrict file permissions
        
        apiLogger.info({
          address: this.keypair.publicKey.toBase58(),
          file: devWalletPath,
        }, 'New development wallet generated and saved');
      }
    } catch (error) {
      apiLogger.error({ error }, 'Failed to load/generate development wallet');
      throw error;
    }
  }

  /**
   * Base58 decode utility (simplified implementation)
   */
  private decodeBase58(input: string): number[] {
    const alphabet = '**********************************************************';
    const base = alphabet.length;
    
    let result = [0];
    
    for (const char of input) {
      const charIndex = alphabet.indexOf(char);
      if (charIndex === -1) {
        throw new Error(`Invalid base58 character: ${char}`);
      }
      
      let carry = charIndex;
      for (let i = 0; i < result.length; i++) {
        carry += result[i] * base;
        result[i] = carry & 0xff;
        carry >>= 8;
      }
      
      while (carry > 0) {
        result.push(carry & 0xff);
        carry >>= 8;
      }
    }
    
    // Count leading zeros
    let leadingZeros = 0;
    for (const char of input) {
      if (char === '1') leadingZeros++;
      else break;
    }
    
    // Add leading zeros
    for (let i = 0; i < leadingZeros; i++) {
      result.push(0);
    }
    
    return result.reverse();
  }

  /**
   * Get wallet information
   */
  getWalletInfo(): WalletInfo {
    if (!this.keypair) {
      return {
        publicKey: '',
        address: '',
        isValid: false,
      };
    }

    return {
      publicKey: this.keypair.publicKey.toBase58(),
      address: this.keypair.publicKey.toBase58(),
      isValid: true,
    };
  }

  /**
   * Get wallet address
   */
  getAddress(): string {
    return this.keypair?.publicKey.toBase58() || '';
  }

  /**
   * Get public key
   */
  getPublicKey(): PublicKey | null {
    return this.keypair?.publicKey || null;
  }

  /**
   * Check if wallet is initialized and valid
   */
  isReady(): boolean {
    return this.isInitialized && this.keypair !== null;
  }

  /**
   * Sign a transaction with the wallet's private key
   */
  async signTransaction(transaction: Transaction | VersionedTransaction): Promise<SigningResult> {
    if (!this.keypair) {
      return {
        signature: '',
        transaction,
        success: false,
        error: 'Wallet not initialized',
      };
    }

    try {
      let signature = '';
      
      if (transaction instanceof VersionedTransaction) {
        // Sign versioned transaction
        transaction.sign([this.keypair]);
        signature = transaction.signatures[0] ? Buffer.from(transaction.signatures[0]).toString('base64') : '';
      } else {
        // Sign legacy transaction
        transaction.sign(this.keypair);
        signature = transaction.signature?.toString('base64') || '';
      }
      
      apiLogger.info({
        from: this.getAddress(),
        transactionType: transaction instanceof VersionedTransaction ? 'VersionedTransaction' : 'Transaction',
        signature: signature.slice(0, 8) + '...', // Log partial signature for security
      }, 'Transaction signed successfully');

      return {
        signature,
        transaction,
        success: true,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      apiLogger.error({
        error: errorMessage,
        from: this.getAddress(),
        transactionType: transaction instanceof VersionedTransaction ? 'VersionedTransaction' : 'Transaction',
      }, 'Transaction signing failed');

      return {
        signature: '',
        transaction,
        success: false,
        error: errorMessage,
      };
    }
  }


  /**
   * Create a simple transfer transaction (mock)
   */
  async createTransferTransaction(
    to: string,
    amountLamports: number
  ): Promise<Transaction> {
    if (!this.keypair) {
      throw new Error('Wallet not initialized');
    }

    try {
      const toPublicKey = new PublicKey(to);
      
      // Create a mock transfer transaction
      const transaction = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey: this.keypair.publicKey,
          toPubkey: toPublicKey,
          lamports: amountLamports,
        })
      );

      return transaction;
    } catch (error) {
      apiLogger.error({ error, to, amountLamports }, 'Failed to create transfer transaction');
      throw error;
    }
  }

  /**
   * Validate an address
   */
  static validateAddress(address: string): boolean {
    try {
      new PublicKey(address);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Convert SOL to lamports
   */
  static solToLamports(sol: number): number {
    return Math.floor(sol * LAMPORTS_PER_SOL);
  }

  /**
   * Convert lamports to SOL
   */
  static lamportsToSol(lamports: number): number {
    return lamports / LAMPORTS_PER_SOL;
  }
}

// Global wallet instance
let globalWallet: SecureWallet | null = null;

/**
 * Get or create global wallet instance
 */
export function getWallet(): SecureWallet {
  if (!globalWallet) {
    globalWallet = new SecureWallet();
  }
  return globalWallet;
}

/**
 * Reset global wallet instance (for testing)
 */
export function resetWallet(): void {
  globalWallet = null;
}