import { Connection, PublicKey, LAMPORTS_PER_SOL } from '@solana/web3.js';
import { apiLogger } from '../lib/logger';
import { walletConfig } from '../lib/config';
import { getWallet, WalletInfo } from '../lib/wallet';

export interface WalletBalance {
  sol: number;
  lamports: number;
  lastUpdated: Date;
}

export interface WalletValidationResult {
  isValid: boolean;
  address?: string;
  error?: string;
}

export class WalletService {
  private connection: Connection;
  private knownTokens: Record<string, { symbol: string; name: string; decimals: number }>;
  
  constructor() {
    this.connection = new Connection(walletConfig.rpcUrl, 'confirmed');
    
    // Known token registry for metadata
    this.knownTokens = {
      'So11111111111111111111111111111111111111112': { symbol: 'SOL', name: 'Solana', decimals: 9 },
      'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': { symbol: 'USDC', name: 'USD Coin', decimals: 6 },
      'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': { symbol: 'USDT', name: 'Tether USD', decimals: 6 },
      'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263': { symbol: 'BONK', name: 'Bonk', decimals: 5 },
      'JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN': { symbol: 'JUP', name: 'Jupiter', decimals: 6 },
      'rndrizKT3MK1iimdxRdWabcF7Zg7AR5T4nud4EkHBof': { symbol: 'RND', name: 'Render Network', decimals: 8 },
      'FYXnJhgLmhcELqsKyAfdKZExFfA7gaskQa5TuEZE9RZT': { symbol: 'TEST', name: 'Test Token', decimals: 6 },
      // Add more tokens as needed
    };
    
    this.logMainnetWarning();
  }

  private logMainnetWarning(): void {
    if (walletConfig.network === 'mainnet-beta') {
      apiLogger.warn({
        network: walletConfig.network,
        maxTransactionAmount: walletConfig.maxTransactionAmount,
      }, '🔴 MAINNET TRADING ENABLED - USING REAL MONEY');
    } else {
      apiLogger.info({
        network: walletConfig.network,
      }, '🟡 Using test network');
    }
  }

  /**
   * Get wallet information including address and connection status
   */
  async getWalletInfo(): Promise<WalletInfo & { network: string }> {
    try {
      const wallet = getWallet();
      const walletInfo = wallet.getWalletInfo();
      
      return {
        ...walletInfo,
        network: walletConfig.network,
      };
    } catch (error) {
      apiLogger.error({ error }, 'Failed to get wallet info');
      return {
        publicKey: '',
        address: '',
        isValid: false,
        network: walletConfig.network,
      };
    }
  }

  /**
   * Get wallet balance in SOL and lamports
   */
  async getWalletBalance(): Promise<WalletBalance> {
    try {
      const wallet = getWallet();
      const publicKey = wallet.getPublicKey();
      
      if (!publicKey) {
        throw new Error('Wallet not initialized');
      }

      apiLogger.info({
        publicKey: publicKey.toString(),
        rpcUrl: this.connection.rpcEndpoint,
      }, 'Attempting to fetch wallet balance');

      const lamports = await this.connection.getBalance(publicKey);
      const sol = lamports / LAMPORTS_PER_SOL;

      const balance: WalletBalance = {
        sol,
        lamports,
        lastUpdated: new Date(),
      };

      apiLogger.info({
        address: wallet.getAddress(),
        balance: sol,
        network: walletConfig.network,
      }, 'Wallet balance fetched');

      return balance;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorDetails = {
        message: errorMessage,
        stack: error instanceof Error ? error.stack : undefined,
        rpcUrl: this.connection?.rpcEndpoint || 'undefined',
      };
      
      apiLogger.error(errorDetails, 'Failed to fetch wallet balance');
      throw new Error(`Failed to fetch wallet balance: ${errorMessage}`);
    }
  }

  /**
   * Validate wallet connection and permissions
   */
  async validateWallet(): Promise<WalletValidationResult> {
    try {
      const wallet = getWallet();
      
      if (!wallet.isReady()) {
        return {
          isValid: false,
          error: 'Wallet not initialized or invalid private key',
        };
      }

      const address = wallet.getAddress();
      const publicKey = wallet.getPublicKey();
      
      if (!publicKey) {
        return {
          isValid: false,
          error: 'Unable to get wallet public key',
        };
      }
      
      // Try to fetch balance to verify RPC connection
      try {
        await this.connection.getBalance(publicKey);
      } catch (rpcError) {
        const rpcErrorMessage = rpcError instanceof Error ? rpcError.message : 'RPC connection failed';
        return {
          isValid: false,
          error: `RPC connection failed: ${rpcErrorMessage}`,
        };
      }
      
      // Validate transaction limits
      if (walletConfig.maxTransactionAmount <= 0) {
        return {
          isValid: false,
          error: 'Invalid transaction amount limit configured',
        };
      }

      apiLogger.info({
        address,
        network: walletConfig.network,
        maxTransactionAmount: walletConfig.maxTransactionAmount,
      }, 'Wallet validation successful');

      return {
        isValid: true,
        address,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      apiLogger.error({ error: errorMessage }, 'Wallet validation failed');
      
      return {
        isValid: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Validate a Solana address
   */
  validateAddress(address: string): boolean {
    try {
      new PublicKey(address);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Convert SOL to lamports
   */
  solToLamports(sol: number): number {
    return Math.floor(sol * LAMPORTS_PER_SOL);
  }

  /**
   * Convert lamports to SOL
   */
  lamportsToSol(lamports: number): number {
    return lamports / LAMPORTS_PER_SOL;
  }

  /**
   * Get network information
   */
  getNetworkInfo(): { network: string; rpcUrl: string; isMainnet: boolean } {
    return {
      network: walletConfig.network,
      rpcUrl: walletConfig.rpcUrl,
      isMainnet: walletConfig.network === 'mainnet-beta',
    };
  }

  /**
   * Check if transaction amount is within limits
   */
  validateTransactionAmount(amountSol: number, inputMint?: string): { isValid: boolean; error?: string } {
    if (amountSol <= 0) {
      return { isValid: false, error: 'Transaction amount must be positive' };
    }

    // Apply different limits based on token type
    if (!inputMint || inputMint === 'So11111111111111111111111111111111111111112') {
      // SOL transaction limits
      if (amountSol > walletConfig.maxTransactionAmount) {
        return { 
          isValid: false, 
          error: `SOL transaction amount ${amountSol} exceeds limit of ${walletConfig.maxTransactionAmount} SOL` 
        };
      }
    } else {
      // For other tokens, use a much higher limit (1M tokens)
      if (amountSol > 1000000) {
        return { 
          isValid: false, 
          error: `Token transaction amount ${amountSol} exceeds limit of 1,000,000 tokens` 
        };
      }
    }

    return { isValid: true };
  }

  /**
   * Get SPL token balances for the wallet
   */
  async getTokenBalances(): Promise<Array<{
    mint: string;
    amount: string;
    decimals: number;
    uiAmount: number;
    symbol?: string;
    name?: string;
  }>> {
    try {
      const wallet = getWallet();
      const publicKey = wallet.getPublicKey();
      
      if (!publicKey) {
        throw new Error('Wallet not initialized');
      }

      apiLogger.info({ publicKey: publicKey.toString() }, 'Fetching token balances');
      
      // Get all token accounts for this wallet
      const tokenAccounts = await this.connection.getParsedTokenAccountsByOwner(
        publicKey,
        {
          programId: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'), // SPL Token program
        }
      );

      const balances = tokenAccounts.value
        .map(tokenAccount => {
          const accountData = tokenAccount.account.data;
          if ('parsed' in accountData) {
            const parsedInfo = accountData.parsed.info;
            const mint = parsedInfo.mint;
            const tokenInfo = this.knownTokens[mint];
            
            return {
              mint,
              amount: parsedInfo.tokenAmount.amount,
              decimals: parsedInfo.tokenAmount.decimals,
              uiAmount: parsedInfo.tokenAmount.uiAmount || 0,
              symbol: tokenInfo?.symbol,
              name: tokenInfo?.name,
            };
          }
          return null;
        })
        .filter(balance => balance !== null && balance.uiAmount > 0) // Only non-zero balances
        .sort((a, b) => b.uiAmount - a.uiAmount); // Sort by amount descending

      apiLogger.info({ 
        tokenCount: balances.length,
        totalAccounts: tokenAccounts.value.length 
      }, 'Token balances fetched successfully');

      return balances;
    } catch (error) {
      apiLogger.error({ error }, 'Failed to fetch token balances');
      throw new Error('Unable to fetch token balances');
    }
  }

  /**
   * Get balance for a specific token
   */
  async getTokenBalance(tokenMint: string): Promise<{
    mint: string;
    amount: string;
    decimals: number;
    uiAmount: number;
    symbol?: string;
    name?: string;
  } | null> {
    try {
      const wallet = getWallet();
      const publicKey = wallet.getPublicKey();
      
      if (!publicKey) {
        throw new Error('Wallet not initialized');
      }

      apiLogger.info({ publicKey: publicKey.toString(), tokenMint }, 'Fetching specific token balance');
      
      // Get token accounts for specific mint
      const tokenAccounts = await this.connection.getParsedTokenAccountsByOwner(
        publicKey,
        {
          mint: new PublicKey(tokenMint),
        }
      );

      if (tokenAccounts.value.length === 0) {
        apiLogger.info({ tokenMint }, 'No token accounts found for mint');
        return null;
      }

      // Use the first (largest) token account
      const tokenAccount = tokenAccounts.value[0];
      const accountData = tokenAccount.account.data;
      
      if ('parsed' in accountData) {
        const parsedInfo = accountData.parsed.info;
        const mint = parsedInfo.mint;
        const tokenInfo = this.knownTokens[mint];
        
        const balance = {
          mint,
          amount: parsedInfo.tokenAmount.amount,
          decimals: parsedInfo.tokenAmount.decimals,
          uiAmount: parsedInfo.tokenAmount.uiAmount || 0,
          symbol: tokenInfo?.symbol,
          name: tokenInfo?.name,
        };

        apiLogger.info({ tokenMint, balance: balance.uiAmount }, 'Token balance fetched');
        return balance;
      }

      return null;
    } catch (error) {
      apiLogger.error({ error, tokenMint }, 'Failed to fetch specific token balance');
      return null;
    }
  }
}