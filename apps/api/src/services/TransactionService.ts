import { Connection, PublicKey, Transaction, sendAndConfirmTransaction, SystemProgram, LAMPORTS_PER_SOL } from '@solana/web3.js';
import { getWallet, getConnection } from '../lib/solana';
import { walletConfig } from '../lib/config';

interface TransactionParams {
  recipient: string;
  amountSol: number;
  memo?: string;
}

interface TransactionValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

interface TransactionLimits {
  maxPerTransaction: number;
  maxDaily: number;
  maxCount: number;
  requireConfirmation: boolean;
}

interface TransactionResult {
  signature: string;
  success: boolean;
  confirmationStatus: 'processed' | 'confirmed' | 'finalized';
  slot?: number;
  fee?: number;
  error?: string;
}

interface TransactionHistory {
  signature: string;
  type: 'transfer' | 'swap';
  recipient?: string;
  amount: number;
  fee: number;
  timestamp: Date;
  status: 'success' | 'failed';
}

export class TransactionService {
  private connection: Connection;
  private dailyTransactionCount: Map<string, number> = new Map();
  private dailyTransactionAmount: Map<string, number> = new Map();
  private transactionHistory: TransactionHistory[] = [];

  constructor() {
    this.connection = getConnection();
  }

  /**
   * Get current transaction limits
   */
  getTransactionLimits(): TransactionLimits {
    return {
      maxPerTransaction: walletConfig.maxTransactionAmount || 0.05,
      maxDaily: (walletConfig.maxTransactionAmount || 0.05) * 10, // 10x per transaction limit
      maxCount: 50, // Maximum 50 transactions per day
      requireConfirmation: true, // Always require confirmation for safety
    };
  }

  /**
   * Validate transaction parameters before signing
   */
  validateTransaction(params: TransactionParams): TransactionValidation {
    const errors: string[] = [];
    const warnings: string[] = [];
    const limits = this.getTransactionLimits();

    // Validate recipient address
    try {
      new PublicKey(params.recipient);
    } catch (error) {
      errors.push('Invalid recipient address format');
    }

    // Validate amount
    if (params.amountSol <= 0) {
      errors.push('Transaction amount must be positive');
    }

    if (params.amountSol > limits.maxPerTransaction) {
      errors.push(`Transaction amount exceeds limit of ${limits.maxPerTransaction} SOL`);
    }

    // Check daily limits
    const today = new Date().toDateString();
    const dailyAmount = this.dailyTransactionAmount.get(today) || 0;
    const dailyCount = this.dailyTransactionCount.get(today) || 0;

    if (dailyAmount + params.amountSol > limits.maxDaily) {
      errors.push(`Daily transaction limit would be exceeded (${limits.maxDaily} SOL)`);
    }

    if (dailyCount >= limits.maxCount) {
      errors.push(`Daily transaction count limit exceeded (${limits.maxCount} transactions)`);
    }

    // Add warnings for large amounts
    if (params.amountSol > limits.maxPerTransaction * 0.5) {
      warnings.push('Large transaction amount - please double-check recipient address');
    }

    if (params.amountSol > limits.maxPerTransaction * 0.8) {
      warnings.push('⚠️ MAINNET TRADING: This transaction uses real SOL');
    }

    // Validate network
    if (walletConfig.network === 'mainnet-beta') {
      warnings.push('🔴 LIVE MAINNET TRANSACTION - Real money will be spent');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Estimate transaction fee
   */
  async estimateTransactionFee(params: TransactionParams): Promise<number> {
    try {
      const wallet = getWallet();
      const recipientPubkey = new PublicKey(params.recipient);
      const lamports = Math.floor(params.amountSol * LAMPORTS_PER_SOL);

      // Create a test transaction to estimate fee
      const transaction = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey: wallet.getPublicKey(),
          toPubkey: recipientPubkey,
          lamports,
        })
      );

      // Get recent blockhash for fee calculation
      const { blockhash } = await this.connection.getLatestBlockhash();
      transaction.recentBlockhash = blockhash;
      transaction.feePayer = wallet.getPublicKey();

      // Calculate fee
      const fee = await transaction.getEstimatedFee(this.connection);
      return fee ? fee / LAMPORTS_PER_SOL : 0.000005; // Default estimate if calculation fails
    } catch (error) {
      console.warn('Fee estimation failed, using default:', error);
      return 0.000005; // Conservative default fee estimate
    }
  }

  /**
   * Build a simple SOL transfer transaction
   */
  private async buildSolTransferTransaction(params: TransactionParams): Promise<Transaction> {
    const wallet = getWallet();
    const recipientPubkey = new PublicKey(params.recipient);
    const lamports = Math.floor(params.amountSol * LAMPORTS_PER_SOL);

    const transaction = new Transaction().add(
      SystemProgram.transfer({
        fromPubkey: wallet.getPublicKey(),
        toPubkey: recipientPubkey,
        lamports,
      })
    );

    // Get recent blockhash
    const { blockhash } = await this.connection.getLatestBlockhash();
    transaction.recentBlockhash = blockhash;
    transaction.feePayer = wallet.getPublicKey();

    return transaction;
  }

  /**
   * Sign and send a SOL transfer transaction
   */
  async signAndSendTransaction(params: TransactionParams, simulate: boolean = false): Promise<TransactionResult> {
    // Validate transaction
    const validation = this.validateTransaction(params);
    if (!validation.isValid) {
      throw new Error(`Transaction validation failed: ${validation.errors.join(', ')}`);
    }

    try {
      const wallet = getWallet();
      const transaction = await this.buildSolTransferTransaction(params);
      
      // If simulating, just return mock result
      if (simulate) {
        const estimatedFee = await this.estimateTransactionFee(params);
        return {
          signature: 'SIMULATION_' + Date.now(),
          success: true,
          confirmationStatus: 'processed',
          fee: estimatedFee,
        };
      }

      // Sign the transaction
      transaction.sign(wallet.getKeypair());

      // Send and confirm transaction
      const signature = await sendAndConfirmTransaction(
        this.connection,
        transaction,
        [wallet.getKeypair()],
        {
          commitment: 'confirmed',
          preflightCommitment: 'confirmed',
        }
      );

      // Get transaction details
      const txDetails = await this.connection.getTransaction(signature, {
        commitment: 'confirmed',
      });

      // Update daily limits
      this.updateDailyLimits(params.amountSol);

      // Add to transaction history
      this.addToHistory({
        signature,
        type: 'transfer',
        recipient: params.recipient,
        amount: params.amountSol,
        fee: txDetails?.meta?.fee ? txDetails.meta.fee / LAMPORTS_PER_SOL : 0,
        timestamp: new Date(),
        status: 'success',
      });

      return {
        signature,
        success: true,
        confirmationStatus: 'confirmed',
        slot: txDetails?.slot,
        fee: txDetails?.meta?.fee ? txDetails.meta.fee / LAMPORTS_PER_SOL : 0,
      };

    } catch (error) {
      // Add failed transaction to history
      this.addToHistory({
        signature: '',
        type: 'transfer',
        recipient: params.recipient,
        amount: params.amountSol,
        fee: 0,
        timestamp: new Date(),
        status: 'failed',
      });

      console.error('Transaction failed:', error);
      return {
        signature: '',
        success: false,
        confirmationStatus: 'processed',
        error: error instanceof Error ? error.message : 'Unknown transaction error',
      };
    }
  }

  /**
   * Test wallet connection with a minimal transaction
   */
  async testWalletConnection(): Promise<{
    success: boolean;
    address: string;
    balance: number;
    canSign: boolean;
    error?: string;
  }> {
    try {
      const wallet = getWallet();
      const publicKey = wallet.getPublicKey();
      const balance = await this.connection.getBalance(publicKey);

      // Test signing capability (without sending)
      const testTransaction = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey: publicKey,
          toPubkey: publicKey, // Send to self
          lamports: 1, // 1 lamport
        })
      );

      const { blockhash } = await this.connection.getLatestBlockhash();
      testTransaction.recentBlockhash = blockhash;
      testTransaction.feePayer = publicKey;

      // Test signing (but don't send)
      testTransaction.sign(wallet.getKeypair());

      return {
        success: true,
        address: publicKey.toBase58(),
        balance: balance / LAMPORTS_PER_SOL,
        canSign: true,
      };
    } catch (error) {
      return {
        success: false,
        address: '',
        balance: 0,
        canSign: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get transaction status
   */
  async getTransactionStatus(signature: string): Promise<{
    signature: string;
    confirmations: number;
    status: 'processed' | 'confirmed' | 'finalized' | 'failed';
    slot?: number;
    blockTime?: number;
  }> {
    try {
      const status = await this.connection.getSignatureStatus(signature);
      const txDetails = await this.connection.getTransaction(signature);

      return {
        signature,
        confirmations: status.value?.confirmations || 0,
        status: status.value?.confirmationStatus || 'failed',
        slot: txDetails?.slot,
        blockTime: txDetails?.blockTime || undefined,
      };
    } catch (error) {
      return {
        signature,
        confirmations: 0,
        status: 'failed',
      };
    }
  }

  /**
   * Get transaction history
   */
  getTransactionHistory(): TransactionHistory[] {
    return [...this.transactionHistory].sort(
      (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
    );
  }

  /**
   * Get daily transaction stats
   */
  getDailyStats(): {
    transactionsToday: number;
    amountToday: number;
    limits: TransactionLimits;
    remainingAmount: number;
    remainingCount: number;
  } {
    const today = new Date().toDateString();
    const transactionsToday = this.dailyTransactionCount.get(today) || 0;
    const amountToday = this.dailyTransactionAmount.get(today) || 0;
    const limits = this.getTransactionLimits();

    return {
      transactionsToday,
      amountToday,
      limits,
      remainingAmount: Math.max(0, limits.maxDaily - amountToday),
      remainingCount: Math.max(0, limits.maxCount - transactionsToday),
    };
  }

  /**
   * Update daily transaction limits
   */
  private updateDailyLimits(amount: number): void {
    const today = new Date().toDateString();
    
    const currentCount = this.dailyTransactionCount.get(today) || 0;
    const currentAmount = this.dailyTransactionAmount.get(today) || 0;

    this.dailyTransactionCount.set(today, currentCount + 1);
    this.dailyTransactionAmount.set(today, currentAmount + amount);
  }

  /**
   * Add transaction to history
   */
  private addToHistory(tx: TransactionHistory): void {
    this.transactionHistory.push(tx);
    
    // Keep only last 100 transactions in memory
    if (this.transactionHistory.length > 100) {
      this.transactionHistory.shift();
    }
  }

  /**
   * Reset daily limits (for testing)
   */
  resetDailyLimits(): void {
    this.dailyTransactionCount.clear();
    this.dailyTransactionAmount.clear();
  }

  /**
   * Clear transaction history (for privacy)
   */
  clearTransactionHistory(): void {
    this.transactionHistory = [];
  }
}

// Export singleton instance
export const transactionService = new TransactionService();