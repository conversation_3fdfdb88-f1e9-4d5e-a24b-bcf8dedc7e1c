import axios from 'axios';
import { PublicKey } from '@solana/web3.js';
import { apiLogger } from '../lib/logger';

export interface TokenMetadata {
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  logoURI?: string;
  verified?: boolean;
  tags?: string[];
  description?: string;
  website?: string;
  twitter?: string;
  telegram?: string;
  discord?: string;
}

export interface TokenSearchResult {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  logoURI?: string;
  verified: boolean;
  rank?: number;
  daily_volume?: number;
}

interface JupiterTokenListEntry {
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  logoURI?: string;
  tags?: string[];
}

interface SolanaTokenRegistryEntry {
  chainId: number;
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  logoURI?: string;
  tags?: string[];
  extensions?: {
    website?: string;
    twitter?: string;
    telegram?: string;
    discord?: string;
    description?: string;
  };
}

export class TokenMetadataService {
  private jupiterTokenList: Map<string, JupiterTokenListEntry> = new Map();
  private tokenListLastUpdated: Date | null = null;
  private readonly TOKEN_LIST_CACHE_DURATION = 300000; // 5 minutes

  constructor() {
    // Initialize with popular tokens
    this.initializeKnownTokens();
  }

  private initializeKnownTokens() {
    const knownTokens: JupiterTokenListEntry[] = [
      {
        address: 'So1111111111111****************************',
        name: 'Solana',
        symbol: 'SOL',
        decimals: 9,
        logoURI: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So1111111111111****************************/logo.png',
        tags: ['verified'],
      },
      {
        address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
        name: 'USD Coin',
        symbol: 'USDC',
        decimals: 6,
        logoURI: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v/logo.png',
        tags: ['verified', 'stablecoin'],
      },
      {
        address: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
        name: 'Tether USD',
        symbol: 'USDT',
        decimals: 6,
        logoURI: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB/logo.png',
        tags: ['verified', 'stablecoin'],
      },
      {
        address: 'JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN',
        name: 'Jupiter',
        symbol: 'JUP',
        decimals: 6,
        logoURI: 'https://static.jup.ag/jup/icon.png',
        tags: ['verified'],
      },
    ];

    knownTokens.forEach(token => {
      this.jupiterTokenList.set(token.address, token);
    });
  }

  /**
   * Validate Solana address format
   */
  private isValidSolanaAddress(address: string): boolean {
    try {
      new PublicKey(address);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get token metadata by address
   */
  async getTokenMetadata(address: string): Promise<TokenMetadata> {
    if (!this.isValidSolanaAddress(address)) {
      throw new Error('Invalid Solana address format');
    }

    try {
      // Ensure we have the latest token list
      await this.refreshTokenListIfNeeded();

      // Check local cache first
      const cachedToken = this.jupiterTokenList.get(address);
      if (cachedToken) {
        apiLogger.info({ tokenAddress: address }, 'Token metadata found in cache');
        return this.mapToTokenMetadata(cachedToken);
      }

      // Try fetching from Jupiter API directly
      const jupiterMetadata = await this.fetchFromJupiterAPI(address);
      if (jupiterMetadata) {
        return jupiterMetadata;
      }

      // Try Solana token registry
      const registryMetadata = await this.fetchFromSolanaRegistry(address);
      if (registryMetadata) {
        return registryMetadata;
      }

      // Return basic metadata as fallback
      apiLogger.warn({ tokenAddress: address }, 'Token metadata not found, using fallback');
      return {
        address,
        name: 'Unknown Token',
        symbol: 'UNK',
        decimals: 9,
        verified: false,
      };

    } catch (error) {
      apiLogger.error({ error, tokenAddress: address }, 'Failed to fetch token metadata');
      throw new Error(`Failed to fetch token metadata: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Search tokens by symbol or name
   */
  async searchTokens(query: string, limit: number = 10): Promise<TokenSearchResult[]> {
    if (!query || query.length < 2) {
      return [];
    }

    try {
      await this.refreshTokenListIfNeeded();

      const searchQuery = query.toLowerCase();
      const results: TokenSearchResult[] = [];

      // Search through cached token list
      for (const [address, token] of this.jupiterTokenList) {
        if (results.length >= limit) break;

        const symbolMatch = token.symbol.toLowerCase().includes(searchQuery);
        const nameMatch = token.name.toLowerCase().includes(searchQuery);
        const addressMatch = address.toLowerCase().startsWith(searchQuery);

        if (symbolMatch || nameMatch || addressMatch) {
          results.push({
            address,
            symbol: token.symbol,
            name: token.name,
            decimals: token.decimals,
            logoURI: token.logoURI,
            verified: token.tags?.includes('verified') || false,
          });
        }
      }

      // Sort by relevance (exact symbol match first, then partial matches)
      results.sort((a, b) => {
        const aExactSymbol = a.symbol.toLowerCase() === searchQuery;
        const bExactSymbol = b.symbol.toLowerCase() === searchQuery;
        
        if (aExactSymbol && !bExactSymbol) return -1;
        if (!aExactSymbol && bExactSymbol) return 1;
        
        const aStartsWithSymbol = a.symbol.toLowerCase().startsWith(searchQuery);
        const bStartsWithSymbol = b.symbol.toLowerCase().startsWith(searchQuery);
        
        if (aStartsWithSymbol && !bStartsWithSymbol) return -1;
        if (!aStartsWithSymbol && bStartsWithSymbol) return 1;
        
        return a.symbol.localeCompare(b.symbol);
      });

      apiLogger.info({ query, resultsCount: results.length }, 'Token search completed');
      return results;

    } catch (error) {
      apiLogger.error({ error, query }, 'Failed to search tokens');
      return [];
    }
  }

  /**
   * Refresh token list from Jupiter if cache is stale
   */
  private async refreshTokenListIfNeeded(): Promise<void> {
    const now = new Date();
    
    if (this.tokenListLastUpdated && 
        (now.getTime() - this.tokenListLastUpdated.getTime()) < this.TOKEN_LIST_CACHE_DURATION) {
      return;
    }

    try {
      apiLogger.info('Refreshing Jupiter token list');
      const response = await axios.get('https://token.jup.ag/strict', {
        timeout: 10000,
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'BMad-Trading-App/1.0'
        }
      });

      if (response.data && Array.isArray(response.data)) {
        // Clear existing cache and rebuild
        this.jupiterTokenList.clear();
        this.initializeKnownTokens(); // Re-add known tokens

        response.data.forEach((token: JupiterTokenListEntry) => {
          if (token.address && token.symbol && token.name && typeof token.decimals === 'number') {
            this.jupiterTokenList.set(token.address, token);
          }
        });

        this.tokenListLastUpdated = now;
        apiLogger.info({ tokenCount: this.jupiterTokenList.size }, 'Jupiter token list updated');
      }
    } catch (error) {
      apiLogger.error({ error }, 'Failed to refresh Jupiter token list');
      // Continue with cached data
    }
  }

  /**
   * Fetch token metadata directly from Jupiter API
   */
  private async fetchFromJupiterAPI(address: string): Promise<TokenMetadata | null> {
    try {
      // Jupiter doesn't have a single token endpoint, but we can try their price API
      // which sometimes includes token metadata
      const response = await axios.get(`https://price.jup.ag/v4/price`, {
        params: { ids: address },
        timeout: 5000,
      });

      if (response.data?.data?.[address]) {
        const priceData = response.data.data[address];
        return {
          address,
          name: priceData.name || 'Unknown Token',
          symbol: priceData.symbol || 'UNK',
          decimals: priceData.decimals || 9,
          verified: false,
        };
      }
    } catch (error) {
      apiLogger.debug({ error, address }, 'Failed to fetch from Jupiter API');
    }
    
    return null;
  }

  /**
   * Fetch token metadata from Solana token registry
   */
  private async fetchFromSolanaRegistry(address: string): Promise<TokenMetadata | null> {
    try {
      const response = await axios.get(
        'https://raw.githubusercontent.com/solana-labs/token-list/main/src/tokens/solana.tokenlist.json',
        { timeout: 5000 }
      );

      if (response.data?.tokens) {
        const token = response.data.tokens.find((t: SolanaTokenRegistryEntry) => t.address === address);
        if (token) {
          return {
            address: token.address,
            name: token.name,
            symbol: token.symbol,
            decimals: token.decimals,
            logoURI: token.logoURI,
            verified: token.tags?.includes('verified') || false,
            website: token.extensions?.website,
            twitter: token.extensions?.twitter,
            telegram: token.extensions?.telegram,
            discord: token.extensions?.discord,
            description: token.extensions?.description,
          };
        }
      }
    } catch (error) {
      apiLogger.debug({ error, address }, 'Failed to fetch from Solana registry');
    }
    
    return null;
  }

  /**
   * Map Jupiter token list entry to TokenMetadata
   */
  private mapToTokenMetadata(token: JupiterTokenListEntry): TokenMetadata {
    return {
      address: token.address,
      name: token.name,
      symbol: token.symbol,
      decimals: token.decimals,
      logoURI: token.logoURI,
      verified: token.tags?.includes('verified') || false,
      tags: token.tags,
    };
  }

  /**
   * Get token list size for monitoring
   */
  getTokenListSize(): number {
    return this.jupiterTokenList.size;
  }

  /**
   * Get last updated timestamp
   */
  getLastUpdated(): Date | null {
    return this.tokenListLastUpdated;
  }
}

// Export singleton instance
export const tokenMetadataService = new TokenMetadataService();