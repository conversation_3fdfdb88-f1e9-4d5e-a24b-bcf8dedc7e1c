import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { EventEmitter } from 'events';
import {
  PriceMonitoringData,
  IPriceMonitoringService,
  ExitStrategyError
} from '../types/exit-strategy.js';

interface JupiterPriceV3Response {
  data: {
    [tokenAddress: string]: {
      id: string;
      mintSymbol: string;
      vsToken: string;
      vsTokenSymbol: string;
      price: number;
    };
  };
  timeTaken: number;
}

interface JupiterTokenInfo {
  address: string;
  chainId: number;
  decimals: number;
  name: string;
  symbol: string;
  logoURI?: string;
  tags?: string[];
}

export class JupiterPriceV3Service extends EventEmitter implements IPriceMonitoringService {
  private client: AxiosInstance;
  private monitoringTokens: Set<string> = new Set();
  private monitoringInterval: NodeJS.Timeout | null = null;
  private readonly POLLING_INTERVAL_MS = 30_000; // 30 seconds
  private readonly MAX_TOKENS_PER_REQUEST = 100;
  private lastPriceData: Map<string, PriceMonitoringData> = new Map();
  private readonly BASE_URL = 'https://price.jup.ag/v4';

  constructor() {
    super();
    this.client = axios.create({
      baseURL: this.BASE_URL,
      timeout: 10_000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'BMad-Trading-Bot/1.0.0'
      }
    });
  }

  async startMonitoring(tokenAddresses: string[]): Promise<void> {
    try {
      // Validate token addresses
      const validTokens = await this.validateTokenAddresses(tokenAddresses);

      // Add tokens to monitoring set
      for (const token of validTokens) {
        this.monitoringTokens.add(token);
      }

      console.log(`🎯 Started price monitoring for ${this.monitoringTokens.size} tokens`);

      // Start polling if not already running
      if (!this.monitoringInterval && this.monitoringTokens.size > 0) {
        await this.startPolling();
      }

      // Emit monitoring started event
      this.emit('monitoringStarted', {
        tokenAddresses: Array.from(this.monitoringTokens),
        timestamp: new Date()
      });

    } catch (error) {
      throw new ExitStrategyError(
        `Failed to start price monitoring: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'MONITORING_START_FAILED'
      );
    }
  }

  async stopMonitoring(tokenAddresses?: string[]): Promise<void> {
    try {
      if (tokenAddresses) {
        // Remove specific tokens
        for (const token of tokenAddresses) {
          this.monitoringTokens.delete(token);
          this.lastPriceData.delete(token);
        }

        console.log(`🎯 Stopped price monitoring for ${tokenAddresses.length} tokens`);
      } else {
        // Stop monitoring all tokens
        console.log(`🎯 Stopped price monitoring for all ${this.monitoringTokens.size} tokens`);
        this.monitoringTokens.clear();
        this.lastPriceData.clear();
      }

      // Stop polling if no tokens left
      if (this.monitoringTokens.size === 0 && this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
        this.monitoringInterval = null;
        console.log('🎯 Price monitoring polling stopped');
      }

      // Emit monitoring stopped event
      this.emit('monitoringStopped', {
        tokenAddresses: tokenAddresses || [],
        timestamp: new Date()
      });

    } catch (error) {
      throw new ExitStrategyError(
        `Failed to stop price monitoring: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'MONITORING_STOP_FAILED'
      );
    }
  }

  async getCurrentPrice(tokenAddress: string): Promise<number | null> {
    try {
      const response = await this.client.get<JupiterPriceV3Response>('/price', {
        params: {
          ids: tokenAddress,
          vsToken: 'So11111111111111111111111111111111111111112' // SOL
        }
      });

      const priceData = response.data.data[tokenAddress];
      return priceData ? priceData.price : null;

    } catch (error) {
      console.error(`Failed to get current price for ${tokenAddress}:`, error);
      return null;
    }
  }

  async getLatestPriceData(tokenAddress: string): Promise<PriceMonitoringData | null> {
    return this.lastPriceData.get(tokenAddress) || null;
  }

  private async startPolling(): Promise<void> {
    console.log(`🎯 Starting price polling every ${this.POLLING_INTERVAL_MS / 1000} seconds`);

    // Initial poll
    await this.pollPrices();

    // Set up interval
    this.monitoringInterval = setInterval(async () => {
      await this.pollPrices();
    }, this.POLLING_INTERVAL_MS);
  }

  private async pollPrices(): Promise<void> {
    if (this.monitoringTokens.size === 0) {
      return;
    }

    try {
      const tokenArray = Array.from(this.monitoringTokens);
      const timestamp = new Date();

      // Process tokens in batches
      const batches = this.chunkArray(tokenArray, this.MAX_TOKENS_PER_REQUEST);

      for (const batch of batches) {
        await this.processBatch(batch, timestamp);
      }

    } catch (error) {
      console.error('Error during price polling:', error);

      // Emit error event
      this.emit('pollingError', {
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      });
    }
  }

  private async processBatch(tokenAddresses: string[], timestamp: Date): Promise<void> {
    try {
      const response = await this.client.get<JupiterPriceV3Response>('/price', {
        params: {
          ids: tokenAddresses.join(','),
          vsToken: 'So11111111111111111111111111111111111111112' // SOL
        }
      });

      // Process each token's price data
      for (const tokenAddress of tokenAddresses) {
        const priceInfo = response.data.data[tokenAddress];

        if (priceInfo) {
          const previousData = this.lastPriceData.get(tokenAddress);
          const currentPrice = priceInfo.price;

          // Calculate 24h price change (approximate based on previous data)
          const priceChange24h = previousData
            ? ((currentPrice - previousData.currentPrice) / previousData.currentPrice) * 100
            : 0;

          const priceData: PriceMonitoringData = {
            tokenAddress,
            timestamp,
            currentPrice: currentPrice,
            volume24h: undefined, // Jupiter Price API doesn't provide volume
            priceChange24h
          };

          // Store the latest data
          this.lastPriceData.set(tokenAddress, priceData);

          // Emit price update event
          this.emit('priceUpdate', priceData);

          // Log significant price changes
          if (Math.abs(priceChange24h) > 10) {
            console.log(`📈 Significant price change for ${tokenAddress}: ${priceChange24h.toFixed(2)}%`);
          }
        } else {
          console.warn(`⚠️ No price data received for token: ${tokenAddress}`);
        }
      }

    } catch (error) {
      console.error(`Failed to process price batch:`, error);
      throw error;
    }
  }

  private async validateTokenAddresses(tokenAddresses: string[]): Promise<string[]> {
    // Basic validation - check if addresses are valid Solana addresses
    const validTokens: string[] = [];

    for (const address of tokenAddresses) {
      if (this.isValidSolanaAddress(address)) {
        validTokens.push(address);
      } else {
        console.warn(`⚠️ Invalid token address: ${address}`);
      }
    }

    return validTokens;
  }

  private isValidSolanaAddress(address: string): boolean {
    // Basic Solana address validation (44 characters, base58)
    if (address.length !== 44) {
      return false;
    }

    // Check if it contains only valid base58 characters
    const base58Regex = /^[1-9A-HJ-NP-Za-km-z]+$/;
    return base58Regex.test(address);
  }

  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  // Utility methods for monitoring statistics
  getMonitoringStats(): {
    tokensCount: number;
    pollingInterval: number;
    isActive: boolean;
    lastUpdate: Date | null;
  } {
    const latestUpdate = Array.from(this.lastPriceData.values())
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())[0];

    return {
      tokensCount: this.monitoringTokens.size,
      pollingInterval: this.POLLING_INTERVAL_MS,
      isActive: this.monitoringInterval !== null,
      lastUpdate: latestUpdate?.timestamp || null
    };
  }

  getMonitoredTokens(): string[] {
    return Array.from(this.monitoringTokens);
  }

  async getHistoricalPriceChange(tokenAddress: string, hours: number = 24): Promise<number | null> {
    // This would require additional Jupiter API calls or external data sources
    // For now, return the current 24h change from our stored data
    const priceData = this.lastPriceData.get(tokenAddress);
    return priceData?.priceChange24h || null;
  }

  // Clean up on service shutdown
  async shutdown(): Promise<void> {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    this.monitoringTokens.clear();
    this.lastPriceData.clear();
    this.removeAllListeners();

    console.log('🎯 Jupiter Price V3 Service shut down');
  }
}
