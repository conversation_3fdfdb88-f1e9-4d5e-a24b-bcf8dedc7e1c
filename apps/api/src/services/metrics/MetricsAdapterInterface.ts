import { TokenSnapshot } from '@shared/types';

/**
 * Information about a metrics data provider
 */
export interface ProviderInfo {
  name: string;
  rateLimit: {
    requestsPerSecond: number;
    requestsPerMinute: number;
    requestsPerDay?: number;
  };
  batchSize: number;
  supportsRealtime: boolean;
  requiresAuth: boolean;
}

/**
 * Configuration options for adapter operations
 */
export interface AdapterOptions {
  timeout?: number;
  retries?: number;
  cacheTtl?: number;
}

/**
 * Abstract base class for metrics data adapters
 * Provides a standardized interface for fetching token market data from various providers
 */
export abstract class MetricsAdapter {
  protected readonly name: string;

  constructor(name: string) {
    this.name = name;
  }

  /**
   * Get information about this provider's capabilities and limits
   */
  abstract getProviderInfo(): ProviderInfo;

  /**
   * Fetch market data snapshot for a single token
   * @param tokenAddress - Solana token address
   * @param options - Optional configuration for the request
   * @returns Promise resolving to TokenSnapshot with current market data
   * @throws AdapterError on failure or timeout
   */
  abstract fetchTokenSnapshot(
    tokenAddress: string,
    options?: AdapterOptions
  ): Promise<TokenSnapshot>;

  /**
   * Fetch market data snapshots for multiple tokens in a single request
   * @param addresses - Array of Solana token addresses
   * @param options - Optional configuration for the request
   * @returns Promise resolving to array of TokenSnapshots
   * @throws AdapterError on failure or timeout
   */
  abstract fetchBatchSnapshots(
    addresses: string[],
    options?: AdapterOptions
  ): Promise<TokenSnapshot[]>;

  /**
   * Check if the adapter is currently available and operational
   * @returns Promise resolving to true if adapter is healthy
   */
  abstract isHealthy(): Promise<boolean>;

  /**
   * Get the adapter's identifier name
   */
  getName(): string {
    return this.name;
  }

  /**
   * Validate a token address format
   * @param address - Token address to validate
   * @returns true if address format is valid
   */
  protected isValidTokenAddress(address: string): boolean {
    return typeof address === 'string' && address.length >= 32 && address.length <= 44;
  }

  /**
   * Validate batch size against provider limits
   * @param addresses - Array of addresses to validate
   * @throws Error if batch size exceeds provider limits
   */
  protected validateBatchSize(addresses: string[]): void {
    const providerInfo = this.getProviderInfo();
    if (addresses.length > providerInfo.batchSize) {
      throw new AdapterError(
        `Batch size ${addresses.length} exceeds provider limit of ${providerInfo.batchSize}`,
        'BATCH_SIZE_EXCEEDED',
        this.name
      );
    }
  }

  /**
   * Validate all addresses in a batch
   * @param addresses - Array of addresses to validate
   * @throws Error if any address is invalid
   */
  protected validateAddresses(addresses: string[]): void {
    const invalidAddresses = addresses.filter(addr => !this.isValidTokenAddress(addr));
    if (invalidAddresses.length > 0) {
      throw new AdapterError(
        `Invalid token addresses: ${invalidAddresses.join(', ')}`,
        'INVALID_ADDRESS',
        this.name
      );
    }
  }
}

/**
 * Custom error class for adapter-related failures
 */
export class AdapterError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly adapterName: string,
    public readonly cause?: Error
  ) {
    super(message);
    this.name = 'AdapterError';
  }
}

/**
 * Registry for managing multiple metrics adapters
 */
export class AdapterRegistry {
  private adapters = new Map<string, MetricsAdapter>();
  private primaryAdapter: string | null = null;

  /**
   * Register a metrics adapter
   * @param adapter - Adapter instance to register
   */
  register(adapter: MetricsAdapter): void {
    this.adapters.set(adapter.getName(), adapter);
    
    // Set first registered adapter as primary
    if (this.primaryAdapter === null) {
      this.primaryAdapter = adapter.getName();
    }
  }

  /**
   * Unregister an adapter
   * @param name - Name of adapter to unregister
   */
  unregister(name: string): void {
    this.adapters.delete(name);
    
    // Clear primary if it was unregistered
    if (this.primaryAdapter === name) {
      this.primaryAdapter = this.adapters.size > 0 
        ? this.adapters.keys().next().value 
        : null;
    }
  }

  /**
   * Get an adapter by name
   * @param name - Name of adapter to retrieve
   * @returns Adapter instance or undefined if not found
   */
  get(name: string): MetricsAdapter | undefined {
    return this.adapters.get(name);
  }

  /**
   * Get the primary adapter
   * @returns Primary adapter instance or undefined if none set
   */
  getPrimary(): MetricsAdapter | undefined {
    return this.primaryAdapter ? this.adapters.get(this.primaryAdapter) : undefined;
  }

  /**
   * Set the primary adapter
   * @param name - Name of adapter to set as primary
   * @throws Error if adapter is not registered
   */
  setPrimary(name: string): void {
    if (!this.adapters.has(name)) {
      throw new Error(`Adapter ${name} is not registered`);
    }
    this.primaryAdapter = name;
  }

  /**
   * Get all registered adapter names
   * @returns Array of adapter names
   */
  getAdapterNames(): string[] {
    return Array.from(this.adapters.keys());
  }

  /**
   * Get all registered adapters
   * @returns Array of adapter instances
   */
  getAdapters(): MetricsAdapter[] {
    return Array.from(this.adapters.values());
  }

  /**
   * Check health of all registered adapters
   * @returns Map of adapter names to health status
   */
  async checkHealth(): Promise<Map<string, boolean>> {
    const healthChecks = new Map<string, boolean>();
    
    await Promise.all(
      Array.from(this.adapters.entries()).map(async ([name, adapter]) => {
        try {
          const isHealthy = await adapter.isHealthy();
          healthChecks.set(name, isHealthy);
        } catch (error) {
          healthChecks.set(name, false);
        }
      })
    );
    
    return healthChecks;
  }
}

// Export singleton registry instance
export const adapterRegistry = new AdapterRegistry();