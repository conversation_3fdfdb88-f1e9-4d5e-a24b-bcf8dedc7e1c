import { Decimal } from 'decimal.js';
import { TokenSnapshot } from '@shared/types';
import { MetricsAdapter, ProviderInfo, AdapterOptions, AdapterError } from './MetricsAdapterInterface';
import { apiLogger } from '../../lib/logger';

/**
 * Jupiter V3 API response interface
 */
interface JupiterV3Response {
  [tokenId: string]: {
    usdPrice: number;
    blockId: number;
    decimals: number;
    priceChange24h: number;
  };
}

/**
 * Configuration for Jupiter V3 adapter
 */
export interface JupiterV3Config {
  baseUrl?: string; // Default: lite tier
  tier: 'lite' | 'pro';
  apiKey?: string; // Required for pro tier
  timeout?: number;
  maxRetries?: number;
}

/**
 * Jupiter Price API V3 adapter implementation
 * Fetches real-time price data from Jupiter's Price API V3
 */
export class JupiterV3Adapter extends MetricsAdapter {
  private readonly config: Required<JupiterV3Config>;
  private readonly logger = apiLogger.child({ service: 'JupiterV3Adapter' });

  constructor(config: JupiterV3Config = { tier: 'lite' }) {
    super('jupiter-v3');
    
    // Set default configuration
    this.config = {
      baseUrl: config.baseUrl ?? 'https://lite-api.jup.ag/price/v3',
      tier: config.tier,
      apiKey: config.apiKey,
      timeout: config.timeout ?? 5000,
      maxRetries: config.maxRetries ?? 3
    };

    // Validate configuration
    if (this.config.tier === 'pro') {
      if (!this.config.apiKey) {
        throw new Error('API key required for Jupiter V3 Pro tier');
      }
      this.config.baseUrl = 'https://api.jup.ag/price/v3';
    }

    this.logger.info({ tier: this.config.tier }, 'Jupiter V3 adapter initialized');
  }

  getProviderInfo(): ProviderInfo {
    return {
      name: `Jupiter Price API V3 (${this.config.tier})`,
      rateLimit: {
        requestsPerSecond: this.config.tier === 'pro' ? 50 : 10,
        requestsPerMinute: this.config.tier === 'pro' ? 3000 : 600,
        requestsPerDay: this.config.tier === 'pro' ? 432000 : 86400
      },
      batchSize: 50, // Jupiter V3 maximum
      supportsRealtime: true,
      requiresAuth: this.config.tier === 'pro'
    };
  }

  async fetchTokenSnapshot(
    tokenAddress: string,
    options: AdapterOptions = {}
  ): Promise<TokenSnapshot> {
    const snapshots = await this.fetchBatchSnapshots([tokenAddress], options);
    
    if (snapshots.length === 0) {
      throw new AdapterError(
        `Token not found or not traded in last 7 days: ${tokenAddress}`,
        'TOKEN_NOT_FOUND',
        this.name
      );
    }

    return snapshots[0];
  }

  async fetchBatchSnapshots(
    addresses: string[],
    options: AdapterOptions = {}
  ): Promise<TokenSnapshot[]> {
    // Validate inputs
    this.validateAddresses(addresses);
    this.validateBatchSize(addresses);

    const timeout = options.timeout ?? this.config.timeout;
    let attempts = 0;
    const maxRetries = options.retries ?? this.config.maxRetries;

    while (attempts < maxRetries) {
      attempts++;

      try {
        this.logger.debug({
          addresses: addresses.length,
          attempt: attempts
        }, 'Fetching data from Jupiter V3');

        const response = await this.makeRequest(addresses, timeout);
        const snapshots = this.mapResponseToSnapshots(response, addresses);

        this.logger.info({
          requested: addresses.length,
          received: snapshots.length,
          attempt: attempts
        }, 'Successfully fetched Jupiter V3 data');

        return snapshots;

      } catch (error) {
        const isLastAttempt = attempts === maxRetries;
        
        this.logger.warn({
          error: error.message,
          attempt: attempts,
          maxRetries,
          isLastAttempt
        }, 'Jupiter V3 request failed');

        if (isLastAttempt) {
          throw new AdapterError(
            `Jupiter V3 request failed after ${maxRetries} attempts: ${error.message}`,
            'REQUEST_FAILED',
            this.name,
            error
          );
        }

        // Exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempts - 1), 30000);
        await this.sleep(delay);
      }
    }

    throw new AdapterError(
      'Unexpected error in fetchBatchSnapshots',
      'UNEXPECTED_ERROR',
      this.name
    );
  }

  async isHealthy(): Promise<boolean> {
    try {
      // Test with SOL token
      const solAddress = 'So11111111111111111111111111111111111111112';
      const response = await this.makeRequest([solAddress], 3000);
      return Object.keys(response).length > 0;
    } catch (error) {
      this.logger.warn({ error: error.message }, 'Jupiter V3 health check failed');
      return false;
    }
  }

  /**
   * Make HTTP request to Jupiter V3 API
   */
  private async makeRequest(
    addresses: string[],
    timeout: number
  ): Promise<JupiterV3Response> {
    const url = `${this.config.baseUrl}?ids=${addresses.join(',')}`;
    
    const headers: Record<string, string> = {
      'Accept': 'application/json',
      'User-Agent': 'MemeTrader-Pro/1.0'
    };

    if (this.config.apiKey) {
      headers['Authorization'] = `Bearer ${this.config.apiKey}`;
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        if (response.status === 429) {
          throw new AdapterError(
            'Rate limit exceeded',
            'RATE_LIMIT_EXCEEDED',
            this.name
          );
        }

        throw new AdapterError(
          `HTTP ${response.status}: ${response.statusText}`,
          'HTTP_ERROR',
          this.name
        );
      }

      const data = await response.json();
      
      if (!data || typeof data !== 'object') {
        throw new AdapterError(
          'Invalid response format from Jupiter V3',
          'INVALID_RESPONSE',
          this.name
        );
      }

      return data as JupiterV3Response;

    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        throw new AdapterError(
          `Request timeout after ${timeout}ms`,
          'TIMEOUT',
          this.name
        );
      }

      throw error;
    }
  }

  /**
   * Map Jupiter V3 response to TokenSnapshot array
   */
  private mapResponseToSnapshots(
    response: JupiterV3Response,
    requestedAddresses: string[]
  ): TokenSnapshot[] {
    const snapshots: TokenSnapshot[] = [];
    const now = new Date();

    for (const [tokenAddress, data] of Object.entries(response)) {
      try {
        // Validate required fields
        if (typeof data.usdPrice !== 'number' || data.usdPrice <= 0) {
          this.logger.warn(
            { tokenAddress, usdPrice: data.usdPrice },
            'Invalid usdPrice from Jupiter V3'
          );
          continue;
        }

        const snapshot: TokenSnapshot = {
          tokenAddress,
          priceUsd: new Decimal(data.usdPrice),
          priceChange24h: typeof data.priceChange24h === 'number' 
            ? new Decimal(data.priceChange24h) 
            : undefined,
          // Jupiter V3 doesn't provide these fields - will be undefined
          priceChange1h: undefined,
          volume24h: undefined,
          liquidity: undefined,
          fdv: undefined,
          ageInDays: undefined,
          // Use current time with blockId context for freshness
          lastUpdated: now,
          source: this.name
        };

        snapshots.push(snapshot);

        this.logger.debug({
          tokenAddress,
          price: data.usdPrice,
          blockId: data.blockId,
          priceChange24h: data.priceChange24h
        }, 'Mapped Jupiter V3 data to snapshot');

      } catch (error) {
        this.logger.error({
          error: error.message,
          tokenAddress,
          data
        }, 'Failed to map Jupiter V3 response to snapshot');
      }
    }

    // Log missing tokens
    const receivedAddresses = new Set(Object.keys(response));
    const missingAddresses = requestedAddresses.filter(addr => !receivedAddresses.has(addr));
    
    if (missingAddresses.length > 0) {
      this.logger.info({
        missing: missingAddresses.length,
        addresses: missingAddresses
      }, 'Some tokens not found in Jupiter V3 response (likely not traded in 7 days)');
    }

    return snapshots;
  }

  /**
   * Validate Jupiter V3 specific constraints
   */
  protected validateBatchSize(addresses: string[]): void {
    if (addresses.length > 50) {
      throw new AdapterError(
        `Batch size ${addresses.length} exceeds Jupiter V3 limit of 50 tokens`,
        'BATCH_SIZE_EXCEEDED',
        this.name
      );
    }
  }

  /**
   * Utility method for delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}