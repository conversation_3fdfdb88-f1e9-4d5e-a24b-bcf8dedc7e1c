import { Decimal } from 'decimal.js';
import { TokenSnapshot } from '@shared/types';
import { MetricsAdapter, ProviderInfo, AdapterOptions, AdapterError } from './MetricsAdapterInterface';
import { apiLogger } from '../../lib/logger';

interface JupiterV2Token {
  id: string;
  name: string;
  symbol: string;
  icon?: string;
  decimals: number;
  circSupply?: number;
  totalSupply?: number;
  holderCount?: number;
  fdv?: number;
  mcap?: number;
  usdPrice?: number;
  priceBlockId?: number;
  liquidity?: number;
  isVerified?: boolean;
  tags?: string[];
  cexes?: string[];
  organicScore?: number;
  organicScoreLabel?: string;
  firstPool?: {
    id?: string;
    createdAt?: string;
  };
  audit?: {
    mintAuthorityDisabled?: boolean;
    freezeAuthorityDisabled?: boolean;
    topHoldersPercentage?: number;
  };
  stats5m?: {
    priceChange: number;
    buyVolume: number;
    sellVolume: number;
    numBuys: number;
    numSells: number;
  };
  stats1h?: {
    priceChange: number;
    buyVolume: number;
    sellVolume: number;
    numBuys: number;
    numSells: number;
  };
  stats24h?: {
    priceChange: number;
    buyVolume: number;
    sellVolume: number;
    numBuys: number;
    numSells: number;
  };
  updatedAt?: string;
}

interface JupiterV2Config {
  tier?: 'lite' | 'pro';
  apiKey?: string;
  timeout?: number;
}

export class JupiterV2Adapter extends MetricsAdapter {
  private readonly config: JupiterV2Config;
  private readonly baseUrl: string;
  private readonly headers: Record<string, string>;
  private readonly timeout: number;
  private readonly logger = apiLogger.child({ service: 'JupiterV2Adapter' });
  private holderHistory: Map<string, { count: number; timestamp: Date }> = new Map();

  constructor(config: JupiterV2Config = {}) {
    super('jupiter-v2');
    this.config = config;
    const { tier = 'lite', apiKey, timeout = 10000 } = config;
    
    this.baseUrl = tier === 'pro' 
      ? 'https://api.jup.ag/tokens/v2'
      : 'https://lite-api.jup.ag/tokens/v2';
    
    this.headers = {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'User-Agent': 'solana-trading-app/1.0'
    };
    
    if (apiKey && tier === 'pro') {
      this.headers['Authorization'] = `Bearer ${apiKey}`;
    }
    
    this.timeout = timeout;
    
    this.logger.info({
      tier,
      hasApiKey: !!apiKey,
      timeout: this.timeout
    }, 'JupiterV2Adapter initialized');
  }
  
  getProviderInfo(): ProviderInfo {
    return {
      name: 'Jupiter V2',
      endpoint: this.baseUrl,
      rateLimit: {
        requestsPerMinute: this.config.tier === 'pro' ? 600 : 60,
        requestsPerDay: this.config.tier === 'pro' ? 100000 : 10000
      },
      batchSize: 100,
      supportsRealtime: true,
      requiresAuth: this.config.tier === 'pro'
    };
  }

  async fetchTokenSnapshot(tokenAddress: string, options?: AdapterOptions): Promise<TokenSnapshot> {
    try {
      const response = await this.makeRequest(`/search?query=${tokenAddress}`);
      
      if (!response || response.length === 0) {
        this.logger.warn({ tokenAddress }, 'No data found for token');
        return null;
      }
      
      const tokenData = response[0] as JupiterV2Token;
      const snapshot = this.transformToSnapshot(tokenData);
      if (!snapshot) {
        throw new AdapterError('Invalid token data', 'DATA_INVALID', this.name);
      }
      return snapshot;
    } catch (error) {
      this.logger.error({ error, tokenAddress }, 'Failed to fetch single token data');
      return null;
    }
  }

  async fetchBatchSnapshots(tokenAddresses: string[], options?: AdapterOptions): Promise<TokenSnapshot[]> {
    const startTime = Date.now();
    
    try {
      // Jupiter V2 allows comma-separated addresses (up to 100)
      const batchSize = 100;
      const batches: string[][] = [];
      
      for (let i = 0; i < tokenAddresses.length; i += batchSize) {
        batches.push(tokenAddresses.slice(i, i + batchSize));
      }
      
      const allSnapshots: TokenSnapshot[] = [];
      
      for (const batch of batches) {
        const query = batch.join(',');
        const response = await this.makeRequest(`/search?query=${query}`);
        
        if (response && Array.isArray(response)) {
          const snapshots = response.map((token: JupiterV2Token) => 
            this.transformToSnapshot(token)
          ).filter((s): s is TokenSnapshot => s !== null);
          
          allSnapshots.push(...snapshots);
        }
      }
      
      const elapsedTime = Date.now() - startTime;
      
      this.logger.info({
        requestedCount: tokenAddresses.length,
        returnedCount: allSnapshots.length,
        duration: elapsedTime,
        adapter: this.name
      }, 'Successfully fetched Jupiter V2 batch data');
      
      return allSnapshots;
    } catch (error) {
      this.logger.error({ 
        error, 
        tokenCount: tokenAddresses.length 
      }, 'Failed to fetch Jupiter V2 batch data');
      
      throw new AdapterError(
        error instanceof Error ? error.message : 'Failed to fetch batch data',
        'BATCH_FETCH_ERROR',
        this.name
      );
    }
  }

  async isHealthy(): Promise<boolean> {
    try {
      // Test with SOL token
      const response = await this.makeRequest(
        '/search?query=So11111111111111111111111111111111111111112',
        5000 // 5 second timeout for health check
      );
      return response && response.length > 0;
    } catch (error) {
      this.logger.error({ error }, 'Jupiter V2 health check failed');
      return false;
    }
  }

  private async makeRequest(endpoint: string, timeout?: number): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(
      () => controller.abort(), 
      timeout || this.timeout
    );
    
    try {
      const url = `${this.baseUrl}${endpoint}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: this.headers,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      
      throw error;
    }
  }

  private transformToSnapshot(token: JupiterV2Token): TokenSnapshot | null {
    try {
      if (!token.usdPrice) {
        return null;
      }
      
      const now = new Date();
      
      // Calculate 24h volume from buy + sell volume
      const volume24h = token.stats24h 
        ? (token.stats24h.buyVolume + token.stats24h.sellVolume)
        : undefined;
      
      // Calculate 5m holder change
      let holderChange5m: number | undefined = undefined;
      if (token.holderCount) {
        const previousHolder = this.holderHistory.get(token.id);
        if (previousHolder) {
          const timeDiff = now.getTime() - previousHolder.timestamp.getTime();
          // If data is between 4-6 minutes old, calculate the change
          if (timeDiff >= 4 * 60 * 1000 && timeDiff <= 6 * 60 * 1000) {
            holderChange5m = token.holderCount - previousHolder.count;
          }
        }
        // Update holder history
        this.holderHistory.set(token.id, {
          count: token.holderCount,
          timestamp: now
        });
      }
      
      // Clean up old holder history entries (older than 10 minutes)
      const tenMinutesAgo = new Date(now.getTime() - 10 * 60 * 1000);
      for (const [key, value] of this.holderHistory.entries()) {
        if (value.timestamp < tenMinutesAgo) {
          this.holderHistory.delete(key);
        }
      }
      
      // Return TokenSnapshot with additional data stored in metadata
      const snapshot: TokenSnapshot = {
        tokenAddress: token.id,
        priceUsd: new Decimal(token.usdPrice),
        priceChange1h: token.stats1h?.priceChange 
          ? new Decimal(token.stats1h.priceChange)
          : undefined,
        priceChange24h: token.stats24h?.priceChange
          ? new Decimal(token.stats24h.priceChange)
          : undefined,
        volume24h: volume24h ? new Decimal(volume24h) : undefined,
        liquidity: token.liquidity ? new Decimal(token.liquidity) : undefined,
        fdv: token.fdv ? new Decimal(token.fdv) : undefined,
        lastUpdated: now,
        source: this.name
      };
      
      // Calculate token age
      let tokenAge: string | undefined;
      if (token.firstPool?.createdAt) {
        const createdDate = new Date(token.firstPool.createdAt);
        const ageMs = now.getTime() - createdDate.getTime();
        const ageHours = Math.floor(ageMs / (1000 * 60 * 60));
        const ageDays = Math.floor(ageMs / (1000 * 60 * 60 * 24));
        
        if (ageDays > 0) {
          tokenAge = `${ageDays}d`;
        } else if (ageHours > 0) {
          tokenAge = `${ageHours}h`;
        } else {
          const ageMinutes = Math.floor(ageMs / (1000 * 60));
          tokenAge = `${ageMinutes}m`;
        }
      }
      
      // Store additional data in metadata for MarketDataService to use
      (snapshot as any).metadata = {
        tokenSymbol: token.symbol,
        tokenName: token.name,
        tokenIcon: token.icon,
        marketCap: token.mcap,
        priceChange5m: token.stats5m?.priceChange,
        holderCount: token.holderCount,
        holderChange5m: holderChange5m,
        isVerified: token.isVerified,
        tags: token.tags,
        cexes: token.cexes,
        organicScore: token.organicScore,
        organicScoreLabel: token.organicScoreLabel,
        audit: token.audit,
        tokenAge: tokenAge,
        createdAt: token.firstPool?.createdAt,
        blockId: token.priceBlockId
      };
      
      return snapshot;
    } catch (error) {
      this.logger.error({ 
        error, 
        tokenId: token.id 
      }, 'Failed to transform Jupiter V2 token to snapshot');
      return null;
    }
  }
}