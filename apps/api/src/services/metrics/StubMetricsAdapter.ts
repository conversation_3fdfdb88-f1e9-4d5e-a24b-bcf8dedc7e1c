import { Decimal } from 'decimal.js';
import { TokenSnapshot } from '@shared/types';
import { MetricsAdapter, ProviderInfo, AdapterOptions, AdapterError } from './MetricsAdapterInterface';

/**
 * Configuration options for the stub adapter
 */
export interface StubAdapterConfig {
  simulateLatency?: boolean;
  minLatencyMs?: number;
  maxLatencyMs?: number;
  errorRate?: number; // 0-1, probability of simulating an error
  enableBullMarket?: boolean; // If true, generates more positive price changes
  enableBearMarket?: boolean; // If true, generates more negative price changes
}

/**
 * Stub implementation of MetricsAdapter for testing and development
 * Generates deterministic mock data based on token address hash
 */
export class StubMetricsAdapter extends MetricsAdapter {
  private readonly config: Required<StubAdapterConfig>;

  constructor(config: StubAdapterConfig = {}) {
    super('stub');
    
    // Set default configuration
    this.config = {
      simulateLatency: config.simulateLatency ?? true,
      minLatencyMs: config.minLatencyMs ?? 100,
      maxLatencyMs: config.maxLatencyMs ?? 500,
      errorRate: config.errorRate ?? 0.05, // 5% error rate by default
      enableBullMarket: config.enableBullMarket ?? false,
      enableBearMarket: config.enableBearMarket ?? false
    };
  }

  getProviderInfo(): ProviderInfo {
    return {
      name: 'Stub Provider (Mock Data)',
      rateLimit: {
        requestsPerSecond: 100,
        requestsPerMinute: 6000,
        requestsPerDay: 864000
      },
      batchSize: 100,
      supportsRealtime: false,
      requiresAuth: false
    };
  }

  async fetchTokenSnapshot(
    tokenAddress: string,
    options: AdapterOptions = {}
  ): Promise<TokenSnapshot> {
    // Validate address
    if (!this.isValidTokenAddress(tokenAddress)) {
      throw new AdapterError(
        `Invalid token address: ${tokenAddress}`,
        'INVALID_ADDRESS',
        this.name
      );
    }

    // Simulate potential errors
    if (Math.random() < this.config.errorRate) {
      throw new AdapterError(
        'Simulated network error',
        'NETWORK_ERROR',
        this.name
      );
    }

    // Simulate network latency
    if (this.config.simulateLatency) {
      await this.simulateDelay();
    }

    return this.generateMockSnapshot(tokenAddress);
  }

  async fetchBatchSnapshots(
    addresses: string[],
    options: AdapterOptions = {}
  ): Promise<TokenSnapshot[]> {
    // Validate inputs
    this.validateAddresses(addresses);
    this.validateBatchSize(addresses);

    // Simulate potential errors
    if (Math.random() < this.config.errorRate) {
      throw new AdapterError(
        'Simulated batch request failure',
        'BATCH_ERROR',
        this.name
      );
    }

    // Simulate network latency (slightly longer for batch requests)
    if (this.config.simulateLatency) {
      await this.simulateDelay(1.5);
    }

    // Generate snapshots for all addresses
    const snapshots = await Promise.all(
      addresses.map(address => this.generateMockSnapshot(address))
    );

    return snapshots;
  }

  async isHealthy(): Promise<boolean> {
    // Simulate occasional health check failures
    return Math.random() > 0.01; // 1% chance of being unhealthy
  }

  /**
   * Generate a deterministic mock snapshot based on token address
   * @param tokenAddress - Token address to generate data for
   * @returns Mock TokenSnapshot with realistic data
   */
  private generateMockSnapshot(tokenAddress: string): TokenSnapshot {
    // Create a simple hash from the token address for deterministic data
    const hash = this.createSimpleHash(tokenAddress);
    
    // Generate base price between $0.0001 and $100
    const basePrice = this.generatePrice(hash);
    
    // Generate realistic price changes
    const priceChange1h = this.generatePriceChange(hash, 'short');
    const priceChange24h = this.generatePriceChange(hash, 'long');
    
    // Generate volume based on price (higher priced tokens tend to have higher volume)
    const volume24h = this.generateVolume(basePrice, hash);
    
    // Generate liquidity (typically 10-50% of 24h volume)
    const liquidity = volume24h.mul(new Decimal(Math.random() * 0.4 + 0.1));
    
    // Generate FDV (fully diluted valuation)
    const fdv = this.generateFDV(basePrice, hash);
    
    // Generate token age (0-1000 days)
    const ageInDays = Math.floor((hash % 1000) + 1);
    
    // Add some time variance to lastUpdated
    const now = new Date();
    const variance = Math.floor(Math.random() * 30000); // 0-30 seconds variance
    const lastUpdated = new Date(now.getTime() - variance);

    return {
      tokenAddress,
      priceUsd: basePrice,
      priceChange1h,
      priceChange24h,
      volume24h,
      liquidity,
      fdv,
      ageInDays,
      lastUpdated,
      source: this.name
    };
  }

  /**
   * Create a simple numeric hash from a string
   */
  private createSimpleHash(input: string): number {
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Generate a realistic price based on hash
   */
  private generatePrice(hash: number): Decimal {
    // Use hash to determine price tier
    const tier = hash % 100;
    
    let basePrice: number;
    if (tier < 5) {
      // 5% chance of high-value token ($10-$100)
      basePrice = Math.random() * 90 + 10;
    } else if (tier < 20) {
      // 15% chance of mid-value token ($1-$10)
      basePrice = Math.random() * 9 + 1;
    } else if (tier < 60) {
      // 40% chance of low-value token ($0.01-$1)
      basePrice = Math.random() * 0.99 + 0.01;
    } else {
      // 40% chance of very low-value token ($0.0001-$0.01)
      basePrice = Math.random() * 0.0099 + 0.0001;
    }

    // Add deterministic variance based on hash
    const variance = ((hash % 1000) / 1000) * 0.2 - 0.1; // -10% to +10%
    basePrice *= (1 + variance);

    return new Decimal(Math.max(basePrice, 0.0001));
  }

  /**
   * Generate price change percentage
   */
  private generatePriceChange(hash: number, timeframe: 'short' | 'long'): Decimal {
    const isShort = timeframe === 'short';
    const baseVariance = isShort ? 0.1 : 0.3; // 1h vs 24h typical variance
    
    // Apply market condition modifiers
    let trendModifier = 0;
    if (this.config.enableBullMarket) {
      trendModifier = 0.05; // Bias toward positive changes
    } else if (this.config.enableBearMarket) {
      trendModifier = -0.05; // Bias toward negative changes
    }

    // Generate change based on hash with trend bias
    const hashSeed = isShort ? hash : hash * 2;
    const normalizedHash = ((hashSeed % 2000) / 2000) - 0.5; // -0.5 to 0.5
    const change = (normalizedHash * baseVariance * 2) + trendModifier;
    
    // Cap extreme changes
    const cappedChange = Math.max(-0.5, Math.min(0.5, change)); // -50% to +50%
    
    return new Decimal(cappedChange * 100); // Return as percentage
  }

  /**
   * Generate 24h volume based on price and hash
   */
  private generateVolume(price: Decimal, hash: number): Decimal {
    // Higher priced tokens tend to have higher volume in USD terms
    const priceNum = price.toNumber();
    const baseVolume = Math.log10(priceNum + 1) * 50000;
    
    // Add hash-based variance
    const variance = ((hash % 500) / 500) * 2; // 0 to 2x multiplier
    const volume = baseVolume * variance;
    
    return new Decimal(Math.max(volume, 100)); // Minimum $100 volume
  }

  /**
   * Generate FDV (Fully Diluted Valuation)
   */
  private generateFDV(price: Decimal, hash: number): Decimal {
    // Typical token supply ranges
    const supplyTier = hash % 10;
    let supply: number;
    
    if (supplyTier < 2) {
      // 20% chance of low supply (1M-10M)
      supply = Math.random() * 9_000_000 + 1_000_000;
    } else if (supplyTier < 6) {
      // 40% chance of medium supply (10M-1B)
      supply = Math.random() * 990_000_000 + 10_000_000;
    } else {
      // 40% chance of high supply (1B-100B)
      supply = Math.random() * 99_000_000_000 + 1_000_000_000;
    }

    return price.mul(new Decimal(supply));
  }

  /**
   * Simulate network delay
   */
  private async simulateDelay(multiplier: number = 1): Promise<void> {
    const delay = Math.random() * 
      (this.config.maxLatencyMs - this.config.minLatencyMs) + 
      this.config.minLatencyMs;
    
    await new Promise(resolve => 
      setTimeout(resolve, delay * multiplier)
    );
  }
}