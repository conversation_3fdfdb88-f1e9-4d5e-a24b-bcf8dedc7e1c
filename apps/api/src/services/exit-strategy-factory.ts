import { PrismaClient } from '@prisma/client';
import { ExitStrategyService } from './exit-strategy.js';
import { JupiterPriceV3Service } from './jupiter-price-v3.js';
import { TriggerDetectionService } from './trigger-detection.js';
import { ExitExecutionService } from './exit-execution.js';
import { ExitStrategyOrchestrator } from './exit-strategy-orchestrator.js';

// Service factory to create all exit strategy services with proper dependencies
export class ExitStrategyServiceFactory {
  private prisma: PrismaClient;
  private exitStrategyService: ExitStrategyService;
  private priceService: JupiterPriceV3Service;
  private triggerDetectionService: TriggerDetectionService;
  private executionService: ExitExecutionService;
  private orchestrator: ExitStrategyOrchestrator;

  constructor(prisma: PrismaClient, tradingService: any) {
    this.prisma = prisma;

    // Create services in dependency order
    this.exitStrategyService = new ExitStrategyService(prisma);
    this.priceService = new JupiterPriceV3Service();
    this.triggerDetectionService = new TriggerDetectionService(prisma);
    this.executionService = new ExitExecutionService(prisma, tradingService);

    // Create orchestrator with all dependencies
    this.orchestrator = new ExitStrategyOrchestrator(
      prisma,
      this.exitStrategyService,
      this.priceService,
      this.triggerDetectionService,
      this.executionService
    );
  }

  // Get individual services
  getExitStrategyService(): ExitStrategyService {
    return this.exitStrategyService;
  }

  getPriceService(): JupiterPriceV3Service {
    return this.priceService;
  }

  getTriggerDetectionService(): TriggerDetectionService {
    return this.triggerDetectionService;
  }

  getExecutionService(): ExitExecutionService {
    return this.executionService;
  }

  getOrchestrator(): ExitStrategyOrchestrator {
    return this.orchestrator;
  }

  // Lifecycle methods
  async startServices(): Promise<void> {
    console.log('🚀 Starting Exit Strategy Services...');

    try {
      // Start the orchestrator (which will start all dependent services)
      await this.orchestrator.start();

      console.log('✅ All Exit Strategy Services started successfully');

    } catch (error) {
      console.error('❌ Failed to start Exit Strategy Services:', error);
      throw error;
    }
  }

  async stopServices(): Promise<void> {
    console.log('🛑 Stopping Exit Strategy Services...');

    try {
      await this.orchestrator.stop();

      console.log('✅ All Exit Strategy Services stopped successfully');

    } catch (error) {
      console.error('❌ Failed to stop Exit Strategy Services:', error);
      throw error;
    }
  }

  async shutdownServices(): Promise<void> {
    console.log('🚪 Shutting down Exit Strategy Services...');

    try {
      await this.orchestrator.shutdown();

      console.log('✅ All Exit Strategy Services shut down successfully');

    } catch (error) {
      console.error('❌ Failed to shutdown Exit Strategy Services:', error);
      throw error;
    }
  }

  // Health check for all services
  async getSystemHealth(): Promise<{
    overall: boolean;
    services: {
      orchestrator: boolean;
      priceMonitoring: boolean;
      executionQueue: boolean;
      database: boolean;
    };
    stats: any;
  }> {
    try {
      const [health, status] = await Promise.all([
        this.orchestrator.getSystemHealth(),
        this.orchestrator.getStatus()
      ]);

      const overall = Object.values(health).every(status => status === true);

      return {
        overall,
        services: health,
        stats: {
          monitoring: status.monitoringStats,
          queue: status.queueStats,
          triggers: await status.triggerStats
        }
      };

    } catch (error) {
      console.error('Error getting system health:', error);
      return {
        overall: false,
        services: {
          orchestrator: false,
          priceMonitoring: false,
          executionQueue: false,
          database: false
        },
        stats: {}
      };
    }
  }
}

// Export the factory for use in the main application
export { ExitStrategyServiceFactory };

// Export individual services for direct imports if needed
export {
  ExitStrategyService,
  JupiterPriceV3Service,
  TriggerDetectionService,
  ExitExecutionService,
  ExitStrategyOrchestrator
};
