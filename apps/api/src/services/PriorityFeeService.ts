import { Connection, GetRecentPrioritizationFeesConfig } from '@solana/web3.js';
import { getConnection } from '../lib/solana';
import { apiLogger } from '../lib/logger';
import Decimal from 'decimal.js';

export interface NetworkCongestion {
  level: 'low' | 'medium' | 'high' | 'extreme';
  recentFees: {
    min: number;
    max: number;
    median: number;
    percentile75: number;
    percentile95: number;
  };
  blockHeight: number;
  timestamp: Date;
  sampleSize: number;
}

export interface PriorityFeeRecommendation {
  economyFee: number;      // Slow but cheap
  standardFee: number;     // Balanced speed/cost
  fastFee: number;         // Fast execution
  turboFee: number;        // Maximum priority
  networkCongestion: NetworkCongestion;
}

export interface MEVProtectionLevel {
  name: 'basic' | 'standard' | 'maximum';
  description: string;
  priorityFeeMultiplier: number;
  computeUnitMultiplier: number;
  jitoTipLamports: number;
  estimatedCostIncrease: number; // Percentage
}

export class PriorityFeeService {
  private connection: Connection;
  private cachedCongestion: NetworkCongestion | null = null;
  private cacheExpiry: number = 0;
  private readonly CACHE_DURATION_MS = 10000; // 10 seconds

  constructor() {
    this.connection = getConnection();
  }

  /**
   * Get current network congestion level
   */
  async getNetworkCongestion(): Promise<NetworkCongestion> {
    // Return cached result if still valid
    if (this.cachedCongestion && Date.now() < this.cacheExpiry) {
      return this.cachedCongestion;
    }

    try {
      // Get recent prioritization fees
      const config: GetRecentPrioritizationFeesConfig = {
        lockedWritableAccounts: [], // Empty to get general network fees
      };

      const recentFees = await this.connection.getRecentPrioritizationFees(config);
      
      if (recentFees.length === 0) {
        // Fallback if no recent fees available
        const fallbackCongestion: NetworkCongestion = {
          level: 'low',
          recentFees: {
            min: 0,
            max: 10000,
            median: 1000,
            percentile75: 5000,
            percentile95: 10000,
          },
          blockHeight: 0,
          timestamp: new Date(),
          sampleSize: 0,
        };

        apiLogger.warn('No recent prioritization fees available, using fallback values');
        return fallbackCongestion;
      }

      // Extract fee values and sort
      const feeValues = recentFees.map(fee => fee.prioritizationFee).sort((a, b) => a - b);
      const sampleSize = feeValues.length;

      // Calculate percentiles
      const min = feeValues[0];
      const max = feeValues[sampleSize - 1];
      const median = this.calculatePercentile(feeValues, 50);
      const percentile75 = this.calculatePercentile(feeValues, 75);
      const percentile95 = this.calculatePercentile(feeValues, 95);

      // Determine congestion level based on percentiles
      const congestionLevel = this.determineCongestionLevel(percentile75, percentile95);

      const congestion: NetworkCongestion = {
        level: congestionLevel,
        recentFees: {
          min,
          max,
          median,
          percentile75,
          percentile95,
        },
        blockHeight: recentFees[0]?.slot || 0,
        timestamp: new Date(),
        sampleSize,
      };

      // Cache the result
      this.cachedCongestion = congestion;
      this.cacheExpiry = Date.now() + this.CACHE_DURATION_MS;

      apiLogger.info({
        level: congestionLevel,
        medianFee: median,
        percentile75,
        percentile95,
        sampleSize,
      }, 'Network congestion updated');

      return congestion;
    } catch (error) {
      apiLogger.error({ error }, 'Failed to get network congestion');
      
      // Return fallback congestion data
      return {
        level: 'medium',
        recentFees: {
          min: 0,
          max: 50000,
          median: 5000,
          percentile75: 15000,
          percentile95: 30000,
        },
        blockHeight: 0,
        timestamp: new Date(),
        sampleSize: 0,
      };
    }
  }

  /**
   * Get priority fee recommendations based on network conditions
   */
  async getPriorityFeeRecommendation(
    userPreference: 'economy' | 'standard' | 'fast' | 'turbo' = 'standard'
  ): Promise<PriorityFeeRecommendation> {
    const networkCongestion = await this.getNetworkCongestion();
    const { recentFees } = networkCongestion;

    // Calculate base recommendations based on recent network data
    const economyFee = Math.max(recentFees.median, 1000); // At least 1000 lamports
    const standardFee = recentFees.percentile75;
    const fastFee = recentFees.percentile95;
    const turboFee = Math.max(recentFees.max * 1.2, fastFee * 1.5); // 20% above max or 50% above fast

    // Apply congestion multipliers
    const congestionMultiplier = this.getCongestionMultiplier(networkCongestion.level);
    
    const recommendation: PriorityFeeRecommendation = {
      economyFee: Math.round(economyFee * congestionMultiplier.economy),
      standardFee: Math.round(standardFee * congestionMultiplier.standard),
      fastFee: Math.round(fastFee * congestionMultiplier.fast),
      turboFee: Math.round(turboFee * congestionMultiplier.turbo),
      networkCongestion,
    };

    apiLogger.info({
      userPreference,
      recommendation: {
        economy: recommendation.economyFee,
        standard: recommendation.standardFee,
        fast: recommendation.fastFee,
        turbo: recommendation.turboFee,
      },
      congestionLevel: networkCongestion.level,
    }, 'Priority fee recommendation calculated');

    return recommendation;
  }

  /**
   * Get MEV protection level configurations
   */
  getMEVProtectionLevels(): MEVProtectionLevel[] {
    return [
      {
        name: 'basic',
        description: 'Standard priority fees with basic compute unit optimization',
        priorityFeeMultiplier: 1.0,
        computeUnitMultiplier: 1.0,
        jitoTipLamports: 0,
        estimatedCostIncrease: 0,
      },
      {
        name: 'standard',
        description: 'Enhanced priority fees with MEV-aware routing preferences',
        priorityFeeMultiplier: 1.5,
        computeUnitMultiplier: 1.2,
        jitoTipLamports: 10000, // 0.01 SOL Jito tip
        estimatedCostIncrease: 15,
      },
      {
        name: 'maximum',
        description: 'Maximum protection with Jito bundles and highest priority',
        priorityFeeMultiplier: 2.5,
        computeUnitMultiplier: 1.5,
        jitoTipLamports: 50000, // 0.05 SOL Jito tip
        estimatedCostIncrease: 40,
      },
    ];
  }

  /**
   * Calculate optimal priority fee for MEV protection
   */
  async calculateMEVProtectedFee(
    protectionLevel: 'basic' | 'standard' | 'maximum' = 'standard',
    userSpeedPreference: 'economy' | 'standard' | 'fast' | 'turbo' = 'standard'
  ): Promise<{
    priorityFeeLamports: number;
    computeUnitPriceMicroLamports: number;
    jitoTipLamports: number;
    totalEstimatedCost: number;
    protectionConfig: MEVProtectionLevel;
  }> {
    const feeRecommendation = await this.getPriorityFeeRecommendation(userSpeedPreference);
    const protectionLevels = this.getMEVProtectionLevels();
    const protectionConfig = protectionLevels.find(level => level.name === protectionLevel)!;

    // Get base fee based on user preference
    let baseFee: number;
    switch (userSpeedPreference) {
      case 'economy': baseFee = feeRecommendation.economyFee; break;
      case 'standard': baseFee = feeRecommendation.standardFee; break;
      case 'fast': baseFee = feeRecommendation.fastFee; break;
      case 'turbo': baseFee = feeRecommendation.turboFee; break;
      default: baseFee = feeRecommendation.standardFee;
    }

    // Apply MEV protection multipliers
    const priorityFeeLamports = Math.round(baseFee * protectionConfig.priorityFeeMultiplier);
    const computeUnitPriceMicroLamports = Math.round(1 * protectionConfig.computeUnitMultiplier); // Base 1 micro-lamport per compute unit
    const jitoTipLamports = protectionConfig.jitoTipLamports;

    // Estimate total cost (priority fee + jito tip + base transaction fee)
    const baseTxFee = 5000; // ~0.005 SOL base transaction fee
    const totalEstimatedCost = priorityFeeLamports + jitoTipLamports + baseTxFee;

    const result = {
      priorityFeeLamports,
      computeUnitPriceMicroLamports,
      jitoTipLamports,
      totalEstimatedCost,
      protectionConfig,
    };

    apiLogger.info({
      protectionLevel,
      userSpeedPreference,
      priorityFeeLamports,
      jitoTipLamports,
      totalEstimatedCost,
      networkCongestion: feeRecommendation.networkCongestion.level,
    }, 'MEV-protected fee calculated');

    return result;
  }

  /**
   * Validate priority fee parameters
   */
  validatePriorityFee(
    priorityFeeLamports: number,
    maxAllowedFee: number = 100000 // Default max 0.1 SOL
  ): { isValid: boolean; error?: string; warning?: string } {
    if (priorityFeeLamports < 0) {
      return { isValid: false, error: 'Priority fee cannot be negative' };
    }

    if (priorityFeeLamports > maxAllowedFee) {
      return { 
        isValid: false, 
        error: `Priority fee exceeds maximum allowed (${maxAllowedFee} lamports)` 
      };
    }

    // Warn for unusually high fees
    if (priorityFeeLamports > 50000) { // > 0.05 SOL
      return {
        isValid: true,
        warning: 'Priority fee is unusually high - please verify network conditions',
      };
    }

    return { isValid: true };
  }

  /**
   * Get estimated transaction success probability
   */
  async getSuccessProbability(
    priorityFeeLamports: number
  ): Promise<{ probability: number; reasoning: string }> {
    const congestion = await this.getNetworkCongestion();
    const { recentFees } = congestion;

    let probability: number;
    let reasoning: string;

    if (priorityFeeLamports >= recentFees.percentile95) {
      probability = 0.95;
      reasoning = 'Very high success probability - fee is in top 5% of recent transactions';
    } else if (priorityFeeLamports >= recentFees.percentile75) {
      probability = 0.85;
      reasoning = 'High success probability - fee is above 75th percentile';
    } else if (priorityFeeLamports >= recentFees.median) {
      probability = 0.70;
      reasoning = 'Good success probability - fee is above median';
    } else if (priorityFeeLamports > 0) {
      probability = 0.50;
      reasoning = 'Moderate success probability - fee is below median, may experience delays';
    } else {
      probability = 0.25;
      reasoning = 'Low success probability - no priority fee set during network congestion';
    }

    return { probability, reasoning };
  }

  /**
   * Calculate percentile from sorted array
   */
  private calculatePercentile(sortedValues: number[], percentile: number): number {
    if (sortedValues.length === 0) return 0;
    
    const index = (percentile / 100) * (sortedValues.length - 1);
    const lower = Math.floor(index);
    const upper = Math.ceil(index);
    
    if (lower === upper) {
      return sortedValues[lower];
    }
    
    const weight = index - lower;
    return sortedValues[lower] * (1 - weight) + sortedValues[upper] * weight;
  }

  /**
   * Determine congestion level based on fee percentiles
   */
  private determineCongestionLevel(
    percentile75: number, 
    percentile95: number
  ): 'low' | 'medium' | 'high' | 'extreme' {
    if (percentile95 > 100000) return 'extreme'; // > 0.1 SOL
    if (percentile75 > 50000) return 'high';     // > 0.05 SOL
    if (percentile75 > 10000) return 'medium';   // > 0.01 SOL
    return 'low';
  }

  /**
   * Get congestion-based multipliers
   */
  private getCongestionMultiplier(level: 'low' | 'medium' | 'high' | 'extreme') {
    switch (level) {
      case 'low':
        return { economy: 1.0, standard: 1.0, fast: 1.1, turbo: 1.2 };
      case 'medium':
        return { economy: 1.1, standard: 1.2, fast: 1.3, turbo: 1.5 };
      case 'high':
        return { economy: 1.2, standard: 1.4, fast: 1.6, turbo: 2.0 };
      case 'extreme':
        return { economy: 1.5, standard: 1.8, fast: 2.2, turbo: 3.0 };
      default:
        return { economy: 1.0, standard: 1.0, fast: 1.0, turbo: 1.0 };
    }
  }

  /**
   * Clear cache (useful for testing)
   */
  clearCache(): void {
    this.cachedCongestion = null;
    this.cacheExpiry = 0;
  }
}

// Export singleton instance
export const priorityFeeService = new PriorityFeeService();