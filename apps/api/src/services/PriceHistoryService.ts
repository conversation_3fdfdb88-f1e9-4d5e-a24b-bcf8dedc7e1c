import { PrismaClient } from '@prisma/client';
import { Decimal } from 'decimal.js';
import { apiLogger } from '../lib/logger';
import { MarketDataService } from './MarketDataService';
import { WatchlistService } from './WatchlistService';

export interface PriceSnapshot {
  tokenAddress: string;
  tokenSymbol: string;
  priceUsd: Decimal;
  volume24h?: Decimal;
  marketCap?: Decimal;
  liquidity?: Decimal;
  timestamp: Date;
}

export interface PriceChange {
  currentPrice: Decimal;
  previousPrice?: Decimal;
  changeAmount?: Decimal;
  changePercent?: Decimal;
  interval: string;
}

export interface TokenPriceMetrics {
  tokenAddress: string;
  tokenSymbol: string;
  currentPrice: Decimal;
  changes: {
    '5m'?: PriceChange;
    '15m'?: PriceChange;
    '30m'?: PriceChange;
    '1h'?: PriceChange;
    '4h'?: PriceChange;
    '24h'?: PriceChange;
  };
  volume24h?: Decimal;
  marketCap?: Decimal;
  lastUpdated: Date;
}

export class PriceHistoryService {
  private readonly logger = apiLogger.child({ service: 'PriceHistoryService' });
  private snapshotInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

  constructor(
    private readonly prisma: PrismaClient,
    private readonly marketDataService: MarketDataService,
    private readonly watchlistService: WatchlistService
  ) {}

  /**
   * Start recording price snapshots
   */
  async startRecording(intervalMs: number = 60000): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('Price history recording is already running');
      return;
    }

    this.isRunning = true;
    this.logger.info({ intervalMs }, 'Starting price history recording');

    // Take initial snapshot
    await this.takeSnapshot();

    // Schedule periodic snapshots
    this.snapshotInterval = setInterval(async () => {
      try {
        await this.takeSnapshot();
      } catch (error) {
        this.logger.error({ error }, 'Failed to take price snapshot');
      }
    }, intervalMs);
  }

  /**
   * Stop recording price snapshots
   */
  stopRecording(): void {
    if (this.snapshotInterval) {
      clearInterval(this.snapshotInterval);
      this.snapshotInterval = null;
    }
    this.isRunning = false;
    this.logger.info('Stopped price history recording');
  }

  /**
   * Take a snapshot of current prices
   */
  async takeSnapshot(): Promise<void> {
    try {
      // Get all active watchlist items
      const watchlistItems = await this.watchlistService.findAll();
      
      if (watchlistItems.length === 0) {
        this.logger.debug('No watchlist items to snapshot');
        return;
      }

      const addresses = watchlistItems.map(item => item.tokenAddress);
      
      // Fetch current market data using fetchBatchData
      const marketDataResponse = await this.marketDataService.fetchBatchData(addresses);
      
      // Extract snapshots from response
      const marketSnapshots = marketDataResponse.data || [];
      
      if (marketSnapshots.length === 0) {
        this.logger.warn('No valid market data snapshots to record');
        return;
      }
      
      // Prepare batch insert data
      const snapshots = marketSnapshots.map(snapshot => ({
        recorded_at: new Date(),
        token_address: snapshot.tokenAddress,
        token_symbol: watchlistItems.find(w => w.tokenAddress === snapshot.tokenAddress)?.tokenSymbol || 'UNK',
        price_usd: snapshot.priceUsd || new Decimal(0),
        volume_24h: snapshot.volume24h,
        market_cap: snapshot.fdv,
        liquidity: snapshot.liquidity,
        source: marketDataResponse.adapter || 'jupiter-v3'
      }));

      // Batch insert into price_history table
      if (snapshots.length > 0) {
        await this.prisma.$executeRawUnsafe(`
          INSERT INTO price_history (recorded_at, token_address, token_symbol, price_usd, volume_24h, market_cap, liquidity, source)
          VALUES ${snapshots.map((_, i) => 
            `($${i * 8 + 1}, $${i * 8 + 2}, $${i * 8 + 3}, $${i * 8 + 4}::numeric, $${i * 8 + 5}::numeric, $${i * 8 + 6}::numeric, $${i * 8 + 7}::numeric, $${i * 8 + 8})`
          ).join(', ')}
        `, ...snapshots.flatMap(s => [
          s.recorded_at,
          s.token_address,
          s.token_symbol,
          s.price_usd.toString(),
          s.volume_24h?.toString() || null,
          s.market_cap?.toString() || null,
          s.liquidity?.toString() || null,
          s.source
        ]));

        this.logger.info({ count: snapshots.length }, 'Recorded price snapshots');
      }
    } catch (error) {
      this.logger.error({ error }, 'Failed to record price snapshots');
      throw error;
    }
  }

  /**
   * Get price metrics with calculated changes for a token
   */
  async getTokenPriceMetrics(tokenAddress: string): Promise<TokenPriceMetrics | null> {
    try {
      // Get current price
      const currentPrice = await this.prisma.$queryRaw<any[]>`
        SELECT price_usd, token_symbol, volume_24h, market_cap, recorded_at
        FROM price_history
        WHERE token_address = ${tokenAddress}
        ORDER BY recorded_at DESC
        LIMIT 1
      `;

      if (!currentPrice || currentPrice.length === 0) {
        return null;
      }

      const current = currentPrice[0];
      const intervals = ['5m', '15m', '30m', '1h', '4h', '24h'];
      const changes: any = {};

      // Calculate changes for each interval
      for (const interval of intervals) {
        const change = await this.calculatePriceChange(tokenAddress, interval);
        if (change) {
          changes[interval] = change;
        }
      }

      return {
        tokenAddress,
        tokenSymbol: current.token_symbol,
        currentPrice: new Decimal(current.price_usd),
        changes,
        volume24h: current.volume_24h ? new Decimal(current.volume_24h) : undefined,
        marketCap: current.market_cap ? new Decimal(current.market_cap) : undefined,
        lastUpdated: current.recorded_at
      };
    } catch (error) {
      this.logger.error({ error, tokenAddress }, 'Failed to get token price metrics');
      throw error;
    }
  }

  /**
   * Calculate price change for a specific interval
   */
  async calculatePriceChange(tokenAddress: string, interval: string): Promise<PriceChange | null> {
    try {
      // Convert interval string to PostgreSQL interval
      const pgInterval = this.convertToPostgresInterval(interval);
      
      const result = await this.prisma.$queryRawUnsafe<any[]>(`
        WITH current_price AS (
          SELECT price_usd, recorded_at
          FROM price_history
          WHERE token_address = $1
          ORDER BY recorded_at DESC
          LIMIT 1
        ),
        previous_price AS (
          SELECT price_usd, recorded_at
          FROM price_history
          WHERE token_address = $1
            AND recorded_at <= NOW() - INTERVAL '${pgInterval}'
          ORDER BY recorded_at DESC
          LIMIT 1
        )
        SELECT 
          cp.price_usd AS current_price,
          pp.price_usd AS previous_price,
          cp.price_usd - pp.price_usd AS change_amount,
          CASE 
            WHEN pp.price_usd > 0 THEN 
              ((cp.price_usd - pp.price_usd) / pp.price_usd) * 100
            ELSE 0
          END AS change_percent
        FROM current_price cp
        LEFT JOIN previous_price pp ON true
      `, tokenAddress);

      if (!result || result.length === 0) {
        return null;
      }

      const data = result[0];
      if (!data.previous_price) {
        // Not enough historical data for this interval
        return null;
      }

      return {
        currentPrice: new Decimal(data.current_price),
        previousPrice: new Decimal(data.previous_price),
        changeAmount: new Decimal(data.change_amount),
        changePercent: new Decimal(data.change_percent),
        interval
      };
    } catch (error) {
      this.logger.error({ error, tokenAddress, interval }, 'Failed to calculate price change');
      return null;
    }
  }

  /**
   * Get price metrics for multiple tokens
   */
  async getBatchPriceMetrics(tokenAddresses: string[]): Promise<TokenPriceMetrics[]> {
    const metrics = await Promise.all(
      tokenAddresses.map(address => this.getTokenPriceMetrics(address))
    );
    
    return metrics.filter((m): m is TokenPriceMetrics => m !== null);
  }

  /**
   * Clean up old price history data
   */
  async cleanupOldData(daysToKeep: number = 30): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const result = await this.prisma.$executeRaw`
        DELETE FROM price_history
        WHERE recorded_at < ${cutoffDate}
      `;

      this.logger.info({ deletedRows: result, daysToKeep }, 'Cleaned up old price history');
    } catch (error) {
      this.logger.error({ error }, 'Failed to clean up old price history');
      throw error;
    }
  }

  /**
   * Convert interval string to PostgreSQL interval format
   */
  private convertToPostgresInterval(interval: string): string {
    const intervalMap: Record<string, string> = {
      '5m': '5 minutes',
      '15m': '15 minutes',
      '30m': '30 minutes',
      '1h': '1 hour',
      '4h': '4 hours',
      '24h': '24 hours',
      '7d': '7 days',
      '30d': '30 days'
    };

    return intervalMap[interval] || '1 hour';
  }

  /**
   * Get OHLCV candles for a token
   */
  async getCandles(
    tokenAddress: string,
    interval: '1m' | '5m' | '1h',
    limit: number = 100
  ): Promise<any[]> {
    const table = `price_candles_${interval}`;
    
    const candles = await this.prisma.$queryRawUnsafe(`
      SELECT 
        bucket as time,
        open,
        high,
        low,
        close,
        volume,
        market_cap
      FROM ${table}
      WHERE token_address = $1
      ORDER BY bucket DESC
      LIMIT $2
    `, tokenAddress, limit);

    return candles as any[];
  }
}