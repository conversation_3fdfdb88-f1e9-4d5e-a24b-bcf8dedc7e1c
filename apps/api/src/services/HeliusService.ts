import { 
  Connection, 
  PublicKey, 
  VersionedTransaction, 
  Transaction,
  SendOptions,
  ConfirmOptions,
  TransactionSignature,
  LAMPORTS_PER_SOL 
} from '@solana/web3.js';
import { apiLogger } from '../lib/logger';
import { walletConfig } from '../lib/config';

export interface TransactionResult {
  signature: string;
  success: boolean;
  error?: string;
  confirmationStatus?: 'processed' | 'confirmed' | 'finalized';
  slot?: number;
}

export interface AccountBalance {
  address: string;
  lamports: number;
  sol: number;
  executable: boolean;
  owner: string;
  rentEpoch: number;
}

export interface TransactionStatus {
  signature: string;
  confirmationStatus: 'processed' | 'confirmed' | 'finalized' | null;
  confirmations: number | null;
  slot: number | null;
  blockTime: number | null;
  err: any;
}

export interface EnhancedSendOptions extends SendOptions {
  priorityFeeLamports?: number;
  computeUnitPriceMicroLamports?: number;
  jitoTip?: number;
  retryOptions?: RetryOptions;
}

export interface RetryOptions {
  maxRetries: number;
  initialDelayMs: number;
  maxDelayMs: number;
  backoffMultiplier: number;
  retryableErrors: string[];
}

export interface TransactionSubmissionResult {
  signature: string;
  success: boolean;
  error?: string;
  retries: number;
  totalTime: number;
  confirmationStatus?: 'processed' | 'confirmed' | 'finalized';
  slot?: number;
  submissionMethod: 'standard' | 'enhanced' | 'jito-bundle';
}

export class HeliusService {
  private connection: Connection;
  private rpcUrl: string;

  constructor() {
    this.rpcUrl = walletConfig.rpcUrl;
    this.connection = new Connection(this.rpcUrl, {
      commitment: 'confirmed',
      confirmTransactionInitialTimeout: 30000, // 30 seconds
    });
    
    apiLogger.info({ 
      rpcUrl: this.rpcUrl,
      network: walletConfig.network,
    }, 'Helius service initialized');
  }

  /**
   * Get account balance for a wallet address
   */
  async getBalance(address: string): Promise<AccountBalance> {
    try {
      const publicKey = new PublicKey(address);
      const accountInfo = await this.connection.getAccountInfo(publicKey);
      const balance = await this.connection.getBalance(publicKey);

      const result: AccountBalance = {
        address,
        lamports: balance,
        sol: balance / LAMPORTS_PER_SOL,
        executable: accountInfo?.executable || false,
        owner: accountInfo?.owner?.toBase58() || '',
        rentEpoch: accountInfo?.rentEpoch || 0,
      };

      apiLogger.info({
        address,
        balance: result.sol,
      }, 'Account balance retrieved');

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      apiLogger.error({
        error: errorMessage,
        address,
      }, 'Failed to get account balance');
      throw new Error(`Failed to get balance: ${errorMessage}`);
    }
  }

  /**
   * Send a signed transaction to the network
   */
  async sendTransaction(
    transaction: VersionedTransaction | Transaction,
    options?: SendOptions
  ): Promise<TransactionResult> {
    try {
      const sendOptions: SendOptions = {
        skipPreflight: false,
        preflightCommitment: 'processed',
        maxRetries: 3,
        ...options,
      };

      let signature: TransactionSignature;
      
      if (transaction instanceof VersionedTransaction) {
        signature = await this.connection.sendTransaction(transaction, sendOptions);
      } else {
        signature = await this.connection.sendTransaction(transaction, [], sendOptions);
      }

      apiLogger.info({
        signature,
        options: sendOptions,
      }, 'Transaction sent to network');

      return {
        signature,
        success: true,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      apiLogger.error({
        error: errorMessage,
      }, 'Failed to send transaction');

      return {
        signature: '',
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Confirm a transaction and get its status
   */
  async confirmTransaction(
    signature: string,
    commitment: 'processed' | 'confirmed' | 'finalized' = 'confirmed'
  ): Promise<TransactionStatus> {
    try {
      const confirmOptions: ConfirmOptions = {
        commitment,
      };

      const confirmation = await this.connection.confirmTransaction(signature, commitment);
      const status = await this.connection.getSignatureStatus(signature);

      const result: TransactionStatus = {
        signature,
        confirmationStatus: status.value?.confirmationStatus || null,
        confirmations: status.value?.confirmations || null,
        slot: status.value?.slot || null,
        blockTime: null,
        err: status.value?.err,
      };

      // Get block time if we have a slot
      if (result.slot) {
        try {
          result.blockTime = await this.connection.getBlockTime(result.slot);
        } catch (error) {
          // Block time not critical, continue without it
          apiLogger.warn({ error, slot: result.slot }, 'Failed to get block time');
        }
      }

      apiLogger.info({
        signature,
        confirmationStatus: result.confirmationStatus,
        confirmations: result.confirmations,
        hasError: !!result.err,
      }, 'Transaction confirmation status retrieved');

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      apiLogger.error({
        error: errorMessage,
        signature,
      }, 'Failed to confirm transaction');

      throw new Error(`Failed to confirm transaction: ${errorMessage}`);
    }
  }

  /**
   * Send and confirm a transaction in one call
   */
  async sendAndConfirmTransaction(
    transaction: VersionedTransaction | Transaction,
    commitment: 'processed' | 'confirmed' | 'finalized' = 'confirmed',
    options?: SendOptions
  ): Promise<TransactionResult & TransactionStatus> {
    try {
      // Send transaction
      const sendResult = await this.sendTransaction(transaction, options);
      
      if (!sendResult.success) {
        return {
          ...sendResult,
          confirmationStatus: null,
          confirmations: null,
          slot: null,
          blockTime: null,
          err: sendResult.error,
        };
      }

      // Confirm transaction
      const confirmResult = await this.confirmTransaction(sendResult.signature, commitment);

      return {
        ...sendResult,
        ...confirmResult,
        confirmationStatus: confirmResult.confirmationStatus,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      apiLogger.error({
        error: errorMessage,
      }, 'Failed to send and confirm transaction');

      return {
        signature: '',
        success: false,
        error: errorMessage,
        confirmationStatus: null,
        confirmations: null,
        slot: null,
        blockTime: null,
        err: errorMessage,
      };
    }
  }

  /**
   * Get recent transactions for an address
   */
  async getRecentTransactions(
    address: string,
    limit: number = 10
  ): Promise<Array<{ signature: string; blockTime: number | null; slot: number }>> {
    try {
      const publicKey = new PublicKey(address);
      const signatures = await this.connection.getSignaturesForAddress(
        publicKey,
        { limit },
        'confirmed'
      );

      const transactions = signatures.map(sig => ({
        signature: sig.signature,
        blockTime: sig.blockTime,
        slot: sig.slot,
      }));

      apiLogger.info({
        address,
        count: transactions.length,
        limit,
      }, 'Recent transactions retrieved');

      return transactions;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      apiLogger.error({
        error: errorMessage,
        address,
      }, 'Failed to get recent transactions');
      throw new Error(`Failed to get recent transactions: ${errorMessage}`);
    }
  }

  /**
   * Get network information
   */
  async getNetworkInfo(): Promise<{
    network: string;
    rpcUrl: string;
    currentSlot: number;
    epochInfo: any;
  }> {
    try {
      const slot = await this.connection.getSlot();
      const epochInfo = await this.connection.getEpochInfo();

      const info = {
        network: walletConfig.network,
        rpcUrl: this.rpcUrl,
        currentSlot: slot,
        epochInfo,
      };

      apiLogger.info({
        network: info.network,
        currentSlot: info.currentSlot,
        epoch: epochInfo.epoch,
      }, 'Network info retrieved');

      return info;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      apiLogger.error({
        error: errorMessage,
      }, 'Failed to get network info');
      throw new Error(`Failed to get network info: ${errorMessage}`);
    }
  }

  /**
   * Simulate a transaction before sending
   */
  async simulateTransaction(
    transaction: VersionedTransaction | Transaction
  ): Promise<{ success: boolean; error?: string; logs?: string[] }> {
    try {
      let simulation;
      
      if (transaction instanceof VersionedTransaction) {
        simulation = await this.connection.simulateTransaction(transaction);
      } else {
        simulation = await this.connection.simulateTransaction(transaction);
      }

      const success = simulation.value.err === null;
      
      apiLogger.info({
        success,
        logs: simulation.value.logs?.length || 0,
        unitsConsumed: simulation.value.unitsConsumed,
      }, 'Transaction simulation completed');

      return {
        success,
        error: simulation.value.err ? JSON.stringify(simulation.value.err) : undefined,
        logs: simulation.value.logs || [],
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      apiLogger.error({
        error: errorMessage,
      }, 'Failed to simulate transaction');

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Get connection instance for advanced operations
   */
  getConnection(): Connection {
    return this.connection;
  }

  /**
   * Enhanced transaction submission with MEV protection and retry logic
   */
  async submitTransactionWithRetry(
    transaction: VersionedTransaction | Transaction,
    options: EnhancedSendOptions = {}
  ): Promise<TransactionSubmissionResult> {
    const startTime = Date.now();
    const retryOptions: RetryOptions = {
      maxRetries: 3,
      initialDelayMs: 1000,
      maxDelayMs: 10000,
      backoffMultiplier: 2,
      retryableErrors: [
        'Transaction was not confirmed',
        'Blockhash not found',
        '429',
        'timeout',
        'network error',
      ],
      ...options.retryOptions,
    };

    let lastError: string = '';
    let signature = '';
    
    for (let attempt = 0; attempt <= retryOptions.maxRetries; attempt++) {
      try {
        // Prepare send options
        const sendOptions: SendOptions = {
          skipPreflight: options.skipPreflight || false,
          preflightCommitment: options.preflightCommitment || 'processed',
          maxRetries: 0, // We handle retries manually
        };

        // Send transaction
        if (transaction instanceof VersionedTransaction) {
          signature = await this.connection.sendTransaction(transaction, sendOptions);
        } else {
          signature = await this.connection.sendTransaction(transaction, [], sendOptions);
        }

        // Wait for confirmation
        const confirmation = await this.confirmTransaction(signature, 'confirmed');
        
        if (confirmation.err) {
          throw new Error(`Transaction failed: ${JSON.stringify(confirmation.err)}`);
        }

        // Success!
        const totalTime = Date.now() - startTime;
        
        apiLogger.info({
          signature,
          attempts: attempt + 1,
          totalTime,
          confirmationStatus: confirmation.confirmationStatus,
        }, 'Transaction submitted successfully with retry logic');

        return {
          signature,
          success: true,
          retries: attempt,
          totalTime,
          confirmationStatus: confirmation.confirmationStatus,
          slot: confirmation.slot || undefined,
          submissionMethod: 'enhanced',
        };

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        lastError = errorMessage;

        // Check if error is retryable
        const isRetryable = retryOptions.retryableErrors.some(retryableError =>
          errorMessage.toLowerCase().includes(retryableError.toLowerCase())
        );

        if (!isRetryable || attempt === retryOptions.maxRetries) {
          break;
        }

        // Calculate delay for next retry
        const delay = Math.min(
          retryOptions.initialDelayMs * Math.pow(retryOptions.backoffMultiplier, attempt),
          retryOptions.maxDelayMs
        );

        apiLogger.warn({
          attempt: attempt + 1,
          error: errorMessage,
          delayMs: delay,
          willRetry: attempt < retryOptions.maxRetries,
        }, 'Transaction failed, will retry');

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // All retries failed
    const totalTime = Date.now() - startTime;
    
    apiLogger.error({
      error: lastError,
      retries: retryOptions.maxRetries,
      totalTime,
    }, 'Transaction submission failed after all retries');

    return {
      signature: signature || '',
      success: false,
      error: lastError,
      retries: retryOptions.maxRetries,
      totalTime,
      submissionMethod: 'enhanced',
    };
  }

  /**
   * Submit transaction with Jito bundle (MEV protection)
   */
  async submitJitoBundle(
    transactions: Array<VersionedTransaction | Transaction>,
    tipLamports: number = 10000
  ): Promise<TransactionSubmissionResult> {
    const startTime = Date.now();

    try {
      // For now, simulate Jito bundle submission
      // In a real implementation, you would use Jito's bundle API
      
      // Send the main transaction with enhanced options
      const mainTransaction = transactions[0];
      if (!mainTransaction) {
        throw new Error('No transactions in bundle');
      }

      const result = await this.submitTransactionWithRetry(mainTransaction, {
        skipPreflight: true, // Higher success rate with bundles
        retryOptions: {
          maxRetries: 1, // Fewer retries needed with bundles
          initialDelayMs: 2000,
          maxDelayMs: 5000,
          backoffMultiplier: 1.5,
          retryableErrors: ['Transaction was not confirmed', 'Blockhash not found'],
        },
      });

      const totalTime = Date.now() - startTime;

      apiLogger.info({
        signature: result.signature,
        tipLamports,
        bundleSize: transactions.length,
        totalTime,
      }, 'Jito bundle submitted (simulated)');

      return {
        ...result,
        totalTime,
        submissionMethod: 'jito-bundle',
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const totalTime = Date.now() - startTime;

      apiLogger.error({
        error: errorMessage,
        tipLamports,
        bundleSize: transactions.length,
        totalTime,
      }, 'Jito bundle submission failed');

      return {
        signature: '',
        success: false,
        error: errorMessage,
        retries: 0,
        totalTime,
        submissionMethod: 'jito-bundle',
      };
    }
  }

  /**
   * Monitor transaction until final confirmation
   */
  async monitorTransaction(
    signature: string,
    targetCommitment: 'processed' | 'confirmed' | 'finalized' = 'confirmed',
    timeoutMs: number = 60000
  ): Promise<TransactionStatus> {
    const startTime = Date.now();
    const pollInterval = 2000; // 2 seconds

    while (Date.now() - startTime < timeoutMs) {
      try {
        const status = await this.confirmTransaction(signature, targetCommitment);
        
        // Check if we've reached the target commitment
        const commitmentReached = this.hasReachedCommitment(
          status.confirmationStatus,
          targetCommitment
        );

        if (commitmentReached || status.err) {
          apiLogger.info({
            signature,
            finalStatus: status.confirmationStatus,
            confirmations: status.confirmations,
            monitoringTime: Date.now() - startTime,
            hasError: !!status.err,
          }, 'Transaction monitoring completed');

          return status;
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, pollInterval));

      } catch (error) {
        apiLogger.warn({
          signature,
          error: error instanceof Error ? error.message : 'Unknown error',
          monitoringTime: Date.now() - startTime,
        }, 'Error during transaction monitoring, will continue');

        // Continue monitoring despite errors
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      }
    }

    // Timeout reached
    apiLogger.warn({
      signature,
      timeoutMs,
      targetCommitment,
    }, 'Transaction monitoring timed out');

    throw new Error(`Transaction monitoring timed out after ${timeoutMs}ms`);
  }

  /**
   * Get transaction details with enhanced information
   */
  async getTransactionDetails(signature: string): Promise<{
    signature: string;
    status: TransactionStatus;
    fee: number | null;
    computeUnitsConsumed: number | null;
    logs: string[];
    accounts: string[];
  }> {
    try {
      const status = await this.confirmTransaction(signature);
      const transaction = await this.connection.getTransaction(signature, {
        commitment: 'confirmed',
        maxSupportedTransactionVersion: 0,
      });

      const result = {
        signature,
        status,
        fee: transaction?.meta?.fee || null,
        computeUnitsConsumed: transaction?.meta?.computeUnitsConsumed || null,
        logs: transaction?.meta?.logMessages || [],
        accounts: transaction?.transaction.message.getAccountKeys().map(key => key.toBase58()) || [],
      };

      apiLogger.info({
        signature,
        fee: result.fee,
        computeUnits: result.computeUnitsConsumed,
        logsCount: result.logs.length,
        accountsCount: result.accounts.length,
      }, 'Transaction details retrieved');

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      apiLogger.error({
        error: errorMessage,
        signature,
      }, 'Failed to get transaction details');
      throw new Error(`Failed to get transaction details: ${errorMessage}`);
    }
  }

  /**
   * Check if a commitment level has been reached
   */
  private hasReachedCommitment(
    current: 'processed' | 'confirmed' | 'finalized' | null,
    target: 'processed' | 'confirmed' | 'finalized'
  ): boolean {
    if (!current) return false;

    const commitmentLevels = { processed: 1, confirmed: 2, finalized: 3 };
    return commitmentLevels[current] >= commitmentLevels[target];
  }

  /**
   * Get default retry options
   */
  getDefaultRetryOptions(): RetryOptions {
    return {
      maxRetries: 3,
      initialDelayMs: 1000,
      maxDelayMs: 10000,
      backoffMultiplier: 2,
      retryableErrors: [
        'Transaction was not confirmed',
        'Blockhash not found',
        '429',
        'timeout',
        'network error',
        'insufficient funds',
      ],
    };
  }

  /**
   * Check if the RPC connection is healthy
   */
  async isHealthy(): Promise<{ healthy: boolean; latency?: number; error?: string }> {
    try {
      const startTime = Date.now();
      await this.connection.getSlot();
      const latency = Date.now() - startTime;

      return {
        healthy: true,
        latency,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        healthy: false,
        error: errorMessage,
      };
    }
  }
}