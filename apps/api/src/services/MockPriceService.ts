import { apiLogger } from '../lib/logger';

export interface TokenPrice {
  contractAddress: string;
  symbol: string;
  name: string;
  price: string;
  priceChangePercent24h: string;
  volume24h: string;
  marketCap: string;
  lastUpdated: Date;
  source: 'mock';
}

export interface PriceAlert {
  contractAddress: string;
  triggerPrice: string;
  direction: 'above' | 'below';
  currentPrice: string;
  triggered: boolean;
  triggeredAt?: Date;
}

export class MockPriceService {
  /**
   * Get current price for a token by contract address
   */
  async getTokenPrice(contractAddress: string): Promise<TokenPrice> {
    const price = this.generateMockPrice(contractAddress);
    
    apiLogger.info({
      contractAddress,
      price: price.price,
      source: price.source,
    }, 'Token price retrieved');

    return price;
  }

  /**
   * Get prices for multiple tokens
   */
  async getTokenPrices(contractAddresses: string[]): Promise<TokenPrice[]> {
    return contractAddresses.map(address => this.generateMockPrice(address));
  }

  /**
   * Check price alerts and return triggered ones
   */
  checkPriceAlerts(alerts: PriceAlert[], currentPrices: TokenPrice[]): PriceAlert[] {
    // Simplified implementation - no alerts triggered for mock service
    return [];
  }

  /**
   * Generate mock price data for testing
   */
  private generateMockPrice(contractAddress: string): TokenPrice {
    // Generate deterministic but varied mock prices based on contract address
    const hash = this.hashCode(contractAddress);
    const basePrice = (Math.abs(hash) % 10000) / 10000 + 0.1; // Price between 0.1 and 1.1
    
    // Add some randomness for price changes
    const variation = (Math.random() - 0.5) * 0.2; // ±10%
    const price = basePrice * (1 + variation);
    
    const change24h = (Math.random() - 0.5) * 20; // ±10% change

    return {
      contractAddress,
      symbol: 'TOK',
      name: 'Mock Token',
      price: price.toFixed(6),
      priceChangePercent24h: change24h.toFixed(2),
      volume24h: '1000000',
      marketCap: '10000000',
      lastUpdated: new Date(),
      source: 'mock',
    };
  }

  /**
   * Simple hash function for generating deterministic mock data
   */
  private hashCode(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return hash;
  }
}