import { PrismaClient } from '@prisma/client';
import { EventEmitter } from 'events';
import { ExitStrategyModel, ExitStrategyPresetModel } from '../models/exit-strategy.js';
import {
  ExitStrategyConfiguration,
  CreateExitStrategyRequest,
  UpdateExitStrategyRequest,
  IExitStrategyService,
  ExitStrategyEvent,
  ExitStrategyError,
  StrategyEventType
} from '../types/exit-strategy.js';

export class ExitStrategyService extends EventEmitter implements IExitStrategyService {
  private exitStrategyModel: ExitStrategyModel;
  private presetModel: ExitStrategyPresetModel;

  constructor(private prisma: PrismaClient) {
    super();
    this.exitStrategyModel = new ExitStrategyModel(prisma);
    this.presetModel = new ExitStrategyPresetModel(prisma);
  }

  async createStrategy(request: CreateExitStrategyRequest): Promise<string> {
    try {
      const strategyId = await this.exitStrategyModel.createExitStrategy(
        request.positionId,
        request.configuration,
        request.presetId
      );

      this.emitEvent('STRATEGY_CREATED', {
        strategyId,
        positionId: request.positionId,
        configuration: request.configuration,
        presetId: request.presetId
      });

      return strategyId;
    } catch (error) {
      throw new ExitStrategyError(
        `Failed to create exit strategy: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'STRATEGY_CREATION_FAILED'
      );
    }
  }

  async updateStrategy(strategyId: string, request: UpdateExitStrategyRequest): Promise<void> {
    try {
      const currentStrategy = await this.exitStrategyModel.getExitStrategy(strategyId);
      if (!currentStrategy) {
        throw new Error('Exit strategy not found');
      }

      await this.exitStrategyModel.updateExitStrategy(strategyId, request.configuration);

      this.emit('strategyEvent', {
        type: 'STRATEGY_UPDATED',
        timestamp: new Date(),
        strategyId,
        positionId: currentStrategy.positionId,
        previousConfiguration: currentStrategy.configuration,
        newConfiguration: request.configuration
      } as ExitStrategyEvent);

    } catch (error) {
      throw new ExitStrategyError(
        `Failed to update exit strategy: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'STRATEGY_UPDATE_FAILED'
      );
    }
  }

  async deleteStrategy(strategyId: string): Promise<void> {
    try {
      const strategy = await this.exitStrategyModel.getExitStrategy(strategyId);
      if (!strategy) {
        throw new Error('Exit strategy not found');
      }

      await this.exitStrategyModel.deleteExitStrategy(strategyId);

      this.emitEvent('STRATEGY_DELETED', {
        strategyId,
        positionId: strategy.positionId
      });

    } catch (error) {
      throw new ExitStrategyError(
        `Failed to delete exit strategy: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'STRATEGY_DELETION_FAILED'
      );
    }
  }

  async getStrategy(strategyId: string): Promise<any> {
    try {
      const strategy = await this.exitStrategyModel.getExitStrategy(strategyId);
      if (!strategy) {
        throw new Error('Exit strategy not found');
      }
      return strategy;
    } catch (error) {
      throw new ExitStrategyError(
        `Failed to get exit strategy: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'STRATEGY_RETRIEVAL_FAILED'
      );
    }
  }

  async getStrategiesByPosition(positionId: string): Promise<any[]> {
    try {
      return await this.exitStrategyModel.getStrategiesByPosition(positionId);
    } catch (error) {
      throw new ExitStrategyError(
        `Failed to get strategies for position: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'STRATEGY_RETRIEVAL_FAILED'
      );
    }
  }

  async validateConfiguration(configuration: ExitStrategyConfiguration): Promise<boolean> {
    try {
      return this.exitStrategyModel.validateConfiguration(configuration);
    } catch (error) {
      throw new ExitStrategyError(
        `Configuration validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'CONFIGURATION_VALIDATION_FAILED'
      );
    }
  }

  async getStrategiesRequiringPriceMonitoring(): Promise<string[]> {
    try {
      const activeStrategies = await this.prisma.exitStrategy.findMany({
        where: {
          status: {
            in: ['ACTIVE', 'PENDING']
          }
        },
        include: {
          position: {
            select: {
              tokenAddress: true
            }
          }
        }
      });

      const tokenAddresses = new Set<string>();
      activeStrategies.forEach(strategy => {
        if (strategy.position?.tokenAddress) {
          tokenAddresses.add(strategy.position.tokenAddress);
        }
      });

      return Array.from(tokenAddresses);
    } catch (error) {
      console.error('Error getting strategies requiring price monitoring:', error);
      return [];
    }
  }

  private emitEvent(type: StrategyEventType, data: any): void {
    this.emit('strategyEvent', {
      type,
      timestamp: new Date(),
      ...data
    });
  }
}

export default ExitStrategyService;
