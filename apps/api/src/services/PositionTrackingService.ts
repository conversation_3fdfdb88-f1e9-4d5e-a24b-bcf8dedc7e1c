import { apiLogger } from '../lib/logger';
import { HeliusService } from './HeliusService';
import { PriorityFeeService } from './PriorityFeeService';
import Decimal from 'decimal.js';

export interface Position {
  id: string;
  userPublicKey: string;
  inputMint: string;
  outputMint: string;
  entryTransaction: string;
  entryPrice: string;
  entryAmount: string;
  outputAmount: string;
  currentValue?: string;
  unrealizedPnL?: string;
  status: 'active' | 'closed' | 'monitoring_failed';
  entryTimestamp: Date;
  lastUpdateTimestamp: Date;
  exitStrategy?: ExitStrategy;
  alerts?: PriceAlert[];
  metadata?: {
    mevProtectionUsed: string;
    totalFeePaid: number;
    priceImpactAtEntry: string;
    route: string[];
  };
}

export interface ExitStrategy {
  type: 'stop_loss' | 'take_profit' | 'trailing_stop' | 'time_based';
  trigger: {
    stopLossPercentage?: number;
    takeProfitPercentage?: number;
    trailingStopPercentage?: number;
    timeBasedExit?: Date;
    priceTarget?: string;
  };
  isActive: boolean;
  lastEvaluated: Date;
}

export interface PriceAlert {
  id: string;
  type: 'price_above' | 'price_below' | 'percentage_change';
  threshold: string;
  isActive: boolean;
  triggered: boolean;
  triggeredAt?: Date;
}

export interface PositionUpdate {
  position: Position;
  currentPrice?: string;
  priceChange24h?: string;
  recommendedAction?: 'hold' | 'consider_exit' | 'urgent_exit';
  alertsTriggered: PriceAlert[];
}

export interface PositionSummary {
  totalPositions: number;
  activePositions: number;
  totalUnrealizedPnL: string;
  bestPerformer?: Position;
  worstPerformer?: Position;
  alertsCount: number;
  averageHoldingTime: string;
}

export class PositionTrackingService {
  private heliusService: HeliusService;
  private priorityFeeService: PriorityFeeService;
  private positions: Map<string, Position> = new Map();
  private monitoringInterval?: NodeJS.Timeout;
  private readonly MONITORING_INTERVAL_MS = 30000; // 30 seconds

  constructor() {
    this.heliusService = new HeliusService();
    this.priorityFeeService = new PriorityFeeService();
    this.startPositionMonitoring();
    
    apiLogger.info('Position tracking service initialized');
  }

  /**
   * Create a new position for tracking
   */
  async createPosition(params: {
    userPublicKey: string;
    inputMint: string;
    outputMint: string;
    entryTransaction: string;
    entryPrice: string;
    entryAmount: string;
    outputAmount: string;
    mevProtectionUsed: string;
    totalFeePaid: number;
    priceImpactAtEntry: string;
    route: string[];
    exitStrategy?: Partial<ExitStrategy>;
  }): Promise<Position> {
    try {
      const positionId = `pos_${params.entryTransaction.slice(0, 8)}_${Date.now()}`;
      
      const position: Position = {
        id: positionId,
        userPublicKey: params.userPublicKey,
        inputMint: params.inputMint,
        outputMint: params.outputMint,
        entryTransaction: params.entryTransaction,
        entryPrice: params.entryPrice,
        entryAmount: params.entryAmount,
        outputAmount: params.outputAmount,
        status: 'active',
        entryTimestamp: new Date(),
        lastUpdateTimestamp: new Date(),
        metadata: {
          mevProtectionUsed: params.mevProtectionUsed,
          totalFeePaid: params.totalFeePaid,
          priceImpactAtEntry: params.priceImpactAtEntry,
          route: params.route,
        },
        alerts: [],
      };

      // Set up default exit strategy if provided
      if (params.exitStrategy) {
        position.exitStrategy = {
          type: params.exitStrategy.type || 'stop_loss',
          trigger: params.exitStrategy.trigger || { stopLossPercentage: 10 }, // Default 10% stop loss
          isActive: true,
          lastEvaluated: new Date(),
        };
      }

      // Store position
      this.positions.set(positionId, position);

      // Calculate initial position value
      await this.updatePositionValue(positionId);

      apiLogger.info({
        positionId,
        userPublicKey: params.userPublicKey,
        inputMint: params.inputMint,
        outputMint: params.outputMint,
        entryPrice: params.entryPrice,
      }, 'Position created for tracking');

      return position;
    } catch (error) {
      apiLogger.error({ error, params }, 'Failed to create position');
      throw new Error(`Failed to create position: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get a position by ID
   */
  getPosition(positionId: string): Position | null {
    return this.positions.get(positionId) || null;
  }

  /**
   * Get all positions for a user
   */
  getUserPositions(userPublicKey: string): Position[] {
    return Array.from(this.positions.values())
      .filter(position => position.userPublicKey === userPublicKey)
      .sort((a, b) => b.entryTimestamp.getTime() - a.entryTimestamp.getTime());
  }

  /**
   * Get all active positions
   */
  getActivePositions(): Position[] {
    return Array.from(this.positions.values())
      .filter(position => position.status === 'active');
  }

  /**
   * Update position exit strategy
   */
  async updateExitStrategy(
    positionId: string,
    exitStrategy: Partial<ExitStrategy>
  ): Promise<Position | null> {
    const position = this.positions.get(positionId);
    if (!position) return null;

    position.exitStrategy = {
      type: exitStrategy.type || position.exitStrategy?.type || 'stop_loss',
      trigger: { ...position.exitStrategy?.trigger, ...exitStrategy.trigger },
      isActive: exitStrategy.isActive ?? true,
      lastEvaluated: new Date(),
    };

    position.lastUpdateTimestamp = new Date();
    
    apiLogger.info({
      positionId,
      exitStrategy: position.exitStrategy,
    }, 'Position exit strategy updated');

    return position;
  }

  /**
   * Add price alert to position
   */
  async addPriceAlert(
    positionId: string,
    alert: Omit<PriceAlert, 'id' | 'triggered' | 'triggeredAt'>
  ): Promise<Position | null> {
    const position = this.positions.get(positionId);
    if (!position) return null;

    const alertId = `alert_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    const newAlert: PriceAlert = {
      ...alert,
      id: alertId,
      triggered: false,
    };

    if (!position.alerts) {
      position.alerts = [];
    }

    position.alerts.push(newAlert);
    position.lastUpdateTimestamp = new Date();

    apiLogger.info({
      positionId,
      alertId,
      alertType: alert.type,
      threshold: alert.threshold,
    }, 'Price alert added to position');

    return position;
  }

  /**
   * Close a position
   */
  async closePosition(positionId: string, exitTransaction?: string): Promise<Position | null> {
    const position = this.positions.get(positionId);
    if (!position) return null;

    position.status = 'closed';
    position.lastUpdateTimestamp = new Date();

    if (exitTransaction) {
      // In a full implementation, you would analyze the exit transaction
      // to calculate realized PnL, fees, etc.
    }

    apiLogger.info({
      positionId,
      exitTransaction,
      holdingTime: Date.now() - position.entryTimestamp.getTime(),
    }, 'Position closed');

    return position;
  }

  /**
   * Get position summary for a user
   */
  async getPositionSummary(userPublicKey: string): Promise<PositionSummary> {
    const userPositions = this.getUserPositions(userPublicKey);
    const activePositions = userPositions.filter(p => p.status === 'active');

    let totalUnrealizedPnL = new Decimal(0);
    let bestPerformer: Position | undefined;
    let worstPerformer: Position | undefined;
    let bestPnL = new Decimal(-Infinity);
    let worstPnL = new Decimal(Infinity);
    let totalHoldingTime = 0;
    let alertsCount = 0;

    for (const position of activePositions) {
      if (position.unrealizedPnL) {
        const pnl = new Decimal(position.unrealizedPnL);
        totalUnrealizedPnL = totalUnrealizedPnL.plus(pnl);

        if (pnl.gt(bestPnL)) {
          bestPnL = pnl;
          bestPerformer = position;
        }

        if (pnl.lt(worstPnL)) {
          worstPnL = pnl;
          worstPerformer = position;
        }
      }

      totalHoldingTime += Date.now() - position.entryTimestamp.getTime();
      alertsCount += position.alerts?.filter(a => a.isActive && !a.triggered).length || 0;
    }

    const averageHoldingTime = activePositions.length > 0 
      ? Math.floor((totalHoldingTime / activePositions.length) / 1000 / 60) // Minutes
      : 0;

    return {
      totalPositions: userPositions.length,
      activePositions: activePositions.length,
      totalUnrealizedPnL: totalUnrealizedPnL.toString(),
      bestPerformer,
      worstPerformer,
      alertsCount,
      averageHoldingTime: `${Math.floor(averageHoldingTime / 60)}h ${averageHoldingTime % 60}m`,
    };
  }

  /**
   * Start monitoring all positions
   */
  private startPositionMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.monitoringInterval = setInterval(async () => {
      await this.monitorAllPositions();
    }, this.MONITORING_INTERVAL_MS);

    apiLogger.info(
      { intervalMs: this.MONITORING_INTERVAL_MS },
      'Position monitoring started'
    );
  }

  /**
   * Monitor all active positions
   */
  private async monitorAllPositions(): Promise<void> {
    const activePositions = this.getActivePositions();
    
    if (activePositions.length === 0) return;

    apiLogger.debug(
      { activePositionsCount: activePositions.length },
      'Monitoring active positions'
    );

    for (const position of activePositions) {
      try {
        await this.updatePositionValue(position.id);
        await this.evaluateExitStrategy(position.id);
        await this.checkPriceAlerts(position.id);
      } catch (error) {
        apiLogger.error(
          { error, positionId: position.id },
          'Failed to monitor position'
        );
        
        // Mark position as monitoring failed after multiple failures
        position.status = 'monitoring_failed';
      }
    }
  }

  /**
   * Update the current value and PnL of a position
   */
  private async updatePositionValue(positionId: string): Promise<void> {
    const position = this.positions.get(positionId);
    if (!position || position.status !== 'active') return;

    try {
      // For now, simulate price update
      // In a full implementation, you would fetch real-time prices from Jupiter/other price feeds
      const mockCurrentPrice = this.generateMockPrice(position.entryPrice);
      const entryPrice = new Decimal(position.entryPrice);
      const currentPrice = new Decimal(mockCurrentPrice);
      const outputAmount = new Decimal(position.outputAmount);

      // Calculate current value and unrealized PnL
      const currentValue = outputAmount.mul(currentPrice);
      const entryValue = outputAmount.mul(entryPrice);
      const unrealizedPnL = currentValue.minus(entryValue);

      position.currentValue = currentValue.toString();
      position.unrealizedPnL = unrealizedPnL.toString();
      position.lastUpdateTimestamp = new Date();

      apiLogger.debug({
        positionId,
        currentPrice: mockCurrentPrice,
        currentValue: currentValue.toString(),
        unrealizedPnL: unrealizedPnL.toString(),
      }, 'Position value updated');
    } catch (error) {
      apiLogger.error({ error, positionId }, 'Failed to update position value');
    }
  }

  /**
   * Evaluate exit strategy for a position
   */
  private async evaluateExitStrategy(positionId: string): Promise<void> {
    const position = this.positions.get(positionId);
    if (!position || !position.exitStrategy || !position.exitStrategy.isActive) return;

    try {
      const exitStrategy = position.exitStrategy;
      const unrealizedPnL = new Decimal(position.unrealizedPnL || '0');
      const entryValue = new Decimal(position.entryAmount);
      const pnlPercentage = unrealizedPnL.div(entryValue).mul(100);

      let shouldExit = false;
      let exitReason = '';

      switch (exitStrategy.type) {
        case 'stop_loss':
          if (exitStrategy.trigger.stopLossPercentage && 
              pnlPercentage.lte(-exitStrategy.trigger.stopLossPercentage)) {
            shouldExit = true;
            exitReason = `Stop loss triggered at ${pnlPercentage.toFixed(2)}%`;
          }
          break;

        case 'take_profit':
          if (exitStrategy.trigger.takeProfitPercentage &&
              pnlPercentage.gte(exitStrategy.trigger.takeProfitPercentage)) {
            shouldExit = true;
            exitReason = `Take profit triggered at ${pnlPercentage.toFixed(2)}%`;
          }
          break;

        case 'time_based':
          if (exitStrategy.trigger.timeBasedExit &&
              new Date() >= exitStrategy.trigger.timeBasedExit) {
            shouldExit = true;
            exitReason = 'Time-based exit triggered';
          }
          break;
      }

      if (shouldExit) {
        apiLogger.warn({
          positionId,
          exitReason,
          pnlPercentage: pnlPercentage.toString(),
        }, 'Exit strategy triggered');

        // In a full implementation, you would execute the exit trade here
        // For now, just mark the position for attention
      }

      exitStrategy.lastEvaluated = new Date();
    } catch (error) {
      apiLogger.error({ error, positionId }, 'Failed to evaluate exit strategy');
    }
  }

  /**
   * Check price alerts for a position
   */
  private async checkPriceAlerts(positionId: string): Promise<void> {
    const position = this.positions.get(positionId);
    if (!position || !position.alerts || position.alerts.length === 0) return;

    try {
      const currentPrice = new Decimal(this.generateMockPrice(position.entryPrice));
      const currentTime = new Date();

      for (const alert of position.alerts) {
        if (!alert.isActive || alert.triggered) continue;

        let shouldTrigger = false;
        const threshold = new Decimal(alert.threshold);

        switch (alert.type) {
          case 'price_above':
            shouldTrigger = currentPrice.gte(threshold);
            break;
          case 'price_below':
            shouldTrigger = currentPrice.lte(threshold);
            break;
          case 'percentage_change':
            const entryPrice = new Decimal(position.entryPrice);
            const changePercentage = currentPrice.minus(entryPrice).div(entryPrice).mul(100);
            shouldTrigger = changePercentage.abs().gte(threshold);
            break;
        }

        if (shouldTrigger) {
          alert.triggered = true;
          alert.triggeredAt = currentTime;

          apiLogger.info({
            positionId,
            alertId: alert.id,
            alertType: alert.type,
            threshold: alert.threshold,
            currentPrice: currentPrice.toString(),
          }, 'Price alert triggered');
        }
      }
    } catch (error) {
      apiLogger.error({ error, positionId }, 'Failed to check price alerts');
    }
  }

  /**
   * Generate a mock price for testing (simulates price movement)
   */
  private generateMockPrice(entryPrice: string): string {
    const entry = new Decimal(entryPrice);
    // Simulate price movement between -10% and +10%
    const changePercent = (Math.random() - 0.5) * 20; // -10% to +10%
    const change = entry.mul(changePercent / 100);
    return entry.plus(change).toString();
  }

  /**
   * Stop position monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }
    apiLogger.info('Position monitoring stopped');
  }

  /**
   * Get monitoring statistics
   */
  getMonitoringStats(): {
    totalPositions: number;
    activePositions: number;
    monitoringActive: boolean;
    lastUpdate: Date | null;
  } {
    const positions = Array.from(this.positions.values());
    const lastUpdate = positions.length > 0 
      ? positions.reduce((latest, pos) => 
          pos.lastUpdateTimestamp > latest ? pos.lastUpdateTimestamp : latest, 
          positions[0].lastUpdateTimestamp)
      : null;

    return {
      totalPositions: positions.length,
      activePositions: positions.filter(p => p.status === 'active').length,
      monitoringActive: !!this.monitoringInterval,
      lastUpdate,
    };
  }
}

// Export singleton instance
export const positionTrackingService = new PositionTrackingService();