import { apiLogger } from '../lib/logger';

interface TokenBoost {
  chainId: string;
  tokenAddress: string;
  amount: number;
  totalAmount: number;
  boostedAt: string;
  boostedAtBlockNumber: number;
  icon?: string;
  description?: string;
  banner?: string;
  links?: {
    twitter?: string;
    telegram?: string;
    website?: string;
    discord?: string;
  };
}

interface TokenProfile {
  chainId: string;
  tokenAddress: string;
  icon?: string;
  description?: string;
  links?: {
    twitter?: string;
    telegram?: string;
    website?: string;
    discord?: string;
  };
}

interface DexPair {
  chainId: string;
  pairAddress: string;
  baseToken: {
    address: string;
    symbol: string;
    name: string;
  };
  quoteToken: {
    address: string;
    symbol: string;
    name: string;
  };
  priceUsd?: string;
  priceChange?: {
    h1?: number;
    h24?: number;
  };
  volume?: {
    h24?: number;
  };
  liquidity?: {
    usd?: number;
  };
  boosts?: {
    active?: number;
  };
  trendingScore?: number;
  trendingScoreChange?: number;
}

export class DexScreenerService {
  private readonly baseUrl = 'https://api.dexscreener.com';
  private readonly logger = apiLogger.child({ service: 'DexScreenerService' });
  private boostCache = new Map<string, { data: TokenBoost | null; timestamp: Date }>();
  private profileCache = new Map<string, { data: TokenProfile | null; timestamp: Date }>();
  private readonly cacheTtl = 5 * 60 * 1000; // 5 minutes cache

  constructor() {
    this.logger.info('DEX Screener service initialized');
  }

  /**
   * Get latest token boosts
   */
  async getLatestBoosts(limit: number = 50): Promise<TokenBoost[]> {
    try {
      const response = await fetch(`${this.baseUrl}/token-boosts/latest/v1?limit=${limit}`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();
      return data || [];
    } catch (error) {
      this.logger.error({ error }, 'Failed to fetch latest boosts');
      return [];
    }
  }

  /**
   * Get top boosted tokens
   */
  async getTopBoosts(): Promise<TokenBoost[]> {
    try {
      const response = await fetch(`${this.baseUrl}/token-boosts/top/v1`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();
      return data || [];
    } catch (error) {
      this.logger.error({ error }, 'Failed to fetch top boosts');
      return [];
    }
  }

  /**
   * Check if a token is boosted on Solana
   */
  async isTokenBoosted(tokenAddress: string): Promise<{ isBoosted: boolean; boostAmount?: number; boostRank?: number }> {
    try {
      // Check cache first
      const cached = this.boostCache.get(tokenAddress);
      if (cached && Date.now() - cached.timestamp.getTime() < this.cacheTtl) {
        return {
          isBoosted: !!cached.data,
          boostAmount: cached.data?.amount,
          boostRank: undefined // Would need to calculate from top boosts
        };
      }

      // Fetch top boosts and check if token is in list
      const topBoosts = await this.getTopBoosts();
      const boostIndex = topBoosts.findIndex(b => 
        b.chainId === 'solana' && 
        b.tokenAddress.toLowerCase() === tokenAddress.toLowerCase()
      );

      const boost = boostIndex >= 0 ? topBoosts[boostIndex] : null;
      
      // Cache result
      this.boostCache.set(tokenAddress, {
        data: boost,
        timestamp: new Date()
      });

      return {
        isBoosted: !!boost,
        boostAmount: boost?.amount,
        boostRank: boostIndex >= 0 ? boostIndex + 1 : undefined
      };
    } catch (error) {
      this.logger.error({ error, tokenAddress }, 'Failed to check token boost status');
      return { isBoosted: false };
    }
  }

  /**
   * Get token profile (paid features)
   */
  async getTokenProfile(tokenAddress: string): Promise<TokenProfile | null> {
    try {
      // Check cache first
      const cached = this.profileCache.get(tokenAddress);
      if (cached && Date.now() - cached.timestamp.getTime() < this.cacheTtl) {
        return cached.data;
      }

      // Fetch latest profiles
      const response = await fetch(`${this.baseUrl}/token-profiles/latest/v1?limit=100`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const profiles: TokenProfile[] = await response.json() || [];
      const profile = profiles.find(p => 
        p.chainId === 'solana' && 
        p.tokenAddress.toLowerCase() === tokenAddress.toLowerCase()
      );

      // Cache result
      this.profileCache.set(tokenAddress, {
        data: profile || null,
        timestamp: new Date()
      });

      return profile || null;
    } catch (error) {
      this.logger.error({ error, tokenAddress }, 'Failed to fetch token profile');
      return null;
    }
  }

  /**
   * Get pair data for a token
   */
  async getTokenPairs(tokenAddress: string): Promise<DexPair[]> {
    try {
      const response = await fetch(`${this.baseUrl}/latest/dex/tokens/solana/${tokenAddress}`);
      if (!response.ok) {
        if (response.status === 404) {
          return [];
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      return data.pairs || [];
    } catch (error) {
      this.logger.error({ error, tokenAddress }, 'Failed to fetch token pairs');
      return [];
    }
  }

  /**
   * Get trending score for a token
   */
  async getTrendingInfo(tokenAddress: string): Promise<{ trendingScore?: number; trendingRank?: number; hasPaid: boolean; isBoosted: boolean }> {
    try {
      // Get pairs to check trending score
      const pairs = await this.getTokenPairs(tokenAddress);
      
      // Find best trending score across all pairs
      let bestTrendingScore = 0;
      let hasBoost = false;
      
      for (const pair of pairs) {
        if (pair.trendingScore && pair.trendingScore > bestTrendingScore) {
          bestTrendingScore = pair.trendingScore;
        }
        if (pair.boosts?.active) {
          hasBoost = true;
        }
      }

      // Check if token has paid profile
      const profile = await this.getTokenProfile(tokenAddress);
      const hasPaid = !!profile;

      // Check if token is boosted
      const boostInfo = await this.isTokenBoosted(tokenAddress);

      return {
        trendingScore: bestTrendingScore > 0 ? bestTrendingScore : undefined,
        trendingRank: undefined, // Would need to fetch trending list to calculate rank
        hasPaid,
        isBoosted: boostInfo.isBoosted || hasBoost
      };
    } catch (error) {
      this.logger.error({ error, tokenAddress }, 'Failed to get trending info');
      return { hasPaid: false, isBoosted: false };
    }
  }

  /**
   * Get trending tokens from DEX Screener
   * Note: DEX Screener doesn't have a dedicated trending endpoint, 
   * but we can use search with specific sorting
   */
  async getTrendingTokens(): Promise<Map<string, number>> {
    try {
      // Search for trending tokens on Solana
      const response = await fetch(`${this.baseUrl}/latest/dex/search?q=trending`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      const trendingMap = new Map<string, number>();
      
      if (data.pairs && Array.isArray(data.pairs)) {
        // Get unique tokens and assign ranks
        const seenTokens = new Set<string>();
        let rank = 1;
        
        for (const pair of data.pairs) {
          if (pair.chainId === 'solana' && pair.baseToken?.address) {
            const tokenAddress = pair.baseToken.address.toLowerCase();
            if (!seenTokens.has(tokenAddress)) {
              seenTokens.add(tokenAddress);
              trendingMap.set(tokenAddress, rank);
              rank++;
              
              // Limit to top 100 trending
              if (rank > 100) break;
            }
          }
        }
      }
      
      return trendingMap;
    } catch (error) {
      this.logger.error({ error }, 'Failed to fetch trending tokens');
      return new Map();
    }
  }

  /**
   * Get batch DEX info for multiple tokens
   */
  async getBatchDexInfo(tokenAddresses: string[]): Promise<Map<string, { hasPaid: boolean; isBoosted: boolean; trendingScore?: number; trendingRank?: number; boostRank?: number; boostAmount?: number }>> {
    const results = new Map();
    
    try {
      // Fetch all data in parallel
      const [topBoosts, latestProfiles, trendingTokens] = await Promise.all([
        this.getTopBoosts(),
        this.getLatestProfiles(),
        this.getTrendingTokens()
      ]);

      // Create lookup maps
      const boostMap = new Map(
        topBoosts
          .filter(b => b.chainId === 'solana')
          .map((b, index) => [b.tokenAddress.toLowerCase(), { boost: b, rank: index + 1 }])
      );
      
      const profileMap = new Map(
        latestProfiles
          .filter(p => p.chainId === 'solana')
          .map(p => [p.tokenAddress.toLowerCase(), p])
      );

      // Process each token individually to check for boosts in pairs
      for (const address of tokenAddresses) {
        const lowerAddress = address.toLowerCase();
        let isBoosted = false;
        let boostAmount = 0;
        
        // Check if in top boosts list
        const boostInfo = boostMap.get(lowerAddress);
        if (boostInfo) {
          isBoosted = true;
        }
        
        // Also check by searching for the token's pairs (more accurate for active boosts)
        try {
          const searchResponse = await fetch(`${this.baseUrl}/latest/dex/search?q=${address}`);
          if (searchResponse.ok) {
            const searchData = await searchResponse.json();
            if (searchData.pairs && Array.isArray(searchData.pairs)) {
              for (const pair of searchData.pairs) {
                if (pair.baseToken?.address?.toLowerCase() === lowerAddress && pair.boosts?.active) {
                  isBoosted = true;
                  boostAmount = Math.max(boostAmount, pair.boosts.active);
                }
              }
            }
          }
        } catch (searchError) {
          this.logger.debug({ error: searchError, address }, 'Failed to search for token pairs');
        }
        
        const hasPaid = profileMap.has(lowerAddress);
        const trendingRank = trendingTokens.get(lowerAddress);

        results.set(address, {
          hasPaid,
          isBoosted,
          boostAmount: boostAmount > 0 ? boostAmount : undefined,
          trendingScore: undefined,
          trendingRank: trendingRank,
          boostRank: boostInfo?.rank
        });
      }
    } catch (error) {
      this.logger.error({ error }, 'Failed to get batch DEX info');
      
      // Return empty results for all tokens on error
      for (const address of tokenAddresses) {
        results.set(address, { hasPaid: false, isBoosted: false });
      }
    }

    return results;
  }

  /**
   * Get latest profiles
   */
  private async getLatestProfiles(limit: number = 100): Promise<TokenProfile[]> {
    try {
      const response = await fetch(`${this.baseUrl}/token-profiles/latest/v1?limit=${limit}`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();
      return data || [];
    } catch (error) {
      this.logger.error({ error }, 'Failed to fetch latest profiles');
      return [];
    }
  }
}

// Export singleton instance
export const dexScreenerService = new DexScreenerService();