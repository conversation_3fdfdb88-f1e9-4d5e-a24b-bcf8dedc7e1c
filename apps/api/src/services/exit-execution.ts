import { PrismaClient, ExitTrigger, ExitStrategy, Position } from '@prisma/client';
import { EventEmitter } from 'events';
import {
  IExitExecutionService,
  ExitStrategyError,
  TriggerExecutionError
} from '../types/exit-strategy.js';

interface TradingService {
  createSwapTransaction(params: {
    tokenIn: string;
    tokenOut: string;
    amount: number;
    slippage: number;
    priorityFee?: number;
  }): Promise<{
    success: boolean;
    signature?: string;
    error?: string;
  }>;
}

interface QueueItem {
  id: string;
  type: 'EXIT_TRIGGER' | 'PARTIAL_SELL';
  priority: number;
  data: any;
  createdAt: Date;
  retryCount: number;
  maxRetries: number;
}

export class ExitExecutionService extends EventEmitter implements IExitExecutionService {
  private executionQueue: QueueItem[] = [];
  private isProcessing = false;
  private readonly MAX_CONCURRENT_EXECUTIONS = 3;
  private readonly DEFAULT_MAX_RETRIES = 3;
  private readonly RETRY_DELAY_MS = 5000;
  private readonly RATE_LIMIT_DELAY_MS = 1000; // 1 second between executions
  private readonly SOL_TOKEN_ADDRESS = 'So11111111111111111111111111111111111111112';

  constructor(
    private prisma: PrismaClient,
    private tradingService: TradingService
  ) {
    super();
    this.startQueueProcessor();
  }

  async executeTrigger(triggerId: string): Promise<{ success: boolean; transactionSignature?: string; error?: string }> {
    try {
      // Get trigger with strategy and position data
      const trigger = await this.prisma.exitTrigger.findUnique({
        where: { id: triggerId },
        include: {
          strategy: {
            include: {
              position: true
            }
          }
        }
      });

      if (!trigger) {
        throw new TriggerExecutionError(
          `Trigger ${triggerId} not found`,
          triggerId,
          0,
          this.DEFAULT_MAX_RETRIES
        );
      }

      if (trigger.status !== 'TRIGGERED') {
        throw new TriggerExecutionError(
          `Trigger ${triggerId} is not in triggered state`,
          triggerId,
          trigger.retryCount,
          this.DEFAULT_MAX_RETRIES
        );
      }

      // Add to execution queue
      const queueItem: QueueItem = {
        id: `trigger_${triggerId}`,
        type: 'EXIT_TRIGGER',
        priority: this.getTriggerPriority(trigger.type),
        data: { triggerId },
        createdAt: new Date(),
        retryCount: trigger.retryCount,
        maxRetries: this.DEFAULT_MAX_RETRIES
      };

      this.addToQueue(queueItem);

      return { success: true };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      if (error instanceof TriggerExecutionError) {
        await this.handleExecutionFailure(triggerId, errorMessage);
        throw error;
      }

      throw new TriggerExecutionError(
        `Failed to execute trigger: ${errorMessage}`,
        triggerId,
        0,
        this.DEFAULT_MAX_RETRIES
      );
    }
  }

  async executePartialSell(
    positionId: string,
    percentage: number,
    reason: string
  ): Promise<{ success: boolean; transactionSignature?: string; error?: string }> {
    try {
      // Validate percentage
      if (percentage <= 0 || percentage > 100) {
        throw new Error('Percentage must be between 0 and 100');
      }

      // Get position
      const position = await this.prisma.position.findUnique({
        where: { id: positionId }
      });

      if (!position) {
        throw new Error(`Position ${positionId} not found`);
      }

      if (position.status !== 'ACTIVE') {
        throw new Error(`Position ${positionId} is not active`);
      }

      // Add to execution queue
      const queueItem: QueueItem = {
        id: `partial_sell_${positionId}_${Date.now()}`,
        type: 'PARTIAL_SELL',
        priority: 2, // Medium priority
        data: { positionId, percentage, reason },
        createdAt: new Date(),
        retryCount: 0,
        maxRetries: this.DEFAULT_MAX_RETRIES
      };

      this.addToQueue(queueItem);

      return { success: true };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return { success: false, error: errorMessage };
    }
  }

  async retryFailedExecution(triggerId: string): Promise<{ success: boolean; transactionSignature?: string; error?: string }> {
    try {
      const trigger = await this.prisma.exitTrigger.findUnique({
        where: { id: triggerId }
      });

      if (!trigger) {
        throw new Error(`Trigger ${triggerId} not found`);
      }

      if (trigger.status !== 'FAILED') {
        throw new Error(`Trigger ${triggerId} is not in failed state`);
      }

      if (trigger.retryCount >= this.DEFAULT_MAX_RETRIES) {
        throw new Error(`Trigger ${triggerId} has exceeded maximum retry attempts`);
      }

      // Reset trigger to triggered state for retry
      await this.prisma.exitTrigger.update({
        where: { id: triggerId },
        data: {
          status: 'TRIGGERED',
          errorMessage: null
        }
      });

      // Execute again
      return await this.executeTrigger(triggerId);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return { success: false, error: errorMessage };
    }
  }

  private addToQueue(item: QueueItem): void {
    // Insert based on priority (higher priority first)
    let insertIndex = this.executionQueue.length;

    for (let i = 0; i < this.executionQueue.length; i++) {
      if (this.executionQueue[i].priority < item.priority) {
        insertIndex = i;
        break;
      }
    }

    this.executionQueue.splice(insertIndex, 0, item);

    console.log(`📋 Added ${item.type} to execution queue (position ${insertIndex + 1}/${this.executionQueue.length})`);
  }

  private startQueueProcessor(): void {
    setInterval(async () => {
      if (!this.isProcessing && this.executionQueue.length > 0) {
        await this.processQueue();
      }
    }, this.RATE_LIMIT_DELAY_MS);
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.executionQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      const concurrentPromises: Promise<void>[] = [];
      const itemsToProcess = this.executionQueue.splice(0, this.MAX_CONCURRENT_EXECUTIONS);

      for (const item of itemsToProcess) {
        concurrentPromises.push(this.processQueueItem(item));
      }

      await Promise.allSettled(concurrentPromises);

    } catch (error) {
      console.error('Error processing execution queue:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  private async processQueueItem(item: QueueItem): Promise<void> {
    try {
      console.log(`🚀 Processing ${item.type}: ${item.id}`);

      if (item.type === 'EXIT_TRIGGER') {
        await this.executeExitTrigger(item.data.triggerId);
      } else if (item.type === 'PARTIAL_SELL') {
        await this.executePartialSellOrder(item.data);
      }

    } catch (error) {
      console.error(`Failed to process queue item ${item.id}:`, error);

      // Handle retry logic
      if (item.retryCount < item.maxRetries) {
        const retryItem = {
          ...item,
          retryCount: item.retryCount + 1
        };

        // Add back to queue with delay
        setTimeout(() => {
          this.addToQueue(retryItem);
        }, this.RETRY_DELAY_MS * (item.retryCount + 1));

        console.log(`🔄 Retrying ${item.type} ${item.id} (attempt ${item.retryCount + 1}/${item.maxRetries})`);
      } else {
        console.error(`❌ Max retries exceeded for ${item.type} ${item.id}`);

        if (item.type === 'EXIT_TRIGGER') {
          await this.handleExecutionFailure(
            item.data.triggerId,
            error instanceof Error ? error.message : 'Unknown error'
          );
        }
      }
    }
  }

  private async executeExitTrigger(triggerId: string): Promise<void> {
    const trigger = await this.prisma.exitTrigger.findUnique({
      where: { id: triggerId },
      include: {
        strategy: {
          include: {
            position: true
          }
        }
      }
    });

    if (!trigger || !trigger.strategy) {
      throw new Error(`Trigger or strategy not found for ${triggerId}`);
    }

    const position = trigger.strategy.position;
    const tokenAmount = trigger.tokenAmount.toNumber();

    // Calculate actual amount to sell based on current position
    const currentQuantity = position.quantity.toNumber();
    const sellAmount = Math.min(tokenAmount, currentQuantity);

    if (sellAmount <= 0) {
      throw new Error(`No tokens available to sell for trigger ${triggerId}`);
    }

    // Execute the swap transaction
    const swapResult = await this.tradingService.createSwapTransaction({
      tokenIn: position.tokenAddress,
      tokenOut: this.SOL_TOKEN_ADDRESS,
      amount: sellAmount,
      slippage: 2.0, // 2% slippage for exit trades
      priorityFee: 0.001 // Higher priority fee for exit trades
    });

    if (!swapResult.success) {
      throw new Error(swapResult.error || 'Swap transaction failed');
    }

    // Update trigger status
    await this.prisma.exitTrigger.update({
      where: { id: triggerId },
      data: {
        status: 'EXECUTED',
        executedAt: new Date(),
        executedAmount: sellAmount,
        transactionSignature: swapResult.signature
      }
    });

    // Update position quantity
    await this.prisma.position.update({
      where: { id: position.id },
      data: {
        quantity: {
          decrement: sellAmount
        }
      }
    });

    // Log the execution
    await this.prisma.exitTriggerLog.create({
      data: {
        triggerId,
        action: 'EXECUTED',
        details: {
          type: trigger.type,
          executedAmount: sellAmount,
          executedPrice: trigger.triggeredPrice?.toNumber(),
          transactionSignature: swapResult.signature,
          timestamp: new Date()
        }
      }
    });

    // Emit execution event
    this.emit('triggerExecuted', {
      triggerId,
      positionId: position.id,
      type: trigger.type,
      executedAmount: sellAmount,
      transactionSignature: swapResult.signature,
      timestamp: new Date()
    });

    console.log(`✅ Successfully executed ${trigger.type} trigger for ${sellAmount} tokens`);
  }

  private async executePartialSellOrder(data: {
    positionId: string;
    percentage: number;
    reason: string;
  }): Promise<void> {
    const position = await this.prisma.position.findUnique({
      where: { id: data.positionId }
    });

    if (!position) {
      throw new Error(`Position ${data.positionId} not found`);
    }

    const sellAmount = position.quantity.toNumber() * (data.percentage / 100);

    if (sellAmount <= 0) {
      throw new Error(`No tokens available to sell for position ${data.positionId}`);
    }

    // Execute the swap
    const swapResult = await this.tradingService.createSwapTransaction({
      tokenIn: position.tokenAddress,
      tokenOut: this.SOL_TOKEN_ADDRESS,
      amount: sellAmount,
      slippage: 2.0,
      priorityFee: 0.001
    });

    if (!swapResult.success) {
      throw new Error(swapResult.error || 'Partial sell transaction failed');
    }

    // Update position
    await this.prisma.position.update({
      where: { id: data.positionId },
      data: {
        quantity: {
          decrement: sellAmount
        }
      }
    });

    // Create transaction record
    await this.prisma.transaction.create({
      data: {
        positionId: data.positionId,
        signature: swapResult.signature!,
        type: 'PARTIAL_SELL',
        tokenAddress: position.tokenAddress,
        amountToken: sellAmount,
        amountSol: 0, // Would need current price calculation
        price: 0, // Would need current price
        fees: 0, // Would need fee calculation
        slippage: 2.0,
        status: 'CONFIRMED'
      }
    });

    // Emit execution event
    this.emit('partialSellExecuted', {
      positionId: data.positionId,
      percentage: data.percentage,
      executedAmount: sellAmount,
      reason: data.reason,
      transactionSignature: swapResult.signature,
      timestamp: new Date()
    });

    console.log(`✅ Successfully executed partial sell: ${data.percentage}% (${sellAmount} tokens)`);
  }

  private async handleExecutionFailure(triggerId: string, errorMessage: string): Promise<void> {
    await this.prisma.exitTrigger.update({
      where: { id: triggerId },
      data: {
        status: 'FAILED',
        errorMessage,
        retryCount: {
          increment: 1
        }
      }
    });

    // Log the failure
    await this.prisma.exitTriggerLog.create({
      data: {
        triggerId,
        action: 'FAILED',
        details: {
          error: errorMessage,
          timestamp: new Date()
        }
      }
    });

    // Emit failure event
    this.emit('triggerExecutionFailed', {
      triggerId,
      error: errorMessage,
      timestamp: new Date()
    });
  }

  private getTriggerPriority(triggerType: string): number {
    // Higher numbers = higher priority
    switch (triggerType) {
      case 'STOP_LOSS':
        return 3; // Highest priority
      case 'TRAILING_STOP':
        return 2; // High priority
      case 'TAKE_PROFIT':
        return 1; // Normal priority
      default:
        return 0; // Lowest priority
    }
  }

  // Utility methods
  getQueueStats(): {
    queueLength: number;
    isProcessing: boolean;
    byType: { [key: string]: number };
  } {
    const byType: { [key: string]: number } = {};

    for (const item of this.executionQueue) {
      byType[item.type] = (byType[item.type] || 0) + 1;
    }

    return {
      queueLength: this.executionQueue.length,
      isProcessing: this.isProcessing,
      byType
    };
  }

  async getFailedTriggers(): Promise<ExitTrigger[]> {
    return await this.prisma.exitTrigger.findMany({
      where: {
        status: 'FAILED',
        retryCount: {
          lt: this.DEFAULT_MAX_RETRIES
        }
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });
  }

  // Clean up on service shutdown
  async shutdown(): Promise<void> {
    this.isProcessing = false;
    this.executionQueue = [];
    this.removeAllListeners();

    console.log('🚪 Exit Execution Service shut down');
  }
}
