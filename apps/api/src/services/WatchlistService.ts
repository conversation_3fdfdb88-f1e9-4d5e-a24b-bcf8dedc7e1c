import { PrismaClient, WatchlistItem, Prisma } from '@prisma/client';
import { PublicKey } from '@solana/web3.js';
import { WATCHLIST_ERRORS, WATCHLIST_LIMITS } from '../../../../packages/shared/src/types/watchlist';

export class WatchlistService {
  private performanceMetrics = {
    queryCount: 0,
    totalQueryTime: 0,
    averageQueryTime: 0,
    slowestQuery: 0,
    fastestQuery: Number.MAX_VALUE
  };

  constructor(private prisma: PrismaClient) {}

  async findAll(options?: {
    limit?: number;
    offset?: number;
    sortBy?: 'addedAt' | 'updatedAt' | 'tokenSymbol' | 'tokenName';
    sortOrder?: 'asc' | 'desc';
  }): Promise<WatchlistItem[]> {
    const {
      limit,
      offset,
      sortBy = 'addedAt',
      sortOrder = 'asc'
    } = options || {};

    const orderBy: Prisma.WatchlistItemOrderByWithRelationInput[] = [
      { isPinned: 'desc' } // Always prioritize pinned items
    ];

    // Add secondary sorting
    switch (sortBy) {
      case 'addedAt':
        orderBy.push({ addedAt: sortOrder });
        break;
      case 'updatedAt':
        orderBy.push({ updatedAt: sortOrder });
        break;
      case 'tokenSymbol':
        orderBy.push({ tokenSymbol: sortOrder });
        break;
      case 'tokenName':
        orderBy.push({ tokenName: sortOrder });
        break;
    }

    return this.executeWithMetrics(() => 
      this.prisma.watchlistItem.findMany({
        where: { isActive: true },
        orderBy,
        take: limit,
        skip: offset
      })
    );
  }

  async findById(id: string): Promise<WatchlistItem | null> {
    return this.prisma.watchlistItem.findFirst({
      where: {
        id,
        isActive: true
      }
    });
  }

  async create(data: {
    tokenAddress: string;
    tokenSymbol: string;
    tokenName: string;
    customName?: string;
    notes?: string;
  }): Promise<WatchlistItem> {
    // Validate token address format using @solana/web3.js
    try {
      new PublicKey(data.tokenAddress);
    } catch (error) {
      throw new Error(WATCHLIST_ERRORS.INVALID_TOKEN_ADDRESS);
    }

    try {
      return await this.prisma.watchlistItem.create({
        data: {
          tokenAddress: data.tokenAddress,
          tokenSymbol: data.tokenSymbol,
          tokenName: data.tokenName,
          customName: data.customName,
          notes: data.notes,
          isPinned: false,
          isActive: true
        }
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new Error(WATCHLIST_ERRORS.DUPLICATE_TOKEN);
        }
      }
      throw error;
    }
  }

  async update(
    id: string,
    data: {
      customName?: string;
      notes?: string;
      isPinned?: boolean;
    }
  ): Promise<WatchlistItem> {
    const item = await this.findById(id);
    if (!item) {
      throw new Error(WATCHLIST_ERRORS.ITEM_NOT_FOUND);
    }

    return this.prisma.watchlistItem.update({
      where: { id },
      data: {
        customName: data.customName,
        notes: data.notes,
        isPinned: data.isPinned
      }
    });
  }

  async softDelete(id: string): Promise<WatchlistItem> {
    const item = await this.findById(id);
    if (!item) {
      throw new Error(WATCHLIST_ERRORS.ITEM_NOT_FOUND);
    }

    return this.prisma.watchlistItem.update({
      where: { id },
      data: { isActive: false }
    });
  }

  async findByTokenAddress(tokenAddress: string): Promise<WatchlistItem | null> {
    return this.prisma.watchlistItem.findFirst({
      where: {
        tokenAddress
        // Check both active and inactive items
      }
    });
  }
  
  async reactivate(id: string, data: CreateWatchlistItemDto): Promise<WatchlistItem> {
    // Reactivate a soft-deleted item with updated data
    return this.prisma.watchlistItem.update({
      where: { id },
      data: {
        isActive: true,
        tokenSymbol: data.tokenSymbol,
        tokenName: data.tokenName,
        customName: data.customName,
        notes: data.notes,
        isPinned: data.isPinned || false,
        updatedAt: new Date()
      }
    });
  }

  async togglePin(id: string): Promise<WatchlistItem> {
    const item = await this.findById(id);
    if (!item) {
      throw new Error(WATCHLIST_ERRORS.ITEM_NOT_FOUND);
    }

    return this.prisma.watchlistItem.update({
      where: { id },
      data: { isPinned: !item.isPinned }
    });
  }

  async countActive(): Promise<number> {
    return this.prisma.watchlistItem.count({
      where: { isActive: true }
    });
  }

  async findPinned(): Promise<WatchlistItem[]> {
    return this.prisma.watchlistItem.findMany({
      where: {
        isActive: true,
        isPinned: true
      },
      orderBy: { addedAt: 'desc' }
    });
  }

  async createBulk(tokens: Array<{
    tokenAddress: string;
    tokenSymbol: string;
    tokenName: string;
    customName?: string;
    notes?: string;
  }>): Promise<{
    successful: WatchlistItem[];
    failed: Array<{
      tokenAddress: string;
      error: string;
    }>;
    summary: {
      total: number;
      successful: number;
      failed: number;
      skippedDuplicates: number;
    };
  }> {
    const successful: WatchlistItem[] = [];
    const failed: Array<{ tokenAddress: string; error: string }> = [];
    let skippedDuplicates = 0;

    // Check current count before bulk operation
    const currentCount = await this.countActive();

    // Use transaction for atomic operations
    await this.prisma.$transaction(async (tx) => {
      for (const token of tokens) {
        try {
          // Validate token address format
          new PublicKey(token.tokenAddress);

          // Check if we would exceed maximum items
          if (currentCount + successful.length >= WATCHLIST_LIMITS.MAX_ITEMS) {
            failed.push({
              tokenAddress: token.tokenAddress,
              error: WATCHLIST_ERRORS.MAX_ITEMS_REACHED
            });
            continue;
          }

          // Check for existing token
          const existing = await tx.watchlistItem.findFirst({
            where: {
              tokenAddress: token.tokenAddress,
              isActive: true
            }
          });

          if (existing) {
            skippedDuplicates++;
            continue;
          }

          // Create the item
          const item = await tx.watchlistItem.create({
            data: {
              tokenAddress: token.tokenAddress,
              tokenSymbol: token.tokenSymbol,
              tokenName: token.tokenName,
              customName: token.customName,
              notes: token.notes,
              isPinned: false,
              isActive: true
            }
          });

          successful.push(item);
        } catch (error) {
          let errorMessage = 'Unknown error';
          
          if (error instanceof Error) {
            if (error.message === WATCHLIST_ERRORS.INVALID_TOKEN_ADDRESS) {
              errorMessage = WATCHLIST_ERRORS.INVALID_TOKEN_ADDRESS;
            } else if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
              skippedDuplicates++;
              continue;
            } else {
              errorMessage = error.message;
            }
          }

          failed.push({
            tokenAddress: token.tokenAddress,
            error: errorMessage
          });
        }
      }
    });

    return {
      successful,
      failed,
      summary: {
        total: tokens.length,
        successful: successful.length,
        failed: failed.length,
        skippedDuplicates
      }
    };
  }

  /**
   * Track query performance
   */
  private trackQueryPerformance(queryTime: number): void {
    this.performanceMetrics.queryCount++;
    this.performanceMetrics.totalQueryTime += queryTime;
    this.performanceMetrics.averageQueryTime = 
      this.performanceMetrics.totalQueryTime / this.performanceMetrics.queryCount;
    
    if (queryTime > this.performanceMetrics.slowestQuery) {
      this.performanceMetrics.slowestQuery = queryTime;
    }
    
    if (queryTime < this.performanceMetrics.fastestQuery) {
      this.performanceMetrics.fastestQuery = queryTime;
    }
  }

  /**
   * Execute a query with performance tracking
   */
  private async executeWithMetrics<T>(queryFn: () => Promise<T>): Promise<T> {
    const startTime = Date.now();
    try {
      const result = await queryFn();
      const queryTime = Date.now() - startTime;
      this.trackQueryPerformance(queryTime);
      return result;
    } catch (error) {
      const queryTime = Date.now() - startTime;
      this.trackQueryPerformance(queryTime);
      throw error;
    }
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): {
    queryCount: number;
    averageQueryTime: number;
    slowestQuery: number;
    fastestQuery: number;
    totalQueryTime: number;
  } {
    return {
      ...this.performanceMetrics,
      fastestQuery: this.performanceMetrics.fastestQuery === Number.MAX_VALUE 
        ? 0 
        : this.performanceMetrics.fastestQuery
    };
  }

  // Helper method to validate Solana address
  static isValidSolanaAddress(address: string): boolean {
    try {
      new PublicKey(address);
      return true;
    } catch {
      return false;
    }
  }
}