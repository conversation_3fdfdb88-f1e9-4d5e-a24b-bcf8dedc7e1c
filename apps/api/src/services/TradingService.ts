import { VersionedTransaction, Transaction } from '@solana/web3.js';
import { apiLogger } from '../lib/logger';
import { walletConfig } from '../lib/config';
import { getWallet } from '../lib/wallet';
import { JupiterService, JupiterQuoteParams, QuoteWithMetadata } from './JupiterService';
import { HeliusService, TransactionResult } from './HeliusService';
import { MockPriceService, TokenPrice, PriceAlert } from './MockPriceService';
import Decimal from 'decimal.js';

export interface TradeRequest {
  inputMint: string;
  outputMint: string;
  amountSol: number; // Amount of input token (not necessarily SOL, despite the name)
  slippageBps?: number;
  maxPriceImpact?: number; // Maximum acceptable price impact percentage
  simulate?: boolean; // Whether to simulate before executing
}

export interface TradeResult {
  success: boolean;
  signature?: string;
  error?: string;
  quote?: QuoteWithMetadata;
  transaction?: {
    signature: string;
    confirmationStatus?: 'processed' | 'confirmed' | 'finalized';
    slot?: number;
  };
  priceMonitoring?: {
    entryPrice: string;
    monitoringStarted: boolean;
  };
}

export interface PositionInfo {
  id: string;
  tokenAddress: string;
  entryPrice: string;
  quantity: string;
  currentPrice: string;
  pnlPercent: string;
  pnlAmount: string;
  createdAt: Date;
  lastUpdated: Date;
}

export interface TradingStats {
  totalTrades: number;
  successfulTrades: number;
  failedTrades: number;
  totalVolumeSol: string;
  averageTradeSize: string;
  successRate: string;
}

export class TradingService {
  private jupiterService: JupiterService;
  private heliusService: HeliusService;
  private priceService: MockPriceService;
  private positions: Map<string, PositionInfo> = new Map();
  private activeAlerts: Map<string, PriceAlert[]> = new Map();
  private priceMonitoringInterval?: NodeJS.Timeout;

  constructor() {
    this.jupiterService = new JupiterService();
    this.heliusService = new HeliusService();
    this.priceService = new MockPriceService();

    apiLogger.info('Trading service initialized with complete 5-step pipeline');
  }

  /**
   * Execute complete buy workflow: Jupiter quote → build → sign → send → monitor
   * This is the main 5-step trading pipeline
   */
  async executeBuy(request: TradeRequest): Promise<TradeResult> {
    const startTime = Date.now();
    
    try {
      apiLogger.info({
        inputMint: request.inputMint,
        outputMint: request.outputMint,
        amountSol: request.amountSol,
        slippageBps: request.slippageBps,
      }, '🚀 Starting complete buy workflow (5-step pipeline)');

      // Validate transaction amount against safety limits
      const validation = this.validateTradeRequest(request);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error,
        };
      }

      // STEP 1: Get Jupiter quote
      const quote = await this.getQuote(request);
      
      // Check price impact
      if (request.maxPriceImpact) {
        const priceImpact = new Decimal(quote.priceImpactPct);
        if (priceImpact.gt(request.maxPriceImpact)) {
          return {
            success: false,
            error: `Price impact ${quote.priceImpactPct}% exceeds maximum ${request.maxPriceImpact}%`,
            quote,
          };
        }
      }

      // STEP 2: Build swap transaction
      const wallet = getWallet();
      if (!wallet.isReady()) {
        return {
          success: false,
          error: 'Wallet not ready',
          quote,
        };
      }

      const swapResponse = await this.jupiterService.buildSwapTransaction({
        quoteResponse: quote,
        userPublicKey: wallet.getAddress(),
        wrapAndUnwrapSol: true,
        useSharedAccounts: true,
        asLegacyTransaction: true, // Use legacy for now
      });

      // Deserialize transaction
      const transactionBuffer = Buffer.from(swapResponse.swapTransaction, 'base64');
      const transaction = VersionedTransaction.deserialize(transactionBuffer);

      // Simulate transaction if requested
      if (request.simulate) {
        const simulation = await this.heliusService.simulateTransaction(transaction);
        if (!simulation.success) {
          return {
            success: false,
            error: `Transaction simulation failed: ${simulation.error}`,
            quote,
          };
        }
        apiLogger.info({ simulation }, 'Transaction simulation successful');
      }

      // STEP 3: Sign transaction with wallet
      const signingResult = await wallet.signTransaction(transaction);
      if (!signingResult.success) {
        return {
          success: false,
          error: `Transaction signing failed: ${signingResult.error}`,
          quote,
        };
      }

      // STEP 4: Send signed transaction
      const sendResult = await this.heliusService.sendAndConfirmTransaction(
        signingResult.transaction as VersionedTransaction,
        'confirmed',
        { skipPreflight: false }
      );

      if (!sendResult.success) {
        return {
          success: false,
          error: sendResult.error,
          quote,
        };
      }

      // STEP 5: Start price monitoring
      const entryPrice = await this.startPositionMonitoring(request.outputMint, quote);

      const duration = Date.now() - startTime;
      
      apiLogger.info({
        signature: sendResult.signature,
        confirmationStatus: sendResult.confirmationStatus,
        duration,
        entryPrice,
      }, '✅ Complete buy workflow successful (5-step pipeline)');

      return {
        success: true,
        signature: sendResult.signature,
        quote,
        transaction: {
          signature: sendResult.signature,
          confirmationStatus: sendResult.confirmationStatus,
          slot: sendResult.slot || undefined,
        },
        priceMonitoring: {
          entryPrice,
          monitoringStarted: true,
        },
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const duration = Date.now() - startTime;
      
      apiLogger.error({
        error: errorMessage,
        request,
        duration,
      }, '❌ Buy workflow failed');

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Get token decimals for amount conversion
   */
  private getTokenDecimals(tokenMint: string): number {
    // Known token decimals
    const knownTokenDecimals: Record<string, number> = {
      'So11111111111111111111111111111111111111112': 9,  // SOL
      'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 6,  // USDC
      'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 6,  // USDT
      'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263': 5,  // BONK
      'JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN': 6,  // JUP
      'EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm': 6,  // WIF
      'FYXnJhgLmhcELqsKyAfdKZExFfA7gaskQa5TuEZE9RZT': 6,  // TEST
      'rndrizKT3MK1iimdxRdWabcF7Zg7AR5T4nud4EkHBof': 9,  // RND
    };

    return knownTokenDecimals[tokenMint] || 6; // Default to 6 decimals for unknown tokens
  }

  /**
   * Convert token amount to proper decimal units
   */
  private convertTokenAmount(amount: number, tokenMint: string): string {
    const decimals = this.getTokenDecimals(tokenMint);
    const multiplier = Math.pow(10, decimals);
    const convertedAmount = Math.floor(amount * multiplier);
    
    apiLogger.debug({
      tokenMint,
      inputAmount: amount,
      decimals,
      convertedAmount,
    }, 'Token amount conversion');

    return convertedAmount.toString();
  }

  /**
   * Get quote from Jupiter (Step 1 of pipeline)
   */
  async getQuote(request: TradeRequest): Promise<QuoteWithMetadata> {
    try {
      // Convert input amount to proper token units based on token decimals
      const convertedAmount = this.convertTokenAmount(request.amountSol, request.inputMint);

      const quoteParams: JupiterQuoteParams = {
        inputMint: request.inputMint,
        outputMint: request.outputMint,
        amount: convertedAmount,
        slippageBps: request.slippageBps || 100,
        onlyDirectRoutes: false,
        asLegacyTransaction: true,
      };

      const quote = await this.jupiterService.getQuote(quoteParams);
      
      apiLogger.info({
        inputMint: request.inputMint,
        outputMint: request.outputMint,
        inputAmount: request.amountSol,
        convertedAmount,
        outputAmount: quote.outAmount,
        priceImpact: quote.priceImpactPct,
        routes: quote.routes.length,
      }, 'Jupiter quote obtained');

      return quote;
    } catch (error) {
      apiLogger.error({ error, request }, 'Failed to get Jupiter quote');
      throw error;
    }
  }

  /**
   * Start price monitoring for a position (Step 4 of pipeline)
   */
  async startPositionMonitoring(tokenAddress: string, quote: QuoteWithMetadata): Promise<string> {
    try {
      // Get current price to establish entry point
      const currentPrice = await this.priceService.getTokenPrice(tokenAddress);
      
      // Create position record
      const positionId = `pos_${Date.now()}_${tokenAddress.slice(0, 8)}`;
      const position: PositionInfo = {
        id: positionId,
        tokenAddress,
        entryPrice: currentPrice.price,
        quantity: quote.outAmount,
        currentPrice: currentPrice.price,
        pnlPercent: '0',
        pnlAmount: '0',
        createdAt: new Date(),
        lastUpdated: new Date(),
      };

      this.positions.set(positionId, position);

      apiLogger.info({
        positionId,
        tokenAddress,
        entryPrice: currentPrice.price,
        quantity: quote.outAmount,
      }, 'Position monitoring started');

      return currentPrice.price;
    } catch (error) {
      apiLogger.error({ error, tokenAddress }, 'Failed to start position monitoring');
      throw error;
    }
  }

  /**
   * Update positions with current prices and calculate P&L
   */
  async updatePositions(): Promise<void> {
    if (this.positions.size === 0) return;

    try {
      const tokenAddresses = Array.from(this.positions.keys()).map(id => 
        this.positions.get(id)!.tokenAddress
      );

      const currentPrices = await this.priceService.getTokenPrices(tokenAddresses);
      const now = new Date();

      for (const [positionId, position] of this.positions) {
        const currentPrice = currentPrices.find(p => p.contractAddress === position.tokenAddress);
        if (!currentPrice) continue;

        const entryPrice = new Decimal(position.entryPrice);
        const current = new Decimal(currentPrice.price);
        const quantity = new Decimal(position.quantity);

        // Calculate P&L
        const pnlPercent = current.minus(entryPrice).div(entryPrice).mul(100);
        const pnlAmount = current.minus(entryPrice).mul(quantity);

        // Update position
        const updatedPosition: PositionInfo = {
          ...position,
          currentPrice: currentPrice.price,
          pnlPercent: pnlPercent.toString(),
          pnlAmount: pnlAmount.toString(),
          lastUpdated: now,
        };

        this.positions.set(positionId, updatedPosition);
      }

      apiLogger.debug({
        positionsUpdated: this.positions.size,
      }, 'Positions updated with current prices');

    } catch (error) {
      apiLogger.error({ error }, 'Failed to update positions');
    }
  }

  /**
   * Set up automated price monitoring
   */
  startAutomatedPriceMonitoring(intervalMs: number = 5000): void {
    if (this.priceMonitoringInterval) {
      clearInterval(this.priceMonitoringInterval);
    }

    this.priceMonitoringInterval = setInterval(async () => {
      await this.updatePositions();
      await this.checkPriceAlerts();
    }, intervalMs);

    apiLogger.info({ intervalMs }, 'Automated price monitoring started');
  }

  /**
   * Stop automated price monitoring
   */
  stopAutomatedPriceMonitoring(): void {
    if (this.priceMonitoringInterval) {
      clearInterval(this.priceMonitoringInterval);
      this.priceMonitoringInterval = undefined;
    }
    apiLogger.info('Automated price monitoring stopped');
  }

  /**
   * Check and trigger price alerts
   */
  async checkPriceAlerts(): Promise<PriceAlert[]> {
    const triggeredAlerts: PriceAlert[] = [];

    try {
      for (const [tokenAddress, alerts] of this.activeAlerts) {
        const currentPrice = await this.priceService.getTokenPrice(tokenAddress);
        const triggered = this.priceService.checkPriceAlerts(alerts, [currentPrice]);
        triggeredAlerts.push(...triggered);
      }

      if (triggeredAlerts.length > 0) {
        apiLogger.info({
          triggeredCount: triggeredAlerts.length,
        }, 'Price alerts triggered');
      }
    } catch (error) {
      apiLogger.error({ error }, 'Failed to check price alerts');
    }

    return triggeredAlerts;
  }

  /**
   * Get all current positions
   */
  getPositions(): PositionInfo[] {
    return Array.from(this.positions.values());
  }

  /**
   * Get position by ID
   */
  getPosition(positionId: string): PositionInfo | null {
    return this.positions.get(positionId) || null;
  }

  /**
   * Get trading statistics
   */
  getTradingStats(): TradingStats {
    // This would be populated from a database in a real implementation
    return {
      totalTrades: 0,
      successfulTrades: 0,
      failedTrades: 0,
      totalVolumeSol: '0',
      averageTradeSize: '0',
      successRate: '0',
    };
  }

  /**
   * Validate trade request against safety limits
   */
  private validateTradeRequest(request: TradeRequest): { isValid: boolean; error?: string } {
    // Check transaction amount limits with token-aware validation
    if (request.amountSol <= 0) {
      return { isValid: false, error: 'Trade amount must be positive' };
    }

    // Apply different limits based on token type
    if (request.inputMint === 'So11111111111111111111111111111111111111112') {
      // SOL limits
      if (request.amountSol > walletConfig.maxTransactionAmount) {
        return {
          isValid: false,
          error: `SOL trade amount ${request.amountSol} exceeds limit of ${walletConfig.maxTransactionAmount} SOL`,
        };
      }
    } else {
      // For other tokens, use much higher limit
      if (request.amountSol > 1000000) {
        return {
          isValid: false,
          error: `Token trade amount ${request.amountSol} exceeds limit of 1,000,000 tokens`,
        };
      }
    }

    // Validate token addresses
    const inputValidation = this.jupiterService.validateTokenAddress(request.inputMint);
    if (!inputValidation.isValid) {
      return { isValid: false, error: `Invalid input token: ${inputValidation.error}` };
    }

    const outputValidation = this.jupiterService.validateTokenAddress(request.outputMint);
    if (!outputValidation.isValid) {
      return { isValid: false, error: `Invalid output token: ${outputValidation.error}` };
    }

    // Validate slippage
    if (request.slippageBps && (request.slippageBps < 1 || request.slippageBps > 5000)) {
      return { isValid: false, error: 'Slippage must be between 1 and 5000 basis points' };
    }

    return { isValid: true };
  }
}