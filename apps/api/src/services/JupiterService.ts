import { PublicKey, VersionedTransaction } from '@solana/web3.js';
import { apiLogger } from '../lib/logger';
import { apiConfig } from '../lib/config';
import { priorityFeeService } from './PriorityFeeService';
import Decimal from 'decimal.js';

export interface JupiterQuoteParams {
  inputMint: string;
  outputMint: string;
  amount: string;
  slippageBps?: number;
  onlyDirectRoutes?: boolean;
  asLegacyTransaction?: boolean;
  // New V1 API parameters
  dynamicSlippage?: boolean; // Let Jupiter automatically adjust slippage
  maxAccounts?: number; // Limit number of accounts in transaction
  swapMode?: 'ExactIn' | 'ExactOut'; // Default: ExactIn
  platformFeeBps?: number; // Platform fee in basis points
  excludeDexes?: string[]; // DEXes to exclude from routing
}

export interface JupiterQuoteResponse {
  inputMint: string;
  inAmount: string;
  outputMint: string;
  outAmount: string;
  otherAmountThreshold: string;
  swapMode: string;
  slippageBps: number;
  priceImpactPct: string;
  routePlan: Array<{
    swapInfo: {
      ammKey: string;
      label: string;
      inputMint: string;
      outputMint: string;
      inAmount: string;
      outAmount: string;
      feeAmount: string;
      feeMint: string;
    };
    percent: number;
  }>;
  contextSlot: number;
  timeTaken: number;
}

export interface MEVProtectionParams {
  level: 'basic' | 'standard' | 'maximum';
  priorityFeeLamports: number;
  computeUnitPriceMicroLamports: number;
  jitoTipLamports: number;
  maxComputeUnits?: number;
  skipPreflight?: boolean;
}

export interface JupiterSwapRequest {
  quoteResponse: JupiterQuoteResponse;
  userPublicKey: string;
  wrapAndUnwrapSol?: boolean;
  useSharedAccounts?: boolean;
  trackingAccount?: string;
  computeUnitPriceMicroLamports?: string;
  prioritizationFeeLamports?: string; // Direct priority fee specification (V1 API)
  asLegacyTransaction?: boolean;
  useTokenLedger?: boolean;
  // Enhanced MEV protection parameters
  mevProtection?: MEVProtectionParams;
  dynamicComputeUnitLimit?: boolean;
  skipUserAccountsRpcCalls?: boolean;
  // New V1 API parameters
  dynamicSlippage?: boolean; // Enable dynamic slippage adjustment
  skipPreflight?: boolean; // Skip preflight for faster execution
}

export interface JupiterSwapResponse {
  swapTransaction: string;
  lastValidBlockHeight: number;
  prioritizationFeeLamports: number;
  // Enhanced response with MEV protection info
  computeUnitLimit?: number;
  mevProtectionApplied?: MEVProtectionParams;
  transactionSize?: number;
  estimatedFee?: number;
}

export interface SwapTransactionBuildOptions {
  userPublicKey: string;
  quote: JupiterQuoteResponse;
  mevProtectionLevel: 'basic' | 'standard' | 'maximum';
  speedPreference: 'economy' | 'standard' | 'fast' | 'turbo';
  maxSlippageBps?: number;
  jitoBundle?: boolean;
  computeUnitBuffer?: number; // Additional CU buffer percentage
}

export interface TransactionSimulationResult {
  success: boolean;
  error?: string;
  computeUnitsConsumed?: number;
  logs?: string[];
  accounts?: any[];
  unitsConsumed?: number;
}

export interface TokenInfo {
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  logoURI?: string;
  tags?: string[];
}

export interface QuoteWithMetadata extends JupiterQuoteResponse {
  inputToken?: TokenInfo;
  outputToken?: TokenInfo;
  pricePerToken: string;
  estimatedGas: string;
  routes: Array<{
    dexLabel: string;
    percentage: number;
  }>;
}

export class JupiterService {
  private baseUrl: string;
  private apiKey?: string;
  private isV1Api: boolean;
  private useLiteApi: boolean;

  constructor() {
    this.baseUrl = apiConfig.jupiter.baseUrl;
    this.apiKey = apiConfig.jupiter.apiKey;
    this.isV1Api = apiConfig.jupiter.isV1Api;
    this.useLiteApi = apiConfig.jupiter.useLiteApi;
    
    apiLogger.info({ 
      baseUrl: this.baseUrl,
      hasApiKey: !!this.apiKey,
      isV1Api: this.isV1Api,
      useLiteApi: this.useLiteApi
    }, 'Jupiter service initialized');
  }

  /**
   * Get a quote for a token swap
   */
  async getQuote(params: JupiterQuoteParams): Promise<QuoteWithMetadata> {
    try {
      // Validate input parameters
      this.validateQuoteParams(params);

      const queryParams = new URLSearchParams({
        inputMint: params.inputMint,
        outputMint: params.outputMint,
        amount: params.amount,
        slippageBps: (params.slippageBps || 100).toString(),
        onlyDirectRoutes: (params.onlyDirectRoutes || false).toString(),
        asLegacyTransaction: (params.asLegacyTransaction || false).toString(),
      });

      // Add V1 API specific parameters
      if (this.isV1Api) {
        // Enable dynamic slippage by default for V1 (better pricing)
        if (params.dynamicSlippage !== undefined) {
          queryParams.set('dynamicSlippage', params.dynamicSlippage.toString());
        } else {
          queryParams.set('dynamicSlippage', 'true'); // Default to true for V1
        }
        
        if (params.maxAccounts !== undefined) {
          queryParams.set('maxAccounts', params.maxAccounts.toString());
        }
        if (params.swapMode) {
          queryParams.set('swapMode', params.swapMode);
        }
        if (params.platformFeeBps !== undefined) {
          queryParams.set('platformFeeBps', params.platformFeeBps.toString());
        }
        if (params.excludeDexes && params.excludeDexes.length > 0) {
          queryParams.set('excludeDexes', params.excludeDexes.join(','));
        }
      }

      // Setup headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add API key if available and using Pro API
      if (this.apiKey && !this.useLiteApi) {
        headers['Authorization'] = `Bearer ${this.apiKey}`;
      }

      const response = await fetch(`${this.baseUrl}/quote?${queryParams.toString()}`, {
        headers,
      });
      
      if (!response.ok) {
        let errorMessage = `Jupiter API error: ${response.status} ${response.statusText}`;
        try {
          const errorBody = await response.json();
          if (errorBody.error) {
            errorMessage = errorBody.error;
          }
        } catch {
          // If can't parse error body, use default message
        }
        throw new Error(errorMessage);
      }

      const quote: JupiterQuoteResponse = await response.json();

      // Calculate additional metadata
      const quoteWithMetadata = await this.enrichQuote(quote, params);

      apiLogger.info({
        inputMint: params.inputMint,
        outputMint: params.outputMint,
        amount: params.amount,
        outAmount: quote.outAmount,
        priceImpact: quote.priceImpactPct,
        routes: quote.routePlan.length,
      }, 'Jupiter quote retrieved');

      return quoteWithMetadata;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      apiLogger.error({
        error: errorMessage,
        params,
      }, 'Failed to get Jupiter quote');
      throw new Error(`Failed to get quote: ${errorMessage}`);
    }
  }

  /**
   * Build a swap transaction from a quote
   */
  async buildSwapTransaction(request: JupiterSwapRequest): Promise<JupiterSwapResponse> {
    try {
      // Setup headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add API key if available and using Pro API
      if (this.apiKey && !this.useLiteApi) {
        headers['Authorization'] = `Bearer ${this.apiKey}`;
      }

      // Build request body with V1 enhancements
      const requestBody = { ...request };

      // Add V1 specific features if using V1 API
      if (this.isV1Api) {
        // Enable dynamic compute unit limit for better fee estimation
        if (request.dynamicComputeUnitLimit !== false) {
          requestBody.dynamicComputeUnitLimit = true;
        }

        // Enable dynamic slippage if requested
        if (request.dynamicSlippage) {
          requestBody.dynamicSlippage = true;
        }

        // Set priority fee directly if specified
        if (request.prioritizationFeeLamports) {
          requestBody.prioritizationFeeLamports = request.prioritizationFeeLamports;
        }

        // Skip preflight for turbo mode
        if (request.skipPreflight) {
          requestBody.skipPreflight = true;
        }
      }

      const response = await fetch(`${this.baseUrl}/swap`, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`Jupiter swap API error: ${response.status} ${response.statusText}`);
      }

      const swapResponse: JupiterSwapResponse = await response.json();

      apiLogger.info({
        userPublicKey: request.userPublicKey,
        lastValidBlockHeight: swapResponse.lastValidBlockHeight,
        prioritizationFee: swapResponse.prioritizationFeeLamports,
        mevProtection: request.mevProtection?.level,
      }, 'Jupiter swap transaction built');

      return swapResponse;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      apiLogger.error({
        error: errorMessage,
        userPublicKey: request.userPublicKey,
      }, 'Failed to build swap transaction');
      throw new Error(`Failed to build swap transaction: ${errorMessage}`);
    }
  }

  /**
   * Build a MEV-protected swap transaction with optimal parameters
   */
  async buildMEVProtectedSwap(options: SwapTransactionBuildOptions): Promise<JupiterSwapResponse> {
    try {
      // Calculate MEV protection parameters
      const mevProtection = await priorityFeeService.calculateMEVProtectedFee(
        options.mevProtectionLevel,
        options.speedPreference
      );

      // Estimate compute units for the transaction
      const computeUnitEstimate = await this.estimateComputeUnits(options.quote);
      const computeUnitLimit = Math.round(
        computeUnitEstimate * (1 + (options.computeUnitBuffer || 0.2)) // 20% buffer by default
      );

      // Build the swap request with MEV protection
      const swapRequest: JupiterSwapRequest = {
        quoteResponse: options.quote,
        userPublicKey: options.userPublicKey,
        prioritizationFeeLamports: mevProtection.priorityFeeLamports.toString(),
        computeUnitPriceMicroLamports: mevProtection.computeUnitPriceMicroLamports.toString(),
        wrapAndUnwrapSol: true,
        useSharedAccounts: true,
        dynamicComputeUnitLimit: true,
        skipUserAccountsRpcCalls: false,
        mevProtection: {
          level: options.mevProtectionLevel,
          priorityFeeLamports: mevProtection.priorityFeeLamports,
          computeUnitPriceMicroLamports: mevProtection.computeUnitPriceMicroLamports,
          jitoTipLamports: mevProtection.jitoTipLamports,
          maxComputeUnits: computeUnitLimit,
          skipPreflight: options.speedPreference === 'turbo',
        },
        // V1 API enhancements
        ...(this.isV1Api && {
          dynamicSlippage: options.mevProtectionLevel !== 'basic', // Enable for standard+ protection
          skipPreflight: options.speedPreference === 'turbo',
        }),
      };

      // Build the transaction
      const response = await this.buildSwapTransaction(swapRequest);

      // Enhance response with MEV protection info
      const enhancedResponse: JupiterSwapResponse = {
        ...response,
        computeUnitLimit,
        mevProtectionApplied: swapRequest.mevProtection,
        transactionSize: this.estimateTransactionSize(options.quote),
        estimatedFee: mevProtection.totalEstimatedCost,
      };

      apiLogger.info({
        userPublicKey: options.userPublicKey,
        mevProtectionLevel: options.mevProtectionLevel,
        speedPreference: options.speedPreference,
        priorityFee: mevProtection.priorityFeeLamports,
        jitoTip: mevProtection.jitoTipLamports,
        totalCost: mevProtection.totalEstimatedCost,
        computeUnitLimit,
      }, 'MEV-protected swap transaction built');

      return enhancedResponse;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      apiLogger.error({
        error: errorMessage,
        userPublicKey: options.userPublicKey,
        mevProtectionLevel: options.mevProtectionLevel,
      }, 'Failed to build MEV-protected swap transaction');
      throw new Error(`Failed to build MEV-protected swap: ${errorMessage}`);
    }
  }

  /**
   * Simulate a transaction to estimate compute units and validate success
   */
  async simulateSwapTransaction(
    swapTransaction: string,
    userPublicKey: string
  ): Promise<TransactionSimulationResult> {
    try {
      // For now, return a mock simulation result
      // In a full implementation, you would use Solana's simulateTransaction RPC method
      const result: TransactionSimulationResult = {
        success: true,
        computeUnitsConsumed: 150000, // Typical swap transaction
        logs: [
          'Program ******************************** invoke [1]',
          'Program ******************************** success',
        ],
        unitsConsumed: 150000,
      };

      apiLogger.info({
        userPublicKey,
        success: result.success,
        computeUnits: result.computeUnitsConsumed,
      }, 'Swap transaction simulated');

      return result;
    } catch (error) {
      apiLogger.error({ error, userPublicKey }, 'Failed to simulate swap transaction');
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Simulation failed',
      };
    }
  }

  /**
   * Validate token contract address
   */
  validateTokenAddress(address: string): { isValid: boolean; error?: string } {
    if (!address || typeof address !== 'string') {
      return { isValid: false, error: 'Token address is required' };
    }

    try {
      // Try to create PublicKey to validate format
      new PublicKey(address);
      return { isValid: true };
    } catch (error) {
      // If PublicKey construction fails, do basic validation
      apiLogger.warn({ address, error: error instanceof Error ? error.message : 'Unknown error' }, 'PublicKey validation failed, falling back to basic validation');
      
      // Basic Solana address validation
      if (address.length >= 32 && address.length <= 44 && /^[1-9A-HJ-NP-Za-km-z]+$/.test(address)) {
        // Accept addresses that look like valid base58
        return { isValid: true };
      }
      
      return { isValid: false, error: 'Invalid token address format' };
    }
  }

  /**
   * Get token information (simplified version)
   */
  async getTokenInfo(tokenAddress: string): Promise<TokenInfo | null> {
    try {
      // For now, return minimal token info
      // In a full implementation, you would fetch from Jupiter's token list API
      const validation = this.validateTokenAddress(tokenAddress);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // Default token info - in production you would fetch real metadata
      return {
        address: tokenAddress,
        name: 'Unknown Token',
        symbol: 'UNK',
        decimals: 9,
      };
    } catch (error) {
      apiLogger.error({ error, tokenAddress }, 'Failed to get token info');
      return null;
    }
  }

  /**
   * Calculate price impact warning level
   */
  getPriceImpactLevel(priceImpactPct: string): 'low' | 'medium' | 'high' | 'very-high' {
    const impact = new Decimal(priceImpactPct);
    
    if (impact.lt(1)) return 'low';
    if (impact.lt(3)) return 'medium';
    if (impact.lt(5)) return 'high';
    return 'very-high';
  }

  /**
   * Validate quote parameters
   */
  private validateQuoteParams(params: JupiterQuoteParams): void {
    if (!params.inputMint) throw new Error('Input mint is required');
    if (!params.outputMint) throw new Error('Output mint is required');
    if (!params.amount) throw new Error('Amount is required');
    
    // Validate token addresses
    const inputValidation = this.validateTokenAddress(params.inputMint);
    if (!inputValidation.isValid) {
      throw new Error(`Invalid input mint: ${inputValidation.error}`);
    }
    
    const outputValidation = this.validateTokenAddress(params.outputMint);
    if (!outputValidation.isValid) {
      throw new Error(`Invalid output mint: ${outputValidation.error}`);
    }

    // Validate amount
    try {
      const amount = new Decimal(params.amount);
      if (amount.lte(0)) {
        throw new Error('Amount must be greater than 0');
      }
    } catch {
      throw new Error('Invalid amount format');
    }

    // Validate slippage
    if (params.slippageBps && (params.slippageBps < 1 || params.slippageBps > 5000)) {
      throw new Error('Slippage must be between 1 and 5000 basis points');
    }
  }

  /**
   * Enrich quote with additional metadata
   */
  private async enrichQuote(
    quote: JupiterQuoteResponse,
    params: JupiterQuoteParams
  ): Promise<QuoteWithMetadata> {
    // Calculate price per token
    const inAmount = new Decimal(quote.inAmount);
    const outAmount = new Decimal(quote.outAmount);
    const pricePerToken = inAmount.div(outAmount).toString();

    // Extract unique DEXes from route plan
    const routes = quote.routePlan.map(route => ({
      dexLabel: route.swapInfo.label,
      percentage: route.percent,
    }));

    // Estimate gas (simplified)
    const estimatedGas = '0.005'; // ~0.005 SOL estimate for swap

    // Get token info (if available)
    const inputToken = await this.getTokenInfo(params.inputMint);
    const outputToken = await this.getTokenInfo(params.outputMint);

    return {
      ...quote,
      inputToken: inputToken || undefined,
      outputToken: outputToken || undefined,
      pricePerToken,
      estimatedGas,
      routes,
    };
  }

  /**
   * Estimate compute units required for a swap transaction
   */
  private async estimateComputeUnits(quote: JupiterQuoteResponse): Promise<number> {
    // Estimate based on route complexity
    const baseComputeUnits = 100000; // Base CU for simple swap
    const routeComplexity = quote.routePlan.length;
    const complexityMultiplier = 1 + (routeComplexity - 1) * 0.3; // 30% more CU per additional route
    
    const estimatedCU = Math.round(baseComputeUnits * complexityMultiplier);
    
    // Cap at reasonable maximum
    return Math.min(estimatedCU, 400000); // Max 400k CU
  }

  /**
   * Estimate transaction size in bytes
   */
  private estimateTransactionSize(quote: JupiterQuoteResponse): number {
    const baseSize = 300; // Base transaction size in bytes
    const routeComplexity = quote.routePlan.length;
    const sizePerRoute = 100; // Additional bytes per route
    
    return baseSize + (routeComplexity * sizePerRoute);
  }

  /**
   * Check if a quote has expired (Jupiter quotes expire after ~30 seconds)
   */
  isQuoteExpired(contextSlot: number): boolean {
    // Simplified expiration check - in production you'd check against current slot
    // For now, assume quotes expire after 30 seconds
    return false; // TODO: Implement proper slot-based expiration
  }

  /**
   * Get supported tokens (simplified)
   */
  async getSupportedTokens(): Promise<TokenInfo[]> {
    try {
      // In production, you would fetch from Jupiter's token list
      return [
        {
          address: 'So********************************111111112',
          name: 'Solana',
          symbol: 'SOL',
          decimals: 9,
        },
        // Add more tokens as needed
      ];
    } catch (error) {
      apiLogger.error({ error }, 'Failed to get supported tokens');
      return [];
    }
  }
}