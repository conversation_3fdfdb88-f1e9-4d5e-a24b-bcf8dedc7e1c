import { PrismaClient } from '@prisma/client';
import { EventEmitter } from 'events';
import { ExitStrategyService } from './exit-strategy.js';
import { JupiterPriceV3Service } from './jupiter-price-v3.js';
import { TriggerDetectionService } from './trigger-detection.js';
import { ExitExecutionService } from './exit-execution.js';
import { ExitStrategyError } from '../types/exit-strategy.js';

interface TradingService {
  createSwapTransaction(params: any): Promise<{ success: boolean; signature?: string; error?: string }>;
}

export class ExitStrategyOrchestrator extends EventEmitter {
  private isRunning = false;
  private readonly MONITORING_INTERVAL_MS = 30_000; // 30 seconds
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor(
    private prisma: PrismaClient,
    private exitStrategyService: ExitStrategyService,
    private priceService: JupiterPriceV3Service,
    private triggerDetectionService: TriggerDetectionService,
    private executionService: ExitExecutionService
  ) {
    super();
    this.setupEventListeners();
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('⚠️ Exit Strategy Orchestrator is already running');
      return;
    }

    try {
      this.isRunning = true;

      console.log('🚀 Starting Exit Strategy Orchestrator...');

      // Start price monitoring for tokens with active strategies
      await this.initializePriceMonitoring();

      // Start the main monitoring loop
      await this.startMonitoringLoop();

      console.log('✅ Exit Strategy Orchestrator started successfully');

      this.emit('orchestratorStarted', {
        timestamp: new Date()
      });

    } catch (error) {
      this.isRunning = false;
      throw new ExitStrategyError(
        `Failed to start orchestrator: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'ORCHESTRATOR_START_FAILED'
      );
    }
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      console.log('⚠️ Exit Strategy Orchestrator is not running');
      return;
    }

    try {
      console.log('🛑 Stopping Exit Strategy Orchestrator...');

      this.isRunning = false;

      // Stop monitoring interval
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
        this.monitoringInterval = null;
      }

      // Stop price monitoring
      await this.priceService.stopMonitoring();

      console.log('✅ Exit Strategy Orchestrator stopped successfully');

      this.emit('orchestratorStopped', {
        timestamp: new Date()
      });

    } catch (error) {
      throw new ExitStrategyError(
        `Failed to stop orchestrator: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'ORCHESTRATOR_STOP_FAILED'
      );
    }
  }

  private async initializePriceMonitoring(): Promise<void> {
    try {
      // Get all token addresses that have active strategies
      const tokensToMonitor = await this.exitStrategyService.getStrategiesRequiringPriceMonitoring();

      if (tokensToMonitor.length > 0) {
        console.log(`🎯 Initializing price monitoring for ${tokensToMonitor.length} tokens`);
        await this.priceService.startMonitoring(tokensToMonitor);
      } else {
        console.log('📊 No active strategies found - price monitoring will start when strategies are created');
      }

    } catch (error) {
      console.error('Failed to initialize price monitoring:', error);
      throw error;
    }
  }

  private async startMonitoringLoop(): Promise<void> {
    // Initial check
    await this.performMonitoringCycle();

    // Set up interval
    this.monitoringInterval = setInterval(async () => {
      if (this.isRunning) {
        await this.performMonitoringCycle();
      }
    }, this.MONITORING_INTERVAL_MS);

    console.log(`🔄 Monitoring loop started (${this.MONITORING_INTERVAL_MS / 1000}s intervals)`);
  }

  private async performMonitoringCycle(): Promise<void> {
    try {
      console.log('🔍 Performing monitoring cycle...');

      // 1. Check for new strategies that need price monitoring
      await this.updatePriceMonitoring();

      // 2. Check pending triggers without waiting for price updates
      await this.checkPendingTriggers();

      // 3. Update trailing stops
      await this.updateTrailingStops();

      // 4. Clean up completed strategies
      await this.cleanupCompletedStrategies();

      console.log('✅ Monitoring cycle completed');

    } catch (error) {
      console.error('Error during monitoring cycle:', error);

      this.emit('monitoringError', {
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      });
    }
  }

  private async updatePriceMonitoring(): Promise<void> {
    try {
      const currentlyMonitored = new Set(this.priceService.getMonitoredTokens());
      const shouldBeMonitored = new Set(await this.exitStrategyService.getStrategiesRequiringPriceMonitoring());

      // Start monitoring new tokens
      const tokensToAdd = Array.from(shouldBeMonitored).filter(token => !currentlyMonitored.has(token));
      if (tokensToAdd.length > 0) {
        console.log(`🎯 Starting price monitoring for ${tokensToAdd.length} new tokens`);
        await this.priceService.startMonitoring(tokensToAdd);
      }

      // Stop monitoring tokens no longer needed
      const tokensToRemove = Array.from(currentlyMonitored).filter(token => !shouldBeMonitored.has(token));
      if (tokensToRemove.length > 0) {
        console.log(`🎯 Stopping price monitoring for ${tokensToRemove.length} tokens`);
        await this.priceService.stopMonitoring(tokensToRemove);
      }

    } catch (error) {
      console.error('Failed to update price monitoring:', error);
    }
  }

  private async checkPendingTriggers(): Promise<void> {
    try {
      const triggerResults = await this.triggerDetectionService.checkPendingTriggers();

      if (triggerResults.length > 0) {
        console.log(`🎯 Found ${triggerResults.length} strategy(ies) with triggered conditions`);

        for (const result of triggerResults) {
          await this.processTriggerResults(result);
        }
      }

    } catch (error) {
      console.error('Failed to check pending triggers:', error);
    }
  }

  private async updateTrailingStops(): Promise<void> {
    try {
      // Get current price data for all monitored tokens
      const monitoredTokens = this.priceService.getMonitoredTokens();

      for (const tokenAddress of monitoredTokens) {
        const priceData = await this.priceService.getLatestPriceData(tokenAddress);

        if (priceData) {
          await this.triggerDetectionService.updateTrailingStops(priceData);
        }
      }

    } catch (error) {
      console.error('Failed to update trailing stops:', error);
    }
  }

  private async processTriggerResults(result: any): Promise<void> {
    try {
      // Process triggered conditions
      for (const condition of result.triggeredConditions) {
        await this.handleTriggeredCondition(condition);
      }

      // Process trailing stop adjustments
      for (const adjustment of result.trailingStopAdjustments) {
        await this.handleTrailingStopAdjustment(adjustment);
      }

    } catch (error) {
      console.error(`Failed to process trigger results for strategy ${result.strategyId}:`, error);
    }
  }

  private async handleTriggeredCondition(condition: any): Promise<void> {
    try {
      console.log(`🎯 Processing triggered condition: ${condition.type} for trigger ${condition.triggerId}`);

      // Mark trigger as triggered
      await this.triggerDetectionService.markTriggerAsTriggered(
        condition.triggerId,
        condition.currentPrice
      );

      // Execute the trigger
      const executionResult = await this.executionService.executeTrigger(condition.triggerId);

      if (executionResult.success) {
        console.log(`✅ Successfully queued execution for trigger ${condition.triggerId}`);
      } else {
        console.error(`❌ Failed to queue execution for trigger ${condition.triggerId}: ${executionResult.error}`);
      }

    } catch (error) {
      console.error(`Failed to handle triggered condition ${condition.triggerId}:`, error);
    }
  }

  private async handleTrailingStopAdjustment(adjustment: any): Promise<void> {
    try {
      console.log(`📈 Processing trailing stop adjustment for strategy ${adjustment.strategyId}`);

      // The adjustment is already handled by the trigger detection service
      // Just emit an event for monitoring
      this.emit('trailingStopAdjusted', {
        strategyId: adjustment.strategyId,
        adjustment,
        timestamp: new Date()
      });

    } catch (error) {
      console.error(`Failed to handle trailing stop adjustment for strategy ${adjustment.strategyId}:`, error);
    }
  }

  private async cleanupCompletedStrategies(): Promise<void> {
    try {
      // Mark strategies as completed if all their triggers are executed or failed
      const completedStrategies = await this.prisma.exitStrategy.findMany({
        where: {
          status: 'ACTIVE',
          triggers: {
            every: {
              status: {
                in: ['EXECUTED', 'FAILED']
              }
            }
          }
        },
        include: {
          triggers: true
        }
      });

      for (const strategy of completedStrategies) {
        await this.prisma.exitStrategy.update({
          where: { id: strategy.id },
          data: {
            status: 'COMPLETED',
            completedAt: new Date()
          }
        });

        console.log(`✅ Marked strategy ${strategy.id} as completed`);

        this.emit('strategyCompleted', {
          strategyId: strategy.id,
          positionId: strategy.positionId,
          timestamp: new Date()
        });
      }

    } catch (error) {
      console.error('Failed to cleanup completed strategies:', error);
    }
  }

  private setupEventListeners(): void {
    // Listen for price updates
    this.priceService.on('priceUpdate', async (priceData) => {
      try {
        // Detect triggers for this price update
        const triggerResults = await this.triggerDetectionService.detectTriggers(priceData);

        if (triggerResults.length > 0) {
          for (const result of triggerResults) {
            await this.processTriggerResults(result);
          }
        }

      } catch (error) {
        console.error('Error processing price update:', error);
      }
    });

    // Listen for strategy events
    this.exitStrategyService.on('strategyEvent', async (event) => {
      try {
        if (event.type === 'STRATEGY_CREATED') {
          // Check if we need to start monitoring this token
          await this.updatePriceMonitoring();
        }
      } catch (error) {
        console.error('Error handling strategy event:', error);
      }
    });

    // Listen for execution events
    this.executionService.on('triggerExecuted', async (event) => {
      console.log(`✅ Trigger executed: ${event.type} for position ${event.positionId}`);

      this.emit('triggerExecuted', event);
    });

    this.executionService.on('triggerExecutionFailed', async (event) => {
      console.error(`❌ Trigger execution failed: ${event.triggerId} - ${event.error}`);

      this.emit('triggerExecutionFailed', event);
    });
  }

  // Status and monitoring methods
  getStatus(): {
    isRunning: boolean;
    monitoringStats: any;
    queueStats: any;
    triggerStats: Promise<any>;
  } {
    return {
      isRunning: this.isRunning,
      monitoringStats: this.priceService.getMonitoringStats(),
      queueStats: this.executionService.getQueueStats(),
      triggerStats: this.triggerDetectionService.getActiveTriggerStats()
    };
  }

  async getSystemHealth(): Promise<{
    orchestrator: boolean;
    priceMonitoring: boolean;
    executionQueue: boolean;
    database: boolean;
  }> {
    try {
      // Test database connection
      await this.prisma.$queryRaw`SELECT 1`;

      return {
        orchestrator: this.isRunning,
        priceMonitoring: this.priceService.getMonitoringStats().isActive,
        executionQueue: !this.executionService.getQueueStats().isProcessing ||
                      this.executionService.getQueueStats().queueLength < 100, // Arbitrary threshold
        database: true
      };

    } catch (error) {
      return {
        orchestrator: this.isRunning,
        priceMonitoring: this.priceService.getMonitoringStats().isActive,
        executionQueue: false,
        database: false
      };
    }
  }

  // Manual trigger methods for testing/debugging
  async manualTriggerCheck(): Promise<void> {
    if (!this.isRunning) {
      throw new Error('Orchestrator is not running');
    }

    await this.performMonitoringCycle();
  }

  async manualPriceUpdate(tokenAddress: string): Promise<void> {
    const priceData = await this.priceService.getLatestPriceData(tokenAddress);

    if (priceData) {
      const triggerResults = await this.triggerDetectionService.detectTriggers(priceData);

      for (const result of triggerResults) {
        await this.processTriggerResults(result);
      }
    }
  }

  // Clean up on service shutdown
  async shutdown(): Promise<void> {
    await this.stop();
    await this.priceService.shutdown();
    await this.executionService.shutdown();
    this.removeAllListeners();

    console.log('🚪 Exit Strategy Orchestrator shut down completely');
  }
}
