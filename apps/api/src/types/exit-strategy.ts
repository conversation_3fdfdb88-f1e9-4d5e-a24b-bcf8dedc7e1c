import { Decimal } from '@prisma/client/runtime/library';

// Core exit strategy types
export interface TakeProfitTier {
  index: number;
  priceTarget: number;
  percentage: number;
  positionPercentage: number;
  isTriggered: boolean;
  triggeredAt?: Date;
  executedAt?: Date;
}

export interface StopLossConfig {
  enabled: boolean;
  percentage?: number;
  price?: number;
}

export interface TrailingStopConfig {
  enabled: boolean;
  distancePercentage?: number;
  distanceAmount?: number;
  highestPrice?: number;
  currentStopPrice?: number;
}

export interface MoonBagConfig {
  enabled: boolean;
  percentage: number;
}

export interface ExitStrategyConfiguration {
  takeProfitTiers: TakeProfitTier[];
  stopLoss: StopLossConfig;
  trailingStop: TrailingStopConfig;
  moonBag: MoonBagConfig;
}

// API request/response types
export interface CreateExitStrategyRequest {
  positionId: string;
  presetId?: string;
  configuration: ExitStrategyConfiguration;
}

export interface UpdateExitStrategyRequest {
  configuration: Partial<ExitStrategyConfiguration>;
}

export interface CreateExitStrategyPresetRequest {
  name: string;
  description?: string;
  isDefault?: boolean;
  configuration: ExitStrategyConfiguration;
}

export interface UpdateExitStrategyPresetRequest {
  name?: string;
  description?: string;
  isDefault?: boolean;
  configuration?: ExitStrategyConfiguration;
}

// Trigger types
export interface TriggerCondition {
  type: 'TAKE_PROFIT' | 'STOP_LOSS' | 'TRAILING_STOP';
  tierIndex?: number;
  triggerPrice: number;
  triggerPercentage?: number;
  positionPercentage: number;
  tokenAmount: number;
}

export interface TriggerExecution {
  triggeredAt: Date;
  triggeredPrice: number;
  executedAt?: Date;
  executedPrice?: number;
  executedAmount?: number;
  transactionSignature?: string;
  errorMessage?: string;
  retryCount: number;
}

// Monitoring types
export interface PriceMonitoringData {
  tokenAddress: string;
  currentPrice: number;
  timestamp: Date;
  volume24h?: number;
  priceChange24h: number;
}

export interface TriggerDetectionResult {
  strategyId: string;
  triggeredConditions: {
    triggerId: string;
    type: 'TAKE_PROFIT' | 'STOP_LOSS' | 'TRAILING_STOP';
    tierIndex?: number;
    triggerPrice: number;
    currentPrice: number;
    shouldExecute: boolean;
  }[];
  trailingStopAdjustments: {
    strategyId: string;
    previousStopPrice: number;
    newStopPrice: number;
    highestPrice: number;
    reason: string;
  }[];
}

// Service interfaces
export interface IExitStrategyService {
  createStrategy(request: CreateExitStrategyRequest): Promise<string>;
  updateStrategy(strategyId: string, request: UpdateExitStrategyRequest): Promise<void>;
  deleteStrategy(strategyId: string): Promise<void>;
  getStrategy(strategyId: string): Promise<ExitStrategyConfiguration | null>;
  getStrategiesByPosition(positionId: string): Promise<ExitStrategyConfiguration[]>;
  attachStrategyToPosition(positionId: string, presetId?: string): Promise<string>;
}

export interface IExitStrategyPresetService {
  createPreset(request: CreateExitStrategyPresetRequest): Promise<string>;
  updatePreset(presetId: string, request: UpdateExitStrategyPresetRequest): Promise<void>;
  deletePreset(presetId: string): Promise<void>;
  getPreset(presetId: string): Promise<ExitStrategyConfiguration | null>;
  getAllPresets(): Promise<Array<{ id: string; name: string; description?: string; isDefault: boolean; configuration: ExitStrategyConfiguration }>>;
  getDefaultPreset(): Promise<{ id: string; configuration: ExitStrategyConfiguration } | null>;
}

export interface IPriceMonitoringService {
  startMonitoring(tokenAddresses: string[]): Promise<void>;
  stopMonitoring(tokenAddresses?: string[]): Promise<void>;
  getCurrentPrice(tokenAddress: string): Promise<number | null>;
  getLatestPriceData(tokenAddress: string): Promise<PriceMonitoringData | null>;
}

export interface ITriggerDetectionService {
  detectTriggers(priceData: PriceMonitoringData): Promise<TriggerDetectionResult[]>;
  updateTrailingStops(priceData: PriceMonitoringData): Promise<void>;
  checkPendingTriggers(): Promise<TriggerDetectionResult[]>;
}

export interface IExitExecutionService {
  executeTrigger(triggerId: string): Promise<{ success: boolean; transactionSignature?: string; error?: string }>;
  executePartialSell(positionId: string, percentage: number, reason: string): Promise<{ success: boolean; transactionSignature?: string; error?: string }>;
  retryFailedExecution(triggerId: string): Promise<{ success: boolean; transactionSignature?: string; error?: string }>;
}

// Validation types
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ExitStrategyValidation {
  validateConfiguration(config: ExitStrategyConfiguration): ValidationResult;
  validateTakeProfitTiers(tiers: TakeProfitTier[]): ValidationResult;
  validateStopLoss(stopLoss: StopLossConfig, entryPrice: number): ValidationResult;
  validateTrailingStop(trailingStop: TrailingStopConfig): ValidationResult;
  validateMoonBag(moonBag: MoonBagConfig): ValidationResult;
}

// Event types for real-time updates
export interface ExitStrategyEvent {
  type: 'STRATEGY_CREATED' | 'STRATEGY_UPDATED' | 'STRATEGY_DELETED' | 'TRIGGER_ACTIVATED' | 'TRIGGER_EXECUTED' | 'TRAILING_STOP_ADJUSTED';
  strategyId: string;
  positionId: string;
  timestamp: Date;
  data: any;
}

// Error types
export class ExitStrategyError extends Error {
  constructor(
    message: string,
    public code: string,
    public strategyId?: string,
    public triggerId?: string
  ) {
    super(message);
    this.name = 'ExitStrategyError';
  }
}

export class TriggerExecutionError extends ExitStrategyError {
  constructor(
    message: string,
    public triggerId: string,
    public retryCount: number,
    public maxRetries: number
  ) {
    super(message, 'TRIGGER_EXECUTION_ERROR', undefined, triggerId);
    this.name = 'TriggerExecutionError';
  }
}

export class ValidationError extends ExitStrategyError {
  constructor(
    message: string,
    public validationErrors: string[]
  ) {
    super(message, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
  }
}
