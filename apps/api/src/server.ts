import express, { Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import session from 'express-session';
import { config, isProduction, securityConfig } from './lib/config';
import { apiLogger } from './lib/logger';
import { initializeDatabase, disconnectDatabase } from './lib/database';
import { connectRedis, disconnectRedis } from './lib/redis';
import { validateEnvironment } from './lib/env-validation';
import { errorHandler } from './middleware/errorHandler';
import { apiRateLimit } from './middleware/rateLimit';
import { sanitizeRequest } from './middleware/validation';
import { authMiddleware } from './middleware/auth';
import { setupBullBoard } from './jobs/queues';
import { TradeController } from './controllers/TradeController';
import { PositionController } from './controllers/PositionController';
import { validateSchema } from './middleware/validation';
import routes from './routes/index';
import { z } from 'zod';

// Create Express application
const app = express();

// Trust proxy in production
if (isProduction) {
  app.set('trust proxy', 1);
}

// Core middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

app.use(cors({
  origin: isProduction ? false : true, // Restrict origins in production
  credentials: true,
  optionsSuccessStatus: 200,
}));

app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Session configuration
app.use(session({
  secret: securityConfig.sessionSecret,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: isProduction,
    httpOnly: true,
    maxAge: 4 * 60 * 60 * 1000, // 4 hours
    sameSite: isProduction ? 'strict' : 'lax',
  },
  name: 'bmad.sid',
}));

// Global middleware
app.use(apiRateLimit);
app.use(sanitizeRequest);

// Request logging
app.use((req, res, next) => {
  const startTime = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    
    apiLogger.info({
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      contentLength: res.get('Content-Length'),
    }, 'HTTP Request');
  });
  
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: config.NODE_ENV,
  });
});

// Bull Board dashboard (admin only) - Apply authentication middleware
app.use('/admin/queues', authMiddleware, setupBullBoard());

// API Routes
const apiRouter = express.Router();

// Public auth routes (no authentication required)
apiRouter.post('/auth/login', (req: Request, res: Response) => {
  req.session.authenticated = true;
  req.session.loginTime = new Date();
  
  apiLogger.info({
    sessionId: req.sessionID,
    ip: req.ip,
  }, 'User logged in');
  
  res.json({
    success: true,
    message: 'Logged in successfully',
    timestamp: new Date().toISOString(),
  });
});

apiRouter.post('/auth/logout', (req: Request, res: Response) => {
  const wasAuthenticated = req.session.authenticated;
  
  req.session.destroy((err) => {
    if (err) {
      apiLogger.error({ error: err }, 'Session destruction failed');
      return res.status(500).json({
        success: false,
        message: 'Logout failed',
      });
    }
    
    if (wasAuthenticated) {
      apiLogger.info({
        ip: req.ip,
      }, 'User logged out');
    }
    
    res.json({
      success: true,
      message: 'Logged out successfully',
      timestamp: new Date().toISOString(),
    });
  });
});

apiRouter.get('/auth/status', (req: Request, res: Response) => {
  res.json({
    authenticated: !!req.session.authenticated,
    loginTime: req.session.loginTime,
    timestamp: new Date().toISOString(),
  });
});

// Legacy trade routes (for backward compatibility - redirect to /api/trading/*)
apiRouter.post('/trades/quote', authMiddleware, TradeController.getQuote);
apiRouter.post('/trades/buy', authMiddleware, TradeController.executeBuy);

// Legacy position routes (for backward compatibility - redirect to /api/positions/*)
apiRouter.get('/positions', authMiddleware, PositionController.getPositions);
apiRouter.get('/positions/:id', authMiddleware, validateSchema({
  params: z.object({
    id: z.string().uuid('Invalid position ID format'),
  }),
}), PositionController.getPosition);
apiRouter.patch('/positions/:id', authMiddleware, validateSchema({
  params: z.object({
    id: z.string().uuid('Invalid position ID format'),
  }),
}), PositionController.updatePosition);
apiRouter.delete('/positions/:id', authMiddleware, validateSchema({
  params: z.object({
    id: z.string().uuid('Invalid position ID format'),
  }),
}), PositionController.closePosition);

// Mount all API routes with consistent authentication
app.use('/api', routes);

// Mount API routes
app.use('/api', apiRouter);

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    name: 'BMad Solana Trading API',
    version: '1.0.0',
    environment: config.NODE_ENV,
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Cannot ${req.method} ${req.originalUrl}`,
    timestamp: new Date().toISOString()
  });
});

// Global error handler (must be last)
app.use(errorHandler);

// Initialize services and start server
async function startServer() {
  try {
    apiLogger.info('Starting BMad Solana Trading API...');

    // Validate environment and dependencies
    const validation = await validateEnvironment();
    if (!validation.valid) {
      apiLogger.fatal('Environment validation failed - cannot start server');
      process.exit(1);
    }

    // Start HTTP server
    const server = app.listen(config.PORT, config.HOST, () => {
      apiLogger.info({
        port: config.PORT,
        host: config.HOST,
        environment: config.NODE_ENV,
        pid: process.pid,
      }, 'BMad API Server started');
    });

    // Graceful shutdown handlers
    const gracefulShutdown = async (signal: string) => {
      apiLogger.info({ signal }, 'Received shutdown signal, starting graceful shutdown');
      
      server.close(async () => {
        try {
          await disconnectDatabase();
          await disconnectRedis();
          apiLogger.info('Graceful shutdown completed');
          process.exit(0);
        } catch (error) {
          apiLogger.error({ error }, 'Error during graceful shutdown');
          process.exit(1);
        }
      });

      // Force close after timeout
      setTimeout(() => {
        apiLogger.error('Forced shutdown after timeout');
        process.exit(1);
      }, 10000);
    };

    // Handle shutdown signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      apiLogger.fatal({
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack,
        },
      }, 'Uncaught exception');
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      apiLogger.fatal({
        reason,
        promise,
      }, 'Unhandled promise rejection');
      process.exit(1);
    });

  } catch (error) {
    apiLogger.fatal({ error }, 'Server startup failed');
    process.exit(1);
  }
}

// Start the server
if (require.main === module) {
  startServer();
}

export default app;