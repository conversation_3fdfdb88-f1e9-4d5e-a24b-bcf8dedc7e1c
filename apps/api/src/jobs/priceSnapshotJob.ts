import { Queue, Worker, QueueScheduler } from 'bullmq';
import { PrismaClient } from '@prisma/client';
import { apiLogger } from '../lib/logger';
import { PriceHistoryService } from '../services/PriceHistoryService';
import { MarketDataService } from '../services/MarketDataService';
import { WatchlistService } from '../services/WatchlistService';
import { redisConnection } from '../lib/redis';

const logger = apiLogger.child({ job: 'priceSnapshot' });

// Create queue for price snapshots
export const priceSnapshotQueue = new Queue('price-snapshots', {
  connection: redisConnection,
  defaultJobOptions: {
    removeOnComplete: {
      age: 3600, // Keep completed jobs for 1 hour
      count: 100 // Keep max 100 completed jobs
    },
    removeOnFail: {
      age: 86400 // Keep failed jobs for 24 hours
    },
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 5000
    }
  }
});

// Create queue scheduler for recurring jobs
const queueScheduler = new QueueScheduler('price-snapshots', {
  connection: redisConnection
});

// Initialize services
const prisma = new PrismaClient();
const watchlistService = new WatchlistService(prisma);
const marketDataService = new MarketDataService();
const priceHistoryService = new PriceHistoryService(
  prisma,
  marketDataService,
  watchlistService
);

// Create worker to process price snapshot jobs
export const priceSnapshotWorker = new Worker(
  'price-snapshots',
  async (job) => {
    logger.info({ jobId: job.id }, 'Processing price snapshot job');
    
    try {
      // Take a price snapshot
      await priceHistoryService.takeSnapshot();
      
      // Calculate metrics for all tokens
      const watchlistItems = await watchlistService.findAll();
      const addresses = watchlistItems.map(item => item.tokenAddress);
      const metrics = await priceHistoryService.getBatchPriceMetrics(addresses);
      
      logger.info({ 
        jobId: job.id,
        tokensProcessed: metrics.length 
      }, 'Price snapshot completed');
      
      return { 
        success: true, 
        tokensProcessed: metrics.length,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error({ jobId: job.id, error }, 'Price snapshot job failed');
      throw error;
    }
  },
  {
    connection: redisConnection,
    concurrency: 1, // Process one snapshot at a time
    lockDuration: 30000 // 30 second lock
  }
);

// Worker event handlers
priceSnapshotWorker.on('completed', (job) => {
  logger.debug({ jobId: job.id }, 'Price snapshot job completed');
});

priceSnapshotWorker.on('failed', (job, err) => {
  logger.error({ jobId: job?.id, error: err }, 'Price snapshot job failed');
});

// Schedule recurring price snapshots
export async function schedulePriceSnapshots(intervalMinutes: number = 1): Promise<void> {
  try {
    // Remove existing recurring job if it exists
    await priceSnapshotQueue.removeRepeatable('snapshot', {
      every: intervalMinutes * 60 * 1000
    });
    
    // Add new recurring job
    await priceSnapshotQueue.add(
      'snapshot',
      { type: 'recurring' },
      {
        repeat: {
          every: intervalMinutes * 60 * 1000 // Convert minutes to milliseconds
        },
        jobId: 'recurring-snapshot'
      }
    );
    
    logger.info({ intervalMinutes }, 'Scheduled recurring price snapshots');
  } catch (error) {
    logger.error({ error }, 'Failed to schedule price snapshots');
    throw error;
  }
}

// Cleanup old data job (runs daily)
export async function scheduleDataCleanup(): Promise<void> {
  try {
    await priceSnapshotQueue.add(
      'cleanup',
      { type: 'cleanup', daysToKeep: 30 },
      {
        repeat: {
          pattern: '0 3 * * *' // Run at 3 AM daily
        },
        jobId: 'daily-cleanup'
      }
    );
    
    logger.info('Scheduled daily price history cleanup');
  } catch (error) {
    logger.error({ error }, 'Failed to schedule cleanup job');
    throw error;
  }
}

// Add cleanup job processor
priceSnapshotWorker.on('active', async (job) => {
  if (job.data.type === 'cleanup') {
    logger.info('Running price history cleanup');
    await priceHistoryService.cleanupOldData(job.data.daysToKeep || 30);
  }
});

// Graceful shutdown
export async function shutdownPriceSnapshotJobs(): Promise<void> {
  logger.info('Shutting down price snapshot jobs...');
  
  await priceSnapshotWorker.close();
  await queueScheduler.close();
  await priceSnapshotQueue.close();
  await prisma.$disconnect();
  
  logger.info('Price snapshot jobs shut down');
}