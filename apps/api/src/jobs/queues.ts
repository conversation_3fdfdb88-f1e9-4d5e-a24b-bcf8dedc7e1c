import { Queue, QueueOptions } from 'bullmq';
import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';
import { getRedisConfig } from '../lib/config';
import { jobLogger } from '../lib/logger';

// Redis connection configuration for BullMQ
const redisConnection = getRedisConfig();

// Common queue options
const defaultQueueOptions: QueueOptions = {
  connection: redisConnection,
  defaultJobOptions: {
    removeOnComplete: 100, // Keep last 100 completed jobs
    removeOnFail: 50, // Keep last 50 failed jobs
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
  },
};

// Price Monitor Queue - High frequency price updates and monitoring
export const priceMonitorQueue = new Queue('priceMonitor', {
  ...defaultQueueOptions,
  defaultJobOptions: {
    ...defaultQueueOptions.defaultJobOptions,
    attempts: 2, // Price data should be relatively fresh
    delay: 0,
  },
});

// Exit Execution Queue - Critical trading operations
export const exitExecutionQueue = new Queue('exitExecution', {
  ...defaultQueueOptions,
  defaultJobOptions: {
    ...defaultQueueOptions.defaultJobOptions,
    attempts: 5, // Critical operations need more retries
    priority: 10, // High priority for trading operations
  },
});

// Notifications Queue - User alerts and notifications
export const notificationQueue = new Queue('notifications', {
  ...defaultQueueOptions,
  defaultJobOptions: {
    ...defaultQueueOptions.defaultJobOptions,
    attempts: 3,
    delay: 1000, // Small delay for notification batching
  },
});

// Data Sync Queue - Background data synchronization
export const dataSyncQueue = new Queue('dataSync', {
  ...defaultQueueOptions,
  defaultJobOptions: {
    ...defaultQueueOptions.defaultJobOptions,
    attempts: 3,
    priority: 1, // Lower priority for background tasks
  },
});

// Export all queues for easy access
export const queues = {
  priceMonitor: priceMonitorQueue,
  exitExecution: exitExecutionQueue,
  notifications: notificationQueue,
  dataSync: dataSyncQueue,
} as const;

// Queue event handlers for logging and monitoring
Object.entries(queues).forEach(([name, queue]) => {
  // Job completion logging
  queue.on('completed', (job) => {
    jobLogger.info({
      queueName: name,
      jobId: job.id,
      jobName: job.name,
      duration: job.processedOn ? job.processedOn - job.timestamp : 0,
    }, `Job completed successfully`);
  });

  // Job failure logging
  queue.on('failed', (job, err) => {
    jobLogger.error({
      queueName: name,
      jobId: job?.id,
      jobName: job?.name,
      error: {
        name: err.name,
        message: err.message,
        stack: err.stack,
      },
      attemptsMade: job?.attemptsMade,
      maxAttempts: job?.opts.attempts,
    }, `Job failed`);
  });

  // Queue error logging
  queue.on('error', (err) => {
    jobLogger.error({
      queueName: name,
      error: {
        name: err.name,
        message: err.message,
        stack: err.stack,
      },
    }, `Queue error occurred`);
  });

  // Job stalled logging
  queue.on('stalled', (jobId) => {
    jobLogger.warn({
      queueName: name,
      jobId,
    }, `Job stalled and will be retried`);
  });
});

// Bull Board configuration for queue monitoring dashboard
const serverAdapter = new ExpressAdapter();
serverAdapter.setBasePath('/admin/queues');

export const { addQueue, removeQueue, setQueues, replaceQueues } = createBullBoard({
  queues: [
    new BullMQAdapter(priceMonitorQueue),
    new BullMQAdapter(exitExecutionQueue),
    new BullMQAdapter(notificationQueue),
    new BullMQAdapter(dataSyncQueue),
  ],
  serverAdapter,
});

export { serverAdapter as bullBoardAdapter };

// Setup function for Bull Board middleware with authentication
export function setupBullBoard() {
  return serverAdapter.getRouter();
}

// Job type definitions for type safety
export interface PriceMonitorJob {
  tokenAddress: string;
  source: 'coinmarketcap' | 'jupiter' | 'helius';
  timestamp: number;
}

export interface ExitExecutionJob {
  positionId: string;
  strategyId: string;
  triggerType: 'take_profit' | 'stop_loss' | 'trailing_stop' | 'time_based';
  triggerPrice?: number;
  percentage?: number;
}

export interface NotificationJob {
  type: 'price_alert' | 'trade_execution' | 'error' | 'system';
  recipient: 'telegram' | 'email' | 'webhook';
  title: string;
  message: string;
  data?: Record<string, any>;
}

export interface DataSyncJob {
  type: 'price_history' | 'position_sync' | 'market_data';
  tokenAddress?: string;
  positionId?: string;
  timeRange?: {
    start: Date;
    end: Date;
  };
}

// Job priority levels
export const JOB_PRIORITIES = {
  CRITICAL: 20,
  HIGH: 10,
  NORMAL: 5,
  LOW: 1,
} as const;

// Utility functions for adding jobs
export async function addPriceMonitorJob(
  data: PriceMonitorJob,
  options?: { delay?: number; priority?: number }
) {
  return priceMonitorQueue.add('monitor-price', data, {
    priority: options?.priority || JOB_PRIORITIES.NORMAL,
    delay: options?.delay || 0,
  });
}

export async function addExitExecutionJob(
  data: ExitExecutionJob,
  options?: { delay?: number }
) {
  return exitExecutionQueue.add('execute-exit', data, {
    priority: JOB_PRIORITIES.CRITICAL,
    delay: options?.delay || 0,
  });
}

export async function addNotificationJob(
  data: NotificationJob,
  options?: { delay?: number }
) {
  return notificationQueue.add('send-notification', data, {
    priority: JOB_PRIORITIES.HIGH,
    delay: options?.delay || 0,
  });
}

export async function addDataSyncJob(
  data: DataSyncJob,
  options?: { delay?: number }
) {
  return dataSyncQueue.add('sync-data', data, {
    priority: JOB_PRIORITIES.LOW,
    delay: options?.delay || 0,
  });
}

// Graceful shutdown function
export async function closeQueues(): Promise<void> {
  jobLogger.info('Closing job queues...');
  
  const closePromises = Object.entries(queues).map(async ([name, queue]) => {
    try {
      await queue.close();
      jobLogger.info(`${name} queue closed successfully`);
    } catch (error) {
      jobLogger.error({
        queueName: name,
        error,
      }, `Failed to close ${name} queue`);
    }
  });

  await Promise.all(closePromises);
  jobLogger.info('All job queues closed');
}