import { Worker, Job } from 'bullmq';
import { getRedisConfig } from '../../lib/redis';
import { jobLogger } from '../../lib/logger';
import { ExitExecutionJob } from '../queues';
import { prisma } from '../../lib/database';

// Redis connection for worker
const redisConnection = getRedisConfig();

// Exit Execution Worker
export const exitExecutionWorker = new Worker<ExitExecutionJob>(
  'exitExecution',
  async (job: Job<ExitExecutionJob>) => {
    const { positionId, strategyId, triggerType, triggerPrice, percentage } = job.data;
    
    jobLogger.info({
      jobId: job.id,
      positionId,
      strategyId,
      triggerType,
    }, 'Processing exit execution job');

    try {
      // Get position and verify it exists and is active
      const position = await prisma.position.findFirst({
        where: {
          id: positionId,
          status: 'ACTIVE',
        },
        include: {
          exitStrategies: {
            where: {
              id: strategyId,
              isActive: true,
            },
          },
        },
      });

      if (!position) {
        throw new Error(`Position ${positionId} not found or not active`);
      }

      if (position.exitStrategies.length === 0) {
        throw new Error(`Exit strategy ${strategyId} not found or not active`);
      }

      const strategy = position.exitStrategies[0];
      
      // Mock execution logic (will be replaced with actual Jupiter integration)
      const executionResult = await mockExitExecution({
        position,
        strategy,
        triggerType,
        triggerPrice,
        percentage,
      });

      // Create transaction record
      const transaction = await prisma.transaction.create({
        data: {
          positionId: position.id,
          signature: executionResult.signature,
          type: percentage === 100 ? 'SELL' : 'PARTIAL_SELL',
          tokenAddress: position.tokenAddress,
          amountSol: executionResult.amountSol,
          amountToken: executionResult.amountToken,
          price: position.currentPrice,
          fees: executionResult.fees,
          slippage: executionResult.slippage,
          status: 'CONFIRMED',
          confirmedAt: new Date(),
        },
      });

      // Update position based on execution
      if (percentage === 100) {
        // Full exit - close position
        await prisma.position.update({
          where: { id: position.id },
          data: {
            status: 'CLOSED',
            exitTimestamp: new Date(),
          },
        });

        // Deactivate all exit strategies for this position
        await prisma.exitStrategy.updateMany({
          where: { positionId: position.id },
          data: { isActive: false },
        });
      } else {
        // Partial exit - update position quantities
        const remainingPercentage = (100 - (percentage || 0)) / 100;
        const newQuantity = position.quantity.mul(remainingPercentage);
        const newCurrentValueSol = newQuantity.mul(position.currentPrice);
        const newPnlSol = newCurrentValueSol.minus(position.entryAmountSol.mul(remainingPercentage));
        const newPnlPercentage = position.entryAmountSol.isZero()
          ? position.pnlPercentage
          : newPnlSol.div(position.entryAmountSol.mul(remainingPercentage)).mul(100);

        await prisma.position.update({
          where: { id: position.id },
          data: {
            quantity: newQuantity,
            currentValueSol: newCurrentValueSol,
            pnlSol: newPnlSol,
            pnlPercentage: newPnlPercentage,
          },
        });

        // Update the specific tier in the exit strategy if it's a take profit
        if (triggerType === 'take_profit' && strategy.takeProfitTiers) {
          const tiers = strategy.takeProfitTiers as any[];
          const updatedTiers = tiers.map(tier => {
            if (tier.priceTarget === triggerPrice) {
              return {
                ...tier,
                isExecuted: true,
                executedAt: new Date().toISOString(),
                transactionSignature: transaction.signature,
              };
            }
            return tier;
          });

          await prisma.exitStrategy.update({
            where: { id: strategy.id },
            data: {
              takeProfitTiers: updatedTiers,
            },
          });
        }
      }

      jobLogger.info({
        jobId: job.id,
        positionId,
        transactionId: transaction.id,
        signature: transaction.signature,
        amountSol: executionResult.amountSol.toString(),
        percentage,
      }, 'Exit execution job completed successfully');

      return {
        positionId,
        transactionId: transaction.id,
        signature: transaction.signature,
        amountSol: executionResult.amountSol.toString(),
        percentage: percentage || 100,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      jobLogger.error({
        jobId: job.id,
        positionId,
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
        },
      }, 'Exit execution job failed');
      
      throw error;
    }
  },
  {
    connection: redisConnection,
    concurrency: 3, // Process up to 3 exit execution jobs concurrently
  }
);

// Mock exit execution function (will be replaced with Jupiter integration)
async function mockExitExecution({
  position,
  strategy,
  triggerType,
  triggerPrice,
  percentage = 100,
}: {
  position: any;
  strategy: any;
  triggerType: string;
  triggerPrice?: number;
  percentage?: number;
}) {
  // Simulate execution delay
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

  const executionPercentage = percentage / 100;
  const amountToken = position.quantity.mul(executionPercentage);
  const amountSol = amountToken.mul(position.currentPrice);
  const fees = amountSol.mul(0.005); // 0.5% fees
  const slippage = 0.5 + Math.random() * 1.5; // 0.5% to 2% slippage

  // Generate mock transaction signature
  const signature = generateMockSignature();

  return {
    signature,
    amountSol,
    amountToken,
    fees,
    slippage,
  };
}

// Generate mock Solana transaction signature
function generateMockSignature(): string {
  const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz123456789';
  let result = '';
  for (let i = 0; i < 88; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Worker event handlers
exitExecutionWorker.on('completed', (job) => {
  jobLogger.info({
    jobId: job.id,
    duration: job.processedOn ? job.processedOn - job.timestamp : 0,
  }, 'Exit execution job completed');
});

exitExecutionWorker.on('failed', (job, err) => {
  jobLogger.error({
    jobId: job?.id,
    error: {
      name: err.name,
      message: err.message,
      stack: err.stack,
    },
  }, 'Exit execution job failed');
});

exitExecutionWorker.on('error', (err) => {
  jobLogger.error({
    error: {
      name: err.name,
      message: err.message,
      stack: err.stack,
    },
  }, 'Exit execution worker error');
});