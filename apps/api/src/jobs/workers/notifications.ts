import { Worker, Job } from 'bullmq';
import { getRedisConfig } from '../../lib/redis';
import { jobLogger } from '../../lib/logger';
import { NotificationJob } from '../queues';

// Redis connection for worker
const redisConnection = getRedisConfig();

// Notifications Worker
export const notificationWorker = new Worker<NotificationJob>(
  'notifications',
  async (job: Job<NotificationJob>) => {
    const { type, recipient, title, message, data } = job.data;
    
    jobLogger.info({
      jobId: job.id,
      type,
      recipient,
      title,
    }, 'Processing notification job');

    try {
      let result;

      switch (recipient) {
        case 'telegram':
          result = await sendTelegramNotification({ title, message, data });
          break;
        case 'email':
          result = await sendEmailNotification({ title, message, data });
          break;
        case 'webhook':
          result = await sendWebhookNotification({ type, title, message, data });
          break;
        default:
          throw new Error(`Unknown notification recipient: ${recipient}`);
      }

      jobLogger.info({
        jobId: job.id,
        type,
        recipient,
        success: result.success,
      }, 'Notification job completed successfully');

      return {
        type,
        recipient,
        title,
        success: result.success,
        messageId: result.messageId,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      jobLogger.error({
        jobId: job.id,
        type,
        recipient,
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
        },
      }, 'Notification job failed');
      
      throw error;
    }
  },
  {
    connection: redisConnection,
    concurrency: 10, // Process up to 10 notifications concurrently
  }
);

// Mock Telegram notification function
async function sendTelegramNotification({
  title,
  message,
  data,
}: {
  title: string;
  message: string;
  data?: Record<string, any>;
}) {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

  // Mock implementation - in real app, this would call Telegram Bot API
  const messageId = Math.random().toString(36).substring(2, 15);
  
  jobLogger.info({
    title,
    message,
    messageId,
  }, 'Mock Telegram notification sent');

  return {
    success: true,
    messageId,
  };
}

// Mock Email notification function
async function sendEmailNotification({
  title,
  message,
  data,
}: {
  title: string;
  message: string;
  data?: Record<string, any>;
}) {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

  // Mock implementation - in real app, this would use email service
  const messageId = Math.random().toString(36).substring(2, 15);
  
  jobLogger.info({
    title,
    message,
    messageId,
  }, 'Mock email notification sent');

  return {
    success: true,
    messageId,
  };
}

// Mock Webhook notification function
async function sendWebhookNotification({
  type,
  title,
  message,
  data,
}: {
  type: string;
  title: string;
  message: string;
  data?: Record<string, any>;
}) {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 500));

  // Mock implementation - in real app, this would make HTTP request to webhook URL
  const messageId = Math.random().toString(36).substring(2, 15);
  
  jobLogger.info({
    type,
    title,
    message,
    data,
    messageId,
  }, 'Mock webhook notification sent');

  return {
    success: true,
    messageId,
  };
}

// Worker event handlers
notificationWorker.on('completed', (job) => {
  jobLogger.info({
    jobId: job.id,
    duration: job.processedOn ? job.processedOn - job.timestamp : 0,
  }, 'Notification job completed');
});

notificationWorker.on('failed', (job, err) => {
  jobLogger.error({
    jobId: job?.id,
    error: {
      name: err.name,
      message: err.message,
      stack: err.stack,
    },
  }, 'Notification job failed');
});

notificationWorker.on('error', (err) => {
  jobLogger.error({
    error: {
      name: err.name,
      message: err.message,
      stack: err.stack,
    },
  }, 'Notification worker error');
});