import { Worker, Job } from 'bullmq';
import { getRedisConfig } from '../../lib/redis';
import { jobLogger } from '../../lib/logger';
import { PriceMonitorJob } from '../queues';
import { prisma } from '../../lib/database';
import { Decimal } from 'decimal.js';

// Redis connection for worker
const redisConnection = getRedisConfig();

// Price Monitor Worker
export const priceMonitorWorker = new Worker<PriceMonitorJob>(
  'priceMonitor',
  async (job: Job<PriceMonitorJob>) => {
    const { tokenAddress, source, timestamp } = job.data;
    
    jobLogger.info({
      jobId: job.id,
      tokenAddress,
      source,
    }, 'Processing price monitor job');

    try {
      // Mock price data for now - will be replaced with actual API calls in future stories
      const mockPrice = generateMockPrice(tokenAddress);
      
      // Store price snapshot in database
      await prisma.priceSnapshot.create({
        data: {
          tokenAddress,
          price: new Decimal(mockPrice.price),
          volume24h: mockPrice.volume24h ? new Decimal(mockPrice.volume24h) : null,
          marketCap: mockPrice.marketCap ? new Decimal(mockPrice.marketCap) : null,
          priceChange24h: new Decimal(mockPrice.priceChange24h),
          timestamp: new Date(timestamp),
          source: source.toUpperCase() as 'COINMARKETCAP' | 'JUPITER' | 'HELIUS',
        },
      });

      // Update any positions that track this token
      const positions = await prisma.position.findMany({
        where: {
          tokenAddress,
          status: 'ACTIVE',
        },
      });

      for (const position of positions) {
        const currentPrice = new Decimal(mockPrice.price);
        const quantity = position.quantity;
        const currentValueSol = quantity.mul(currentPrice);
        const pnlSol = currentValueSol.minus(position.entryAmountSol);
        const pnlPercentage = position.entryAmountSol.isZero() 
          ? new Decimal(0)
          : pnlSol.div(position.entryAmountSol).mul(100);

        await prisma.position.update({
          where: { id: position.id },
          data: {
            currentPrice,
            currentValueSol,
            pnlSol,
            pnlPercentage,
          },
        });
      }

      // Update watchlist items if they exist
      await prisma.watchlistItem.updateMany({
        where: { tokenAddress },
        data: {
          currentPrice: new Decimal(mockPrice.price),
          priceChange24h: new Decimal(mockPrice.priceChange24h),
          priceChangePercentage24h: new Decimal(
            (mockPrice.priceChange24h / (mockPrice.price - mockPrice.priceChange24h)) * 100
          ),
          volume24h: mockPrice.volume24h ? new Decimal(mockPrice.volume24h) : null,
          marketCap: mockPrice.marketCap ? new Decimal(mockPrice.marketCap) : null,
        },
      });

      jobLogger.info({
        jobId: job.id,
        tokenAddress,
        price: mockPrice.price,
        positionsUpdated: positions.length,
      }, 'Price monitor job completed successfully');

      return {
        tokenAddress,
        price: mockPrice.price,
        positionsUpdated: positions.length,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      jobLogger.error({
        jobId: job.id,
        tokenAddress,
        error: {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
        },
      }, 'Price monitor job failed');
      
      throw error;
    }
  },
  {
    connection: redisConnection,
    concurrency: 5, // Process up to 5 price monitor jobs concurrently
  }
);

// Mock price generation function (will be replaced with real API calls)
function generateMockPrice(tokenAddress: string) {
  // Base prices for known tokens
  const basePrices: Record<string, number> = {
    'So11111111111111111111111111111111111111112': 98.50, // SOL
    'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263': 0.00002340, // BONK
    'WENWENvqqNya429ubCdR81ZmD69brwQaaBYY6p3LCpk': 0.00012500, // WEN
  };

  const basePrice = basePrices[tokenAddress] || 0.001;
  
  // Add some random variation (±5%)
  const variation = (Math.random() - 0.5) * 0.1; // -5% to +5%
  const currentPrice = basePrice * (1 + variation);
  
  // Calculate 24h change
  const change24h = basePrice * (Math.random() - 0.5) * 0.2; // ±10% max change
  
  return {
    price: currentPrice,
    priceChange24h: change24h,
    volume24h: Math.random() * 1000000, // Random volume
    marketCap: currentPrice * Math.random() * 1000000000, // Random market cap
  };
}

// Worker event handlers
priceMonitorWorker.on('completed', (job) => {
  jobLogger.info({
    jobId: job.id,
    duration: job.processedOn ? job.processedOn - job.timestamp : 0,
  }, 'Price monitor job completed');
});

priceMonitorWorker.on('failed', (job, err) => {
  jobLogger.error({
    jobId: job?.id,
    error: {
      name: err.name,
      message: err.message,
      stack: err.stack,
    },
  }, 'Price monitor job failed');
});

priceMonitorWorker.on('error', (err) => {
  jobLogger.error({
    error: {
      name: err.name,
      message: err.message,
      stack: err.stack,
    },
  }, 'Price monitor worker error');
});