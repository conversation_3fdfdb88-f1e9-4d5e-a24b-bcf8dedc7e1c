import { Router } from 'express';
import { z } from 'zod';
import { transactionService } from '../services/TransactionService';
import { validateSchema } from '../middleware/validation';

const router = Router();

// Validation schemas
const transactionParamsSchema = z.object({
  recipient: z.string().min(32).max(44), // Solana address length
  amountSol: z.number().positive().max(1), // Conservative max 1 SOL
  memo: z.string().optional(),
});

const simulateTransactionSchema = transactionParamsSchema.extend({
  simulate: z.boolean().default(true),
});

const signTransactionSchema = transactionParamsSchema.extend({
  simulate: z.boolean().default(false),
});

/**
 * GET /api/transactions/limits
 * Get current transaction limits and settings
 */
router.get('/limits', async (req, res) => {
  try {
    const limits = transactionService.getTransactionLimits();
    const dailyStats = transactionService.getDailyStats();

    res.json({
      success: true,
      data: {
        limits,
        dailyStats,
      },
    });
  } catch (error) {
    console.error('Failed to get transaction limits:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get transaction limits',
    });
  }
});

/**
 * POST /api/transactions/validate
 * Validate transaction parameters
 */
router.post('/validate', validateSchema({ body: transactionParamsSchema }), async (req, res) => {
  try {
    const params = req.body;
    const validation = transactionService.validateTransaction(params);
    const estimatedFee = await transactionService.estimateTransactionFee(params);

    res.json({
      success: true,
      data: {
        ...validation,
        estimatedFee,
        estimatedTotal: params.amountSol + estimatedFee,
      },
    });
  } catch (error) {
    console.error('Failed to validate transaction:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to validate transaction',
    });
  }
});

/**
 * POST /api/transactions/simulate
 * Simulate a transaction without actually sending it
 */
router.post('/simulate', validateSchema({ body: simulateTransactionSchema }), async (req, res) => {
  try {
    const params = req.body;
    const result = await transactionService.signAndSendTransaction(params, true);

    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Failed to simulate transaction:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to simulate transaction',
    });
  }
});

/**
 * POST /api/transactions/send
 * Sign and send a transaction
 */
router.post('/send', validateSchema({ body: signTransactionSchema }), async (req, res) => {
  try {
    const params = req.body;
    
    // Always validate first
    const validation = transactionService.validateTransaction(params);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: 'Transaction validation failed',
        details: validation.errors,
      });
    }

    // Log transaction attempt (without private data)
    console.log('Transaction attempt:', {
      recipient: params.recipient,
      amount: params.amountSol,
      timestamp: new Date().toISOString(),
      network: process.env.SOLANA_NETWORK,
    });

    const result = await transactionService.signAndSendTransaction(params, false);

    res.json({
      success: result.success,
      data: result,
    });
  } catch (error) {
    console.error('Failed to send transaction:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to send transaction',
    });
  }
});

/**
 * GET /api/transactions/test
 * Test wallet connection and signing capability
 */
router.get('/test', async (req, res) => {
  try {
    const testResult = await transactionService.testWalletConnection();

    res.json({
      success: true,
      data: testResult,
    });
  } catch (error) {
    console.error('Failed to test wallet:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test wallet connection',
    });
  }
});

/**
 * GET /api/transactions/status/:signature
 * Get transaction status by signature
 */
router.get('/status/:signature', async (req, res) => {
  try {
    const { signature } = req.params;
    
    if (!signature || signature.length < 64) {
      return res.status(400).json({
        success: false,
        error: 'Invalid transaction signature',
      });
    }

    const status = await transactionService.getTransactionStatus(signature);

    res.json({
      success: true,
      data: status,
    });
  } catch (error) {
    console.error('Failed to get transaction status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get transaction status',
    });
  }
});

/**
 * GET /api/transactions/history
 * Get transaction history
 */
router.get('/history', async (req, res) => {
  try {
    const history = transactionService.getTransactionHistory();
    const dailyStats = transactionService.getDailyStats();

    res.json({
      success: true,
      data: {
        transactions: history,
        dailyStats,
      },
    });
  } catch (error) {
    console.error('Failed to get transaction history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get transaction history',
    });
  }
});

/**
 * POST /api/transactions/history/clear
 * Clear transaction history (for privacy)
 */
router.post('/history/clear', async (req, res) => {
  try {
    transactionService.clearTransactionHistory();

    res.json({
      success: true,
      data: { message: 'Transaction history cleared' },
    });
  } catch (error) {
    console.error('Failed to clear transaction history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear transaction history',
    });
  }
});

export { router as transactionRoutes };