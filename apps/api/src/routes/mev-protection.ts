import { Router } from 'express';
import { z } from 'zod';
import { priorityFeeService } from '../services/PriorityFeeService';
import { validateSchema } from '../middleware/validation';

const router = Router();

// Validation schemas
const priorityFeeRequestSchema = z.object({
  userPreference: z.enum(['economy', 'standard', 'fast', 'turbo']).optional(),
});

const mevProtectionRequestSchema = z.object({
  protectionLevel: z.enum(['basic', 'standard', 'maximum']),
  speedPreference: z.enum(['economy', 'standard', 'fast', 'turbo']).optional(),
});

const priorityFeeValidationSchema = z.object({
  priorityFeeLamports: z.number().min(0),
  maxAllowedFee: z.number().optional(),
});

/**
 * GET /api/mev-protection/network-congestion
 * Get current network congestion level and fee data
 */
router.get('/network-congestion', async (req, res) => {
  try {
    const congestion = await priorityFeeService.getNetworkCongestion();

    res.json({
      success: true,
      data: congestion,
    });
  } catch (error) {
    console.error('Failed to get network congestion:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get network congestion data',
    });
  }
});

/**
 * POST /api/mev-protection/priority-fee-recommendation
 * Get priority fee recommendations based on network conditions
 */
router.post('/priority-fee-recommendation', validateSchema({ body: priorityFeeRequestSchema }), async (req, res) => {
  try {
    const { userPreference = 'standard' } = req.body;
    const recommendation = await priorityFeeService.getPriorityFeeRecommendation(userPreference);

    res.json({
      success: true,
      data: recommendation,
    });
  } catch (error) {
    console.error('Failed to get priority fee recommendation:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get priority fee recommendation',
    });
  }
});

/**
 * GET /api/mev-protection/levels
 * Get available MEV protection levels and their configurations
 */
router.get('/levels', async (req, res) => {
  try {
    const levels = priorityFeeService.getMEVProtectionLevels();

    res.json({
      success: true,
      data: levels,
    });
  } catch (error) {
    console.error('Failed to get MEV protection levels:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get MEV protection levels',
    });
  }
});

/**
 * POST /api/mev-protection/calculate-fee
 * Calculate MEV-protected fee for given protection level and speed preference
 */
router.post('/calculate-fee', validateSchema({ body: mevProtectionRequestSchema }), async (req, res) => {
  try {
    const { protectionLevel, speedPreference = 'standard' } = req.body;
    const mevFee = await priorityFeeService.calculateMEVProtectedFee(protectionLevel, speedPreference);

    res.json({
      success: true,
      data: mevFee,
    });
  } catch (error) {
    console.error('Failed to calculate MEV-protected fee:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to calculate MEV-protected fee',
    });
  }
});

/**
 * POST /api/mev-protection/validate-priority-fee
 * Validate a priority fee amount
 */
router.post('/validate-priority-fee', validateSchema({ body: priorityFeeValidationSchema }), async (req, res) => {
  try {
    const { priorityFeeLamports, maxAllowedFee } = req.body;
    const validation = priorityFeeService.validatePriorityFee(priorityFeeLamports, maxAllowedFee);

    res.json({
      success: true,
      data: validation,
    });
  } catch (error) {
    console.error('Failed to validate priority fee:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to validate priority fee',
    });
  }
});

/**
 * POST /api/mev-protection/success-probability
 * Get estimated transaction success probability for a given priority fee
 */
router.post('/success-probability', validateSchema({ body: z.object({ priorityFeeLamports: z.number().min(0) }) }), async (req, res) => {
  try {
    const { priorityFeeLamports } = req.body;
    const probability = await priorityFeeService.getSuccessProbability(priorityFeeLamports);

    res.json({
      success: true,
      data: probability,
    });
  } catch (error) {
    console.error('Failed to get success probability:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get success probability',
    });
  }
});

/**
 * POST /api/mev-protection/clear-cache
 * Clear priority fee cache (for testing/debugging)
 */
router.post('/clear-cache', async (req, res) => {
  try {
    priorityFeeService.clearCache();

    res.json({
      success: true,
      data: { message: 'Priority fee cache cleared' },
    });
  } catch (error) {
    console.error('Failed to clear cache:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear cache',
    });
  }
});

/**
 * GET /api/mev-protection/fee-history
 * Get historical fee data and recommendations
 */
router.get('/fee-history', async (req, res) => {
  try {
    // Get current congestion and recommendations for all speed preferences
    const congestion = await priorityFeeService.getNetworkCongestion();
    const recommendations = {
      economy: await priorityFeeService.getPriorityFeeRecommendation('economy'),
      standard: await priorityFeeService.getPriorityFeeRecommendation('standard'),
      fast: await priorityFeeService.getPriorityFeeRecommendation('fast'),
      turbo: await priorityFeeService.getPriorityFeeRecommendation('turbo'),
    };

    res.json({
      success: true,
      data: {
        currentCongestion: congestion,
        recommendations,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Failed to get fee history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get fee history',
    });
  }
});

/**
 * GET /api/mev-protection/optimal-settings
 * Get optimal MEV protection settings based on current network conditions
 */
router.get('/optimal-settings', async (req, res) => {
  try {
    const congestion = await priorityFeeService.getNetworkCongestion();
    const levels = priorityFeeService.getMEVProtectionLevels();

    // Recommend protection level based on congestion
    let recommendedLevel: 'basic' | 'standard' | 'maximum';
    let recommendedSpeed: 'economy' | 'standard' | 'fast' | 'turbo';

    switch (congestion.level) {
      case 'low':
        recommendedLevel = 'basic';
        recommendedSpeed = 'standard';
        break;
      case 'medium':
        recommendedLevel = 'standard';
        recommendedSpeed = 'standard';
        break;
      case 'high':
        recommendedLevel = 'standard';
        recommendedSpeed = 'fast';
        break;
      case 'extreme':
        recommendedLevel = 'maximum';
        recommendedSpeed = 'turbo';
        break;
      default:
        recommendedLevel = 'standard';
        recommendedSpeed = 'standard';
    }

    // Calculate recommended fee
    const recommendedFee = await priorityFeeService.calculateMEVProtectedFee(recommendedLevel, recommendedSpeed);

    res.json({
      success: true,
      data: {
        networkCongestion: congestion,
        recommendedLevel,
        recommendedSpeed,
        recommendedFee,
        availableLevels: levels,
        reasoning: `Based on ${congestion.level} network congestion, we recommend ${recommendedLevel} protection with ${recommendedSpeed} speed`,
      },
    });
  } catch (error) {
    console.error('Failed to get optimal settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get optimal settings',
    });
  }
});

export { router as mevProtectionRoutes };