import express from 'express';
import { TradingService } from '../services/TradingService';
import { JupiterService } from '../services/JupiterService';
import { apiLogger } from '../lib/logger';

const router = express.Router();
const tradingService = new TradingService();
const jupiterService = new JupiterService();

/**
 * POST /api/trading/quote
 * Get a trading quote from Jupiter
 */
router.post('/quote', async (req, res, next) => {
  try {
    const { inputMint, outputMint, amountSol, slippageBps } = req.body;

    if (!inputMint || !outputMint || !amountSol) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: inputMint, outputMint, amountSol',
      });
    }

    const quote = await tradingService.getQuote({
      inputMint,
      outputMint,
      amountSol: parseFloat(amountSol),
      slippageBps: slippageBps ? parseInt(slippageBps) : undefined,
    });

    res.json({
      success: true,
      data: quote,
    });
  } catch (error) {
    apiLogger.error({ error }, 'Failed to get trading quote');
    next(error);
  }
});

/**
 * POST /api/trading/buy
 * Execute a complete buy trade (4-step pipeline)
 */
router.post('/buy', async (req, res, next) => {
  try {
    const { 
      inputMint, 
      outputMint, 
      amountSol, 
      slippageBps, 
      maxPriceImpact, 
      simulate 
    } = req.body;

    if (!inputMint || !outputMint || !amountSol) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: inputMint, outputMint, amountSol',
      });
    }

    const result = await tradingService.executeBuy({
      inputMint,
      outputMint,
      amountSol: parseFloat(amountSol),
      slippageBps: slippageBps ? parseInt(slippageBps) : undefined,
      maxPriceImpact: maxPriceImpact ? parseFloat(maxPriceImpact) : undefined,
      simulate: simulate === true || simulate === 'true',
    });

    res.json({
      success: result.success,
      data: result,
    });
  } catch (error) {
    apiLogger.error({ error }, 'Failed to execute buy trade');
    next(error);
  }
});

/**
 * GET /api/trading/positions
 * Get all current trading positions
 */
router.get('/positions', async (req, res, next) => {
  try {
    const positions = tradingService.getPositions();

    res.json({
      success: true,
      data: positions,
    });
  } catch (error) {
    apiLogger.error({ error }, 'Failed to get positions');
    next(error);
  }
});

/**
 * GET /api/trading/positions/:id
 * Get a specific trading position
 */
router.get('/positions/:id', async (req, res, next) => {
  try {
    const { id } = req.params;
    const position = tradingService.getPosition(id);

    if (!position) {
      return res.status(404).json({
        success: false,
        error: 'Position not found',
      });
    }

    res.json({
      success: true,
      data: position,
    });
  } catch (error) {
    apiLogger.error({ error }, 'Failed to get position');
    next(error);
  }
});

/**
 * GET /api/trading/stats
 * Get trading statistics
 */
router.get('/stats', async (req, res, next) => {
  try {
    const stats = tradingService.getTradingStats();

    res.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    apiLogger.error({ error }, 'Failed to get trading stats');
    next(error);
  }
});

/**
 * POST /api/trading/monitor/start
 * Start automated price monitoring
 */
router.post('/monitor/start', async (req, res, next) => {
  try {
    const { intervalMs } = req.body;
    
    tradingService.startAutomatedPriceMonitoring(
      intervalMs ? parseInt(intervalMs) : undefined
    );

    res.json({
      success: true,
      message: 'Price monitoring started',
    });
  } catch (error) {
    apiLogger.error({ error }, 'Failed to start price monitoring');
    next(error);
  }
});

/**
 * POST /api/trading/monitor/stop
 * Stop automated price monitoring
 */
router.post('/monitor/stop', async (req, res, next) => {
  try {
    tradingService.stopAutomatedPriceMonitoring();

    res.json({
      success: true,
      message: 'Price monitoring stopped',
    });
  } catch (error) {
    apiLogger.error({ error }, 'Failed to stop price monitoring');
    next(error);
  }
});

/**
 * GET /api/trading/tokens/supported
 * Get list of supported tokens
 */
router.get('/tokens/supported', async (req, res, next) => {
  try {
    const tokens = await jupiterService.getSupportedTokens();

    res.json({
      success: true,
      data: tokens,
    });
  } catch (error) {
    apiLogger.error({ error }, 'Failed to get supported tokens');
    next(error);
  }
});

/**
 * POST /api/trading/tokens/validate
 * Validate a token contract address
 */
router.post('/tokens/validate', async (req, res, next) => {
  try {
    const { address } = req.body;

    if (!address) {
      return res.status(400).json({
        success: false,
        error: 'Token address is required',
      });
    }

    const validation = jupiterService.validateTokenAddress(address);

    res.json({
      success: validation.isValid,
      data: validation,
    });
  } catch (error) {
    apiLogger.error({ error }, 'Failed to validate token address');
    next(error);
  }
});

export default router;