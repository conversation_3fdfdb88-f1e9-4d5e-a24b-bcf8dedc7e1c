import { Router } from 'express';
import { authMiddleware } from '../middleware/auth';
import healthRoutes from './health';
import walletRoutes from './wallet';
import watchlistRoutes from './watchlist';
import tradingRoutes from './trading';
import { transactionRoutes } from './transactions';
import { mevProtectionRoutes } from './mev-protection';
import { tradingIntegrationRoutes } from './trading-integration';
import { positionsRoutes } from './positions';

const router = Router();

// Mount health check routes (public)
router.use('/health', healthRoutes);

// Mount wallet routes (public - basic info only)
router.use('/wallet', walletRoutes);

// Mount protected trading routes (require authentication)
router.use('/watchlist', authMiddleware, watchlistRoutes);
router.use('/trading', authMiddleware, tradingRoutes);
router.use('/transactions', authMiddleware, transactionRoutes);
router.use('/mev-protection', authMiddleware, mevProtectionRoutes);
router.use('/trading-integration', authMiddleware, tradingIntegrationRoutes);
router.use('/positions', authMiddleware, positionsRoutes);

// API info route
router.get('/', (req, res) => {
  res.json({
    name: 'Solana Trading API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    authentication: {
      status: '/api/auth/status',
      login: '/api/auth/login',
      logout: '/api/auth/logout'
    },
    endpoints: {
      // Public endpoints
      health: '/api/health',
      healthDatabase: '/api/health/db',
      healthRedis: '/api/health/redis',
      healthAll: '/api/health/all',
      walletInfo: '/api/wallet/info',
      walletBalance: '/api/wallet/balance',
      walletValidate: '/api/wallet/validate',
      
      // Protected endpoints (require authentication)
      watchlist: '/api/watchlist',
      watchlistItem: '/api/watchlist/:id',
      watchlistPin: '/api/watchlist/:id/pin',
      watchlistPinned: '/api/watchlist/filter/pinned',
      watchlistStats: '/api/watchlist/meta/stats',
      watchlistMetrics: '/api/watchlist/metrics',
      
      // Trading endpoints
      tradingQuote: '/api/trading/quote',
      tradingBuy: '/api/trading/buy',
      tradesQuote: '/api/trades/quote',
      tradesBuy: '/api/trades/buy',
      
      // Position management
      positions: '/api/positions',
      positionById: '/api/positions/:id',
      
      
      // Transactions
      transactions: '/api/transactions',
      
      // MEV Protection
      mevProtection: '/api/mev-protection',
      
      // Trading Integration
      tradingIntegration: '/api/trading-integration'
    },
    note: 'All trading-related endpoints require authentication. Use /api/auth/login in production or run in development mode for auto-authentication.'
  });
});

export default router;