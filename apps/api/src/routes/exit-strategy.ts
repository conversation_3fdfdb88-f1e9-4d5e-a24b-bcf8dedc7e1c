import { Router, Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { ExitStrategyModel, ExitStrategyPresetModel } from '../models/exit-strategy.js';
import {
  CreateExitStrategyRequest,
  UpdateExitStrategyRequest,
  CreateExitStrategyPresetRequest,
  UpdateExitStrategyPresetRequest,
  ValidationError,
  ExitStrategyError
} from '../types/exit-strategy.js';

const router = Router();
const prisma = new PrismaClient();
const exitStrategyModel = new ExitStrategyModel(prisma);
const presetModel = new ExitStrategyPresetModel(prisma);

// Exit Strategy Routes

/**
 * POST /api/exit-strategies
 * Create a new exit strategy
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    const requestData = req.body as CreateExitStrategyRequest;

    // Validate required fields
    if (!requestData.positionId) {
      return res.status(400).json({
        error: 'Position ID is required'
      });
    }

    if (!requestData.configuration) {
      return res.status(400).json({
        error: 'Strategy configuration is required'
      });
    }

    const strategyId = await exitStrategyModel.createExitStrategy(
      requestData.positionId,
      requestData.configuration,
      requestData.presetId
    );

    res.status(201).json({
      success: true,
      strategyId,
      message: 'Exit strategy created successfully'
    });

  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(400).json({
        error: 'Validation failed',
        validationErrors: error.validationErrors
      });
    }

    if (error instanceof ExitStrategyError) {
      return res.status(400).json({
        error: error.message,
        code: error.code
      });
    }

    console.error('Error creating exit strategy:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

/**
 * GET /api/exit-strategies/:id
 * Get an exit strategy by ID
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const strategy = await exitStrategyModel.getExitStrategy(id);

    if (!strategy) {
      return res.status(404).json({
        error: 'Exit strategy not found'
      });
    }

    res.json({
      success: true,
      strategy
    });

  } catch (error) {
    console.error('Error fetching exit strategy:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

/**
 * GET /api/exit-strategies/position/:positionId
 * Get exit strategy for a specific position
 */
router.get('/position/:positionId', async (req: Request, res: Response) => {
  try {
    const { positionId } = req.params;

    const strategy = await exitStrategyModel.getExitStrategyByPosition(positionId);

    if (!strategy) {
      return res.status(404).json({
        error: 'No exit strategy found for this position'
      });
    }

    res.json({
      success: true,
      strategy
    });

  } catch (error) {
    console.error('Error fetching exit strategy for position:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

/**
 * PUT /api/exit-strategies/:id
 * Update an exit strategy
 */
router.put('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const requestData = req.body as UpdateExitStrategyRequest;

    await exitStrategyModel.updateExitStrategy(id, requestData.configuration);

    res.json({
      success: true,
      message: 'Exit strategy updated successfully'
    });

  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(400).json({
        error: 'Validation failed',
        validationErrors: error.validationErrors
      });
    }

    if (error instanceof ExitStrategyError) {
      return res.status(400).json({
        error: error.message,
        code: error.code
      });
    }

    console.error('Error updating exit strategy:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

/**
 * DELETE /api/exit-strategies/:id
 * Delete an exit strategy
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    await exitStrategyModel.deleteExitStrategy(id);

    res.json({
      success: true,
      message: 'Exit strategy deleted successfully'
    });

  } catch (error) {
    if (error instanceof ExitStrategyError) {
      return res.status(400).json({
        error: error.message,
        code: error.code
      });
    }

    console.error('Error deleting exit strategy:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

/**
 * POST /api/exit-strategies/attach/:positionId
 * Attach an exit strategy to an existing position
 */
router.post('/attach/:positionId', async (req: Request, res: Response) => {
  try {
    const { positionId } = req.params;
    const { presetId } = req.body;

    // Get preset configuration if provided
    let configuration;
    if (presetId) {
      const preset = await presetModel.getPreset(presetId);
      if (!preset) {
        return res.status(404).json({
          error: 'Preset not found'
        });
      }
      configuration = preset.configuration;
    } else {
      // Use default preset
      const defaultPreset = await presetModel.getDefaultPreset();
      if (!defaultPreset) {
        return res.status(400).json({
          error: 'No preset specified and no default preset available'
        });
      }
      configuration = defaultPreset.configuration;
    }

    const strategyId = await exitStrategyModel.createExitStrategy(
      positionId,
      configuration as any,
      presetId
    );

    res.status(201).json({
      success: true,
      strategyId,
      message: 'Exit strategy attached to position successfully'
    });

  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(400).json({
        error: 'Validation failed',
        validationErrors: error.validationErrors
      });
    }

    console.error('Error attaching exit strategy to position:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Exit Strategy Preset Routes

/**
 * POST /api/exit-strategies/presets
 * Create a new exit strategy preset
 */
router.post('/presets', async (req: Request, res: Response) => {
  try {
    const requestData = req.body as CreateExitStrategyPresetRequest;

    // Validate required fields
    if (!requestData.name) {
      return res.status(400).json({
        error: 'Preset name is required'
      });
    }

    if (!requestData.configuration) {
      return res.status(400).json({
        error: 'Preset configuration is required'
      });
    }

    const presetId = await presetModel.createPreset(
      requestData.name,
      requestData.configuration,
      requestData.description,
      requestData.isDefault
    );

    res.status(201).json({
      success: true,
      presetId,
      message: 'Exit strategy preset created successfully'
    });

  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(400).json({
        error: 'Validation failed',
        validationErrors: error.validationErrors
      });
    }

    console.error('Error creating exit strategy preset:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

/**
 * GET /api/exit-strategies/presets
 * Get all exit strategy presets
 */
router.get('/presets', async (req: Request, res: Response) => {
  try {
    const presets = await presetModel.getAllPresets();

    res.json({
      success: true,
      presets
    });

  } catch (error) {
    console.error('Error fetching exit strategy presets:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

/**
 * GET /api/exit-strategies/presets/:id
 * Get a specific exit strategy preset
 */
router.get('/presets/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const preset = await presetModel.getPreset(id);

    if (!preset) {
      return res.status(404).json({
        error: 'Preset not found'
      });
    }

    res.json({
      success: true,
      preset
    });

  } catch (error) {
    console.error('Error fetching exit strategy preset:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

/**
 * GET /api/exit-strategies/presets/default
 * Get the default exit strategy preset
 */
router.get('/presets/default', async (req: Request, res: Response) => {
  try {
    const preset = await presetModel.getDefaultPreset();

    if (!preset) {
      return res.status(404).json({
        error: 'No default preset found'
      });
    }

    res.json({
      success: true,
      preset
    });

  } catch (error) {
    console.error('Error fetching default preset:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

/**
 * PUT /api/exit-strategies/presets/:id
 * Update an exit strategy preset
 */
router.put('/presets/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const requestData = req.body as UpdateExitStrategyPresetRequest;

    await presetModel.updatePreset(id, requestData);

    res.json({
      success: true,
      message: 'Exit strategy preset updated successfully'
    });

  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(400).json({
        error: 'Validation failed',
        validationErrors: error.validationErrors
      });
    }

    console.error('Error updating exit strategy preset:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

/**
 * DELETE /api/exit-strategies/presets/:id
 * Delete an exit strategy preset
 */
router.delete('/presets/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    await presetModel.deletePreset(id);

    res.json({
      success: true,
      message: 'Exit strategy preset deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting exit strategy preset:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

/**
 * POST /api/exit-strategies/validate
 * Validate an exit strategy configuration
 */
router.post('/validate', async (req: Request, res: Response) => {
  try {
    const { configuration } = req.body;

    if (!configuration) {
      return res.status(400).json({
        error: 'Configuration is required'
      });
    }

    const validation = exitStrategyModel.validateConfiguration(configuration);

    res.json({
      success: true,
      validation
    });

  } catch (error) {
    console.error('Error validating exit strategy configuration:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

export default router;
