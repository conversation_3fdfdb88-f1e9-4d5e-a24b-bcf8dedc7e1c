import { Router } from 'express';
import { z } from 'zod';
import { validateSchema } from '../middleware/validation';
import { JupiterService } from '../services/JupiterService';
import { HeliusService } from '../services/HeliusService';
import { TradingService } from '../services/TradingService';
import { priorityFeeService } from '../services/PriorityFeeService';
import { getWallet } from '../lib/solana';
import { VersionedTransaction } from '@solana/web3.js';
import { apiLogger } from '../lib/logger';

const router = Router();
const jupiterService = new JupiterService();
const heliusService = new HeliusService();
const tradingService = new TradingService();

// Helper function to get token decimals (same as in TradingService)
function getTokenDecimals(tokenMint: string): number {
  const knownTokenDecimals: Record<string, number> = {
    'So11111111111111111111111111111111111111112': 9,  // SOL
    'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 6,  // USDC
    'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 6,  // USDT
    'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263': 5,  // BONK
    'JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN': 6,  // JUP
    'EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm': 6,  // WIF
    'FYXnJhgLmhcELqsKyAfdKZExFfA7gaskQa5TuEZE9RZT': 6,  // TEST
    'rndrizKT3MK1iimdxRdWabcF7Zg7AR5T4nud4EkHBof': 9,  // RND
  };
  return knownTokenDecimals[tokenMint] || 6;
}

// Helper function to convert token amount (same as in TradingService)
function convertTokenAmount(amount: number, tokenMint: string): number {
  const decimals = getTokenDecimals(tokenMint);
  const multiplier = Math.pow(10, decimals);
  return Math.floor(amount * multiplier);
}

// Validation schemas
const executeTradeRequestSchema = z.object({
  inputMint: z.string(),
  outputMint: z.string(),
  amountSol: z.number().positive(),
  slippageBps: z.number().min(1).max(5000).optional().default(100),
  mevProtectionLevel: z.enum(['basic', 'standard', 'maximum']).default('standard'),
  speedPreference: z.enum(['economy', 'standard', 'fast', 'turbo']).default('standard'),
  maxPriceImpact: z.number().max(10).optional().default(5), // Max 5% price impact
  simulateFirst: z.boolean().optional().default(true),
  trackPosition: z.boolean().optional().default(true),
});

const quoteRequestSchema = z.object({
  inputMint: z.string(),
  outputMint: z.string(),
  amountSol: z.number().positive(),
  slippageBps: z.number().min(1).max(5000).optional().default(100),
});

export interface TradeExecutionResult {
  success: boolean;
  transactionSignature?: string;
  error?: string;
  tradeDetails?: {
    inputAmount: string;
    outputAmount: string;
    priceImpact: string;
    route: string[];
    executionTime: number;
    totalFee: number;
    mevProtectionApplied: {
      level: string;
      priorityFee: number;
      jitoTip: number;
      submissionMethod: string;
    };
  };
  positionTracking?: {
    positionId: string;
    entryPrice: string;
    monitoringActive: boolean;
  };
}

/**
 * POST /api/trading-integration/quote
 * Get enhanced quote with MEV protection cost estimation
 */
router.post('/quote', validateSchema({ body: quoteRequestSchema }), async (req, res) => {
  try {
    const { inputMint, outputMint, amountSol, slippageBps } = req.body;

    // Use TradingService to get proper quote with correct token decimal conversion
    const quote = await tradingService.getQuote({
      inputMint,
      outputMint,
      amountSol: parseFloat(amountSol),
      slippageBps: slippageBps ? parseInt(slippageBps) : undefined,
    });

    // Get MEV protection cost estimates for all levels
    const mevCosts = await Promise.all([
      priorityFeeService.calculateMEVProtectedFee('basic', 'standard'),
      priorityFeeService.calculateMEVProtectedFee('standard', 'standard'),
      priorityFeeService.calculateMEVProtectedFee('maximum', 'standard'),
    ]);

    // Get network congestion info
    const networkCongestion = await priorityFeeService.getNetworkCongestion();

    // Validate price impact
    const priceImpact = parseFloat(quote.priceImpactPct);
    const priceImpactLevel = jupiterService.getPriceImpactLevel(quote.priceImpactPct);

    // Calculate minimum received with slippage
    const outAmount = parseFloat(quote.outAmount);
    const slippageMultiplier = slippageBps / 10000;
    const minimumReceived = Math.floor(outAmount * (1 - slippageMultiplier));

    const enhancedQuote = {
      ...quote,
      priceImpactLevel,
      minimumReceived: minimumReceived.toString(),
      mevProtectionCosts: {
        basic: mevCosts[0],
        standard: mevCosts[1],
        maximum: mevCosts[2],
      },
      networkCongestion,
      recommendations: {
        maxRecommendedAmount: priceImpact > 3 ? amountSol * 0.5 : amountSol, // Suggest smaller size for high impact
        recommendedSlippage: priceImpact > 2 ? Math.max(slippageBps, 200) : slippageBps,
        recommendedMevLevel: networkCongestion.level === 'high' || networkCongestion.level === 'extreme' ? 'maximum' : 'standard',
      },
      isExpired: jupiterService.isQuoteExpired(quote.contextSlot),
      estimatedConfirmationTime: {
        basic: '15-30 seconds',
        standard: '8-15 seconds',
        maximum: '3-8 seconds',
      },
    };

    apiLogger.info({
      inputMint,
      outputMint,
      amountSol,
      priceImpact,
      priceImpactLevel,
      networkCongestion: networkCongestion.level,
    }, 'Enhanced quote generated');

    res.json({
      success: true,
      data: enhancedQuote,
    });
  } catch (error) {
    apiLogger.error({ error, body: req.body }, 'Failed to generate enhanced quote');
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get quote',
    });
  }
});

/**
 * POST /api/trading-integration/execute
 * Execute a complete MEV-protected trade with position tracking
 */
router.post('/execute', validateSchema({ body: executeTradeRequestSchema }), async (req, res) => {
  const startTime = Date.now();
  
  try {
    const {
      inputMint,
      outputMint,
      amountSol,
      slippageBps,
      mevProtectionLevel,
      speedPreference,
      maxPriceImpact,
      simulateFirst,
      trackPosition
    } = req.body;

    apiLogger.info({
      inputMint,
      outputMint,
      amountSol,
      mevProtectionLevel,
      speedPreference,
    }, 'Starting MEV-protected trade execution');

    // Step 1: Get fresh quote using TradingService with proper token decimal conversion
    const quote = await tradingService.getQuote({
      inputMint,
      outputMint,
      amountSol: parseFloat(amountSol),
      slippageBps: slippageBps ? parseInt(slippageBps) : undefined,
    });

    // Step 2: Validate trade conditions
    const priceImpact = parseFloat(quote.priceImpactPct);
    if (priceImpact > maxPriceImpact) {
      return res.status(400).json({
        success: false,
        error: `Price impact too high: ${priceImpact.toFixed(2)}% (max: ${maxPriceImpact}%)`,
        priceImpact,
        maxAllowed: maxPriceImpact,
      });
    }

    // Step 3: Get wallet and validate balance
    const wallet = getWallet();
    const connection = heliusService.getConnection();
    const balance = await connection.getBalance(wallet.getPublicKey());
    const requiredBalance = amountLamports + 50000000; // Amount + 0.05 SOL for fees
    
    if (balance < requiredBalance) {
      return res.status(400).json({
        success: false,
        error: 'Insufficient balance for trade and fees',
        required: requiredBalance / 1_000_000_000,
        available: balance / 1_000_000_000,
      });
    }

    // Step 4: Build MEV-protected swap transaction
    const swapOptions = {
      userPublicKey: wallet.publicKey,
      quote,
      mevProtectionLevel,
      speedPreference,
      maxSlippageBps: slippageBps,
      jitoBundle: mevProtectionLevel !== 'basic',
      computeUnitBuffer: 0.25, // 25% buffer for compute units
    };

    const swapResponse = await jupiterService.buildMEVProtectedSwap(swapOptions);
    
    // Step 5: Deserialize and simulate transaction (if requested)
    let simulationResult = null;
    const transactionBuffer = Buffer.from(swapResponse.swapTransaction, 'base64');
    const transaction = VersionedTransaction.deserialize(transactionBuffer);

    if (simulateFirst) {
      simulationResult = await jupiterService.simulateSwapTransaction(
        swapResponse.swapTransaction,
        wallet.publicKey
      );

      if (!simulationResult.success) {
        return res.status(400).json({
          success: false,
          error: `Transaction simulation failed: ${simulationResult.error}`,
          simulationResult,
        });
      }
    }

    // Step 6: Sign transaction
    transaction.sign([wallet.getKeypair()]);

    // Step 7: Submit transaction with MEV protection
    let submissionResult;
    
    if (swapResponse.mevProtectionApplied?.jitoTipLamports && swapResponse.mevProtectionApplied.jitoTipLamports > 0) {
      // Submit via Jito bundle for maximum MEV protection
      submissionResult = await heliusService.submitJitoBundle(
        [transaction],
        swapResponse.mevProtectionApplied.jitoTipLamports
      );
    } else {
      // Submit with enhanced retry logic
      submissionResult = await heliusService.submitTransactionWithRetry(transaction, {
        skipPreflight: speedPreference === 'turbo',
        retryOptions: {
          maxRetries: speedPreference === 'economy' ? 5 : 3,
          initialDelayMs: speedPreference === 'turbo' ? 500 : 1000,
          maxDelayMs: 8000,
          backoffMultiplier: 1.5,
          retryableErrors: [
            'Transaction was not confirmed',
            'Blockhash not found',
            '429',
            'timeout',
          ],
        },
      });
    }

    if (!submissionResult.success) {
      return res.status(500).json({
        success: false,
        error: `Transaction submission failed: ${submissionResult.error}`,
        retries: submissionResult.retries,
        totalTime: submissionResult.totalTime,
      });
    }

    // Step 8: Monitor transaction confirmation
    const monitoringResult = await heliusService.monitorTransaction(
      submissionResult.signature,
      'confirmed',
      60000 // 60 second timeout
    );

    if (monitoringResult.err) {
      return res.status(500).json({
        success: false,
        error: `Transaction failed on-chain: ${JSON.stringify(monitoringResult.err)}`,
        signature: submissionResult.signature,
      });
    }

    // Step 9: Get detailed transaction information
    const transactionDetails = await heliusService.getTransactionDetails(submissionResult.signature);

    // Step 10: Calculate actual trade results
    const executionTime = Date.now() - startTime;
    const actualFee = transactionDetails.fee || swapResponse.estimatedFee || 0;

    // Step 11: Setup position tracking (if enabled)
    let positionTracking = null;
    if (trackPosition) {
      // In a full implementation, you would save position to database here
      positionTracking = {
        positionId: `pos_${submissionResult.signature.slice(0, 8)}`,
        entryPrice: (amountLamports / parseFloat(quote.outAmount)).toString(),
        monitoringActive: true,
      };

      apiLogger.info({
        positionId: positionTracking.positionId,
        signature: submissionResult.signature,
        entryPrice: positionTracking.entryPrice,
      }, 'Position tracking initiated');
    }

    // Step 12: Build success response
    const result: TradeExecutionResult = {
      success: true,
      transactionSignature: submissionResult.signature,
      tradeDetails: {
        inputAmount: quote.inAmount,
        outputAmount: quote.outAmount,
        priceImpact: quote.priceImpactPct,
        route: quote.routePlan.map(r => r.swapInfo.label),
        executionTime,
        totalFee: actualFee,
        mevProtectionApplied: {
          level: mevProtectionLevel,
          priorityFee: swapResponse.mevProtectionApplied?.priorityFeeLamports || 0,
          jitoTip: swapResponse.mevProtectionApplied?.jitoTipLamports || 0,
          submissionMethod: submissionResult.submissionMethod,
        },
      },
      positionTracking,
    };

    apiLogger.info({
      signature: submissionResult.signature,
      executionTime,
      mevLevel: mevProtectionLevel,
      priceImpact,
      totalFee: actualFee,
      confirmationStatus: monitoringResult.confirmationStatus,
    }, 'MEV-protected trade executed successfully');

    res.json({
      success: true,
      data: result,
    });

  } catch (error) {
    const executionTime = Date.now() - startTime;
    
    apiLogger.error({
      error: error instanceof Error ? error.message : String(error),
      body: req.body,
      executionTime,
    }, 'MEV-protected trade execution failed');

    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Trade execution failed',
      executionTime,
    });
  }
});

/**
 * GET /api/trading-integration/network-status
 * Get comprehensive network status for trading decisions
 */
router.get('/network-status', async (req, res) => {
  try {
    const [networkCongestion, healthCheck] = await Promise.all([
      priorityFeeService.getNetworkCongestion(),
      heliusService.isHealthy(),
    ]);

    const networkStatus = {
      congestion: networkCongestion,
      rpcHealth: healthCheck,
      recommendations: {
        optimalMevLevel: networkCongestion.level === 'low' ? 'basic' : 
                        networkCongestion.level === 'medium' ? 'standard' : 'maximum',
        recommendedSpeed: networkCongestion.level === 'extreme' ? 'turbo' : 'standard',
        tradingAdvice: networkCongestion.level === 'extreme' ? 
          'Network extremely congested - consider waiting or using maximum MEV protection' :
          'Network conditions suitable for trading',
      },
      timestamp: new Date().toISOString(),
    };

    res.json({
      success: true,
      data: networkStatus,
    });
  } catch (error) {
    apiLogger.error({ error }, 'Failed to get network status');
    res.status(500).json({
      success: false,
      error: 'Failed to get network status',
    });
  }
});

/**
 * POST /api/trading-integration/validate-trade
 * Pre-validate a trade before execution
 */
router.post('/validate-trade', validateSchema({ body: executeTradeRequestSchema }), async (req, res) => {
  try {
    const { inputMint, outputMint, amountSol, maxPriceImpact, slippageBps } = req.body;

    // Get quote for validation
    // Get quote using TradingService with proper token decimal conversion
    const quote = await tradingService.getQuote({
      inputMint,
      outputMint,
      amountSol: parseFloat(amountSol),
      slippageBps: slippageBps ? parseInt(slippageBps) : undefined,
    });

    // Check wallet balance
    const wallet = getWallet();
    const connection = heliusService.getConnection();
    const balance = await connection.getBalance(wallet.getPublicKey());
    
    // Get MEV protection costs
    const mevCosts = await priorityFeeService.calculateMEVProtectedFee(
      req.body.mevProtectionLevel,
      req.body.speedPreference
    );

    // Get the required amount in proper token units
    const requiredTokenAmount = convertTokenAmount(parseFloat(amountSol), inputMint);
    const requiredBalance = requiredTokenAmount + mevCosts.totalEstimatedCost;
    const priceImpact = parseFloat(quote.priceImpactPct);

    const validation = {
      isValid: true,
      warnings: [] as string[],
      errors: [] as string[],
      priceImpact,
      estimatedOutput: quote.outAmount,
      totalCost: mevCosts.totalEstimatedCost,
      availableBalance: balance,
      requiredBalance,
    };

    // Check balance
    if (balance < requiredBalance) {
      validation.isValid = false;
      validation.errors.push('Insufficient balance for trade and MEV protection fees');
    }

    // Check price impact
    if (priceImpact > maxPriceImpact) {
      validation.isValid = false;
      validation.errors.push(`Price impact too high: ${priceImpact.toFixed(2)}% (max: ${maxPriceImpact}%)`);
    }

    // Add warnings
    if (priceImpact > 2) {
      validation.warnings.push('High price impact detected - consider reducing trade size');
    }

    if (mevCosts.totalEstimatedCost > amountLamports * 0.05) {
      validation.warnings.push('MEV protection cost is >5% of trade amount');
    }

    res.json({
      success: true,
      data: validation,
    });

  } catch (error) {
    apiLogger.error({ error, body: req.body }, 'Trade validation failed');
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Validation failed',
    });
  }
});

export { router as tradingIntegrationRoutes };