import express from 'express';
import { tokenMetadataService } from '../services/TokenMetadataService';
import { apiLogger } from '../lib/logger';

const router = express.Router();

/**
 * GET /api/tokens/metadata/:address
 * Get metadata for a specific token by address
 */
router.get('/metadata/:address', async (req, res, next) => {
  try {
    const { address } = req.params;
    
    if (!address) {
      return res.status(400).json({
        success: false,
        error: 'Token address is required'
      });
    }

    apiLogger.info({ tokenAddress: address }, 'Fetching token metadata');
    
    const metadata = await tokenMetadataService.getTokenMetadata(address);
    
    res.json({
      success: true,
      data: metadata
    });
  } catch (error) {
    apiLogger.error({ error, tokenAddress: req.params.address }, 'Failed to fetch token metadata');
    
    if (error instanceof Error && error.message.includes('Invalid Solana address')) {
      return res.status(400).json({
        success: false,
        error: error.message
      });
    }
    
    next(error);
  }
});

/**
 * GET /api/tokens/search
 * Search for tokens by symbol or name
 */
router.get('/search', async (req, res, next) => {
  try {
    const { q: query, limit } = req.query;
    
    if (!query || typeof query !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Query parameter "q" is required'
      });
    }

    if (query.length < 2) {
      return res.json({
        success: true,
        data: []
      });
    }

    const limitNum = limit && typeof limit === 'string' ? parseInt(limit, 10) : 10;
    const maxLimit = 50;
    const searchLimit = Math.min(limitNum, maxLimit);

    apiLogger.info({ query, limit: searchLimit }, 'Searching tokens');
    
    const results = await tokenMetadataService.searchTokens(query, searchLimit);
    
    res.json({
      success: true,
      data: results,
      meta: {
        query,
        count: results.length,
        limit: searchLimit
      }
    });
  } catch (error) {
    apiLogger.error({ error, query: req.query.q }, 'Failed to search tokens');
    next(error);
  }
});

/**
 * GET /api/tokens/validate/:address
 * Validate if a token address exists and return basic info
 */
router.get('/validate/:address', async (req, res, next) => {
  try {
    const { address } = req.params;
    
    if (!address) {
      return res.status(400).json({
        success: false,
        error: 'Token address is required'
      });
    }

    apiLogger.info({ tokenAddress: address }, 'Validating token address');
    
    try {
      const metadata = await tokenMetadataService.getTokenMetadata(address);
      
      res.json({
        success: true,
        data: {
          isValid: true,
          address: metadata.address,
          symbol: metadata.symbol,
          name: metadata.name,
          decimals: metadata.decimals,
          verified: metadata.verified || false
        }
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes('Invalid Solana address')) {
        return res.json({
          success: true,
          data: {
            isValid: false,
            error: error.message
          }
        });
      }
      throw error;
    }
  } catch (error) {
    apiLogger.error({ error, tokenAddress: req.params.address }, 'Failed to validate token address');
    next(error);
  }
});

/**
 * GET /api/tokens/stats
 * Get token service statistics
 */
router.get('/stats', async (req, res) => {
  try {
    const stats = {
      tokenListSize: tokenMetadataService.getTokenListSize(),
      lastUpdated: tokenMetadataService.getLastUpdated(),
      uptime: process.uptime(),
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    apiLogger.error({ error }, 'Failed to get token service stats');
    res.status(500).json({
      success: false,
      error: 'Failed to get service stats'
    });
  }
});

export default router;