import { Router, Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { PublicKey } from '@solana/web3.js';
import { WatchlistService } from '../services/WatchlistService';
import { MarketDataService } from '../services/MarketDataService';
import { StubMetricsAdapter } from '../services/metrics/StubMetricsAdapter';
import { JupiterV2Adapter } from '../services/metrics/JupiterV2Adapter';
import { adapterRegistry } from '../services/metrics/MetricsAdapterInterface';
import { dexScreenerService } from '../services/DexScreenerService';
import { apiLogger } from '../lib/logger';
import { 
  createWatchlistItemSchema,
  updateWatchlistItemSchema,
  bulkImportSchema,
  WATCHLIST_ERRORS,
  WATCHLIST_LIMITS,
  WatchlistItemWithMetrics
} from '../../../../packages/shared/src/types/watchlist';

const router = Router();
const prisma = new PrismaClient();
const watchlistService = new WatchlistService(prisma);

// Initialize market data services with Jupiter V2 API for better data
const jupiterAdapter = new JupiterV2Adapter({
  tier: 'lite', // Using free lite tier
  timeout: 10000 // 10 second timeout
});

// Register Jupiter V2 as primary adapter
adapterRegistry.register(jupiterAdapter);

// Keep stub as fallback for development (optional)
const stubAdapter = new StubMetricsAdapter();
adapterRegistry.register(stubAdapter);

// Explicitly ensure Jupiter V2 is primary
adapterRegistry.setPrimary('jupiter-v2');

const marketDataService = new MarketDataService();

// Validation schemas are imported from shared types

// POST /api/watchlist/snapshot - Trigger immediate price snapshot
router.post('/snapshot', async (req: Request, res: Response) => {
  try {
    const PriceHistoryService = require('../services/PriceHistoryService').PriceHistoryService;
    const priceHistoryService = new PriceHistoryService(
      prisma,
      marketDataService,
      watchlistService
    );

    await priceHistoryService.takeSnapshot();
    
    res.status(200).json({ 
      success: true,
      message: 'Price snapshot recorded',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    apiLogger.error({ error }, 'Failed to take price snapshot');
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Failed to record price snapshot',
        details: [error instanceof Error ? error.message : 'Unknown error']
      }
    });
  }
});

// GET /api/watchlist/metrics/history - Get watchlist items with historical price metrics
router.get('/metrics/history', async (req: Request, res: Response) => {
  try {
    const items = await watchlistService.findAll();
    
    if (items.length === 0) {
      res.status(200).json({ 
        items: [], 
        total: 0,
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Get price history metrics for all items
    const priceHistoryService = new (require('../services/PriceHistoryService').PriceHistoryService)(
      prisma,
      marketDataService,
      watchlistService
    );

    const addresses = items.map(item => item.tokenAddress);
    const metricsData = await priceHistoryService.getBatchPriceMetrics(addresses);

    // Combine watchlist items with price metrics
    const itemsWithMetrics = items.map(item => {
      const metrics = metricsData.find(m => m.tokenAddress === item.tokenAddress);
      
      return {
        ...item,
        priceMetrics: metrics || null
      };
    });

    res.status(200).json({
      items: itemsWithMetrics,
      total: itemsWithMetrics.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    apiLogger.error({ error }, 'Failed to fetch watchlist items with price history');
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Failed to fetch watchlist metrics',
        details: [error instanceof Error ? error.message : 'Unknown error']
      }
    });
  }
});

// GET /api/watchlist/metrics - Get watchlist items with market data
router.get('/metrics', async (req: Request, res: Response) => {
  try {
    // Get all active watchlist items
    const watchlistItems = await watchlistService.findAll();
    
    if (watchlistItems.length === 0) {
      res.status(200).json({
        items: [],
        total: 0,
        timestamp: new Date().toISOString(),
        fromCache: false
      });
      return;
    }

    // Extract token addresses
    const tokenAddresses = watchlistItems.map(item => item.tokenAddress);
    
    // Fetch market data for all tokens
    const marketDataResponse = await marketDataService.fetchBatchData(tokenAddresses);
    
    // Fetch DEX Screener data for all tokens
    const dexInfoMap = await dexScreenerService.getBatchDexInfo(tokenAddresses);
    
    // Create a map for quick lookup of market data by token address
    const marketDataMap = new Map();
    marketDataResponse.data.forEach(snapshot => {
      marketDataMap.set(snapshot.tokenAddress, snapshot);
    });

    // Combine watchlist items with market data and DEX info
    const itemsWithMetrics: WatchlistItemWithMetrics[] = watchlistItems.map(item => {
      const snapshot = marketDataMap.get(item.tokenAddress);
      const dexInfo = dexInfoMap.get(item.tokenAddress);
      
      // Add DEX info to snapshot metadata
      if (snapshot && dexInfo) {
        (snapshot as any).metadata = {
          ...(snapshot as any).metadata,
          hasDexPaid: dexInfo.hasPaid,
          isBoosted: dexInfo.isBoosted,
          trendingScore: dexInfo.trendingScore,
          trendingRank: dexInfo.trendingRank
        };
      }
      
      return {
        ...item,
        snapshot
      };
    });

    // Set appropriate cache headers
    const cacheMaxAge = marketDataResponse.fromCache ? 60 : 30; // 1 minute for cached, 30 seconds for fresh
    res.set({
      'Cache-Control': `public, max-age=${cacheMaxAge}`,
      'ETag': `W/"${Date.now()}"`,
      'X-Data-Source': marketDataResponse.adapter,
      'X-From-Cache': marketDataResponse.fromCache.toString()
    });

    const response = {
      items: itemsWithMetrics,
      total: itemsWithMetrics.length,
      timestamp: marketDataResponse.timestamp.toISOString(),
      fromCache: marketDataResponse.fromCache,
      adapter: marketDataResponse.adapter,
      errors: marketDataResponse.errors?.length || 0
    };

    apiLogger.info({
      itemCount: itemsWithMetrics.length,
      fromCache: marketDataResponse.fromCache,
      adapter: marketDataResponse.adapter,
      errors: marketDataResponse.errors?.length || 0
    }, 'Fetched watchlist items with metrics');

    res.status(200).json(response);
  } catch (error) {
    apiLogger.error({ error }, 'Failed to fetch watchlist items with metrics');
    
    // Try to return watchlist items without market data as fallback
    try {
      const watchlistItems = await watchlistService.findAll();
      const itemsWithoutMetrics: WatchlistItemWithMetrics[] = watchlistItems.map(item => ({
        ...item,
        snapshot: undefined
      }));

      res.status(206).json({ // 206 Partial Content
        items: itemsWithoutMetrics,
        total: itemsWithoutMetrics.length,
        timestamp: new Date().toISOString(),
        fromCache: false,
        adapter: 'fallback',
        errors: 1,
        warning: 'Market data unavailable, returning watchlist items only'
      });
    } catch (fallbackError) {
      apiLogger.error({ error: fallbackError }, 'Fallback watchlist fetch also failed');
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: 'Failed to fetch watchlist items with metrics',
          details: [error instanceof Error ? error.message : 'Unknown error']
        }
      });
    }
  }
});

// GET /api/watchlist - Get all active watchlist items
router.get('/', async (req: Request, res: Response) => {
  try {
    const items = await watchlistService.findAll();
    apiLogger.info({ count: items.length }, 'Fetched watchlist items');
    res.status(200).json(items);
  } catch (error) {
    apiLogger.error({ error }, 'Failed to fetch watchlist items');
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Failed to fetch watchlist items',
        details: [error instanceof Error ? error.message : 'Unknown error']
      }
    });
  }
});

// GET /api/watchlist/filter/pinned - Get all pinned items
router.get('/filter/pinned', async (req: Request, res: Response) => {
  try {
    const items = await watchlistService.findPinned();
    apiLogger.info({ count: items.length }, 'Fetched pinned watchlist items');
    res.status(200).json(items);
  } catch (error) {
    apiLogger.error({ error }, 'Failed to fetch pinned watchlist items');
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Failed to fetch pinned watchlist items',
        details: [error instanceof Error ? error.message : 'Unknown error']
      }
    });
  }
});

// GET /api/watchlist/meta/stats - Get watchlist statistics
router.get('/meta/stats', async (req: Request, res: Response) => {
  try {
    const count = await watchlistService.countActive();
    const pinnedItems = await watchlistService.findPinned();
    
    res.status(200).json({
      totalItems: count,
      pinnedItems: pinnedItems.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    apiLogger.error({ error }, 'Failed to fetch watchlist stats');
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Failed to fetch watchlist stats',
        details: [error instanceof Error ? error.message : 'Unknown error']
      }
    });
  }
});

// GET /api/watchlist/meta/performance - Get performance analytics
router.get('/meta/performance', async (req: Request, res: Response) => {
  try {
    const marketDataAnalytics = marketDataService.getPerformanceAnalytics();
    const watchlistMetrics = watchlistService.getPerformanceMetrics();
    const healthStatus = await marketDataService.getHealthStatus();
    
    res.status(200).json({
      marketData: marketDataAnalytics,
      database: watchlistMetrics,
      health: healthStatus,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    apiLogger.error({ error }, 'Failed to fetch performance analytics');
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Failed to fetch performance analytics',
        details: [error instanceof Error ? error.message : 'Unknown error']
      }
    });
  }
});

// GET /api/watchlist/:id - Get single watchlist item
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const item = await watchlistService.findById(id);
    
    if (!item) {
      res.status(404).json({
        error: {
          code: 'ITEM_NOT_FOUND',
          message: WATCHLIST_ERRORS.ITEM_NOT_FOUND,
          details: [`Watchlist item with ID '${id}' not found`]
        }
      });
      return;
    }
    
    res.status(200).json(item);
  } catch (error) {
    apiLogger.error({ error, id: req.params.id }, 'Failed to fetch watchlist item');
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Failed to fetch watchlist item',
        details: [error instanceof Error ? error.message : 'Unknown error']
      }
    });
  }
});

// POST /api/watchlist - Create new watchlist item
router.post('/', async (req: Request, res: Response) => {
  try {
    // Validate request body
    const validationResult = createWatchlistItemSchema.safeParse(req.body);
    if (!validationResult.success) {
      res.status(400).json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request body',
          details: validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
        }
      });
      return;
    }

    const data = validationResult.data;

    // Check maximum items limit
    const itemCount = await watchlistService.countActive();
    if (itemCount >= WATCHLIST_LIMITS.MAX_ITEMS) {
      res.status(400).json({
        error: {
          code: 'MAX_ITEMS_REACHED',
          message: WATCHLIST_ERRORS.MAX_ITEMS_REACHED,
          details: [`Maximum ${WATCHLIST_LIMITS.MAX_ITEMS} items allowed`]
        }
      });
      return;
    }

    // Check if token already exists (active or inactive)
    const existing = await watchlistService.findByTokenAddress(data.tokenAddress);
    if (existing) {
      // If it's inactive (soft-deleted), reactivate it instead of creating a duplicate
      if (!existing.isActive) {
        const reactivated = await watchlistService.reactivate(existing.id, data);
        apiLogger.info({ id: reactivated.id }, 'Reactivated soft-deleted watchlist item');
        res.status(200).json(reactivated);
        return;
      }
      
      // If it's already active, return duplicate error
      res.status(409).json({
        error: {
          code: 'DUPLICATE_TOKEN',
          message: WATCHLIST_ERRORS.DUPLICATE_TOKEN,
          details: [`Token address '${data.tokenAddress}' already exists in watchlist`]
        }
      });
      return;
    }

    // Create the item
    const item = await watchlistService.create(data);
    apiLogger.info({ tokenAddress: data.tokenAddress }, 'Created watchlist item');
    res.status(201).json(item);
  } catch (error) {
    apiLogger.error({ error }, 'Failed to create watchlist item');
    
    // Check for duplicate key error
    if (error instanceof Error && error.message.includes('already exists')) {
      res.status(409).json({
        error: {
          code: 'DUPLICATE_TOKEN',
          message: WATCHLIST_ERRORS.DUPLICATE_TOKEN,
          details: ['Token already exists in watchlist']
        }
      });
      return;
    }
    
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Failed to create watchlist item',
        details: [error instanceof Error ? error.message : 'Unknown error']
      }
    });
  }
});

// PATCH /api/watchlist/:id - Update watchlist item
router.patch('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Validate request body
    const validationResult = updateWatchlistItemSchema.safeParse(req.body);
    if (!validationResult.success) {
      res.status(400).json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request body',
          details: validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
        }
      });
      return;
    }

    const data = validationResult.data;

    // Check pin limits if trying to pin
    if (data.isPinned === true) {
      const currentItem = await watchlistService.findById(id);
      if (currentItem && !currentItem.isPinned) {
        const pinnedItems = await watchlistService.findPinned();
        if (pinnedItems.length >= WATCHLIST_LIMITS.MAX_PINNED) {
          res.status(400).json({
            error: {
              code: 'MAX_PINNED_REACHED',
              message: WATCHLIST_ERRORS.MAX_PINNED_REACHED,
              details: [`Maximum ${WATCHLIST_LIMITS.MAX_PINNED} pinned items allowed`]
            }
          });
          return;
        }
      }
    }

    // Update the item
    const item = await watchlistService.update(id, data);
    apiLogger.info({ id, updates: data }, 'Updated watchlist item');
    res.status(200).json(item);
  } catch (error) {
    apiLogger.error({ error, id: req.params.id }, 'Failed to update watchlist item');
    
    // Check for not found error
    if (error instanceof Error && error.message.includes('not found')) {
      res.status(404).json({
        error: {
          code: 'ITEM_NOT_FOUND',
          message: WATCHLIST_ERRORS.ITEM_NOT_FOUND,
          details: [`Watchlist item with ID '${req.params.id}' not found`]
        }
      });
      return;
    }
    
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Failed to update watchlist item',
        details: [error instanceof Error ? error.message : 'Unknown error']
      }
    });
  }
});

// DELETE /api/watchlist/:id - Soft delete watchlist item
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    await watchlistService.softDelete(id);
    apiLogger.info({ id }, 'Soft deleted watchlist item');
    res.status(204).send();
  } catch (error) {
    apiLogger.error({ error, id: req.params.id }, 'Failed to delete watchlist item');
    
    // Check for not found error
    if (error instanceof Error && error.message.includes('not found')) {
      res.status(404).json({
        error: {
          code: 'ITEM_NOT_FOUND',
          message: WATCHLIST_ERRORS.ITEM_NOT_FOUND,
          details: [`Watchlist item with ID '${req.params.id}' not found`]
        }
      });
      return;
    }
    
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Failed to delete watchlist item',
        details: [error instanceof Error ? error.message : 'Unknown error']
      }
    });
  }
});

// PATCH /api/watchlist/:id/pin - Toggle pin status
router.patch('/:id/pin', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Check if trying to pin and would exceed limit
    const item = await watchlistService.findById(id);
    if (!item) {
      res.status(404).json({
        error: {
          code: 'ITEM_NOT_FOUND',
          message: WATCHLIST_ERRORS.ITEM_NOT_FOUND,
          details: [`Watchlist item with ID '${id}' not found`]
        }
      });
      return;
    }
    
    // If unpinned item is being pinned, check limits
    if (!item.isPinned) {
      const pinnedItems = await watchlistService.findPinned();
      if (pinnedItems.length >= WATCHLIST_LIMITS.MAX_PINNED) {
        res.status(400).json({
          error: {
            code: 'MAX_PINNED_REACHED',
            message: WATCHLIST_ERRORS.MAX_PINNED_REACHED,
            details: [`Maximum ${WATCHLIST_LIMITS.MAX_PINNED} pinned items allowed`]
          }
        });
        return;
      }
    }
    
    const updatedItem = await watchlistService.togglePin(id);
    apiLogger.info({ id, isPinned: updatedItem.isPinned }, 'Toggled watchlist item pin status');
    res.status(200).json(updatedItem);
  } catch (error) {
    apiLogger.error({ error, id: req.params.id }, 'Failed to toggle pin status');
    
    // Check for not found error
    if (error instanceof Error && error.message.includes('not found')) {
      res.status(404).json({
        error: {
          code: 'ITEM_NOT_FOUND',
          message: WATCHLIST_ERRORS.ITEM_NOT_FOUND,
          details: [`Watchlist item with ID '${req.params.id}' not found`]
        }
      });
      return;
    }
    
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Failed to toggle pin status',
        details: [error instanceof Error ? error.message : 'Unknown error']
      }
    });
  }
});


// POST /api/watchlist/bulk - Bulk import tokens
router.post('/bulk', async (req: Request, res: Response) => {
  try {
    // Handle both direct tokens array and wrapped format
    let tokensToProcess: Array<{
      tokenAddress: string;
      tokenSymbol?: string;
      tokenName?: string;
      customName?: string;
      notes?: string;
    }>;

    // Support both formats: { tokens: [...] } and raw string input
    if (typeof req.body === 'string') {
      // Parse multiline string input
      tokensToProcess = parseBulkInput(req.body);
    } else if (req.body.tokens) {
      // Validate using schema
      const validationResult = bulkImportSchema.safeParse(req.body);
      if (!validationResult.success) {
        res.status(400).json({
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid request body',
            details: validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
          }
        });
        return;
      }
      tokensToProcess = validationResult.data.tokens;
    } else {
      res.status(400).json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid input format. Expected { tokens: [...] } or multiline string',
          details: ['Request body must contain tokens array or be a multiline string']
        }
      });
      return;
    }

    if (tokensToProcess.length === 0) {
      res.status(400).json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'No tokens provided for import',
          details: ['At least one token must be provided']
        }
      });
      return;
    }

    if (tokensToProcess.length > 50) {
      res.status(400).json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Too many tokens. Maximum 50 tokens per bulk import',
          details: [`Provided ${tokensToProcess.length} tokens, maximum is 50`]
        }
      });
      return;
    }

    // Process tokens and fill in default values - filter out invalid addresses
    const processedTokens = tokensToProcess
      .filter(token => token.tokenAddress && token.tokenAddress.length >= 32)
      .map(token => ({
        tokenAddress: token.tokenAddress!,
        tokenSymbol: token.tokenSymbol || 'Unknown',
        tokenName: token.tokenName || token.tokenSymbol || 'Unknown Token',
        customName: token.customName,
        notes: token.notes
      }));

    // Perform bulk import
    const result = await watchlistService.createBulk(processedTokens);

    apiLogger.info({
      total: result.summary.total,
      successful: result.summary.successful,
      failed: result.summary.failed,
      skippedDuplicates: result.summary.skippedDuplicates
    }, 'Completed bulk import');

    // Return appropriate status code
    const statusCode = result.summary.failed > 0 ? 207 : 201; // 207 Multi-Status if partial success

    res.status(statusCode).json({
      data: result,
      message: `Bulk import completed: ${result.summary.successful} successful, ${result.summary.failed} failed, ${result.summary.skippedDuplicates} duplicates skipped`
    });
  } catch (error) {
    apiLogger.error({ error }, 'Failed to process bulk import');
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Failed to process bulk import',
        details: [error instanceof Error ? error.message : 'Unknown error']
      }
    });
  }
});

// Helper function to parse bulk input string
function parseBulkInput(input: string): Array<{
  tokenAddress?: string;
  tokenSymbol?: string;
  tokenName?: string;
  customName?: string;
}> {
  const lines = input.split('\n').filter(line => line.trim());
  const tokens: Array<{
    tokenAddress?: string;
    tokenSymbol?: string;
    tokenName?: string;
    customName?: string;
  }> = [];

  for (const line of lines) {
    const trimmedLine = line.trim();
    if (!trimmedLine) continue;

    let tokenAddress = '';
    let tokenSymbol: string | undefined;
    let tokenName: string | undefined;

    // Support multiple formats:
    // 1. Address only: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    // 2. Address with pipe: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v|USDC"
    // 3. Address with comma: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v,USDC"

    if (trimmedLine.includes('|')) {
      const parts = trimmedLine.split('|');
      tokenAddress = parts[0].trim();
      if (parts.length > 1) {
        tokenSymbol = parts[1].trim();
        tokenName = tokenSymbol;
      }
    } else if (trimmedLine.includes(',')) {
      const parts = trimmedLine.split(',');
      tokenAddress = parts[0].trim();
      if (parts.length > 1) {
        tokenSymbol = parts[1].trim();
        tokenName = tokenSymbol;
      }
    } else {
      tokenAddress = trimmedLine;
    }

    // Basic validation - more detailed validation happens in service
    if (tokenAddress.length >= 32 && tokenAddress.length <= 44) {
      tokens.push({
        tokenAddress,
        tokenSymbol,
        tokenName,
        customName: tokenSymbol !== tokenName ? tokenSymbol : undefined
      });
    }
  }

  return tokens;
}

export default router;