import { Router } from 'express';
import { z } from 'zod';
import { validateSchema } from '../middleware/validation';
import { positionTrackingService, type ExitStrategy, type PriceAlert } from '../services/PositionTrackingService';
import { apiLogger } from '../lib/logger';

const router = Router();

// Validation schemas
const createPositionSchema = z.object({
  userPublicKey: z.string(),
  inputMint: z.string(),
  outputMint: z.string(),
  entryTransaction: z.string(),
  entryPrice: z.string(),
  entryAmount: z.string(),
  outputAmount: z.string(),
  mevProtectionUsed: z.string(),
  totalFeePaid: z.number(),
  priceImpactAtEntry: z.string(),
  route: z.array(z.string()),
  exitStrategy: z.object({
    type: z.enum(['stop_loss', 'take_profit', 'trailing_stop', 'time_based']).optional(),
    trigger: z.object({
      stopLossPercentage: z.number().optional(),
      takeProfitPercentage: z.number().optional(),
      trailingStopPercentage: z.number().optional(),
      timeBasedExit: z.string().transform((str) => new Date(str)).optional(),
      priceTarget: z.string().optional(),
    }).optional(),
  }).optional(),
});

const updateExitStrategySchema = z.object({
  type: z.enum(['stop_loss', 'take_profit', 'trailing_stop', 'time_based']).optional(),
  trigger: z.object({
    stopLossPercentage: z.number().optional(),
    takeProfitPercentage: z.number().optional(),
    trailingStopPercentage: z.number().optional(),
    timeBasedExit: z.string().transform((str) => new Date(str)).optional(),
    priceTarget: z.string().optional(),
  }).optional(),
  isActive: z.boolean().optional(),
});

const addAlertSchema = z.object({
  type: z.enum(['price_above', 'price_below', 'percentage_change']),
  threshold: z.string(),
  isActive: z.boolean().default(true),
});

const closePositionSchema = z.object({
  exitTransaction: z.string().optional(),
});

/**
 * POST /api/positions
 * Create a new position for tracking
 */
router.post('/', validateSchema({ body: createPositionSchema }), async (req, res) => {
  try {
    const position = await positionTrackingService.createPosition(req.body);
    
    apiLogger.info({
      positionId: position.id,
      userPublicKey: req.body.userPublicKey,
      inputMint: req.body.inputMint,
      outputMint: req.body.outputMint,
    }, 'Position created via API');

    res.status(201).json({
      success: true,
      data: position,
    });
  } catch (error) {
    apiLogger.error({ error, body: req.body }, 'Failed to create position');
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create position',
    });
  }
});

/**
 * GET /api/positions/:id
 * Get a specific position by ID
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const position = positionTrackingService.getPosition(id);

    if (!position) {
      return res.status(404).json({
        success: false,
        error: 'Position not found',
      });
    }

    res.json({
      success: true,
      data: position,
    });
  } catch (error) {
    apiLogger.error({ error, positionId: req.params.id }, 'Failed to get position');
    res.status(500).json({
      success: false,
      error: 'Failed to get position',
    });
  }
});

/**
 * GET /api/positions/user/:userPublicKey
 * Get all positions for a user
 */
router.get('/user/:userPublicKey', async (req, res) => {
  try {
    const { userPublicKey } = req.params;
    const { status, limit, offset } = req.query;

    let positions = positionTrackingService.getUserPositions(userPublicKey);

    // Filter by status if provided
    if (status && typeof status === 'string') {
      positions = positions.filter(p => p.status === status);
    }

    // Apply pagination
    const limitNum = limit ? parseInt(limit as string, 10) : 50;
    const offsetNum = offset ? parseInt(offset as string, 10) : 0;
    
    const paginatedPositions = positions.slice(offsetNum, offsetNum + limitNum);

    res.json({
      success: true,
      data: {
        positions: paginatedPositions,
        pagination: {
          total: positions.length,
          limit: limitNum,
          offset: offsetNum,
          hasMore: offsetNum + limitNum < positions.length,
        },
      },
    });
  } catch (error) {
    apiLogger.error({ error, userPublicKey: req.params.userPublicKey }, 'Failed to get user positions');
    res.status(500).json({
      success: false,
      error: 'Failed to get user positions',
    });
  }
});

/**
 * GET /api/positions/user/:userPublicKey/summary
 * Get position summary for a user
 */
router.get('/user/:userPublicKey/summary', async (req, res) => {
  try {
    const { userPublicKey } = req.params;
    const summary = await positionTrackingService.getPositionSummary(userPublicKey);

    res.json({
      success: true,
      data: summary,
    });
  } catch (error) {
    apiLogger.error({ error, userPublicKey: req.params.userPublicKey }, 'Failed to get position summary');
    res.status(500).json({
      success: false,
      error: 'Failed to get position summary',
    });
  }
});

/**
 * PUT /api/positions/:id/exit-strategy
 * Update exit strategy for a position
 */
router.put('/:id/exit-strategy', validateSchema({ body: updateExitStrategySchema }), async (req, res) => {
  try {
    const { id } = req.params;
    const position = await positionTrackingService.updateExitStrategy(id, req.body);

    if (!position) {
      return res.status(404).json({
        success: false,
        error: 'Position not found',
      });
    }

    apiLogger.info({
      positionId: id,
      exitStrategy: position.exitStrategy,
    }, 'Exit strategy updated');

    res.json({
      success: true,
      data: position,
    });
  } catch (error) {
    apiLogger.error({ error, positionId: req.params.id, body: req.body }, 'Failed to update exit strategy');
    res.status(500).json({
      success: false,
      error: 'Failed to update exit strategy',
    });
  }
});

/**
 * POST /api/positions/:id/alerts
 * Add a price alert to a position
 */
router.post('/:id/alerts', validateSchema({ body: addAlertSchema }), async (req, res) => {
  try {
    const { id } = req.params;
    const position = await positionTrackingService.addPriceAlert(id, req.body);

    if (!position) {
      return res.status(404).json({
        success: false,
        error: 'Position not found',
      });
    }

    apiLogger.info({
      positionId: id,
      alertType: req.body.type,
      threshold: req.body.threshold,
    }, 'Price alert added');

    res.status(201).json({
      success: true,
      data: position,
    });
  } catch (error) {
    apiLogger.error({ error, positionId: req.params.id, body: req.body }, 'Failed to add price alert');
    res.status(500).json({
      success: false,
      error: 'Failed to add price alert',
    });
  }
});

/**
 * POST /api/positions/:id/close
 * Close a position
 */
router.post('/:id/close', validateSchema({ body: closePositionSchema }), async (req, res) => {
  try {
    const { id } = req.params;
    const { exitTransaction } = req.body;
    
    const position = await positionTrackingService.closePosition(id, exitTransaction);

    if (!position) {
      return res.status(404).json({
        success: false,
        error: 'Position not found',
      });
    }

    apiLogger.info({
      positionId: id,
      exitTransaction,
      holdingTime: Date.now() - position.entryTimestamp.getTime(),
    }, 'Position closed');

    res.json({
      success: true,
      data: position,
    });
  } catch (error) {
    apiLogger.error({ error, positionId: req.params.id, body: req.body }, 'Failed to close position');
    res.status(500).json({
      success: false,
      error: 'Failed to close position',
    });
  }
});

/**
 * GET /api/positions/active
 * Get all active positions (admin endpoint)
 */
router.get('/active', async (req, res) => {
  try {
    const { limit, userPublicKey } = req.query;
    
    let activePositions = positionTrackingService.getActivePositions();

    // Filter by user if provided
    if (userPublicKey && typeof userPublicKey === 'string') {
      activePositions = activePositions.filter(p => p.userPublicKey === userPublicKey);
    }

    // Apply limit
    if (limit && typeof limit === 'string') {
      const limitNum = parseInt(limit, 10);
      activePositions = activePositions.slice(0, limitNum);
    }

    res.json({
      success: true,
      data: {
        positions: activePositions,
        total: activePositions.length,
      },
    });
  } catch (error) {
    apiLogger.error({ error, query: req.query }, 'Failed to get active positions');
    res.status(500).json({
      success: false,
      error: 'Failed to get active positions',
    });
  }
});

/**
 * GET /api/positions/monitoring/stats
 * Get monitoring statistics (admin endpoint)
 */
router.get('/monitoring/stats', async (req, res) => {
  try {
    const stats = positionTrackingService.getMonitoringStats();

    res.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    apiLogger.error({ error }, 'Failed to get monitoring stats');
    res.status(500).json({
      success: false,
      error: 'Failed to get monitoring stats',
    });
  }
});

/**
 * GET /api/positions/alerts/active
 * Get all active alerts across positions
 */
router.get('/alerts/active', async (req, res) => {
  try {
    const { userPublicKey } = req.query;
    
    const activePositions = userPublicKey 
      ? positionTrackingService.getUserPositions(userPublicKey as string).filter(p => p.status === 'active')
      : positionTrackingService.getActivePositions();

    const activeAlerts = activePositions
      .flatMap(position => 
        (position.alerts || [])
          .filter(alert => alert.isActive && !alert.triggered)
          .map(alert => ({
            ...alert,
            positionId: position.id,
            userPublicKey: position.userPublicKey,
            tokenPair: `${position.inputMint}/${position.outputMint}`,
          }))
      )
      .sort((a, b) => new Date(b.positionId).getTime() - new Date(a.positionId).getTime());

    res.json({
      success: true,
      data: {
        alerts: activeAlerts,
        total: activeAlerts.length,
      },
    });
  } catch (error) {
    apiLogger.error({ error, query: req.query }, 'Failed to get active alerts');
    res.status(500).json({
      success: false,
      error: 'Failed to get active alerts',
    });
  }
});

/**
 * GET /api/positions/alerts/triggered
 * Get recently triggered alerts
 */
router.get('/alerts/triggered', async (req, res) => {
  try {
    const { userPublicKey, hours } = req.query;
    const hoursBack = hours ? parseInt(hours as string, 10) : 24; // Default 24 hours
    const cutoffTime = new Date(Date.now() - hoursBack * 60 * 60 * 1000);
    
    const positions = userPublicKey 
      ? positionTrackingService.getUserPositions(userPublicKey as string)
      : positionTrackingService.getActivePositions();

    const triggeredAlerts = positions
      .flatMap(position => 
        (position.alerts || [])
          .filter(alert => alert.triggered && alert.triggeredAt && alert.triggeredAt >= cutoffTime)
          .map(alert => ({
            ...alert,
            positionId: position.id,
            userPublicKey: position.userPublicKey,
            tokenPair: `${position.inputMint}/${position.outputMint}`,
          }))
      )
      .sort((a, b) => (b.triggeredAt?.getTime() || 0) - (a.triggeredAt?.getTime() || 0));

    res.json({
      success: true,
      data: {
        alerts: triggeredAlerts,
        total: triggeredAlerts.length,
        timeRange: `${hoursBack} hours`,
      },
    });
  } catch (error) {
    apiLogger.error({ error, query: req.query }, 'Failed to get triggered alerts');
    res.status(500).json({
      success: false,
      error: 'Failed to get triggered alerts',
    });
  }
});

export { router as positionsRoutes };