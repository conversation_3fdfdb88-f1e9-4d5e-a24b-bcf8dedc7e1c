import { Router, Request, Response } from 'express';
import { checkDatabaseConnection } from '../lib/database';
import { checkRedisConnection } from '../lib/redis';
import { config } from '../lib/config';
import { apiLogger } from '../lib/logger';
import { HealthCheckResponse } from '@shared/types';

const router = Router();

// Basic health check
router.get('/', async (req, res) => {
  try {
    const uptime = process.uptime();
    
    const healthResponse: HealthCheckResponse = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: Math.floor(uptime),
      version: '1.0.0'
    };

    apiLogger.info({ uptime }, 'Health check requested');
    res.status(200).json(healthResponse);
  } catch (error) {
    apiLogger.error({ error }, 'Health check failed');
    
    const healthResponse: HealthCheckResponse = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: Math.floor(process.uptime()),
      version: '1.0.0'
    };

    res.status(503).json(healthResponse);
  }
});

// Database health check
router.get('/db', async (req, res) => {
  try {
    const isConnected = await checkDatabaseConnection();
    
    if (isConnected) {
      res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'database',
        connection: 'connected'
      });
    } else {
      res.status(503).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        service: 'database',
        connection: 'disconnected'
      });
    }
  } catch (error) {
    apiLogger.error({ error }, 'Database health check failed');
    
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      service: 'database',
      connection: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Redis health check
router.get('/redis', async (req, res) => {
  try {
    const isConnected = await checkRedisConnection();
    
    if (isConnected) {
      res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'redis',
        connection: 'connected'
      });
    } else {
      res.status(503).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        service: 'redis',
        connection: 'disconnected'
      });
    }
  } catch (error) {
    apiLogger.error({ error }, 'Redis health check failed');
    
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      service: 'redis',
      connection: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// External services health check
router.get('/services', async (req: Request, res: Response) => {
  try {
    const services = {
      helius: 'healthy', // Would ping actual service in production
      jupiter: 'healthy', // Would ping actual service in production
    };

    res.status(200).json({
      status: 'ok',
      services,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    apiLogger.error({ error }, 'External services health check failed');
    
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      services: {
        helius: 'error',
        jupiter: 'error',
        coinmarketcap: 'error',
      },
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Comprehensive health check
router.get('/all', async (req: Request, res: Response) => {
  try {
    const [dbHealth, redisHealth] = await Promise.allSettled([
      checkDatabaseConnection(),
      checkRedisConnection()
    ]);

    const dbStatus = dbHealth.status === 'fulfilled' && dbHealth.value ? 'connected' : 'disconnected';
    const redisStatus = redisHealth.status === 'fulfilled' && redisHealth.value ? 'connected' : 'disconnected';
    
    const allHealthy = dbStatus === 'connected' && redisStatus === 'connected';
    
    const healthResponse: HealthCheckResponse = {
      status: allHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: Math.floor(process.uptime()),
      version: '1.0.0',
      services: {
        database: dbStatus as 'connected' | 'disconnected',
        redis: redisStatus as 'connected' | 'disconnected'
      }
    };

    const statusCode = allHealthy ? 200 : 503;
    res.status(statusCode).json(healthResponse);
  } catch (error) {
    apiLogger.error({ error }, 'Comprehensive health check failed');
    
    const healthResponse: HealthCheckResponse = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: Math.floor(process.uptime()),
      version: '1.0.0',
      services: {
        database: 'error',
        redis: 'error'
      }
    };

    res.status(503).json(healthResponse);
  }
});

export default router;