const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

async function main() {
  try {
    // Test connection
    const result = await prisma.$queryRaw`SELECT current_database(), current_schema()`;
    console.log('Connected successfully:', result);
    
    // Try to count watchlist items
    const count = await prisma.watchlistItem.count();
    console.log('Watchlist items count:', count);
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

main();