/**
 * Helius RPC API Mock Service
 * Provides mock responses for testing Solana RPC integration
 */

export interface HeliusTransactionResponse {
  jsonrpc: string;
  result: string;
  id: number;
}

export interface HeliusAccountResponse {
  jsonrpc: string;
  result: {
    context: {
      slot: number;
    };
    value: {
      data: string[];
      executable: boolean;
      lamports: number;
      owner: string;
      rentEpoch: number;
    } | null;
  };
  id: number;
}

export interface HeliusBalanceResponse {
  jsonrpc: string;
  result: {
    context: {
      slot: number;
    };
    value: number;
  };
  id: number;
}

export class MockHeliusService {
  private static currentSlot = *********;
  
  /**
   * Generate mock transaction signature
   */
  private static generateSignature(): string {
    const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz123456789';
    let result = '';
    for (let i = 0; i < 88; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
  
  /**
   * Mock sendTransaction RPC call
   */
  static sendTransaction(params: {
    transaction: string;
    options?: any;
  }): HeliusTransactionResponse {
    const signature = this.generateSignature();
    
    return {
      jsonrpc: '2.0',
      result: signature,
      id: 1,
    };
  }
  
  /**
   * Mock getAccountInfo RPC call
   */
  static getAccountInfo(params: {
    address: string;
    commitment?: string;
  }): HeliusAccountResponse {
    const { address } = params;
    
    // Mock different account types
    if (address === 'So11111111111111111111111111111111111111112') {
      // SOL mint account
      return {
        jsonrpc: '2.0',
        result: {
          context: {
            slot: this.currentSlot++,
          },
          value: {
            data: ['', 'base64'],
            executable: false,
            lamports: **********, // 1 SOL
            owner: '11111111111111111111111111111111',
            rentEpoch: 300,
          },
        },
        id: 1,
      };
    }
    
    // Mock token account
    return {
      jsonrpc: '2.0',
      result: {
        context: {
          slot: this.currentSlot++,
        },
        value: {
          data: [
            Buffer.from(JSON.stringify({
              mint: '4zMMC9srt5Ri5X14GAgXhaHii3GnPAEERYPJgZJDncDU',
              owner: address,
              amount: '1000000', // 1 USDC (6 decimals)
              delegateOption: 0,
              delegate: null,
              state: 1,
              isNativeOption: 0,
              isNative: 0,
              delegatedAmount: 0,
              closeAuthorityOption: 0,
              closeAuthority: null,
            })).toString('base64'),
            'base64'
          ],
          executable: false,
          lamports: 2039280,
          owner: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
          rentEpoch: 300,
        },
      },
      id: 1,
    };
  }
  
  /**
   * Mock getBalance RPC call
   */
  static getBalance(params: {
    address: string;
    commitment?: string;
  }): HeliusBalanceResponse {
    // Return random balance between 0.1 and 10 SOL
    const balance = Math.floor((Math.random() * 9.9 + 0.1) * **********);
    
    return {
      jsonrpc: '2.0',
      result: {
        context: {
          slot: this.currentSlot++,
        },
        value: balance,
      },
      id: 1,
    };
  }
  
  /**
   * Mock getTransaction RPC call
   */
  static getTransaction(params: {
    signature: string;
    commitment?: string;
  }) {
    return {
      jsonrpc: '2.0',
      result: {
        slot: this.currentSlot - Math.floor(Math.random() * 100),
        transaction: {
          signatures: [params.signature],
          message: {
            accountKeys: [
              'mock-account-1',
              'mock-account-2',
              'mock-program',
            ],
            recentBlockhash: 'mock-blockhash',
            instructions: [
              {
                programIdIndex: 2,
                accounts: [0, 1],
                data: 'mock-instruction-data',
              },
            ],
          },
        },
        meta: {
          err: null,
          fee: 5000,
          preBalances: [**********, 0, **********],
          postBalances: [*********, *********, **********],
          innerInstructions: [],
          logMessages: [
            'Program mock-program invoke [1]',
            'Program mock-program success',
          ],
        },
        blockTime: Math.floor(Date.now() / 1000),
      },
      id: 1,
    };
  }
  
  /**
   * Mock getLatestBlockhash RPC call
   */
  static getLatestBlockhash() {
    return {
      jsonrpc: '2.0',
      result: {
        context: {
          slot: this.currentSlot++,
        },
        value: {
          blockhash: this.generateSignature().substring(0, 44),
          lastValidBlockHeight: this.currentSlot + 150,
        },
      },
      id: 1,
    };
  }
  
  /**
   * Mock simulateTransaction RPC call
   */
  static simulateTransaction(params: {
    transaction: string;
    commitment?: string;
  }) {
    // Simulate random failures 10% of the time
    if (Math.random() < 0.1) {
      return {
        jsonrpc: '2.0',
        result: {
          context: {
            slot: this.currentSlot,
          },
          value: {
            err: {
              InstructionError: [0, 'custom program error: 0x1']
            },
            logs: [
              'Program mock-program invoke [1]',
              'Program log: Error: Insufficient funds',
              'Program mock-program failed: custom program error: 0x1',
            ],
            unitsConsumed: 150000,
          },
        },
        id: 1,
      };
    }
    
    return {
      jsonrpc: '2.0',
      result: {
        context: {
          slot: this.currentSlot,
        },
        value: {
          err: null,
          logs: [
            'Program mock-program invoke [1]',
            'Program log: Instruction: Transfer',
            'Program mock-program success',
          ],
          unitsConsumed: 200000,
        },
      },
      id: 1,
    };
  }
}

/**
 * Express middleware to mock Helius RPC endpoints
 */
export const mockHeliusRpcHandler = (req: any, res: any) => {
  const { method, params, id = 1 } = req.body;
  
  // Simulate network delays
  const delay = Math.random() * 100 + 50; // 50-150ms
  
  setTimeout(() => {
    try {
      let result;
      
      switch (method) {
        case 'sendTransaction':
          result = MockHeliusService.sendTransaction(params);
          break;
          
        case 'getAccountInfo':
          result = MockHeliusService.getAccountInfo(params[0] ? { address: params[0] } : {});
          break;
          
        case 'getBalance':
          result = MockHeliusService.getBalance({ address: params[0] });
          break;
          
        case 'getTransaction':
          result = MockHeliusService.getTransaction({ signature: params[0] });
          break;
          
        case 'getLatestBlockhash':
          result = MockHeliusService.getLatestBlockhash();
          break;
          
        case 'simulateTransaction':
          result = MockHeliusService.simulateTransaction({ transaction: params[0] });
          break;
          
        default:
          return res.status(400).json({
            jsonrpc: '2.0',
            error: {
              code: -32601,
              message: `Method ${method} not found`,
            },
            id,
          });
      }
      
      res.json({ ...result, id });
    } catch (error) {
      res.status(500).json({
        jsonrpc: '2.0',
        error: {
          code: -32603,
          message: 'Internal error',
          data: error instanceof Error ? error.message : 'Unknown error',
        },
        id,
      });
    }
  }, delay);
};