/**
 * Mock Server Setup for External API Testing
 * Creates a mock HTTP server to intercept external API calls during testing
 */

import express from 'express';
import { createServer, Server } from 'http';
import { mockJupiterEndpoints } from './jupiter';
import { mockHeliusRpcHandler } from './helius';

export class MockExternalApiServer {
  private app: express.Application;
  private server: Server | null = null;
  private port: number;

  constructor(port = 3999) {
    this.port = port;
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware() {
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));
    
    // CORS for mock services
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, x-cmc_pro_api_key');
      
      if (req.method === 'OPTIONS') {
        res.sendStatus(200);
      } else {
        next();
      }
    });

    // Request logging for debugging
    this.app.use((req, res, next) => {
      console.log(`[MOCK API] ${req.method} ${req.path}`, req.query);
      next();
    });
  }

  private setupRoutes() {
    // Jupiter API endpoints (V6 legacy support)
    this.app.get('/v6/quote', mockJupiterEndpoints.quote);
    this.app.post('/v6/swap', mockJupiterEndpoints.swap);
    
    // Jupiter V1 API endpoints (new)
    this.app.get('/swap/v1/quote', mockJupiterEndpoints.quote);
    this.app.post('/swap/v1/swap', mockJupiterEndpoints.swap);

    // Helius RPC endpoint (generic RPC handler)
    this.app.post('/rpc', mockHeliusRpcHandler);
    this.app.post('/', mockHeliusRpcHandler); // Some RPC calls go to root


    // Health check for mock server
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'ok',
        server: 'mock-external-apis',
        timestamp: new Date().toISOString(),
        endpoints: {
          jupiter: [
            'GET /v6/quote (legacy)', 
            'POST /v6/swap (legacy)',
            'GET /swap/v1/quote (new)',
            'POST /swap/v1/swap (new)'
          ],
          helius: ['POST /rpc', 'POST /']
        }
      });
    });

    // Catch-all for unhandled endpoints
    this.app.use('*', (req, res) => {
      console.warn(`[MOCK API] Unhandled endpoint: ${req.method} ${req.path}`);
      res.status(404).json({
        error: 'Mock endpoint not found',
        method: req.method,
        path: req.path,
        available_endpoints: [
          'GET /v6/quote (Jupiter V6 - legacy)',
          'POST /v6/swap (Jupiter V6 - legacy)',
          'GET /swap/v1/quote (Jupiter V1 - new)',
          'POST /swap/v1/swap (Jupiter V1 - new)',
          'POST /rpc (Helius)',
          'GET /health'
        ]
      });
    });
  }

  /**
   * Start the mock server
   */
  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.server = createServer(this.app);
      
      this.server.listen(this.port, () => {
        console.log(`🎭 Mock External API Server running on http://localhost:${this.port}`);
        resolve();
      });

      this.server.on('error', (error: any) => {
        if (error.code === 'EADDRINUSE') {
          console.warn(`⚠️ Port ${this.port} in use, trying ${this.port + 1}`);
          this.port += 1;
          this.server?.close();
          setTimeout(() => this.start().then(resolve).catch(reject), 100);
        } else {
          reject(error);
        }
      });
    });
  }

  /**
   * Stop the mock server
   */
  async stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          console.log('🎭 Mock External API Server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  /**
   * Get the server URL
   */
  getUrl(): string {
    return `http://localhost:${this.port}`;
  }

  /**
   * Get the port number
   */
  getPort(): number {
    return this.port;
  }
}

// Global mock server instance for tests
let globalMockServer: MockExternalApiServer | null = null;

/**
 * Setup mock server for testing
 * Call this in test setup files
 */
export async function setupMockServer(): Promise<MockExternalApiServer> {
  if (!globalMockServer) {
    globalMockServer = new MockExternalApiServer();
    await globalMockServer.start();

    // Set environment variables to point to mock server (default to V1 API)
    process.env.JUPITER_API_URL = `${globalMockServer.getUrl()}/swap/v1`;
    process.env.HELIUS_RPC_URL = `${globalMockServer.getUrl()}/rpc`;
    process.env.MOCK_EXTERNAL_APIS = 'true';
  }
  
  return globalMockServer;
}

/**
 * Teardown mock server after testing
 * Call this in test cleanup files
 */
export async function teardownMockServer(): Promise<void> {
  if (globalMockServer) {
    await globalMockServer.stop();
    globalMockServer = null;
  }
}

/**
 * Get the global mock server instance
 */
export function getMockServer(): MockExternalApiServer | null {
  return globalMockServer;
}