/**
 * Jupiter Aggregator API Mock Service
 * Provides mock responses for testing Jupiter integration
 */

export interface JupiterQuoteResponse {
  inputMint: string;
  inAmount: string;
  outputMint: string;
  outAmount: string;
  otherAmountThreshold: string;
  swapMode: 'ExactIn' | 'ExactOut';
  slippageBps: number;
  platformFee?: {
    amount: string;
    feeBps: number;
  };
  priceImpactPct: string;
  routePlan: Array<{
    swapInfo: {
      ammKey: string;
      label: string;
      inputMint: string;
      outputMint: string;
      inAmount: string;
      outAmount: string;
      feeAmount: string;
      feeMint: string;
    };
    percent: number;
  }>;
}

export interface JupiterSwapResponse {
  swapTransaction: string;
  lastValidBlockHeight: number;
  prioritizationFeeLamports: number;
  // V1 API enhancements
  computeUnitLimit?: number;
  dynamicSlippageReport?: {
    slippageBps: number;
    otherAmountThreshold: string;
  };
}

export class MockJupiterService {
  private static readonly SOL_MINT = 'So11111111111111111111111111111111111111112';
  private static readonly USDC_MINT = '4zMMC9srt5Ri5X14GAgXhaHii3GnPAEERYPJgZJDncDU';
  
  /**
   * Generate mock quote response
   */
  static generateQuoteResponse(params: {
    inputMint: string;
    outputMint: string;
    amount: number;
    slippageBps?: number;
  }): JupiterQuoteResponse {
    const { inputMint, outputMint, amount, slippageBps = 100 } = params;
    
    // Simulate different conversion rates based on tokens
    let conversionRate: number;
    if (inputMint === this.SOL_MINT && outputMint === this.USDC_MINT) {
      conversionRate = 150; // 1 SOL = 150 USDC (mock rate)
    } else if (inputMint === this.USDC_MINT && outputMint === this.SOL_MINT) {
      conversionRate = 0.0067; // 1 USDC = 0.0067 SOL
    } else {
      conversionRate = Math.random() * 1000000; // Random rate for other tokens
    }
    
    const outAmount = Math.floor(amount * conversionRate);
    const slippageAdjustment = 1 - (slippageBps / 10000);
    const minOutAmount = Math.floor(outAmount * slippageAdjustment);
    
    return {
      inputMint,
      inAmount: amount.toString(),
      outputMint,
      outAmount: outAmount.toString(),
      otherAmountThreshold: minOutAmount.toString(),
      swapMode: 'ExactIn',
      slippageBps,
      priceImpactPct: (Math.random() * 2).toFixed(2), // 0-2% price impact
      routePlan: [
        {
          swapInfo: {
            ammKey: `mock-amm-${Date.now()}`,
            label: 'Mock DEX',
            inputMint,
            outputMint,
            inAmount: amount.toString(),
            outAmount: outAmount.toString(),
            feeAmount: Math.floor(amount * 0.003).toString(), // 0.3% fee
            feeMint: inputMint,
          },
          percent: 100,
        },
      ],
    };
  }
  
  /**
   * Generate mock swap transaction response (V1 format)
   */
  static generateSwapResponse(isV1Api: boolean = true): JupiterSwapResponse {
    // Generate mock base64 transaction
    const mockTransaction = Buffer.from(
      JSON.stringify({
        transaction: 'mock-serialized-transaction',
        timestamp: Date.now(),
      })
    ).toString('base64');
    
    const response: JupiterSwapResponse = {
      swapTransaction: mockTransaction,
      lastValidBlockHeight: 200000000 + Math.floor(Math.random() * 1000),
      prioritizationFeeLamports: Math.floor(Math.random() * 10000) + 1000,
    };

    // Add V1 API specific fields
    if (isV1Api) {
      response.computeUnitLimit = Math.floor(Math.random() * 200000) + 100000;
      response.dynamicSlippageReport = {
        slippageBps: Math.floor(Math.random() * 100) + 50, // 50-150 bps
        otherAmountThreshold: Math.floor(Math.random() * 1000000).toString(),
      };
    }

    return response;
  }
  
  /**
   * Simulate API errors
   */
  static generateErrorResponse(type: 'quote' | 'swap' | 'timeout' | 'rate_limit') {
    const errors = {
      quote: {
        error: 'No routes found',
        message: 'No routes found for the given input and output tokens',
      },
      swap: {
        error: 'Transaction failed',
        message: 'Failed to create swap transaction',
      },
      timeout: {
        error: 'Request timeout',
        message: 'Request timed out after 10 seconds',
      },
      rate_limit: {
        error: 'Rate limit exceeded',
        message: 'Too many requests, please try again later',
      },
    };
    
    return errors[type];
  }
  
  /**
   * Mock realistic token data
   */
  static getTokenData(mint: string) {
    const tokens: Record<string, any> = {
      [this.SOL_MINT]: {
        address: this.SOL_MINT,
        symbol: 'SOL',
        name: 'Solana',
        decimals: 9,
        logoURI: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png'
      },
      [this.USDC_MINT]: {
        address: this.USDC_MINT,
        symbol: 'USDC',
        name: 'USD Coin',
        decimals: 6,
        logoURI: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v/logo.png'
      },
    };
    
    return tokens[mint] || {
      address: mint,
      symbol: 'UNKNOWN',
      name: 'Unknown Token',
      decimals: 6,
      logoURI: null
    };
  }
}

/**
 * Express middleware to mock Jupiter API endpoints
 */
export const mockJupiterEndpoints = {
  quote: (req: any, res: any) => {
    const { inputMint, outputMint, amount, slippageBps } = req.query;
    
    // Simulate random failures 5% of the time
    if (Math.random() < 0.05) {
      return res.status(500).json(MockJupiterService.generateErrorResponse('quote'));
    }
    
    // Simulate rate limiting 2% of the time
    if (Math.random() < 0.02) {
      return res.status(429).json(MockJupiterService.generateErrorResponse('rate_limit'));
    }
    
    try {
      const quote = MockJupiterService.generateQuoteResponse({
        inputMint,
        outputMint,
        amount: parseInt(amount),
        slippageBps: slippageBps ? parseInt(slippageBps) : undefined,
      });
      
      res.json(quote);
    } catch (error) {
      res.status(400).json(MockJupiterService.generateErrorResponse('quote'));
    }
  },
  
  swap: (req: any, res: any) => {
    const { quoteResponse, userPublicKey } = req.body;
    
    // Simulate random failures 3% of the time
    if (Math.random() < 0.03) {
      return res.status(500).json(MockJupiterService.generateErrorResponse('swap'));
    }
    
    if (!quoteResponse || !userPublicKey) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'quoteResponse and userPublicKey are required',
      });
    }
    
    try {
      const swapResponse = MockJupiterService.generateSwapResponse(true); // V1 API
      res.json(swapResponse);
    } catch (error) {
      res.status(500).json(MockJupiterService.generateErrorResponse('swap'));
    }
  },
};