/**
 * Mock Services Index
 * Exports all mock services and utilities for external API testing
 */

// Mock service classes
export { MockJupiterService } from './jupiter';
export { MockHeliusService } from './helius';

// Mock server setup
export { 
  MockExternalApiServer,
  setupMockServer,
  teardownMockServer,
  getMockServer
} from './server';

// Express middleware exports
export { mockJupiterEndpoints } from './jupiter';
export { mockHeliusRpcHandler } from './helius';

// Mock data generators
export const MockDataGenerators = {
  /**
   * Generate a realistic SOL address
   */
  solanaAddress: (): string => {
    const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz123456789';
    let result = '';
    for (let i = 0; i < 44; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  /**
   * Generate a realistic transaction signature
   */
  transactionSignature: (): string => {
    const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz123456789';
    let result = '';
    for (let i = 0; i < 88; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  /**
   * Generate mock token metadata
   */
  tokenMetadata: (overrides: Partial<any> = {}) => ({
    address: MockDataGenerators.solanaAddress(),
    symbol: `TOKEN${Math.floor(Math.random() * 1000)}`,
    name: `Mock Token ${Math.floor(Math.random() * 1000)}`,
    decimals: 6,
    logoURI: null,
    ...overrides,
  }),

  /**
   * Generate mock position data
   */
  position: (overrides: Partial<any> = {}) => ({
    id: `position-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
    tokenAddress: MockDataGenerators.solanaAddress(),
    tokenSymbol: `TOKEN${Math.floor(Math.random() * 1000)}`,
    tokenName: `Mock Token ${Math.floor(Math.random() * 1000)}`,
    entryPrice: Math.random() * 0.001,
    currentPrice: Math.random() * 0.001,
    quantity: Math.floor(Math.random() * 1000000) + 100000,
    entryAmountSol: Math.random() * 10 + 0.1,
    currentValueSol: Math.random() * 12 + 0.05,
    pnlSol: (Math.random() - 0.5) * 5,
    pnlPercentage: (Math.random() - 0.5) * 50,
    status: ['ACTIVE', 'CLOSED', 'PENDING_EXIT'][Math.floor(Math.random() * 3)],
    entryTimestamp: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
    lastUpdateTimestamp: new Date(),
    transactionSignature: MockDataGenerators.transactionSignature(),
    slippage: Math.random() * 3 + 0.5,
    jupiterQuoteId: `quote-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
    ...overrides,
  }),

  /**
   * Generate mock trade data
   */
  trade: (overrides: Partial<any> = {}) => ({
    inputMint: 'So11111111111111111111111111111111111111112', // SOL
    outputMint: MockDataGenerators.solanaAddress(),
    amountIn: Math.floor(Math.random() * 1000000000) + 100000000, // 0.1 to 1 SOL in lamports
    amountOut: Math.floor(Math.random() * 10000000) + 1000000,
    slippageBps: Math.floor(Math.random() * 300) + 50, // 0.5% to 3%
    signature: MockDataGenerators.transactionSignature(),
    timestamp: new Date(),
    ...overrides,
  }),

  /**
   * Generate mock price data
   */
  priceData: (overrides: Partial<any> = {}) => ({
    symbol: `TOKEN${Math.floor(Math.random() * 1000)}`,
    price: Math.random() * 1000,
    priceChange24h: (Math.random() - 0.5) * 20,
    volume24h: Math.random() * 1000000,
    marketCap: Math.random() * 100000000,
    timestamp: new Date(),
    ...overrides,
  }),
};

// Test utilities
export const TestUtils = {
  /**
   * Wait for a specified amount of time
   */
  wait: (ms: number): Promise<void> => 
    new Promise(resolve => setTimeout(resolve, ms)),

  /**
   * Generate test environment variables
   */
  getTestEnv: () => ({
    NODE_ENV: 'test',
    LOG_LEVEL: 'error',
    SESSION_SECRET: 'test-session-secret-key-for-testing-only-32-chars-long',
    ADMIN_PASSWORD: 'test-admin-password',
    HELIUS_API_KEY: 'test-helius-key',
    WALLET_SECRET_KEY: 'test-wallet-secret-key',
    WALLET_PUBLIC_KEY: 'test-wallet-public-key',
    DEFAULT_SLIPPAGE_BPS: '100',
    MAX_POSITION_SIZE_SOL: '10',
    MOCK_EXTERNAL_APIS: 'true',
  }),

  /**
   * Clean up test data
   */
  cleanupTestData: async () => {
    try {
      const { prisma } = await import('../../src/lib/database');
      
      // Clean up in reverse dependency order
      await prisma.transaction.deleteMany();
      await prisma.exitStrategy.deleteMany();
      await prisma.position.deleteMany();
      await prisma.watchlistItem.deleteMany();
      await prisma.priceSnapshot.deleteMany();
      await prisma.jobQueueState.deleteMany();
      await prisma.pollingConfig.deleteMany();
    } catch (error) {
      console.warn('Test cleanup warning:', error);
    }
  },
};