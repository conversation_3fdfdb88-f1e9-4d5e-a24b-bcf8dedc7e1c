import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import dotenv from 'dotenv';
import { exec } from 'child_process';
import { promisify } from 'util';
import { setupMockServer, teardownMockServer } from './mocks';

const execAsync = promisify(exec);

// Load test environment variables
beforeAll(async () => {
  // Load test environment first
  dotenv.config({ path: '.env.test' });
  
  // Override environment for testing
  process.env.NODE_ENV = 'test';
  process.env.LOG_LEVEL = 'error'; // Minimize log output during tests
  process.env.DEV_SKIP_AUTH = 'false'; // Test authentication in tests
  
  // Test database configuration
  const testDbUrl = process.env.TEST_DATABASE_URL || 
    'postgresql://trader:dev_password_123@localhost:5432/trading_test';
  process.env.DATABASE_URL = testDbUrl;
  
  // Test Redis configuration (use separate Redis DB)
  process.env.REDIS_HOST = process.env.REDIS_HOST || 'localhost';
  process.env.REDIS_PORT = process.env.REDIS_PORT || '6379';
  process.env.REDIS_DB = '1'; // Use DB 1 for tests
  
  // Security configuration for tests
  process.env.SESSION_SECRET = 'test-session-secret-key-for-testing-only-32-chars-long';
  process.env.ADMIN_PASSWORD = 'test-admin-password';
  
  // Mock API keys for testing
  process.env.HELIUS_API_KEY = 'test-helius-key';
  process.env.COINMARKETCAP_API_KEY = 'test-cmc-key';
  process.env.WALLET_SECRET_KEY = 'test-wallet-secret-key';
  process.env.WALLET_PUBLIC_KEY = 'test-wallet-public-key';
  
  // Trading configuration for tests
  process.env.DEFAULT_SLIPPAGE_BPS = '100';
  process.env.MAX_POSITION_SIZE_SOL = '10';
  process.env.JOB_QUEUE_CONCURRENCY = '1';
  process.env.PRICE_MONITOR_INTERVAL_MS = '1000';
  
  console.log('🔧 Setting up test environment...');
  
  try {
    // Setup mock external API server
    await setupMockServer();
    console.log('✅ Mock API server started');
    
    // Ensure test database exists and is migrated
    try {
      await execAsync('npx prisma db push --force-reset');
      console.log('✅ Test database schema deployed successfully');
    } catch (dbError) {
      console.log('ℹ️ Database schema deployment failed, trying alternative approach...');
      try {
        await execAsync('npx prisma generate && npx prisma db push');
        console.log('✅ Test database schema deployed with generate step');
      } catch (fallbackError) {
        console.warn('⚠️ Database schema deployment failed:', fallbackError);
      }
    }
  } catch (error) {
    console.warn('⚠️ Database migration failed (may need manual setup):', error);
  }
}, 60000);

afterAll(async () => {
  console.log('🧹 Cleaning up test environment...');
  
  try {
    // Clean up test data and connections
    const { disconnectDatabase } = await import('../src/lib/database');
    const { disconnectRedis } = await import('../src/lib/redis');
    
    await disconnectDatabase();
    await disconnectRedis();
    
    // Teardown mock server
    await teardownMockServer();
    
    console.log('✅ Test cleanup completed');
  } catch (error) {
    console.warn('⚠️ Test cleanup warning:', error);
  }
}, 30000);

// Test isolation - clean data between tests
beforeEach(async () => {
  // Clean up any test data if needed
  try {
    const { prisma } = await import('../src/lib/database');
    
    // Clean up test data in reverse dependency order
    // Only clean up tables that exist and may have test data
    await prisma.transaction.deleteMany();
    await prisma.exitStrategy.deleteMany(); 
    await prisma.position.deleteMany();
    await prisma.watchlistItem.deleteMany();
    await prisma.priceSnapshot.deleteMany();
    await prisma.jobQueueState.deleteMany();
    await prisma.pollingConfig.deleteMany();
  } catch (error) {
    // Database might not be initialized yet, ignore cleanup errors
    console.log('ℹ️ Test data cleanup skipped (database not ready)');
  }
});

afterEach(async () => {
  // Additional cleanup if needed
});