import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import request from 'supertest';
import app from '../../../src/server';
import { prisma } from '../../../src/lib/database';

describe('Position API Endpoints', () => {
  let testPositionId: string;

  beforeAll(async () => {
    // Create a test position for testing
    const position = await prisma.position.create({
      data: {
        tokenAddress: '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',
        tokenSymbol: 'TEST',
        tokenName: 'Test Token',
        entryPrice: 0.00001,
        currentPrice: 0.00001,
        quantity: 1000000,
        entryAmountSol: 10,
        currentValueSol: 10,
        pnlSol: 0,
        pnlPercentage: 0,
        status: 'ACTIVE',
        entryTimestamp: new Date(),
        lastUpdateTimestamp: new Date(),
        transactionSignature: 'test-signature',
        slippage: 1,
        jupiterQuoteId: 'test-quote',
      }
    });
    testPositionId = position.id;
  });

  afterAll(async () => {
    // Clean up test data
    if (testPositionId) {
      await prisma.position.delete({ where: { id: testPositionId } });
    }
  });

  describe('GET /api/positions', () => {
    it('should return list of positions', async () => {
      const response = await request(app)
        .get('/api/positions')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          positions: expect.any(Array),
          pagination: expect.objectContaining({
            page: expect.any(Number),
            limit: expect.any(Number),
            total: expect.any(Number),
            pages: expect.any(Number)
          })
        }),
        timestamp: expect.any(String)
      });
    });

    it('should filter positions by status', async () => {
      const response = await request(app)
        .get('/api/positions?status=ACTIVE')
        .expect(200);

      expect(response.body.success).toBe(true);
      if (response.body.data.positions.length > 0) {
        response.body.data.positions.forEach((position: any) => {
          expect(position.status).toBe('ACTIVE');
        });
      }
    });

    it('should support pagination', async () => {
      const response = await request(app)
        .get('/api/positions?page=1&limit=5')
        .expect(200);

      expect(response.body.data.pagination).toMatchObject({
        page: 1,
        limit: 5,
        total: expect.any(Number),
        pages: expect.any(Number)
      });
    });
  });

  describe('GET /api/positions/:id', () => {
    it('should return a specific position', async () => {
      const response = await request(app)
        .get(`/api/positions/${testPositionId}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          id: testPositionId,
          tokenAddress: '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',
          tokenSymbol: 'TEST',
          status: 'ACTIVE'
        }),
        timestamp: expect.any(String)
      });
    });

    it('should return 404 for non-existent position', async () => {
      const fakeId = '00000000-0000-0000-0000-000000000000';
      const response = await request(app)
        .get(`/api/positions/${fakeId}`)
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.any(String)
      });
    });

    it('should validate position ID format', async () => {
      const response = await request(app)
        .get('/api/positions/invalid-id')
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.any(String)
      });
    });
  });

  describe('PATCH /api/positions/:id', () => {
    it('should update position exit strategy', async () => {
      const updateRequest = {
        exitStrategy: {
          takeProfitTiers: [
            { percentage: 50, priceTarget: 0.00002 },
            { percentage: 50, priceTarget: 0.00003 }
          ],
          stopLoss: {
            priceTarget: 0.000005,
            isTrailing: false
          }
        }
      };

      const response = await request(app)
        .patch(`/api/positions/${testPositionId}`)
        .send(updateRequest)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Position updated successfully',
        timestamp: expect.any(String)
      });
    });

    it('should validate update parameters', async () => {
      const invalidRequest = {
        exitStrategy: {
          takeProfitTiers: [
            { percentage: 150, priceTarget: -1 } // Invalid percentage and negative price
          ]
        }
      };

      const response = await request(app)
        .patch(`/api/positions/${testPositionId}`)
        .send(invalidRequest)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.any(String)
      });
    });
  });

  describe('DELETE /api/positions/:id', () => {
    it('should close position', async () => {
      const closeRequest = {
        percentage: 100,
        reason: 'Test close'
      };

      const response = await request(app)
        .delete(`/api/positions/${testPositionId}`)
        .send(closeRequest)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('Position close request submitted'),
        data: expect.objectContaining({
          positionId: testPositionId,
          percentage: 100,
          status: 'PENDING_EXIT'
        }),
        timestamp: expect.any(String)
      });
    });
  });
});