import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import { PrismaClient } from '@prisma/client';

// Integration test using a simplified approach
describe('Watchlist API Integration Tests', () => {
  let app: express.Application;
  let prisma: PrismaClient;
  
  beforeEach(async () => {
    // Create a fresh Express app for each test
    app = express();
    app.use(express.json());
    
    // Use in-memory database for testing (simplified)
    prisma = new PrismaClient({
      datasources: {
        db: {
          url: 'file:./test.db'
        }
      }
    });
    
    // Mock external dependencies
    vi.mock('../../../src/services/MarketDataService', () => ({
      MarketDataService: class {
        async fetchBatchData() {
          return {
            data: [],
            timestamp: new Date(),
            fromCache: false,
            adapter: 'test'
          };
        }
      }
    }));

    vi.mock('../../../src/services/metrics/StubMetricsAdapter', () => ({
      StubMetricsAdapter: class {}
    }));

    vi.mock('../../../src/services/metrics/MetricsAdapterInterface', () => ({
      adapterRegistry: {
        register: vi.fn()
      }
    }));

    // Import and setup routes
    const watchlistRoutes = (await import('../../../src/routes/watchlist')).default;
    app.use('/api/watchlist', watchlistRoutes);
  });

  afterEach(async () => {
    // Cleanup
    await prisma.$disconnect();
    vi.resetAllMocks();
  });

  describe('Basic CRUD Operations', () => {
    it('should handle GET /api/watchlist when empty', async () => {
      const response = await request(app)
        .get('/api/watchlist')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body).toHaveLength(0);
    });

    it('should validate token address format in POST', async () => {
      const invalidData = {
        tokenAddress: 'invalid',
        tokenSymbol: 'INV',
        tokenName: 'Invalid'
      };

      const response = await request(app)
        .post('/api/watchlist')
        .send(invalidData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code', 'VALIDATION_ERROR');
    });

    it('should handle bulk import validation errors', async () => {
      const response = await request(app)
        .post('/api/watchlist/bulk')
        .send({ tokens: [] })
        .expect(400);

      expect(response.body.error).toHaveProperty('code', 'VALIDATION_ERROR');
    });
  });

  describe('Route Accessibility', () => {
    it('should have accessible specific routes before parameterized routes', async () => {
      // Test that /filter/pinned is accessible (not caught by /:id)
      const response = await request(app)
        .get('/api/watchlist/filter/pinned')
        .expect(200);
      
      expect(Array.isArray(response.body)).toBe(true);
    });

    it('should have accessible meta/stats route', async () => {
      const response = await request(app)
        .get('/api/watchlist/meta/stats')
        .expect(200);

      expect(response.body).toHaveProperty('totalItems');
      expect(response.body).toHaveProperty('pinnedItems');
      expect(response.body).toHaveProperty('timestamp');
    });
  });
});