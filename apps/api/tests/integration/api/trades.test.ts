import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import app from '../../../src/server';

describe('Trade API Endpoints', () => {
  beforeAll(async () => {
    // Any setup needed before tests
  });

  afterAll(async () => {
    // Any cleanup needed after tests
  });

  describe('POST /api/trades/quote', () => {
    it('should return a valid quote for token swap', async () => {
      const quoteRequest = {
        inputMint: 'So11111111111111111111111111111111111111112', // SOL
        outputMint: '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R', // Random token
        amount: 1.0,
        slippageBps: 100
      };

      const response = await request(app)
        .post('/api/trades/quote')
        .send(quoteRequest)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          inputMint: quoteRequest.inputMint,
          outputMint: quoteRequest.outputMint,
          inAmount: expect.any(String),
          outAmount: expect.any(String),
          quoteId: expect.any(String),
          slippageBps: 100
        }),
        timestamp: expect.any(String)
      });
    });

    it('should validate input parameters', async () => {
      const invalidRequest = {
        inputMint: 'invalid-mint',
        outputMint: '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',
        amount: -1.0
      };

      const response = await request(app)
        .post('/api/trades/quote')
        .send(invalidRequest)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.any(String)
      });
    });
  });

  describe('POST /api/trades/buy', () => {
    it('should execute a buy order', async () => {
      const buyRequest = {
        tokenAddress: '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',
        amountSol: 0.1,
        slippageBps: 100,
        quoteId: 'test-quote-id'
      };

      const response = await request(app)
        .post('/api/trades/buy')
        .send(buyRequest)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          signature: expect.any(String),
          amountSol: 0.1,
          amountToken: expect.any(Number),
          price: expect.any(Number),
          status: 'confirmed'
        }),
        timestamp: expect.any(String)
      });
    });

    it('should validate buy parameters', async () => {
      const invalidRequest = {
        tokenAddress: 'invalid-address',
        amountSol: 0
      };

      const response = await request(app)
        .post('/api/trades/buy')
        .send(invalidRequest)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.any(String)
      });
    });
  });
});