import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import app from '../../../src/server';

describe('Authentication API Endpoints', () => {
  beforeAll(async () => {
    // Any setup needed before tests
  });

  afterAll(async () => {
    // Any cleanup needed after tests
  });

  describe('POST /api/auth/login', () => {
    it('should authenticate user successfully', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({})
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Logged in successfully',
        timestamp: expect.any(String)
      });

      // Should set session cookie
      expect(response.headers['set-cookie']).toBeDefined();
    });
  });

  describe('GET /api/auth/status', () => {
    it('should return authentication status when not authenticated', async () => {
      const response = await request(app)
        .get('/api/auth/status')
        .expect(200);

      expect(response.body).toMatchObject({
        authenticated: expect.any(Boolean),
        timestamp: expect.any(String)
      });
    });

    it('should return authentication status when authenticated', async () => {
      // First login
      const agent = request.agent(app);
      await agent
        .post('/api/auth/login')
        .send({})
        .expect(200);

      // Then check status
      const response = await agent
        .get('/api/auth/status')
        .expect(200);

      expect(response.body).toMatchObject({
        authenticated: true,
        loginTime: expect.any(String),
        timestamp: expect.any(String)
      });
    });
  });

  describe('POST /api/auth/logout', () => {
    it('should logout user successfully', async () => {
      // First login
      const agent = request.agent(app);
      await agent
        .post('/api/auth/login')
        .send({})
        .expect(200);

      // Then logout
      const response = await agent
        .post('/api/auth/logout')
        .send({})
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Logged out successfully',
        timestamp: expect.any(String)
      });
    });

    it('should handle logout when not authenticated', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .send({})
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: 'Logged out successfully',
        timestamp: expect.any(String)
      });
    });
  });

  describe('Protected endpoints authentication', () => {
    it('should allow access when authenticated in development', async () => {
      const agent = request.agent(app);
      
      // In development mode, authentication is automatic
      // Test access to a protected endpoint
      const response = await agent
        .get('/api/positions')
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should return proper error format for validation errors', async () => {
      const response = await request(app)
        .post('/api/trades/quote')
        .send({ invalid: 'data' })
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.any(String)
      });
    });
  });
});