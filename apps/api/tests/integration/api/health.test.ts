import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import app from '../../../src/server';
import { initializeDatabase, disconnectDatabase } from '../../../src/lib/database';
import { connectRedis, disconnectRedis } from '../../../src/lib/redis';

describe('Health Check Endpoints', () => {
  beforeAll(async () => {
    // Initialize services for testing
    try {
      await initializeDatabase();
      await connectRedis();
    } catch (error) {
      console.warn('Some services failed to initialize for testing:', error);
    }
  });

  afterAll(async () => {
    // Cleanup services
    try {
      await disconnectDatabase();
      await disconnectRedis();
    } catch (error) {
      console.warn('Error during cleanup:', error);
    }
  });

  describe('GET /health', () => {
    it('should return healthy status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toMatchObject({
        status: 'ok',
        environment: expect.any(String)
      });
      expect(response.body.timestamp).toBeDefined();
      expect(response.body.version).toBeDefined();
    });
  });

  describe('GET /', () => {
    it('should return API information', async () => {
      const response = await request(app)
        .get('/')
        .expect(200);

      expect(response.body).toMatchObject({
        name: 'BMad Solana Trading API',
        version: '1.0.0',
        environment: expect.any(String)
      });
      expect(response.body.timestamp).toBeDefined();
    });
  });

  describe('GET /api/watchlist', () => {
    it('should return empty watchlist array', async () => {
      const response = await request(app)
        .get('/api/watchlist')
        .expect(200);

      expect(response.body).toEqual([]);
    });
  });

  describe('Error handling', () => {
    it('should return 404 for non-existent endpoints', async () => {
      const response = await request(app)
        .get('/api/non-existent')
        .expect(404);

      expect(response.body).toMatchObject({
        error: 'Not Found',
        message: 'Cannot GET /api/non-existent',
        timestamp: expect.any(String)
      });
    });
  });
});