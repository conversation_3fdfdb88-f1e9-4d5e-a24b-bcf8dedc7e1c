import { describe, it, expect, beforeEach, afterEach, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import { PrismaClient } from '@prisma/client';
import express from 'express';
import watchlistRouter from '../../../src/routes/watchlist';

// Test database setup
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.TEST_DATABASE_URL || 'postgresql://trader:dev_password_123@localhost:5432/trading_test'
    }
  }
});

// Test app setup
const app = express();
app.use(express.json());
app.use('/api/watchlist', watchlistRouter);

describe('Watchlist Metrics API Integration', () => {
  beforeAll(async () => {
    // Ensure test database is clean
    await prisma.watchlistItem.deleteMany();
  });

  afterAll(async () => {
    // Clean up
    await prisma.watchlistItem.deleteMany();
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Clean state before each test
    await prisma.watchlistItem.deleteMany();
  });

  describe('GET /api/watchlist/metrics', () => {
    it('should return empty metrics for empty watchlist', async () => {
      const response = await request(app)
        .get('/api/watchlist/metrics')
        .expect(200);

      expect(response.body).toMatchObject({
        items: [],
        total: 0,
        timestamp: expect.any(String),
        fromCache: false
      });
    });

    it('should return metrics for single watchlist item', async () => {
      // Create a test watchlist item
      await prisma.watchlistItem.create({
        data: {
          tokenAddress: 'So*****************************************',
          tokenSymbol: 'SOL',
          tokenName: 'Solana',
          isPinned: false,
          isActive: true
        }
      });

      const response = await request(app)
        .get('/api/watchlist/metrics')
        .expect(200);

      expect(response.body).toMatchObject({
        items: expect.arrayContaining([
          expect.objectContaining({
            tokenAddress: 'So*****************************************',
            tokenSymbol: 'SOL',
            tokenName: 'Solana',
            snapshot: expect.objectContaining({
              tokenAddress: 'So*****************************************',
              priceUsd: expect.any(String), // Decimal serialized as string
              source: 'stub'
            })
          })
        ]),
        total: 1,
        timestamp: expect.any(String),
        fromCache: false,
        adapter: 'stub',
        errors: 0
      });

      // Verify snapshot data structure
      const item = response.body.items[0];
      expect(item.snapshot).toHaveProperty('priceUsd');
      expect(item.snapshot).toHaveProperty('lastUpdated');
      expect(item.snapshot).toHaveProperty('source', 'stub');
      
      // Optional fields should be present or undefined
      if (item.snapshot.priceChange1h) {
        expect(typeof item.snapshot.priceChange1h).toBe('string');
      }
      if (item.snapshot.volume24h) {
        expect(typeof item.snapshot.volume24h).toBe('string');
      }
    });

    it('should return metrics for 10 watchlist items', async () => {
      // Create 10 test items
      const testItems = Array.from({ length: 10 }, (_, i) => ({
        tokenAddress: `${'1'.repeat(32)}${i.toString().padStart(12, '0')}`,
        tokenSymbol: `TOKEN${i}`,
        tokenName: `Test Token ${i}`,
        isPinned: i % 3 === 0, // Pin every 3rd item
        isActive: true
      }));

      await prisma.watchlistItem.createMany({
        data: testItems
      });

      const response = await request(app)
        .get('/api/watchlist/metrics')
        .expect(200);

      expect(response.body.items).toHaveLength(10);
      expect(response.body.total).toBe(10);
      
      // Verify all items have snapshots
      response.body.items.forEach((item: any) => {
        expect(item.snapshot).toBeDefined();
        expect(item.snapshot.tokenAddress).toBe(item.tokenAddress);
        expect(item.snapshot.priceUsd).toBeDefined();
      });
    });

    it('should return metrics for 25 watchlist items', async () => {
      // Create 25 test items
      const testItems = Array.from({ length: 25 }, (_, i) => ({
        tokenAddress: `${'2'.repeat(32)}${i.toString().padStart(12, '0')}`,
        tokenSymbol: `TOKEN${i}`,
        tokenName: `Test Token ${i}`,
        isPinned: i < 5, // Pin first 5
        isActive: true
      }));

      await prisma.watchlistItem.createMany({
        data: testItems
      });

      const response = await request(app)
        .get('/api/watchlist/metrics')
        .expect(200);

      expect(response.body.items).toHaveLength(25);
      expect(response.body.total).toBe(25);
      
      // Count pinned items
      const pinnedItems = response.body.items.filter((item: any) => item.isPinned);
      expect(pinnedItems).toHaveLength(5);
    });

    it('should return metrics for 50 watchlist items', async () => {
      // Create 50 test items
      const testItems = Array.from({ length: 50 }, (_, i) => ({
        tokenAddress: `${'3'.repeat(32)}${i.toString().padStart(12, '0')}`,
        tokenSymbol: `TOKEN${i}`,
        tokenName: `Test Token ${i}`,
        isPinned: false,
        isActive: true
      }));

      await prisma.watchlistItem.createMany({
        data: testItems
      });

      const startTime = Date.now();
      const response = await request(app)
        .get('/api/watchlist/metrics')
        .expect(200);
      const duration = Date.now() - startTime;

      expect(response.body.items).toHaveLength(50);
      expect(response.body.total).toBe(50);
      
      // Performance requirement: should respond within 2 seconds
      expect(duration).toBeLessThan(2000);
    });

    it('should handle mixed pinned and unpinned tokens', async () => {
      const testItems = [
        {
          tokenAddress: 'So*****************************************',
          tokenSymbol: 'SOL',
          tokenName: 'Solana',
          isPinned: true,
          isActive: true
        },
        {
          tokenAddress: '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',
          tokenSymbol: 'USDC',
          tokenName: 'USD Coin',
          isPinned: false,
          isActive: true
        },
        {
          tokenAddress: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
          tokenSymbol: 'USDT',
          tokenName: 'Tether USD',
          isPinned: true,
          isActive: true
        }
      ];

      await prisma.watchlistItem.createMany({
        data: testItems
      });

      const response = await request(app)
        .get('/api/watchlist/metrics')
        .expect(200);

      expect(response.body.items).toHaveLength(3);
      
      // Verify pinned status
      const solItem = response.body.items.find((item: any) => item.tokenSymbol === 'SOL');
      const usdcItem = response.body.items.find((item: any) => item.tokenSymbol === 'USDC');
      const usdtItem = response.body.items.find((item: any) => item.tokenSymbol === 'USDT');
      
      expect(solItem.isPinned).toBe(true);
      expect(usdcItem.isPinned).toBe(false);
      expect(usdtItem.isPinned).toBe(true);
      
      // All should have snapshots
      [solItem, usdcItem, usdtItem].forEach(item => {
        expect(item.snapshot).toBeDefined();
        expect(item.snapshot.tokenAddress).toBe(item.tokenAddress);
      });
    });

    it('should set appropriate cache headers', async () => {
      await prisma.watchlistItem.create({
        data: {
          tokenAddress: 'So*****************************************',
          tokenSymbol: 'SOL',
          tokenName: 'Solana',
          isPinned: false,
          isActive: true
        }
      });

      const response = await request(app)
        .get('/api/watchlist/metrics')
        .expect(200);

      expect(response.headers).toHaveProperty('cache-control');
      expect(response.headers).toHaveProperty('etag');
      expect(response.headers).toHaveProperty('x-data-source');
      expect(response.headers).toHaveProperty('x-from-cache');
      
      expect(response.headers['x-data-source']).toBe('stub');
      expect(response.headers['x-from-cache']).toBe('false');
    });

    it('should handle inactive watchlist items correctly', async () => {
      // Create mix of active and inactive items
      await prisma.watchlistItem.createMany({
        data: [
          {
            tokenAddress: 'So*****************************************',
            tokenSymbol: 'SOL',
            tokenName: 'Solana',
            isPinned: false,
            isActive: true
          },
          {
            tokenAddress: '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',
            tokenSymbol: 'USDC',
            tokenName: 'USD Coin',
            isPinned: false,
            isActive: false // Inactive
          }
        ]
      });

      const response = await request(app)
        .get('/api/watchlist/metrics')
        .expect(200);

      // Should only return active items
      expect(response.body.items).toHaveLength(1);
      expect(response.body.items[0].tokenSymbol).toBe('SOL');
      expect(response.body.items[0].isActive).toBe(true);
    });

    it('should provide consistent response format', async () => {
      await prisma.watchlistItem.create({
        data: {
          tokenAddress: 'So*****************************************',
          tokenSymbol: 'SOL',
          tokenName: 'Solana',
          isPinned: false,
          isActive: true
        }
      });

      const response = await request(app)
        .get('/api/watchlist/metrics')
        .expect(200);

      // Verify response structure
      expect(response.body).toHaveProperty('items');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('fromCache');
      expect(response.body).toHaveProperty('adapter');
      expect(response.body).toHaveProperty('errors');

      // Verify item structure
      const item = response.body.items[0];
      expect(item).toHaveProperty('id');
      expect(item).toHaveProperty('tokenAddress');
      expect(item).toHaveProperty('tokenSymbol');
      expect(item).toHaveProperty('tokenName');
      expect(item).toHaveProperty('isPinned');
      expect(item).toHaveProperty('isActive');
      expect(item).toHaveProperty('addedAt');
      expect(item).toHaveProperty('updatedAt');
      expect(item).toHaveProperty('snapshot');
    });

    it('should handle graceful degradation when market data fails', async () => {
      // This test would require mocking the adapter to fail
      // For now, we'll test the fallback structure
      await prisma.watchlistItem.create({
        data: {
          tokenAddress: 'So*****************************************',
          tokenSymbol: 'SOL',
          tokenName: 'Solana',
          isPinned: false,
          isActive: true
        }
      });

      const response = await request(app)
        .get('/api/watchlist/metrics')
        .expect(200);

      // With stub adapter, this should succeed
      expect(response.body.items).toHaveLength(1);
      expect(response.body.items[0].snapshot).toBeDefined();
    });
  });

  describe('Performance and Load Testing', () => {
    it('should handle concurrent requests efficiently', async () => {
      // Create test data
      const testItems = Array.from({ length: 20 }, (_, i) => ({
        tokenAddress: `${'4'.repeat(32)}${i.toString().padStart(12, '0')}`,
        tokenSymbol: `TOKEN${i}`,
        tokenName: `Test Token ${i}`,
        isPinned: false,
        isActive: true
      }));

      await prisma.watchlistItem.createMany({
        data: testItems
      });

      // Make 10 concurrent requests
      const promises = Array.from({ length: 10 }, () =>
        request(app).get('/api/watchlist/metrics')
      );

      const startTime = Date.now();
      const responses = await Promise.all(promises);
      const duration = Date.now() - startTime;

      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.items).toHaveLength(20);
      });

      // Should handle concurrent load efficiently
      expect(duration).toBeLessThan(5000); // 5 seconds for 10 concurrent requests
    });

    it('should maintain response times under load', async () => {
      // Create larger dataset
      const testItems = Array.from({ length: 100 }, (_, i) => ({
        tokenAddress: `${'5'.repeat(32)}${i.toString().padStart(12, '0')}`,
        tokenSymbol: `TOKEN${i}`,
        tokenName: `Test Token ${i}`,
        isPinned: i < 10,
        isActive: true
      }));

      await prisma.watchlistItem.createMany({
        data: testItems
      });

      const startTime = Date.now();
      const response = await request(app)
        .get('/api/watchlist/metrics')
        .expect(200);
      const duration = Date.now() - startTime;

      expect(response.body.items).toHaveLength(100);
      expect(duration).toBeLessThan(3000); // Should respond within 3 seconds
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      // This would require mocking Prisma to fail
      // For now, we test that the endpoint exists and handles normal cases
      const response = await request(app)
        .get('/api/watchlist/metrics')
        .expect(200);

      expect(response.body).toHaveProperty('items');
    });

    it('should provide meaningful error messages', async () => {
      // Test malformed requests or edge cases
      const response = await request(app)
        .get('/api/watchlist/metrics')
        .expect(200);

      // Should not error on empty watchlist
      expect(response.body.total).toBe(0);
    });
  });
});