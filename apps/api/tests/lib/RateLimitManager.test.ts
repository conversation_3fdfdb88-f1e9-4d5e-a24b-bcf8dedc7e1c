import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { RateLimitManager } from '../../src/lib/RateLimitManager';
import * as redis from '../../src/lib/redis';

// Mock Redis
vi.mock('../../src/lib/redis', () => ({
  redis: {
    del: vi.fn()
  },
  getCache: vi.fn(),
  setCache: vi.fn(),
  incrementCounter: vi.fn(),
  disconnectRedis: vi.fn()
}));

// Mock logger
vi.mock('../../src/lib/logger', () => ({
  apiLogger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
    child: vi.fn(() => ({
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn()
    }))
  }
}));

describe('RateLimitManager', () => {
  let rateLimiter: RateLimitManager;

  beforeEach(() => {
    vi.clearAllMocks();
    
    rateLimiter = new RateLimitManager({
      name: 'test-limiter',
      requestsPerSecond: 10,
      requestsPerMinute: 100,
      requestsPerHour: 1000,
      burstCapacity: 20,
      refillRate: 10
    });
  });

  describe('token bucket algorithm', () => {
    it('should allow requests when bucket has tokens', async () => {
      // Mock empty cache (new bucket)
      vi.mocked(redis.getCache).mockResolvedValueOnce(null);
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      vi.mocked(redis.incrementCounter).mockResolvedValue(1);
      
      const status = await rateLimiter.checkRateLimit('user1');
      
      expect(status.allowed).toBe(true);
      expect(status.remaining).toBeLessThan(20); // Should have consumed 1 token
    });

    it('should consume multiple tokens', async () => {
      vi.mocked(redis.getCache).mockResolvedValueOnce(null);
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      vi.mocked(redis.incrementCounter).mockResolvedValue(1);
      
      const status = await rateLimiter.checkRateLimit('user1', 5);
      
      expect(status.allowed).toBe(true);
      expect(redis.setCache).toHaveBeenCalledWith(
        expect.stringContaining('user1'),
        expect.objectContaining({
          tokens: 15 // 20 - 5 = 15
        }),
        expect.any(Number)
      );
    });

    it('should refill tokens over time', async () => {
      const now = Date.now();
      const pastTime = now - 2000; // 2 seconds ago
      
      // Mock bucket state from 2 seconds ago with 10 tokens
      vi.mocked(redis.getCache).mockResolvedValueOnce({
        tokens: 10,
        lastRefill: pastTime
      });
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      vi.mocked(redis.incrementCounter).mockResolvedValue(1);
      
      const status = await rateLimiter.checkRateLimit('user1');
      
      expect(status.allowed).toBe(true);
      // Should have refilled: 10 + (2 * 10) = 30, capped at 20
      expect(redis.setCache).toHaveBeenCalledWith(
        expect.stringContaining('user1'),
        expect.objectContaining({
          tokens: 19 // 20 - 1 = 19 (capped and consumed)
        }),
        expect.any(Number)
      );
    });

    it('should reject when insufficient tokens', async () => {
      // Mock bucket with insufficient tokens
      vi.mocked(redis.getCache).mockResolvedValueOnce({
        tokens: 2,
        lastRefill: Date.now()
      });
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      
      const status = await rateLimiter.checkRateLimit('user1', 5);
      
      expect(status.allowed).toBe(false);
      expect(status.retryAfter).toBeGreaterThan(0);
    });

    it('should cap tokens at burst capacity', async () => {
      const pastTime = Date.now() - 10000; // 10 seconds ago
      
      vi.mocked(redis.getCache).mockResolvedValueOnce({
        tokens: 5,
        lastRefill: pastTime
      });
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      vi.mocked(redis.incrementCounter).mockResolvedValue(1);
      
      await rateLimiter.checkRateLimit('user1');
      
      // Should refill: 5 + (10 * 10) = 105, but capped at 20
      expect(redis.setCache).toHaveBeenCalledWith(
        expect.stringContaining('user1'),
        expect.objectContaining({
          tokens: 19 // 20 - 1 = 19
        }),
        expect.any(Number)
      );
    });
  });

  describe('time-based limits', () => {
    it('should check per-minute limits', async () => {
      vi.mocked(redis.getCache)
        .mockResolvedValueOnce(null) // Bucket state
        .mockResolvedValueOnce(99); // Per-minute counter
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      vi.mocked(redis.incrementCounter).mockResolvedValue(100);
      
      const status = await rateLimiter.checkRateLimit('user1');
      
      expect(status.allowed).toBe(true);
      expect(status.remaining).toBe(1); // 100 - 99 = 1
    });

    it('should reject when per-minute limit exceeded', async () => {
      vi.mocked(redis.getCache)
        .mockResolvedValueOnce(null) // Bucket state
        .mockResolvedValueOnce(100); // Per-minute counter at limit
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      
      const status = await rateLimiter.checkRateLimit('user1');
      
      expect(status.allowed).toBe(false);
      expect(status.retryAfter).toBe(60); // Wait 1 minute
    });

    it('should check per-hour limits', async () => {
      vi.mocked(redis.getCache)
        .mockResolvedValueOnce(null) // Bucket state
        .mockResolvedValueOnce(50) // Per-minute counter
        .mockResolvedValueOnce(999); // Per-hour counter
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      vi.mocked(redis.incrementCounter)
        .mockResolvedValueOnce(51) // Minute counter increment
        .mockResolvedValueOnce(1000); // Hour counter increment
      
      const status = await rateLimiter.checkRateLimit('user1');
      
      expect(status.allowed).toBe(true);
      expect(status.remaining).toBe(1); // 1000 - 999 = 1
    });

    it('should reject when per-hour limit exceeded', async () => {
      vi.mocked(redis.getCache)
        .mockResolvedValueOnce(null) // Bucket state
        .mockResolvedValueOnce(50) // Per-minute counter
        .mockResolvedValueOnce(1000); // Per-hour counter at limit
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      
      const status = await rateLimiter.checkRateLimit('user1');
      
      expect(status.allowed).toBe(false);
      expect(status.retryAfter).toBe(3600); // Wait 1 hour
    });

    it('should increment time-based counters on success', async () => {
      vi.mocked(redis.getCache)
        .mockResolvedValueOnce(null) // Bucket state
        .mockResolvedValueOnce(50) // Per-minute counter
        .mockResolvedValueOnce(500); // Per-hour counter
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      vi.mocked(redis.incrementCounter)
        .mockResolvedValueOnce(51)
        .mockResolvedValueOnce(501);
      
      await rateLimiter.checkRateLimit('user1');
      
      expect(redis.incrementCounter).toHaveBeenCalledTimes(2);
      expect(redis.incrementCounter).toHaveBeenCalledWith(
        expect.stringContaining('minute'),
        60
      );
      expect(redis.incrementCounter).toHaveBeenCalledWith(
        expect.stringContaining('hour'),
        3600
      );
    });

    it('should refund tokens when time limit exceeded', async () => {
      vi.mocked(redis.getCache)
        .mockResolvedValueOnce(null) // Bucket state
        .mockResolvedValueOnce(100); // Per-minute counter at limit
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      
      await rateLimiter.checkRateLimit('user1', 5);
      
      // Should refund the 5 tokens since time limit was exceeded
      expect(redis.setCache).toHaveBeenCalledWith(
        expect.stringContaining('user1'),
        expect.objectContaining({
          tokens: 20 // Full capacity restored
        }),
        expect.any(Number)
      );
    });
  });

  describe('request queuing', () => {
    it('should queue requests when rate limited', async () => {
      vi.mocked(redis.getCache).mockResolvedValue({
        tokens: 0,
        lastRefill: Date.now()
      });
      
      const promise = rateLimiter.queueRequest('user1', 1, 1000);
      
      // Should not resolve immediately
      let resolved = false;
      promise.then(() => { resolved = true; }).catch(() => {});
      
      // Give some time to potentially resolve
      await new Promise(resolve => setTimeout(resolve, 50));
      expect(resolved).toBe(false);
    });

    it('should timeout queued requests', async () => {
      vi.mocked(redis.getCache).mockResolvedValue({
        tokens: 0,
        lastRefill: Date.now()
      });
      
      await expect(rateLimiter.queueRequest('user1', 1, 100))
        .rejects.toThrow('Request queued for 100ms without processing');
    });
  });

  describe('status and configuration', () => {
    it('should return current status without consuming tokens', async () => {
      vi.mocked(redis.getCache)
        .mockResolvedValueOnce({
          tokens: 15,
          lastRefill: Date.now()
        })
        .mockResolvedValueOnce(50); // Per-minute counter
      
      const status = await rateLimiter.getStatus('user1');
      
      expect(status.allowed).toBe(true);
      expect(status.remaining).toBe(15);
      // Should not call setCache (no tokens consumed)
      expect(redis.setCache).not.toHaveBeenCalled();
    });

    it('should return configuration', () => {
      const config = rateLimiter.getConfig();
      
      expect(config.name).toBe('test-limiter');
      expect(config.requestsPerSecond).toBe(10);
      expect(config.burstCapacity).toBe(20);
    });

    it('should reset rate limits', async () => {
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      vi.mocked(redis.redis.del).mockResolvedValue(1);
      
      await rateLimiter.reset('user1');
      
      expect(redis.setCache).toHaveBeenCalledWith(
        expect.stringContaining('user1'),
        expect.objectContaining({
          tokens: 20,
          lastRefill: expect.any(Number)
        }),
        expect.any(Number)
      );
    });
  });

  describe('Redis persistence', () => {
    it('should persist bucket state across requests', async () => {
      const bucketState = {
        tokens: 15,
        lastRefill: Date.now() - 1000
      };
      
      vi.mocked(redis.getCache).mockResolvedValueOnce(bucketState);
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      vi.mocked(redis.incrementCounter).mockResolvedValue(1);
      
      await rateLimiter.checkRateLimit('user1');
      
      expect(redis.setCache).toHaveBeenCalledWith(
        expect.stringContaining('user1'),
        expect.objectContaining({
          tokens: expect.any(Number),
          lastRefill: expect.any(Number)
        }),
        expect.any(Number)
      );
    });

    it('should handle Redis errors gracefully', async () => {
      vi.mocked(redis.getCache).mockRejectedValue(new Error('Redis connection failed'));
      
      const status = await rateLimiter.checkRateLimit('user1');
      
      // Should fail open when Redis is down
      expect(status.allowed).toBe(true);
      expect(status.remaining).toBe(20); // Full capacity
    });

    it('should set appropriate TTL for bucket state', async () => {
      vi.mocked(redis.getCache).mockResolvedValueOnce(null);
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      vi.mocked(redis.incrementCounter).mockResolvedValue(1);
      
      await rateLimiter.checkRateLimit('user1');
      
      expect(redis.setCache).toHaveBeenCalledWith(
        expect.any(String),
        expect.any(Object),
        expect.any(Number) // TTL should be provided
      );
    });
  });

  describe('per-provider rate limiting', () => {
    it('should handle different providers independently', async () => {
      vi.mocked(redis.getCache).mockResolvedValue(null);
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      vi.mocked(redis.incrementCounter).mockResolvedValue(1);
      
      const status1 = await rateLimiter.checkRateLimit('provider1');
      const status2 = await rateLimiter.checkRateLimit('provider2');
      
      expect(status1.allowed).toBe(true);
      expect(status2.allowed).toBe(true);
      
      // Should have separate cache keys
      expect(redis.setCache).toHaveBeenCalledWith(
        expect.stringContaining('provider1'),
        expect.any(Object),
        expect.any(Number)
      );
      expect(redis.setCache).toHaveBeenCalledWith(
        expect.stringContaining('provider2'),
        expect.any(Object),
        expect.any(Number)
      );
    });

    it('should apply limits per provider', async () => {
      // Mock provider1 at limit
      vi.mocked(redis.getCache)
        .mockResolvedValueOnce({
          tokens: 0,
          lastRefill: Date.now()
        })
        .mockResolvedValueOnce(null); // provider2 bucket
      
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      vi.mocked(redis.incrementCounter).mockResolvedValue(1);
      
      const status1 = await rateLimiter.checkRateLimit('provider1');
      const status2 = await rateLimiter.checkRateLimit('provider2');
      
      expect(status1.allowed).toBe(false);
      expect(status2.allowed).toBe(true);
    });
  });

  describe('error conditions', () => {
    it('should handle cache read errors', async () => {
      vi.mocked(redis.getCache).mockRejectedValue(new Error('Cache read failed'));
      
      const status = await rateLimiter.checkRateLimit('user1');
      
      expect(status.allowed).toBe(true); // Fail open
    });

    it('should handle cache write errors', async () => {
      vi.mocked(redis.getCache).mockResolvedValue(null);
      vi.mocked(redis.setCache).mockRejectedValue(new Error('Cache write failed'));
      vi.mocked(redis.incrementCounter).mockResolvedValue(1);
      
      // Should not throw, should handle gracefully
      const status = await rateLimiter.checkRateLimit('user1');
      expect(status.allowed).toBe(true);
    });

    it('should handle counter increment errors', async () => {
      vi.mocked(redis.getCache).mockResolvedValue(null);
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      vi.mocked(redis.incrementCounter).mockRejectedValue(new Error('Counter failed'));
      
      // Should not throw, should handle gracefully
      const status = await rateLimiter.checkRateLimit('user1');
      expect(status.allowed).toBe(true);
    });
  });
});