import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';

describe('Watchlist API Routes', () => {
  let app: express.Application;
  let mockWatchlistService: any;
  let mockMarketDataService: any;
  let watchlistRoutes: any;

  beforeEach(async () => {
    // Reset all mocks
    vi.resetAllMocks();
    vi.clearAllMocks();

    // Create fresh mock instances
    mockWatchlistService = {
      findAll: vi.fn(),
      findById: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      softDelete: vi.fn(),
      togglePin: vi.fn(),
      findPinned: vi.fn(),
      countActive: vi.fn(),
      findByTokenAddress: vi.fn(),
      createBulk: vi.fn()
    };

    mockMarketDataService = {
      fetchBatchData: vi.fn().mockResolvedValue({
        data: [],
        timestamp: new Date(),
        fromCache: false,
        adapter: 'stub'
      })
    };

    // Mock all dependencies before importing
    vi.doMock('../../src/services/WatchlistService', () => ({
      WatchlistService: vi.fn(() => mockWatchlistService)
    }));

    vi.doMock('../../src/services/MarketDataService', () => ({
      MarketDataService: vi.fn(() => mockMarketDataService)
    }));

    vi.doMock('../../src/lib/logger', () => ({
      apiLogger: {
        info: vi.fn(),
        error: vi.fn(),
        warn: vi.fn(),
        debug: vi.fn()
      }
    }));

    vi.doMock('@prisma/client', () => ({
      PrismaClient: vi.fn(() => ({}))
    }));

    vi.doMock('@solana/web3.js', () => ({
      PublicKey: vi.fn().mockImplementation((address: string) => {
        if (!address || address.length < 32) {
          throw new Error('Invalid public key');
        }
        return { toString: () => address };
      })
    }));

    vi.doMock('../../src/services/metrics/StubMetricsAdapter', () => ({
      StubMetricsAdapter: vi.fn(() => ({}))
    }));

    vi.doMock('../../src/services/metrics/MetricsAdapterInterface', () => ({
      adapterRegistry: {
        register: vi.fn()
      }
    }));

    // Import routes after mocking
    watchlistRoutes = (await import('../../src/routes/watchlist')).default;

    // Setup Express app
    app = express();
    app.use(express.json());
    app.use('/api/watchlist', watchlistRoutes);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('GET /api/watchlist', () => {
    it('should return all active watchlist items', async () => {
      const mockItems = [
        {
          id: '1',
          tokenAddress: 'token1',
          tokenSymbol: 'TK1',
          tokenName: 'Token 1',
          isPinned: true,
          isActive: true
        },
        {
          id: '2',
          tokenAddress: 'token2',
          tokenSymbol: 'TK2',
          tokenName: 'Token 2',
          isPinned: false,
          isActive: true
        }
      ];

      mockWatchlistService.findAll.mockResolvedValue(mockItems);

      const response = await request(app)
        .get('/api/watchlist')
        .expect(200);

      expect(response.body).toEqual(mockItems);
      expect(mockWatchlistService.findAll).toHaveBeenCalled();
    });

    it('should handle errors gracefully', async () => {
      mockWatchlistService.findAll.mockRejectedValue(new Error('Database error'));

      const response = await request(app)
        .get('/api/watchlist')
        .expect(500);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code', 'SERVER_ERROR');
      expect(response.body.error).toHaveProperty('message', 'Failed to fetch watchlist items');
    });
  });

  describe('GET /api/watchlist/:id', () => {
    it('should return a single watchlist item', async () => {
      const mockItem = {
        id: '1',
        tokenAddress: 'token1',
        tokenSymbol: 'TK1',
        tokenName: 'Token 1',
        isActive: true
      };

      mockWatchlistService.findById.mockResolvedValue(mockItem);

      const response = await request(app)
        .get('/api/watchlist/1')
        .expect(200);

      expect(response.body).toEqual(mockItem);
      expect(mockWatchlistService.findById).toHaveBeenCalledWith('1');
    });

    it('should return 404 if item not found', async () => {
      mockWatchlistService.findById.mockResolvedValue(null);

      const response = await request(app)
        .get('/api/watchlist/nonexistent')
        .expect(404);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code', 'ITEM_NOT_FOUND');
      expect(response.body.error).toHaveProperty('message', 'Watchlist item not found');
    });
  });

  describe('POST /api/watchlist', () => {
    it('should create a new watchlist item', async () => {
      const createData = {
        tokenAddress: 'So11111111111111111111111111111111111111112',
        tokenSymbol: 'SOL',
        tokenName: 'Solana',
        customName: 'My SOL',
        notes: 'Main token'
      };

      const mockCreatedItem = {
        id: '1',
        ...createData,
        isPinned: false,
        isActive: true
      };

      mockWatchlistService.countActive.mockResolvedValue(5); // Under limit
      mockWatchlistService.findByTokenAddress.mockResolvedValue(null);
      mockWatchlistService.create.mockResolvedValue(mockCreatedItem);

      const response = await request(app)
        .post('/api/watchlist')
        .send(createData)
        .expect(201);

      expect(response.body).toEqual(mockCreatedItem);
      expect(mockWatchlistService.create).toHaveBeenCalledWith(createData);
    });

    it('should return 400 for invalid request body', async () => {
      const invalidData = {
        tokenAddress: 'So11111111111111111111111111111111111111112',
        // Missing required fields
      };

      const response = await request(app)
        .post('/api/watchlist')
        .send(invalidData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code', 'VALIDATION_ERROR');
      expect(response.body.error).toHaveProperty('message', 'Invalid request body');
    });

    it('should return 400 for invalid token address', async () => {
      const createData = {
        tokenAddress: 'invalid',
        tokenSymbol: 'INV',
        tokenName: 'Invalid'
      };

      const response = await request(app)
        .post('/api/watchlist')
        .send(createData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code', 'VALIDATION_ERROR');
      expect(response.body.error).toHaveProperty('message', 'Invalid request body');
      expect(response.body.error.details).toContain('tokenAddress: String must contain at least 32 character(s)');
    });

    it('should return 409 for duplicate token', async () => {
      const createData = {
        tokenAddress: 'So11111111111111111111111111111111111111112',
        tokenSymbol: 'SOL',
        tokenName: 'Solana'
      };

      mockWatchlistService.countActive.mockResolvedValue(5); // Under limit
      mockWatchlistService.findByTokenAddress.mockResolvedValue({ id: 'existing' });

      const response = await request(app)
        .post('/api/watchlist')
        .send(createData)
        .expect(409);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code', 'DUPLICATE_TOKEN');
      expect(response.body.error).toHaveProperty('message', 'Token already exists in watchlist');
    });
  });

  describe('PATCH /api/watchlist/:id', () => {
    it('should update a watchlist item', async () => {
      const updateData = {
        customName: 'Updated Name',
        notes: 'Updated notes',
        isPinned: true
      };

      const mockUpdatedItem = {
        id: '1',
        tokenAddress: 'token1',
        ...updateData
      };

      mockWatchlistService.findById.mockResolvedValue({ id: '1', isPinned: false }); // For pin limit check
      mockWatchlistService.findPinned.mockResolvedValue([]); // No pinned items yet
      mockWatchlistService.update.mockResolvedValue(mockUpdatedItem);

      const response = await request(app)
        .patch('/api/watchlist/1')
        .send(updateData)
        .expect(200);

      expect(response.body).toEqual(mockUpdatedItem);
      expect(mockWatchlistService.update).toHaveBeenCalledWith('1', updateData);
    });

    it('should return 404 if item not found', async () => {
      mockWatchlistService.update.mockRejectedValue(new Error('Watchlist item not found'));

      const response = await request(app)
        .patch('/api/watchlist/nonexistent')
        .send({ customName: 'Test' })
        .expect(404);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code', 'ITEM_NOT_FOUND');
      expect(response.body.error).toHaveProperty('message', 'Watchlist item not found');
    });
  });

  describe('DELETE /api/watchlist/:id', () => {
    it('should soft delete a watchlist item', async () => {
      mockWatchlistService.softDelete.mockResolvedValue({ id: '1', isActive: false });

      const response = await request(app)
        .delete('/api/watchlist/1')
        .expect(204);

      expect(response.body).toEqual({});
      expect(mockWatchlistService.softDelete).toHaveBeenCalledWith('1');
    });

    it('should return 404 if item not found', async () => {
      mockWatchlistService.softDelete.mockRejectedValue(new Error('Watchlist item not found'));

      const response = await request(app)
        .delete('/api/watchlist/nonexistent')
        .expect(404);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code', 'ITEM_NOT_FOUND');
      expect(response.body.error).toHaveProperty('message', 'Watchlist item not found');
    });
  });

  describe('PATCH /api/watchlist/:id/pin', () => {
    it('should toggle pin status', async () => {
      const mockToggledItem = {
        id: '1',
        tokenAddress: 'token1',
        isPinned: true
      };

      mockWatchlistService.findById.mockResolvedValue({ id: '1', isPinned: false });
      mockWatchlistService.findPinned.mockResolvedValue([]);
      mockWatchlistService.togglePin.mockResolvedValue(mockToggledItem);

      const response = await request(app)
        .patch('/api/watchlist/1/pin')
        .expect(200);

      expect(response.body).toEqual(mockToggledItem);
      expect(mockWatchlistService.togglePin).toHaveBeenCalledWith('1');
    });
  });

  describe('GET /api/watchlist/filter/pinned', () => {
    it('should return pinned items', async () => {
      const mockPinnedItems = [
        {
          id: '1',
          tokenAddress: 'token1',
          isPinned: true
        },
        {
          id: '2',
          tokenAddress: 'token2',
          isPinned: true
        }
      ];

      mockWatchlistService.findPinned.mockResolvedValue(mockPinnedItems);

      const response = await request(app)
        .get('/api/watchlist/filter/pinned')
        .expect(200);

      expect(response.body).toEqual(mockPinnedItems);
      expect(mockWatchlistService.findPinned).toHaveBeenCalled();
    });
  });

  describe('GET /api/watchlist/meta/stats', () => {
    it('should return watchlist statistics', async () => {
      mockWatchlistService.countActive.mockResolvedValue(10);
      mockWatchlistService.findPinned.mockResolvedValue([{ id: '1' }, { id: '2' }]);

      const response = await request(app)
        .get('/api/watchlist/meta/stats')
        .expect(200);

      expect(response.body).toHaveProperty('totalItems', 10);
      expect(response.body).toHaveProperty('pinnedItems', 2);
      expect(response.body).toHaveProperty('timestamp');
    });
  });

  describe('POST /api/watchlist/bulk', () => {
    it('should successfully import bulk tokens', async () => {
      const bulkData = {
        tokens: [
          {
            tokenAddress: 'So11111111111111111111111111111111111111112',
            tokenSymbol: 'SOL',
            tokenName: 'Solana'
          },
          {
            tokenAddress: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
            tokenSymbol: 'USDC',
            tokenName: 'USD Coin'
          }
        ]
      };

      const mockResult = {
        successful: [
          { id: '1', tokenAddress: 'So11111111111111111111111111111111111111112', tokenSymbol: 'SOL' },
          { id: '2', tokenAddress: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', tokenSymbol: 'USDC' }
        ],
        failed: [],
        summary: {
          total: 2,
          successful: 2,
          failed: 0,
          skippedDuplicates: 0
        }
      };

      mockWatchlistService.createBulk.mockResolvedValue(mockResult);

      const response = await request(app)
        .post('/api/watchlist/bulk')
        .send(bulkData)
        .expect(201);

      expect(response.body.data).toEqual(mockResult);
      expect(mockWatchlistService.createBulk).toHaveBeenCalled();
    });

    it('should handle bulk import with failures', async () => {
      const bulkData = {
        tokens: [
          {
            tokenAddress: 'So11111111111111111111111111111111111111112',
            tokenSymbol: 'SOL',
            tokenName: 'Solana'
          },
          {
            tokenAddress: 'invalid_address',
            tokenSymbol: 'INV',
            tokenName: 'Invalid'
          }
        ]
      };

      const mockResult = {
        successful: [
          { id: '1', tokenAddress: 'So11111111111111111111111111111111111111112', tokenSymbol: 'SOL' }
        ],
        failed: [
          {
            tokenAddress: 'invalid_address',
            error: 'Invalid Solana token address format'
          }
        ],
        summary: {
          total: 2,
          successful: 1,
          failed: 1,
          skippedDuplicates: 0
        }
      };

      mockWatchlistService.createBulk.mockResolvedValue(mockResult);

      const response = await request(app)
        .post('/api/watchlist/bulk')
        .send(bulkData)
        .expect(207); // Multi-Status for partial success

      expect(response.body.data).toEqual(mockResult);
    });

    it('should handle string input format', async () => {
      const stringInput = 'So11111111111111111111111111111111111111112|SOL\nEPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v,USDC';

      const mockResult = {
        successful: [
          { id: '1', tokenAddress: 'So11111111111111111111111111111111111111112', tokenSymbol: 'SOL' },
          { id: '2', tokenAddress: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', tokenSymbol: 'USDC' }
        ],
        failed: [],
        summary: {
          total: 2,
          successful: 2,
          failed: 0,
          skippedDuplicates: 0
        }
      };

      mockWatchlistService.createBulk.mockResolvedValue(mockResult);

      const response = await request(app)
        .post('/api/watchlist/bulk')
        .send(stringInput)
        .expect(201);

      expect(response.body.data).toEqual(mockResult);
    });

    it('should return validation error for empty tokens', async () => {
      const response = await request(app)
        .post('/api/watchlist/bulk')
        .send({ tokens: [] })
        .expect(400);

      expect(response.body.error).toHaveProperty('code', 'VALIDATION_ERROR');
      expect(response.body.error).toHaveProperty('message', 'Invalid request body');
      expect(response.body.error.details).toContain('tokens: Array must contain at least 1 element(s)');
    });

    it('should return validation error for too many tokens', async () => {
      const tooManyTokens = { tokens: new Array(51).fill({ 
        tokenAddress: 'So11111111111111111111111111111111111111112',
        tokenSymbol: 'SOL',
        tokenName: 'Solana'
      }) };

      const response = await request(app)
        .post('/api/watchlist/bulk')
        .send(tooManyTokens)
        .expect(400);

      expect(response.body.error).toHaveProperty('code', 'VALIDATION_ERROR');
      expect(response.body.error).toHaveProperty('message', 'Too many tokens. Maximum 50 tokens per bulk import');
    });

    it('should handle service errors', async () => {
      const bulkData = {
        tokens: [
          {
            tokenAddress: 'So11111111111111111111111111111111111111112',
            tokenSymbol: 'SOL',
            tokenName: 'Solana'
          }
        ]
      };

      mockWatchlistService.createBulk.mockRejectedValue(new Error('Database error'));

      const response = await request(app)
        .post('/api/watchlist/bulk')
        .send(bulkData)
        .expect(500);

      expect(response.body.error).toHaveProperty('code', 'SERVER_ERROR');
      expect(response.body.error).toHaveProperty('message', 'Failed to process bulk import');
    });
  });
});