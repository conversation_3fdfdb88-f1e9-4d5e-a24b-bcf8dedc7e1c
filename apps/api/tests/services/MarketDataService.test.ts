import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { MarketDataService } from '../../src/services/MarketDataService';
import { StubMetricsAdapter } from '../../src/services/metrics/StubMetricsAdapter';
import { adapterRegistry, AdapterError } from '../../src/services/metrics/MetricsAdapterInterface';
import { Decimal } from 'decimal.js';
import * as redis from '../../src/lib/redis';

// Mock Redis
vi.mock('../../src/lib/redis', () => ({
  setCache: vi.fn(),
  getCache: vi.fn(),
  deleteCachePattern: vi.fn()
}));

// Mock logger
vi.mock('../../src/lib/logger', () => ({
  apiLogger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
    child: vi.fn(() => ({
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn()
    }))
  }
}));

describe('MarketDataService', () => {
  let marketDataService: MarketDataService;
  let stubAdapter: StubMetricsAdapter;

  beforeEach(() => {
    // Clear registry
    adapterRegistry.getAdapterNames().forEach(name => {
      adapterRegistry.unregister(name);
    });

    // Setup fresh adapter
    stubAdapter = new StubMetricsAdapter({
      simulateLatency: false,
      errorRate: 0
    });
    adapterRegistry.register(stubAdapter);

    // Clear mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Clean up
    adapterRegistry.getAdapterNames().forEach(name => {
      adapterRegistry.unregister(name);
    });
  });

  describe('constructor and initialization', () => {
    it('should initialize with default configuration', () => {
      marketDataService = new MarketDataService();
      expect(marketDataService).toBeInstanceOf(MarketDataService);
    });

    it('should initialize with custom configuration', () => {
      marketDataService = new MarketDataService({
        defaultCacheTtl: 120,
        maxBatchSize: 25,
        useCache: false
      });
      expect(marketDataService).toBeInstanceOf(MarketDataService);
    });
  });

  describe('fetchTokenData', () => {
    beforeEach(() => {
      marketDataService = new MarketDataService({ useCache: false });
    });

    it('should fetch data for a single token', async () => {
      const tokenAddress = 'So11111111111111111111111111111111111111112'; // SOL address
      
      const result = await marketDataService.fetchTokenData(tokenAddress);
      
      expect(result).toBeTruthy();
      expect(result?.tokenAddress).toBe(tokenAddress);
      expect(result?.priceUsd).toBeInstanceOf(Decimal);
      expect(result?.source).toBe('stub');
      expect(result?.lastUpdated).toBeInstanceOf(Date);
    });

    it('should return null if no data available', async () => {
      // Mock empty cache response
      vi.mocked(redis.getCache).mockResolvedValue(null);
      
      // Remove all adapters to simulate no data sources
      adapterRegistry.unregister('stub');
      
      const tokenAddress = 'So11111111111111111111111111111111111111112';
      
      const result = await marketDataService.fetchTokenData(tokenAddress);
      expect(result).toBeNull();
    });

    it('should validate token address format', async () => {
      const invalidAddress = 'invalid-address';
      
      await expect(marketDataService.fetchTokenData(invalidAddress))
        .rejects.toThrow('Invalid token addresses');
    });
  });

  describe('fetchBatchData', () => {
    beforeEach(() => {
      marketDataService = new MarketDataService({ 
        useCache: false,
        maxBatchSize: 50
      });
    });

    it('should handle single token batch', async () => {
      const addresses = ['So11111111111111111111111111111111111111112'];
      
      const result = await marketDataService.fetchBatchData(addresses);
      
      expect(result.data).toHaveLength(1);
      expect(result.data[0].tokenAddress).toBe(addresses[0]);
      expect(result.fromCache).toBe(false);
      expect(result.adapter).toBe('stub');
      expect(result.errors).toBeUndefined();
    });

    it('should handle batch of 10 tokens', async () => {
      const addresses = Array.from({ length: 10 }, (_, i) => 
        `${'1'.repeat(32)}${i.toString().padStart(12, '0')}`
      );
      
      const result = await marketDataService.fetchBatchData(addresses);
      
      expect(result.data).toHaveLength(10);
      expect(result.fromCache).toBe(false);
      expect(result.adapter).toBe('stub');
      result.data.forEach((snapshot, index) => {
        expect(snapshot.tokenAddress).toBe(addresses[index]);
        expect(snapshot.priceUsd).toBeInstanceOf(Decimal);
        expect(snapshot.source).toBe('stub');
      });
    });

    it('should handle batch of 50 tokens', async () => {
      const addresses = Array.from({ length: 50 }, (_, i) => 
        `${'1'.repeat(32)}${i.toString().padStart(12, '0')}`
      );
      
      const result = await marketDataService.fetchBatchData(addresses);
      
      expect(result.data).toHaveLength(50);
      expect(result.fromCache).toBe(false);
      expect(result.adapter).toBe('stub');
    });

    it('should handle batch of 100 tokens with chunking', async () => {
      marketDataService = new MarketDataService({ 
        useCache: false,
        maxBatchSize: 100
      });
      
      const addresses = Array.from({ length: 100 }, (_, i) => 
        `${'1'.repeat(32)}${i.toString().padStart(12, '0')}`
      );
      
      const result = await marketDataService.fetchBatchData(addresses);
      
      expect(result.data).toHaveLength(100);
      expect(result.fromCache).toBe(false);
      expect(result.adapter).toBe('stub');
    });

    it('should reject batch exceeding maximum size', async () => {
      const addresses = Array.from({ length: 101 }, (_, i) => 
        `${'1'.repeat(32)}${i.toString().padStart(12, '0')}`
      );
      
      await expect(marketDataService.fetchBatchData(addresses))
        .rejects.toThrow('exceeds maximum');
    });

    it('should handle empty batch', async () => {
      await expect(marketDataService.fetchBatchData([]))
        .rejects.toThrow('No token addresses provided');
    });

    it('should remove duplicate addresses', async () => {
      const address = 'So11111111111111111111111111111111111111112';
      const addresses = [address, address, address];
      
      const result = await marketDataService.fetchBatchData(addresses);
      
      expect(result.data).toHaveLength(1);
      expect(result.data[0].tokenAddress).toBe(address);
    });
  });

  describe('caching behavior', () => {
    beforeEach(() => {
      marketDataService = new MarketDataService({ 
        useCache: true,
        defaultCacheTtl: 60
      });
    });

    it('should cache fresh data', async () => {
      const address = 'So11111111111111111111111111111111111111112';
      
      // Mock getCache to return null (cache miss)
      vi.mocked(redis.getCache).mockResolvedValue(null);
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      
      const result = await marketDataService.fetchBatchData([address]);
      
      expect(result.fromCache).toBe(false);
      expect(redis.setCache).toHaveBeenCalled();
    });

    it('should return cached data when available', async () => {
      const address = 'So11111111111111111111111111111111111111112';
      const cachedData = {
        snapshot: {
          tokenAddress: address,
          priceUsd: '1.50',
          priceChange1h: '5.0',
          priceChange24h: '10.0',
          volume24h: '1000000',
          liquidity: '500000',
          fdv: '1500000000',
          ageInDays: 100,
          lastUpdated: new Date().toISOString(),
          source: 'stub'
        },
        timestamp: new Date().toISOString()
      };
      
      // Mock getCache to return cached data
      vi.mocked(redis.getCache).mockResolvedValue(cachedData);
      
      const result = await marketDataService.fetchBatchData([address]);
      
      expect(result.fromCache).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data[0].tokenAddress).toBe(address);
      expect(result.data[0].priceUsd).toBeInstanceOf(Decimal);
    });

    it('should handle cache TTL expiration', async () => {
      const address = 'So11111111111111111111111111111111111111112';
      
      // Mock expired cache data
      const expiredData = {
        snapshot: { /* ... */ },
        timestamp: new Date(Date.now() - 120 * 1000).toISOString() // 2 minutes ago
      };
      
      vi.mocked(redis.getCache).mockResolvedValue(expiredData);
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      
      const result = await marketDataService.fetchBatchData([address]);
      
      expect(result.fromCache).toBe(false); // Should fetch fresh data
      expect(redis.setCache).toHaveBeenCalled(); // Should cache new data
    });

    it('should handle cache invalidation', async () => {
      const address = 'So11111111111111111111111111111111111111112';
      
      vi.mocked(redis.deleteCachePattern).mockResolvedValue(undefined);
      
      await marketDataService.invalidateCache([address]);
      
      expect(redis.deleteCachePattern).toHaveBeenCalledWith(
        expect.stringContaining(address)
      );
    });

    it('should handle global cache invalidation', async () => {
      vi.mocked(redis.deleteCachePattern).mockResolvedValue(undefined);
      
      await marketDataService.invalidateCache();
      
      expect(redis.deleteCachePattern).toHaveBeenCalledWith('market-data:*');
    });
  });

  describe('error handling and fallback', () => {
    beforeEach(() => {
      marketDataService = new MarketDataService({ 
        useCache: true,
        staleTolerance: 300 // 5 minutes
      });
    });

    it('should handle adapter failures', async () => {
      const errorAdapter = new StubMetricsAdapter({
        errorRate: 1.0,
        simulateLatency: false
      });
      adapterRegistry.unregister('stub');
      adapterRegistry.register(errorAdapter);

      const address = 'So11111111111111111111111111111111111111112';
      
      await expect(marketDataService.fetchBatchData([address]))
        .rejects.toThrow();
    });

    it('should fallback to stale data when fresh fetch fails', async () => {
      const address = 'So11111111111111111111111111111111111111112';
      
      // Mock stale but usable data
      const staleData = {
        snapshot: {
          tokenAddress: address,
          priceUsd: '1.50',
          lastUpdated: new Date(Date.now() - 240 * 1000).toISOString(), // 4 minutes ago
          source: 'stub'
        },
        timestamp: new Date(Date.now() - 240 * 1000).toISOString()
      };
      
      vi.mocked(redis.getCache).mockResolvedValue(staleData);
      
      // Create failing adapter
      const errorAdapter = new StubMetricsAdapter({
        errorRate: 1.0,
        simulateLatency: false
      });
      adapterRegistry.unregister('stub');
      adapterRegistry.register(errorAdapter);
      
      const result = await marketDataService.fetchBatchData([address]);
      
      expect(result.data).toHaveLength(1);
      expect(result.adapter).toBe('stale-cache');
      expect(result.errors).toBeDefined();
    });

    it('should handle network timeouts', async () => {
      const timeoutAdapter = new StubMetricsAdapter({
        minLatencyMs: 5000,
        maxLatencyMs: 5000,
        simulateLatency: true
      });
      adapterRegistry.unregister('stub');
      adapterRegistry.register(timeoutAdapter);

      const address = 'So11111111111111111111111111111111111111112';
      
      // This should timeout based on default timeout settings
      await expect(marketDataService.fetchBatchData([address], { timeout: 100 }))
        .rejects.toThrow();
    });

    it('should handle partial batch failures gracefully', async () => {
      // This is more complex to test with the current stub adapter
      // In a real implementation, we'd mock the adapter to fail on specific addresses
      const addresses = [
        'So11111111111111111111111111111111111111112',
        '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R'
      ];
      
      const result = await marketDataService.fetchBatchData(addresses);
      
      // With the stub adapter, this should succeed for both
      expect(result.data).toHaveLength(2);
      expect(result.errors).toBeUndefined();
    });
  });

  describe('performance requirements', () => {
    beforeEach(() => {
      marketDataService = new MarketDataService({ useCache: false });
    });

    it('should respond within 2 seconds for fresh data fetches', async () => {
      const startTime = Date.now();
      const addresses = Array.from({ length: 10 }, (_, i) => 
        `${'1'.repeat(32)}${i.toString().padStart(12, '0')}`
      );
      
      await marketDataService.fetchBatchData(addresses);
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(2000); // Should be much faster with stub adapter
    });

    it('should handle concurrent requests efficiently', async () => {
      const addresses = Array.from({ length: 5 }, (_, i) => 
        `${'1'.repeat(32)}${i.toString().padStart(12, '0')}`
      );
      
      const promises = Array.from({ length: 10 }, () =>
        marketDataService.fetchBatchData(addresses)
      );
      
      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result.data).toHaveLength(5);
      });
    });
  });

  describe('health status', () => {
    beforeEach(() => {
      marketDataService = new MarketDataService();
    });

    it('should report healthy status when adapters are working', async () => {
      vi.mocked(redis.setCache).mockResolvedValue(undefined);
      vi.mocked(redis.getCache).mockResolvedValue('ok');
      
      const health = await marketDataService.getHealthStatus();
      
      expect(health.healthy).toBe(true);
      expect(health.adapters.stub).toBe(true);
      expect(health.cacheConnected).toBe(true);
    });

    it('should report unhealthy status when cache fails', async () => {
      vi.mocked(redis.setCache).mockRejectedValue(new Error('Redis connection failed'));
      
      const health = await marketDataService.getHealthStatus();
      
      expect(health.cacheConnected).toBe(false);
      expect(health.healthy).toBe(false); // Should be false if cache is down
    });
  });
});