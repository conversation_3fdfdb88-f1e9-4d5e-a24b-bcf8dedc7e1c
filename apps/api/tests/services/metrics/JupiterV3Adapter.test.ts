import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { JupiterV3Adapter } from '../../../src/services/metrics/JupiterV3Adapter';
import { Decimal } from 'decimal.js';

// Mock fetch globally
global.fetch = vi.fn();

// Mock logger
vi.mock('../../../src/lib/logger', () => ({
  apiLogger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
    child: vi.fn(() => ({
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn()
    }))
  }
}));

describe('JupiterV3Adapter', () => {
  let adapter: JupiterV3Adapter;
  let fetchMock: any;

  beforeEach(() => {
    fetchMock = vi.mocked(fetch);
    adapter = new JupiterV3Adapter({ tier: 'lite' });
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('constructor and configuration', () => {
    it('should initialize with lite tier by default', () => {
      const liteAdapter = new JupiterV3Adapter();
      const info = liteAdapter.getProviderInfo();
      
      expect(info.name).toContain('lite');
      expect(info.requiresAuth).toBe(false);
    });

    it('should initialize with pro tier configuration', () => {
      const proAdapter = new JupiterV3Adapter({ 
        tier: 'pro', 
        apiKey: 'test-key' 
      });
      const info = proAdapter.getProviderInfo();
      
      expect(info.name).toContain('pro');
      expect(info.requiresAuth).toBe(true);
    });

    it('should throw error for pro tier without API key', () => {
      expect(() => {
        new JupiterV3Adapter({ tier: 'pro' });
      }).toThrow('API key required for Jupiter V3 Pro tier');
    });
  });

  describe('getProviderInfo', () => {
    it('should return correct provider info for lite tier', () => {
      const info = adapter.getProviderInfo();
      
      expect(info.name).toBe('Jupiter Price API V3 (lite)');
      expect(info.batchSize).toBe(50);
      expect(info.supportsRealtime).toBe(true);
      expect(info.requiresAuth).toBe(false);
      expect(info.rateLimit.requestsPerSecond).toBe(10);
    });

    it('should return correct provider info for pro tier', () => {
      const proAdapter = new JupiterV3Adapter({ 
        tier: 'pro', 
        apiKey: 'test-key' 
      });
      const info = proAdapter.getProviderInfo();
      
      expect(info.name).toBe('Jupiter Price API V3 (pro)');
      expect(info.requiresAuth).toBe(true);
      expect(info.rateLimit.requestsPerSecond).toBe(50);
    });
  });

  describe('fetchTokenSnapshot', () => {
    it('should fetch data for a single token', async () => {
      const mockResponse = {
        'So11111111111111111111111111111111111111112': {
          usdPrice: 147.48,
          blockId: 348004023,
          decimals: 9,
          priceChange24h: 1.29
        }
      };

      fetchMock.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const result = await adapter.fetchTokenSnapshot(
        'So11111111111111111111111111111111111111112'
      );

      expect(result.tokenAddress).toBe('So11111111111111111111111111111111111111112');
      expect(result.priceUsd).toBeInstanceOf(Decimal);
      expect(result.priceUsd.toNumber()).toBe(147.48);
      expect(result.priceChange24h?.toNumber()).toBe(1.29);
      expect(result.source).toBe('jupiter-v3');
      expect(result.lastUpdated).toBeInstanceOf(Date);
    });

    it('should throw error for token not found', async () => {
      fetchMock.mockResolvedValueOnce({
        ok: true,
        json: async () => ({}) // Empty response
      });

      await expect(adapter.fetchTokenSnapshot('invalid-token'))
        .rejects.toThrow('Token not found or not traded in last 7 days');
    });

    it('should handle invalid token address', async () => {
      await expect(adapter.fetchTokenSnapshot('invalid'))
        .rejects.toThrow('Invalid token address');
    });
  });

  describe('fetchBatchSnapshots', () => {
    it('should fetch data for multiple tokens', async () => {
      const mockResponse = {
        'So11111111111111111111111111111111111111112': {
          usdPrice: 147.48,
          blockId: 348004023,
          decimals: 9,
          priceChange24h: 1.29
        },
        'JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN': {
          usdPrice: 0.41,
          blockId: 348004026,
          decimals: 6,
          priceChange24h: 0.53
        }
      };

      fetchMock.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const addresses = [
        'So11111111111111111111111111111111111111112',
        'JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN'
      ];

      const results = await adapter.fetchBatchSnapshots(addresses);

      expect(results).toHaveLength(2);
      expect(results[0].tokenAddress).toBe(addresses[0]);
      expect(results[1].tokenAddress).toBe(addresses[1]);
      expect(results[0].priceUsd.toNumber()).toBe(147.48);
      expect(results[1].priceUsd.toNumber()).toBe(0.41);
    });

    it('should handle partial responses (some tokens not found)', async () => {
      const mockResponse = {
        'So11111111111111111111111111111111111111112': {
          usdPrice: 147.48,
          blockId: 348004023,
          decimals: 9,
          priceChange24h: 1.29
        }
        // Second token missing from response
      };

      fetchMock.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const addresses = [
        'So11111111111111111111111111111111111111112',
        'NonExistentToken1111111111111111111111111'
      ];

      const results = await adapter.fetchBatchSnapshots(addresses);

      expect(results).toHaveLength(1);
      expect(results[0].tokenAddress).toBe(addresses[0]);
    });

    it('should enforce 50-token batch limit', async () => {
      const addresses = Array.from({ length: 51 }, (_, i) => 
        `${'1'.repeat(32)}${i.toString().padStart(12, '0')}`
      );

      await expect(adapter.fetchBatchSnapshots(addresses))
        .rejects.toThrow('exceeds Jupiter V3 limit of 50 tokens');
    });

    it('should handle rate limiting error', async () => {
      fetchMock.mockResolvedValueOnce({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests'
      });

      await expect(adapter.fetchBatchSnapshots(['So11111111111111111111111111111111111111112']))
        .rejects.toThrow('Rate limit exceeded');
    });

    it('should handle HTTP errors', async () => {
      fetchMock.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      });

      await expect(adapter.fetchBatchSnapshots(['So11111111111111111111111111111111111111112']))
        .rejects.toThrow('HTTP 500: Internal Server Error');
    });

    it('should handle network timeout', async () => {
      fetchMock.mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('AbortError')), 100)
        )
      );

      const quickAdapter = new JupiterV3Adapter({ 
        tier: 'lite',
        timeout: 50 
      });

      await expect(quickAdapter.fetchBatchSnapshots(['So11111111111111111111111111111111111111112']))
        .rejects.toThrow('Request timeout after 50ms');
    });

    it('should retry on failure', async () => {
      // First call fails, second succeeds
      fetchMock
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            'So11111111111111111111111111111111111111112': {
              usdPrice: 147.48,
              blockId: 348004023,
              decimals: 9,
              priceChange24h: 1.29
            }
          })
        });

      const result = await adapter.fetchBatchSnapshots(['So11111111111111111111111111111111111111112']);
      
      expect(result).toHaveLength(1);
      expect(fetchMock).toHaveBeenCalledTimes(2);
    });

    it('should fail after max retries', async () => {
      fetchMock.mockRejectedValue(new Error('Persistent network error'));

      const quickAdapter = new JupiterV3Adapter({ 
        tier: 'lite',
        maxRetries: 2
      });

      await expect(quickAdapter.fetchBatchSnapshots(['So11111111111111111111111111111111111111112']))
        .rejects.toThrow('failed after 2 attempts');

      expect(fetchMock).toHaveBeenCalledTimes(2);
    });
  });

  describe('response mapping', () => {
    it('should map Jupiter V3 response fields correctly', async () => {
      const mockResponse = {
        'So11111111111111111111111111111111111111112': {
          usdPrice: 147.48,
          blockId: 348004023,
          decimals: 9,
          priceChange24h: 1.29
        }
      };

      fetchMock.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const result = await adapter.fetchTokenSnapshot(
        'So11111111111111111111111111111111111111112'
      );

      // Verify mapped fields
      expect(result.priceUsd).toBeInstanceOf(Decimal);
      expect(result.priceUsd.toNumber()).toBe(147.48);
      expect(result.priceChange24h).toBeInstanceOf(Decimal);
      expect(result.priceChange24h?.toNumber()).toBe(1.29);
      
      // Verify Jupiter V3 doesn't provide these fields
      expect(result.priceChange1h).toBeUndefined();
      expect(result.volume24h).toBeUndefined();
      expect(result.liquidity).toBeUndefined();
      expect(result.fdv).toBeUndefined();
      expect(result.ageInDays).toBeUndefined();
      
      // Verify metadata
      expect(result.source).toBe('jupiter-v3');
      expect(result.lastUpdated).toBeInstanceOf(Date);
    });

    it('should handle missing priceChange24h field', async () => {
      const mockResponse = {
        'So11111111111111111111111111111111111111112': {
          usdPrice: 147.48,
          blockId: 348004023,
          decimals: 9
          // priceChange24h missing
        }
      };

      fetchMock.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const result = await adapter.fetchTokenSnapshot(
        'So11111111111111111111111111111111111111112'
      );

      expect(result.priceChange24h).toBeUndefined();
    });

    it('should skip tokens with invalid usdPrice', async () => {
      const mockResponse = {
        'ValidToken111111111111111111111111111111': {
          usdPrice: 147.48,
          blockId: 348004023,
          decimals: 9,
          priceChange24h: 1.29
        },
        'InvalidToken1111111111111111111111111111': {
          usdPrice: 0, // Invalid price
          blockId: 348004023,
          decimals: 9,
          priceChange24h: 1.29
        }
      };

      fetchMock.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const addresses = [
        'ValidToken111111111111111111111111111111',
        'InvalidToken1111111111111111111111111111'
      ];

      const results = await adapter.fetchBatchSnapshots(addresses);

      // Should only return the valid token
      expect(results).toHaveLength(1);
      expect(results[0].tokenAddress).toBe('ValidToken111111111111111111111111111111');
    });
  });

  describe('isHealthy', () => {
    it('should return true when Jupiter V3 API is responding', async () => {
      fetchMock.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          'So11111111111111111111111111111111111111112': {
            usdPrice: 147.48,
            blockId: 348004023,
            decimals: 9,
            priceChange24h: 1.29
          }
        })
      });

      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(true);
    });

    it('should return false when Jupiter V3 API is not responding', async () => {
      fetchMock.mockRejectedValueOnce(new Error('Service unavailable'));

      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(false);
    });

    it('should return false when Jupiter V3 returns empty response', async () => {
      fetchMock.mockResolvedValueOnce({
        ok: true,
        json: async () => ({})
      });

      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(false);
    });
  });

  describe('authentication and tiers', () => {
    it('should include API key in headers for pro tier', async () => {
      const proAdapter = new JupiterV3Adapter({ 
        tier: 'pro', 
        apiKey: 'test-api-key' 
      });

      fetchMock.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          'So11111111111111111111111111111111111111112': {
            usdPrice: 147.48,
            blockId: 348004023,
            decimals: 9,
            priceChange24h: 1.29
          }
        })
      });

      await proAdapter.fetchTokenSnapshot('So11111111111111111111111111111111111111112');

      expect(fetchMock).toHaveBeenCalledWith(
        expect.stringContaining('api.jup.ag'),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-api-key'
          })
        })
      );
    });

    it('should not include API key in headers for lite tier', async () => {
      fetchMock.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          'So11111111111111111111111111111111111111112': {
            usdPrice: 147.48,
            blockId: 348004023,
            decimals: 9,
            priceChange24h: 1.29
          }
        })
      });

      await adapter.fetchTokenSnapshot('So11111111111111111111111111111111111111112');

      expect(fetchMock).toHaveBeenCalledWith(
        expect.stringContaining('lite-api.jup.ag'),
        expect.objectContaining({
          headers: expect.not.objectContaining({
            'Authorization': expect.anything()
          })
        })
      );
    });
  });

  describe('error handling edge cases', () => {
    it('should handle malformed JSON response', async () => {
      fetchMock.mockResolvedValueOnce({
        ok: true,
        json: async () => {
          throw new Error('Invalid JSON');
        }
      });

      await expect(adapter.fetchBatchSnapshots(['So11111111111111111111111111111111111111112']))
        .rejects.toThrow('failed after 3 attempts');
    });

    it('should handle non-object response', async () => {
      fetchMock.mockResolvedValueOnce({
        ok: true,
        json: async () => "invalid response"
      });

      await expect(adapter.fetchBatchSnapshots(['So11111111111111111111111111111111111111112']))
        .rejects.toThrow('Invalid response format');
    });
  });
});