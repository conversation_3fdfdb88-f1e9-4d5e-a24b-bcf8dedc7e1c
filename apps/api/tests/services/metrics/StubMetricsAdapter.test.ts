import { describe, it, expect, beforeEach } from 'vitest';
import { StubMetricsAdapter } from '../../../src/services/metrics/StubMetricsAdapter';
import { Decimal } from 'decimal.js';

describe('StubMetricsAdapter', () => {
  let adapter: StubMetricsAdapter;

  beforeEach(() => {
    adapter = new StubMetricsAdapter({
      simulateLatency: false,
      errorRate: 0
    });
  });

  describe('getProviderInfo', () => {
    it('should return provider information', () => {
      const info = adapter.getProviderInfo();
      
      expect(info.name).toBe('Stub Provider (Mock Data)');
      expect(info.batchSize).toBe(100);
      expect(info.supportsRealtime).toBe(false);
      expect(info.requiresAuth).toBe(false);
      expect(info.rateLimit.requestsPerSecond).toBe(100);
    });
  });

  describe('fetchTokenSnapshot', () => {
    it('should generate deterministic mock data for valid token address', async () => {
      const tokenAddress = 'So11111111111111111111111111111111111111112';
      
      const snapshot = await adapter.fetchTokenSnapshot(tokenAddress);
      
      expect(snapshot.tokenAddress).toBe(tokenAddress);
      expect(snapshot.priceUsd).toBeInstanceOf(Decimal);
      expect(snapshot.priceUsd.toNumber()).toBeGreaterThan(0);
      expect(snapshot.lastUpdated).toBeInstanceOf(Date);
      expect(snapshot.source).toBe('stub');
    });

    it('should generate consistent data for the same token address', async () => {
      const tokenAddress = 'So11111111111111111111111111111111111111112';
      
      const snapshot1 = await adapter.fetchTokenSnapshot(tokenAddress);
      const snapshot2 = await adapter.fetchTokenSnapshot(tokenAddress);
      
      // Should be deterministic based on address hash
      expect(snapshot1.priceUsd.equals(snapshot2.priceUsd)).toBe(true);
      expect(snapshot1.priceChange1h?.equals(snapshot2.priceChange1h!)).toBe(true);
      expect(snapshot1.priceChange24h?.equals(snapshot2.priceChange24h!)).toBe(true);
    });

    it('should generate different data for different token addresses', async () => {
      const address1 = 'So11111111111111111111111111111111111111112';
      const address2 = '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R';
      
      const snapshot1 = await adapter.fetchTokenSnapshot(address1);
      const snapshot2 = await adapter.fetchTokenSnapshot(address2);
      
      expect(snapshot1.priceUsd.equals(snapshot2.priceUsd)).toBe(false);
    });

    it('should include realistic price changes', async () => {
      const tokenAddress = 'So11111111111111111111111111111111111111112';
      
      const snapshot = await adapter.fetchTokenSnapshot(tokenAddress);
      
      expect(snapshot.priceChange1h).toBeInstanceOf(Decimal);
      expect(snapshot.priceChange24h).toBeInstanceOf(Decimal);
      
      // Price changes should be within reasonable bounds (-50% to +50%)
      expect(snapshot.priceChange1h!.toNumber()).toBeGreaterThanOrEqual(-50);
      expect(snapshot.priceChange1h!.toNumber()).toBeLessThanOrEqual(50);
      expect(snapshot.priceChange24h!.toNumber()).toBeGreaterThanOrEqual(-50);
      expect(snapshot.priceChange24h!.toNumber()).toBeLessThanOrEqual(50);
    });

    it('should include realistic volume and liquidity data', async () => {
      const tokenAddress = 'So11111111111111111111111111111111111111112';
      
      const snapshot = await adapter.fetchTokenSnapshot(tokenAddress);
      
      expect(snapshot.volume24h).toBeInstanceOf(Decimal);
      expect(snapshot.liquidity).toBeInstanceOf(Decimal);
      
      // Volume should be at least $100
      expect(snapshot.volume24h!.toNumber()).toBeGreaterThanOrEqual(100);
      
      // Liquidity should be positive
      expect(snapshot.liquidity!.toNumber()).toBeGreaterThan(0);
    });

    it('should include FDV and age data', async () => {
      const tokenAddress = 'So11111111111111111111111111111111111111112';
      
      const snapshot = await adapter.fetchTokenSnapshot(tokenAddress);
      
      expect(snapshot.fdv).toBeInstanceOf(Decimal);
      expect(snapshot.ageInDays).toBeTypeOf('number');
      
      // FDV should be positive
      expect(snapshot.fdv!.toNumber()).toBeGreaterThan(0);
      
      // Age should be between 1 and 1000 days
      expect(snapshot.ageInDays!).toBeGreaterThanOrEqual(1);
      expect(snapshot.ageInDays!).toBeLessThanOrEqual(1000);
    });

    it('should reject invalid token addresses', async () => {
      const invalidAddress = 'invalid-address';
      
      await expect(adapter.fetchTokenSnapshot(invalidAddress))
        .rejects.toThrow('Invalid token address');
    });

    it('should simulate errors when configured', async () => {
      const errorAdapter = new StubMetricsAdapter({
        simulateLatency: false,
        errorRate: 1.0 // Always error
      });
      
      const tokenAddress = 'So11111111111111111111111111111111111111112';
      
      await expect(errorAdapter.fetchTokenSnapshot(tokenAddress))
        .rejects.toThrow('Simulated network error');
    });

    it('should simulate latency when configured', async () => {
      const latencyAdapter = new StubMetricsAdapter({
        simulateLatency: true,
        minLatencyMs: 100,
        maxLatencyMs: 200
      });
      
      const tokenAddress = 'So11111111111111111111111111111111111111112';
      const startTime = Date.now();
      
      await latencyAdapter.fetchTokenSnapshot(tokenAddress);
      
      const duration = Date.now() - startTime;
      expect(duration).toBeGreaterThanOrEqual(95); // Allow for some timing variance
    });
  });

  describe('fetchBatchSnapshots', () => {
    it('should handle single token batch', async () => {
      const addresses = ['So11111111111111111111111111111111111111112'];
      
      const snapshots = await adapter.fetchBatchSnapshots(addresses);
      
      expect(snapshots).toHaveLength(1);
      expect(snapshots[0].tokenAddress).toBe(addresses[0]);
    });

    it('should handle maximum batch size', async () => {
      const addresses = Array.from({ length: 100 }, (_, i) => 
        `${'1'.repeat(32)}${i.toString().padStart(12, '0')}`
      );
      
      const snapshots = await adapter.fetchBatchSnapshots(addresses);
      
      expect(snapshots).toHaveLength(100);
      snapshots.forEach((snapshot, index) => {
        expect(snapshot.tokenAddress).toBe(addresses[index]);
      });
    });

    it('should reject oversized batches', async () => {
      const addresses = Array.from({ length: 101 }, (_, i) => 
        `${'1'.repeat(32)}${i.toString().padStart(12, '0')}`
      );
      
      await expect(adapter.fetchBatchSnapshots(addresses))
        .rejects.toThrow('exceeds provider limit');
    });

    it('should validate all addresses in batch', async () => {
      const addresses = [
        'So11111111111111111111111111111111111111112',
        'invalid-address'
      ];
      
      await expect(adapter.fetchBatchSnapshots(addresses))
        .rejects.toThrow('Invalid token addresses');
    });

    it('should simulate batch errors when configured', async () => {
      const errorAdapter = new StubMetricsAdapter({
        simulateLatency: false,
        errorRate: 1.0
      });
      
      const addresses = ['So11111111111111111111111111111111111111112'];
      
      await expect(errorAdapter.fetchBatchSnapshots(addresses))
        .rejects.toThrow('Simulated batch request failure');
    });

    it('should have longer latency for batch requests', async () => {
      const latencyAdapter = new StubMetricsAdapter({
        simulateLatency: true,
        minLatencyMs: 100,
        maxLatencyMs: 200
      });
      
      const addresses = ['So11111111111111111111111111111111111111112'];
      const startTime = Date.now();
      
      await latencyAdapter.fetchBatchSnapshots(addresses);
      
      const duration = Date.now() - startTime;
      expect(duration).toBeGreaterThanOrEqual(145); // 1.5x multiplier
    });
  });

  describe('market condition simulation', () => {
    it('should generate more positive changes in bull market', async () => {
      const bullAdapter = new StubMetricsAdapter({
        simulateLatency: false,
        errorRate: 0,
        enableBullMarket: true
      });
      
      // Test with multiple addresses to get statistical significance
      const addresses = Array.from({ length: 20 }, (_, i) => 
        `${'1'.repeat(32)}${i.toString().padStart(12, '0')}`
      );
      
      const snapshots = await bullAdapter.fetchBatchSnapshots(addresses);
      
      const positiveChanges = snapshots.filter(s => 
        s.priceChange24h && s.priceChange24h.toNumber() > 0
      ).length;
      
      // In bull market, expect more than half to be positive
      expect(positiveChanges).toBeGreaterThan(snapshots.length * 0.5);
    });

    it('should generate more negative changes in bear market', async () => {
      const bearAdapter = new StubMetricsAdapter({
        simulateLatency: false,
        errorRate: 0,
        enableBearMarket: true
      });
      
      const addresses = Array.from({ length: 20 }, (_, i) => 
        `${'2'.repeat(32)}${i.toString().padStart(12, '0')}`
      );
      
      const snapshots = await bearAdapter.fetchBatchSnapshots(addresses);
      
      const negativeChanges = snapshots.filter(s => 
        s.priceChange24h && s.priceChange24h.toNumber() < 0
      ).length;
      
      // In bear market, expect more than half to be negative
      expect(negativeChanges).toBeGreaterThan(snapshots.length * 0.5);
    });
  });

  describe('rate limit compliance', () => {
    it('should stay within configured rate limits', async () => {
      const providerInfo = adapter.getProviderInfo();
      const maxConcurrent = Math.min(10, providerInfo.rateLimit.requestsPerSecond);
      
      const addresses = Array.from({ length: maxConcurrent }, (_, i) => 
        `${'3'.repeat(32)}${i.toString().padStart(12, '0')}`
      );
      
      const startTime = Date.now();
      const promises = addresses.map(addr => adapter.fetchTokenSnapshot(addr));
      await Promise.all(promises);
      const duration = Date.now() - startTime;
      
      // With no latency simulation, this should complete very quickly
      expect(duration).toBeLessThan(1000);
    });
  });

  describe('error conditions', () => {
    it('should simulate network errors', async () => {
      const errorAdapter = new StubMetricsAdapter({
        simulateLatency: false,
        errorRate: 0.5 // 50% error rate
      });
      
      const tokenAddress = 'So11111111111111111111111111111111111111112';
      let errors = 0;
      let successes = 0;
      
      // Run multiple requests to test error rate
      for (let i = 0; i < 20; i++) {
        try {
          await errorAdapter.fetchTokenSnapshot(tokenAddress);
          successes++;
        } catch (error) {
          errors++;
        }
      }
      
      // Should have some errors (not exact due to randomness)
      expect(errors).toBeGreaterThan(0);
      expect(successes).toBeGreaterThan(0);
    });

    it('should simulate timeout errors', async () => {
      const timeoutAdapter = new StubMetricsAdapter({
        simulateLatency: true,
        minLatencyMs: 1000,
        maxLatencyMs: 1000
      });
      
      const tokenAddress = 'So11111111111111111111111111111111111111112';
      
      // This won't actually timeout unless we implement timeout in the adapter
      // But we can verify the latency is being simulated
      const startTime = Date.now();
      await timeoutAdapter.fetchTokenSnapshot(tokenAddress);
      const duration = Date.now() - startTime;
      
      expect(duration).toBeGreaterThanOrEqual(990);
    });

    it('should simulate rate limit errors', async () => {
      // The stub adapter doesn't actually implement rate limiting
      // But we can test that it validates batch size limits
      const addresses = Array.from({ length: 101 }, (_, i) => 
        `${'4'.repeat(32)}${i.toString().padStart(12, '0')}`
      );
      
      await expect(adapter.fetchBatchSnapshots(addresses))
        .rejects.toThrow('BATCH_SIZE_EXCEEDED');
    });
  });

  describe('isHealthy', () => {
    it('should report as healthy most of the time', async () => {
      let healthyCount = 0;
      
      // Check health multiple times
      for (let i = 0; i < 10; i++) {
        const isHealthy = await adapter.isHealthy();
        if (isHealthy) healthyCount++;
      }
      
      // Should be healthy most of the time (>90%)
      expect(healthyCount).toBeGreaterThanOrEqual(9);
    });

    it('should occasionally report as unhealthy', async () => {
      let unhealthyCount = 0;
      
      // Check health many times to catch occasional unhealthy state
      for (let i = 0; i < 100; i++) {
        const isHealthy = await adapter.isHealthy();
        if (!isHealthy) unhealthyCount++;
      }
      
      // Should be unhealthy ~1% of the time
      expect(unhealthyCount).toBeGreaterThanOrEqual(0);
    });
  });
});