import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { WatchlistService } from '../../src/services/WatchlistService';

// Mock @prisma/client
vi.mock('@prisma/client', () => {
  // Create error class inside the mock
  class MockPrismaClientKnownRequestError extends Error {
    code: string;
    constructor(message: string, code: string) {
      super(message);
      this.code = code;
    }
  }

  const mockPrismaClient = {
    watchlistItem: {
      findMany: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      count: vi.fn()
    }
  };
  
  return {
    PrismaClient: vi.fn(() => mockPrismaClient),
    Prisma: {
      PrismaClientKnownRequestError: MockPrismaClientKnownRequestError
    }
  };
});

// Mock @solana/web3.js
vi.mock('@solana/web3.js', () => ({
  PublicKey: vi.fn().mockImplementation((address: string) => {
    if (!address || address.length < 32) {
      throw new Error('Invalid public key');
    }
    return { toString: () => address };
  })
}));

// Mock shared types
vi.mock('../../../../packages/shared/src/types/watchlist', () => ({
  WATCHLIST_ERRORS: {
    INVALID_TOKEN_ADDRESS: 'Invalid Solana token address format',
    DUPLICATE_TOKEN: 'Token already exists in watchlist',
    ITEM_NOT_FOUND: 'Watchlist item not found'
  }
}));

describe('WatchlistService', () => {
  let service: WatchlistService;
  let prisma: any;

  beforeEach(async () => {
    // Import after mocking
    const { PrismaClient } = await import('@prisma/client');
    prisma = new PrismaClient();
    service = new WatchlistService(prisma);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('findAll', () => {
    it('should return all active watchlist items sorted by isPinned and addedAt', async () => {
      const mockItems = [
        {
          id: '1',
          tokenAddress: 'token1',
          tokenSymbol: 'TK1',
          tokenName: 'Token 1',
          isPinned: true,
          isActive: true,
          addedAt: new Date('2024-01-01')
        },
        {
          id: '2',
          tokenAddress: 'token2',
          tokenSymbol: 'TK2',
          tokenName: 'Token 2',
          isPinned: false,
          isActive: true,
          addedAt: new Date('2024-01-02')
        }
      ];

      prisma.watchlistItem.findMany.mockResolvedValue(mockItems);

      const result = await service.findAll();

      expect(prisma.watchlistItem.findMany).toHaveBeenCalledWith({
        where: { isActive: true },
        orderBy: [
          { isPinned: 'desc' },
          { addedAt: 'asc' }
        ]
      });
      expect(result).toEqual(mockItems);
    });
  });

  describe('findById', () => {
    it('should return a watchlist item by id', async () => {
      const mockItem = {
        id: '1',
        tokenAddress: 'token1',
        tokenSymbol: 'TK1',
        tokenName: 'Token 1',
        isActive: true
      };

      prisma.watchlistItem.findFirst.mockResolvedValue(mockItem);

      const result = await service.findById('1');

      expect(prisma.watchlistItem.findFirst).toHaveBeenCalledWith({
        where: {
          id: '1',
          isActive: true
        }
      });
      expect(result).toEqual(mockItem);
    });

    it('should return null if item not found', async () => {
      prisma.watchlistItem.findFirst.mockResolvedValue(null);

      const result = await service.findById('nonexistent');

      expect(result).toBeNull();
    });
  });

  describe('create', () => {
    it('should create a new watchlist item with valid token address', async () => {
      const createData = {
        tokenAddress: 'So11111111111111111111111111111111111111112',
        tokenSymbol: 'SOL',
        tokenName: 'Solana',
        customName: 'My SOL',
        notes: 'Main token'
      };

      const mockCreatedItem = {
        id: '1',
        ...createData,
        isPinned: false,
        isActive: true,
        addedAt: new Date(),
        updatedAt: new Date()
      };

      prisma.watchlistItem.create.mockResolvedValue(mockCreatedItem);

      const result = await service.create(createData);

      expect(prisma.watchlistItem.create).toHaveBeenCalledWith({
        data: {
          tokenAddress: createData.tokenAddress,
          tokenSymbol: createData.tokenSymbol,
          tokenName: createData.tokenName,
          customName: createData.customName,
          notes: createData.notes,
          isPinned: false,
          isActive: true
        }
      });
      expect(result).toEqual(mockCreatedItem);
    });

    it('should throw error for invalid token address', async () => {
      const createData = {
        tokenAddress: 'invalid',
        tokenSymbol: 'INV',
        tokenName: 'Invalid',
      };

      await expect(service.create(createData)).rejects.toThrow('Invalid Solana token address format');
      expect(prisma.watchlistItem.create).not.toHaveBeenCalled();
    });

    it('should throw error for duplicate token address', async () => {
      const createData = {
        tokenAddress: 'So11111111111111111111111111111111111111112',
        tokenSymbol: 'SOL',
        tokenName: 'Solana',
      };

      const { Prisma } = await import('@prisma/client');
      const error = new Prisma.PrismaClientKnownRequestError('Unique constraint failed', 'P2002');
      prisma.watchlistItem.create.mockRejectedValue(error);

      await expect(service.create(createData)).rejects.toThrow('Token already exists in watchlist');
    });
  });

  describe('update', () => {
    it('should update a watchlist item', async () => {
      const mockItem = {
        id: '1',
        tokenAddress: 'token1',
        isActive: true
      };

      const updateData = {
        customName: 'Updated Name',
        notes: 'Updated notes',
        isPinned: true
      };

      const mockUpdatedItem = {
        ...mockItem,
        ...updateData
      };

      prisma.watchlistItem.findFirst.mockResolvedValue(mockItem);
      prisma.watchlistItem.update.mockResolvedValue(mockUpdatedItem);

      const result = await service.update('1', updateData);

      expect(prisma.watchlistItem.update).toHaveBeenCalledWith({
        where: { id: '1' },
        data: updateData
      });
      expect(result).toEqual(mockUpdatedItem);
    });

    it('should throw error if item not found', async () => {
      prisma.watchlistItem.findFirst.mockResolvedValue(null);

      await expect(service.update('nonexistent', {})).rejects.toThrow('Watchlist item not found');
      expect(prisma.watchlistItem.update).not.toHaveBeenCalled();
    });
  });

  describe('softDelete', () => {
    it('should soft delete a watchlist item', async () => {
      const mockItem = {
        id: '1',
        tokenAddress: 'token1',
        isActive: true
      };

      const mockDeletedItem = {
        ...mockItem,
        isActive: false
      };

      prisma.watchlistItem.findFirst.mockResolvedValue(mockItem);
      prisma.watchlistItem.update.mockResolvedValue(mockDeletedItem);

      const result = await service.softDelete('1');

      expect(prisma.watchlistItem.update).toHaveBeenCalledWith({
        where: { id: '1' },
        data: { isActive: false }
      });
      expect(result).toEqual(mockDeletedItem);
    });

    it('should throw error if item not found', async () => {
      prisma.watchlistItem.findFirst.mockResolvedValue(null);

      await expect(service.softDelete('nonexistent')).rejects.toThrow('Watchlist item not found');
      expect(prisma.watchlistItem.update).not.toHaveBeenCalled();
    });
  });

  describe('togglePin', () => {
    it('should toggle pin status of a watchlist item', async () => {
      const mockItem = {
        id: '1',
        tokenAddress: 'token1',
        isPinned: false,
        isActive: true
      };

      const mockToggledItem = {
        ...mockItem,
        isPinned: true
      };

      prisma.watchlistItem.findFirst.mockResolvedValue(mockItem);
      prisma.watchlistItem.update.mockResolvedValue(mockToggledItem);

      const result = await service.togglePin('1');

      expect(prisma.watchlistItem.update).toHaveBeenCalledWith({
        where: { id: '1' },
        data: { isPinned: true }
      });
      expect(result).toEqual(mockToggledItem);
    });
  });

  describe('countActive', () => {
    it('should return count of active watchlist items', async () => {
      prisma.watchlistItem.count.mockResolvedValue(5);

      const result = await service.countActive();

      expect(prisma.watchlistItem.count).toHaveBeenCalledWith({
        where: { isActive: true }
      });
      expect(result).toBe(5);
    });
  });

  describe('findPinned', () => {
    it('should return all pinned active items', async () => {
      const mockPinnedItems = [
        {
          id: '1',
          tokenAddress: 'token1',
          isPinned: true,
          isActive: true
        },
        {
          id: '2',
          tokenAddress: 'token2',
          isPinned: true,
          isActive: true
        }
      ];

      prisma.watchlistItem.findMany.mockResolvedValue(mockPinnedItems);

      const result = await service.findPinned();

      expect(prisma.watchlistItem.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
          isPinned: true
        },
        orderBy: { addedAt: 'desc' }
      });
      expect(result).toEqual(mockPinnedItems);
    });
  });
});