const { Client } = require('pg');

const client = new Client({
  user: 'trader',
  host: 'localhost',
  database: 'trading',
  password: 'dev_password_123',
  port: 5432,
});

async function testConnection() {
  try {
    await client.connect();
    console.log('✅ Connected successfully!');
    
    const res = await client.query('SELECT current_user, current_database(), version()');
    console.log('Current user:', res.rows[0].current_user);
    console.log('Current database:', res.rows[0].current_database);
    
    const schemaRes = await client.query(`
      SELECT schema_name, schema_owner 
      FROM information_schema.schemata 
      WHERE schema_name = 'public'
    `);
    console.log('Public schema owner:', schemaRes.rows[0]?.schema_owner);
    
    const privRes = await client.query(`
      SELECT has_database_privilege('trader', 'trading', 'CREATE') as can_create,
             has_schema_privilege('trader', 'public', 'CREATE') as can_create_schema,
             has_schema_privilege('trader', 'public', 'USAGE') as can_use_schema
    `);
    console.log('Privileges:', privRes.rows[0]);
    
    await client.end();
  } catch (err) {
    console.error('❌ Connection error:', err.message);
  }
}

testConnection();