#!/bin/bash

# Color codes for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting price history collection...${NC}"
echo "Recording snapshots every 60 seconds"
echo "Press Ctrl+C to stop"
echo ""

COUNTER=0
FAILURES=0

while true; do
    COUNTER=$((COUNTER + 1))
    TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo -n "[$TIMESTAMP] Snapshot #$COUNTER: "
    
    # Take snapshot and capture response
    RESPONSE=$(curl -s -X POST http://localhost:3001/api/watchlist/snapshot)
    
    # Check if successful
    if echo "$RESPONSE" | grep -q '"success":true'; then
        echo -e "${GREEN}✓ Success${NC}"
        
        # After 5 minutes, show that 5m data is available
        if [ $COUNTER -eq 5 ]; then
            echo -e "${YELLOW}→ 5-minute price changes now available!${NC}"
        fi
        
        # After 60 minutes, show that 1h data is available
        if [ $COUNTER -eq 60 ]; then
            echo -e "${YELLOW}→ 1-hour price changes now available!${NC}"
        fi
    else
        echo -e "${RED}✗ Failed${NC}"
        echo "  Error: $RESPONSE"
        FAILURES=$((FAILURES + 1))
        
        # If too many failures, warn user
        if [ $FAILURES -gt 5 ]; then
            echo -e "${RED}Warning: Multiple failures detected. Check your API server.${NC}"
            FAILURES=0
        fi
    fi
    
    # Show statistics every 10 snapshots
    if [ $((COUNTER % 10)) -eq 0 ]; then
        echo -e "${YELLOW}Stats: $COUNTER snapshots recorded${NC}"
    fi
    
    # Wait 60 seconds
    sleep 60
done