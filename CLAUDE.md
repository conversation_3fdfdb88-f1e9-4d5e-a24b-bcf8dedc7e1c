# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Solana Trading App built using the BMad development methodology. It's a monorepo project designed for automated meme coin trading with manual buy execution and sophisticated automated exit strategies. The system integrates Jupiter Aggregator for DEX routing, <PERSON><PERSON> for blockchain access, and implements real-time monitoring with sub-5-second execution requirements.

## Architecture

The project follows a **monorepo structure** with clear separation of concerns:

- **Frontend**: Next.js 14 with App Router, TypeScript, Tailwind CSS, shadcn/ui components, Zustand state management
- **Backend**: Express.js with TypeScript, BullMQ job queues, Prisma ORM with PostgreSQL + TimescaleDB
- **Deployment**: Vercel (frontend) + Railway (backend, database, Redis)
- **External Integrations**: Jupiter Aggregator, Helius RPC, CoinMarketCap API, Telegram Bot

Key architectural patterns:
- **Repository Pattern** for data access abstraction
- **Job Queue Pattern** with BullMQ for reliable trading operations  
- **State-Aware Polling** for optimized API usage within rate limits
- **Circuit Breaker Pattern** for external API failure handling

## Development Commands

Based on the tech stack (when implementation begins):

```bash
# Install dependencies
npm install

# Frontend development
npm run dev:web

# Backend development  
npm run dev:api

# Run all tests
npm run test

# Run linting
npm run lint

# Type checking
npm run typecheck

# Build for production
npm run build

# Database operations
npm run db:migrate
npm run db:seed
npm run db:studio
```

## Critical Development Rules

### Type Safety
- **Always define types in `packages/shared`** - prevents frontend/backend type mismatches
- Import shared types across frontend and backend boundaries
- Access environment variables only through config objects, never `process.env` directly

### API & Service Layer
- **Never make direct HTTP calls** - use the service layer for consistent error handling
- All API routes must use the standard error handler
- Use repository pattern with Prisma for all database queries

### Financial & Trading Specific
- **Use Decimal.js for ALL financial calculations** - prevents floating point precision errors  
- Always handle Promise rejections in async trading operations
- Validate all configuration at startup - fail fast if environment is misconfigured

### State Management
- Never mutate state directly - use Zustand patterns
- Frontend: Domain-specific stores (tradingStore, positionStore, watchlistStore, systemStore)
- Backend: Stateless request/response with job queues for long-running operations

## Testing Strategy

Follow testing pyramid approach:
- **Unit Tests**: Vitest for both frontend and backend
- **Integration Tests**: API endpoints and database operations  
- **E2E Tests**: Playwright for critical trading workflows

Test organization:
```
apps/web/tests/         # Frontend tests
apps/api/tests/         # Backend tests
packages/shared/tests/  # Shared utility tests
```

## BMad Development Workflow

This project uses the BMad methodology:
- Stories are located in `docs/stories/`
- Architecture documentation in `docs/architecture/`
- Core configuration in `.bmad-core/core-config.yaml`
- Use BMad agent commands with `/BMad:` prefix

## Performance Requirements

- Sub-5-second execution for all exit triggers
- Optimize for Helius free tier (1M credits/month, 10 req/sec)
- MEV protection for all transactions
- Real-time price monitoring with dynamic polling intervals

## Development Context

The project is currently in the design phase with comprehensive documentation. When beginning implementation, follow the established patterns in:
- Coding standards: `docs/architecture/coding-standards.md`
- Tech stack specifications: `docs/architecture/tech-stack.md` 
- Component organization: `docs/architecture/source-tree.md`